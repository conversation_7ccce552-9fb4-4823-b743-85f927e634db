# 🔧 Guide de Résolution des Problèmes - Tableau de Bord BI

## 🚨 **Problème Actuel Identifié**

L'interface affiche "Erreur lors du chargement des métriques" et une erreur 404 dans la console. Cela indique que l'endpoint backend n'est pas accessible.

## 🔍 **Diagnostic Étape par Étape**

### **Étape 1: Vérifier le Backend Django**

1. **Vérifiez que le serveur Django est démarré:**
   ```bash
   cd backend
   python manage.py runserver
   ```

2. **Vérifiez que le serveur écoute sur le bon port:**
   - Le serveur doit être accessible sur `http://localhost:8000`
   - Testez avec: `curl http://localhost:8000/admin/` (doit retourner une page Django)

### **Étape 2: Vérifier l'Endpoint BI**

1. **Testez l'endpoint directement:**
   ```bash
   curl -X GET http://localhost:8000/api/bi/super-admin/dashboard/ \
        -H "Authorization: Bearer YOUR_TOKEN" \
        -H "Content-Type: application/json"
   ```

2. **Vérifiez les URLs Django:**
   - L'endpoint doit être défini dans `urls.py`
   - Le chemin doit être exactement: `/api/bi/super-admin/dashboard/`

### **Étape 3: Vérifier l'Authentification**

1. **Connectez-vous en tant que Super Admin**
2. **Vérifiez le token dans la console du navigateur:**
   ```javascript
   console.log(localStorage.getItem('authToken'));
   ```

3. **Vérifiez le rôle utilisateur:**
   ```javascript
   console.log(JSON.parse(localStorage.getItem('user')));
   ```

## 🛠️ **Solutions Possibles**

### **Solution 1: Backend Non Démarré**
```bash
# Dans le dossier backend
python manage.py runserver
```

### **Solution 2: Endpoint Manquant**
Vérifiez que l'endpoint existe dans votre backend Django:
```python
# Dans urls.py
path('api/bi/super-admin/dashboard/', views.super_admin_dashboard, name='super_admin_dashboard'),
```

### **Solution 3: Problème de CORS**
Ajoutez dans `settings.py`:
```python
CORS_ALLOWED_ORIGINS = [
    "http://localhost:5173",
    "http://127.0.0.1:5173",
]
```

### **Solution 4: Token Expiré**
1. Déconnectez-vous et reconnectez-vous
2. Ou rafraîchissez le token manuellement

## 🧪 **Outils de Test Disponibles**

### **1. Page de Test BI**
- URL: `http://localhost:5173/super-admin/bi-test`
- Effectue un diagnostic complet de l'endpoint

### **2. Console du Navigateur**
Ouvrez F12 et regardez les logs:
```javascript
// Logs de succès attendus
BiService - Récupération du tableau de bord Super Admin...
BiService - Tableau de bord Super Admin récupéré avec succès

// Logs d'erreur
BiService - Response error: {status: 404, url: "/api/bi/super-admin/dashboard/"}
```

### **3. Onglet Network**
- Vérifiez les requêtes HTTP dans l'onglet Network
- Recherchez la requête vers `/api/bi/super-admin/dashboard/`
- Vérifiez le status code (200 = OK, 404 = Not Found, 401 = Unauthorized)

## 📋 **Checklist de Vérification**

- [ ] Backend Django démarré sur port 8000
- [ ] Frontend React démarré sur port 5173
- [ ] Utilisateur connecté en tant que super_admin
- [ ] Token d'authentification présent
- [ ] Endpoint `/api/bi/super-admin/dashboard/` existe dans le backend
- [ ] CORS configuré correctement
- [ ] Pas d'erreurs dans les logs Django

## 🎯 **Actions Immédiates Recommandées**

1. **Testez la page de diagnostic:** `http://localhost:5173/super-admin/bi-test`
2. **Vérifiez les logs de la console du navigateur (F12)**
3. **Démarrez le backend Django si nécessaire**
4. **Testez l'endpoint manuellement avec curl ou Postman**

## 📞 **Si le Problème Persiste**

Fournissez ces informations:
1. **Status du backend:** Démarré/Arrêté
2. **Logs de la console:** Copier les erreurs exactes
3. **Status code HTTP:** De la requête vers l'endpoint BI
4. **Rôle utilisateur:** Affiché dans le diagnostic
5. **Token présent:** Oui/Non

L'interface utilise des **données mockées de secours** si l'endpoint n'est pas accessible, donc elle devrait toujours afficher quelque chose, même en cas d'erreur backend.
