Stack trace:
Frame         Function      Args
0007FFFFABC0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF9AC0) msys-2.0.dll+0x1FE8E
0007FFFFABC0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFAE98) msys-2.0.dll+0x67F9
0007FFFFABC0  000210046832 (000210286019, 0007FFFFAA78, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFABC0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFABC0  000210068E24 (0007FFFFABD0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFAEA0  00021006A225 (0007FFFFABD0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF8A07E0000 ntdll.dll
7FF89F560000 KERNEL32.DLL
7FF89DD10000 KERNELBASE.dll
7FF89ED80000 USER32.dll
000210040000 msys-2.0.dll
7FF89E2E0000 win32u.dll
7FF89E5E0000 GDI32.dll
7FF89E1A0000 gdi32full.dll
7FF89E490000 msvcp_win.dll
7FF89DBC0000 ucrtbase.dll
7FF8A0620000 advapi32.dll
7FF8A00E0000 msvcrt.dll
7FF89E770000 sechost.dll
7FF89EFB0000 RPCRT4.dll
7FF89CFB0000 CRYPTBASE.DLL
7FF89E540000 bcryptPrimitives.dll
7FF8A0760000 IMM32.DLL
