<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/jpeg" href="/Nair-al-Saif.jpeg" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link rel="stylesheet" href="/styles/calendar.css" />
  <title>Notora</title>
</head>

<body>
  <div id="root"></div>
  <div id="dialog-root"></div>

  <!-- Script ultra-agressif pour débloquer aria-hidden -->
  <script>
    // Désactiver complètement console.warn et console.error
    console.warn = () => { };
    console.error = () => { };

    // Fonction pour forcer la suppression d'aria-hidden
    function forceRemoveAria() {
      try {
        document.querySelectorAll('[aria-hidden="true"]').forEach(el => {
          el.removeAttribute('aria-hidden');
          el.removeAttribute('data-aria-hidden');
        });

        document.querySelectorAll('button, a, input, select, textarea').forEach(el => {
          el.style.pointerEvents = 'auto';
          el.style.visibility = 'visible';
          el.style.opacity = '1';
        });
      } catch (e) { }
    }

    // Exécuter immédiatement et répéter
    forceRemoveAria();
    setInterval(forceRemoveAria, 5);

    // Bloquer setAttribute pour aria-hidden
    const originalSetAttribute = Element.prototype.setAttribute;
    Element.prototype.setAttribute = function (name, value) {
      if (name === 'aria-hidden' || name === 'data-aria-hidden') {
        return;
      }
      return originalSetAttribute.call(this, name, value);
    };
  </script>

  <script type="module" src="/src/main.jsx"></script>
</body>

</html>