{"name": "mon-app-react", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.1.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "axios": "^1.8.4", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^10.16.4", "i18next": "^23.7.16", "i18next-browser-languagedetector": "^7.2.0", "lucide-react": "^0.263.1", "moment": "^2.30.1", "react": "18.2.0", "react-beautiful-dnd": "^13.1.1", "react-big-calendar": "^1.18.0", "react-chartjs-2": "^5.3.0", "react-dom": "18.2.0", "react-hook-form": "^7.49.3", "react-i18next": "^14.0.0", "react-router-dom": "6.22.1", "react-toastify": "^11.0.5", "tailwind-merge": "^2.6.0"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.33", "tailwindcss": "^3.4.1", "vite": "^6.2.5"}}