/* Styles pour le calendrier */
.notion-calendar-style {
  font-family: 'Inter', sans-serif;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e2e8f0;
}

.notion-calendar-style .rbc-header {
  padding: 10px;
  font-weight: 600;
  font-size: 0.875rem;
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.notion-calendar-style .rbc-month-view {
  border: none;
}

.notion-calendar-style .rbc-day-bg {
  background-color: #ffffff;
}

.notion-calendar-style .rbc-off-range-bg {
  background-color: #f8fafc;
}

.notion-calendar-style .rbc-today {
  background-color: #ebf4ff;
}

.notion-calendar-style .rbc-event {
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  padding: 2px 5px;
}

.notion-calendar-style .rbc-toolbar {
  margin-bottom: 20px;
  padding: 10px;
  border-bottom: 1px solid #e2e8f0;
}

.notion-calendar-style .rbc-toolbar button {
  color: #4a5568;
  background-color: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 0.875rem;
  font-weight: 500;
}

.notion-calendar-style .rbc-toolbar button:hover {
  background-color: #f8fafc;
  border-color: #cbd5e0;
}

.notion-calendar-style .rbc-toolbar button.rbc-active {
  background-color: #6B4EFF;
  color: white;
  border-color: #6B4EFF;
}

.notion-calendar-style .rbc-toolbar button.rbc-active:hover {
  background-color: #5b3dff;
  border-color: #5b3dff;
}

.notion-calendar-style .rbc-agenda-view table.rbc-agenda-table {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.notion-calendar-style .rbc-agenda-view table.rbc-agenda-table thead>tr>th {
  padding: 10px;
  font-weight: 600;
  font-size: 0.875rem;
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.notion-calendar-style .rbc-agenda-view table.rbc-agenda-table tbody>tr>td {
  padding: 10px;
  border-bottom: 1px solid #e2e8f0;
}

.notion-calendar-style .rbc-agenda-view table.rbc-agenda-table tbody>tr:last-child>td {
  border-bottom: none;
}

.notion-calendar-style .rbc-agenda-time-cell {
  font-size: 0.875rem;
  color: #4a5568;
}

.notion-calendar-style .rbc-agenda-date-cell,
.notion-calendar-style .rbc-agenda-event-cell {
  font-size: 0.875rem;
  color: #1a202c;
}

.calendar-container {
  padding: 20px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* Styles pour les événements archivés */
.event-archived {
  opacity: 0.7 !important;
  text-decoration: line-through !important;
  font-style: italic !important;
  background-color: #F3F4F6 !important;
  color: #6B7280 !important;
}

/* Forcer le style barré pour tous les événements archivés */
.rbc-event.event-archived,
.rbc-event[data-archived="true"] {
  text-decoration: line-through !important;
  opacity: 0.7 !important;
}

/* Style barré pour le titre des événements archivés */
.rbc-event-content {
  text-decoration: inherit !important;
}

/* Forcer le style barré même avec des styles inline */
.notion-calendar-style .rbc-event[data-archived="true"] * {
  text-decoration: line-through !important;
}