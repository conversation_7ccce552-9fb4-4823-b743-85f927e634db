
import { ThemeProvider } from '@/contexts/ThemeContext';
import { TeamTaskProvider } from '@/contexts/TeamTaskContext';
import { PersonalEventProvider } from '@/contexts/PersonalEventContext';
import { PersonalTaskProvider } from '@/contexts/PersonalTaskContext';
import { PersonalNoteProvider } from '@/contexts/PersonalNoteContext';
import { PersonalJournalProvider } from '@/contexts/PersonalJournalContext';
import { ClientProvider } from '@/contexts/ClientContext';
import { AdminProvider } from '@/contexts/AdminContext';
import { EmployeeProvider } from '@/contexts/EmployeeContext';
import { SuperAdminProvider } from '@/contexts/SuperAdminContext';
import AnimatedRoutes from '@/routes/AnimatedRoutes';

function App() {
  return (
    <ThemeProvider>
      {/* Remarque: AuthProvider, TeamProvider et EventProvider sont déjà inclus dans main.jsx */}
      <TeamTaskProvider>
        <PersonalEventProvider>
          <PersonalTaskProvider>
            <PersonalNoteProvider>
              <PersonalJournalProvider>
                <ClientProvider>
                  <AdminProvider>
                    <EmployeeProvider>
                      <SuperAdminProvider>
                        <AnimatedRoutes />
                        {/* ToastContainer est déjà inclus dans main.jsx */}
                      </SuperAdminProvider>
                    </EmployeeProvider>
                  </AdminProvider>
                </ClientProvider>
              </PersonalJournalProvider>
            </PersonalNoteProvider>
          </PersonalTaskProvider>
        </PersonalEventProvider>
      </TeamTaskProvider>
    </ThemeProvider>
  );
}

export default App;