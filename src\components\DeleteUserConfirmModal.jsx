
import { X } from 'lucide-react';

const DeleteUserConfirmModal = ({ isOpen, onClose, onConfirm, userName, userRole }) => {
    if (!isOpen) return null;

    // Déterminer le message spécifique en fonction du rôle
    const getRoleSpecificMessage = () => {
        switch (userRole) {
            case 'admin':
                return (
                    <div className="mt-2">
                        <p className="font-semibold text-red-600">Conséquences spécifiques pour un administrateur :</p>
                        <ul className="list-disc pl-5 mt-1 text-sm">
                            <li>Toutes les équipes dont cet administrateur est responsable seront supprimées</li>
                            <li>Tous les événements et tâches associés à ces équipes seront supprimés</li>
                            <li>Les membres de ces équipes perdront l'accès à ces ressources</li>
                        </ul>
                    </div>
                );
            case 'employee':
                return (
                    <div className="mt-2">
                        <p className="font-semibold text-amber-600">Conséquences spécifiques pour un employé :</p>
                        <ul className="list-disc pl-5 mt-1 text-sm">
                            <li>L'employé sera retiré de toutes les équipes dont il est membre</li>
                            <li>Tous ses événements personnels seront supprimés</li>
                            <li>Toutes ses tâches personnelles seront supprimées</li>
                            <li>Les références à cet employé dans les tâches d'équipe seront supprimées</li>
                        </ul>
                    </div>
                );
            default:
                return (
                    <div className="mt-2">
                        <p className="font-semibold text-blue-600">Conséquences spécifiques pour un client :</p>
                        <ul className="list-disc pl-5 mt-1 text-sm">
                            <li>Tous ses événements personnels seront supprimés</li>
                            <li>Toutes ses tâches personnelles seront supprimées</li>
                        </ul>
                    </div>
                );
        }
    };

    return (
        <div className="fixed inset-0 flex items-center justify-center p-4 z-50">
            <div className="fixed inset-0 bg-black/50 backdrop-blur-sm" onClick={onClose}></div>
            <div className="bg-white rounded-xl shadow-xl w-full max-w-md relative">
                <div className="p-6">
                    <div className="flex justify-between items-center mb-4">
                        <h2 className="text-xl font-bold text-gray-900">Confirmer la suppression</h2>
                        <button
                            onClick={onClose}
                            className="text-gray-400 hover:text-gray-500 transition-colors"
                        >
                            <X className="w-5 h-5" />
                        </button>
                    </div>

                    <div className="space-y-4">
                        <div className="bg-red-50 p-4 rounded-lg border border-red-100">
                            <p className="text-red-800 font-medium">
                                Vous êtes sur le point de supprimer l'utilisateur <span className="font-bold">{userName}</span>.
                            </p>
                            <p className="text-red-700 mt-1 text-sm">
                                Cette action est irréversible et entraînera la suppression de toutes les données associées à cet utilisateur.
                            </p>
                        </div>

                        <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                            <p className="font-medium text-gray-800">Cette suppression entraînera :</p>
                            <ul className="list-disc pl-5 mt-2 text-sm text-gray-700 space-y-1">
                                <li>La suppression de l'utilisateur</li>
                                <li>La suppression de toutes ses données personnelles</li>
                                <li>La mise à jour de toutes les références à cet utilisateur</li>
                            </ul>

                            {getRoleSpecificMessage()}
                        </div>

                        <p className="text-gray-700 text-sm">
                            Êtes-vous sûr de vouloir continuer ?
                        </p>
                    </div>

                    <div className="flex justify-end gap-3 mt-6">
                        <button
                            type="button"
                            onClick={onClose}
                            className="px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-800 transition-colors"
                        >
                            Annuler
                        </button>
                        <button
                            type="button"
                            onClick={onConfirm}
                            className="px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700 transition-colors"
                        >
                            Supprimer définitivement
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default DeleteUserConfirmModal;
