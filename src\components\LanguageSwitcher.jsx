import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { cn } from '@/lib/utils';

const LanguageSwitcher = ({ className }) => {
    const { i18n } = useTranslation();
    const [currentLanguage, setCurrentLanguage] = useState(i18n.language);

    useEffect(() => {
        setCurrentLanguage(i18n.language);
    }, [i18n.language]);

    const toggleLanguage = () => {
        const newLang = currentLanguage === 'fr' ? 'en' : 'fr';
        i18n.changeLanguage(newLang);
        setCurrentLanguage(newLang);
    };

    return (
        <button
            onClick={toggleLanguage}
            className={cn("px-2 py-1 text-sm font-medium rounded hover:bg-muted", className)}
        >
            {currentLanguage === 'fr' ? 'EN' : 'FR'}
        </button>
    );
};

export default LanguageSwitcher;