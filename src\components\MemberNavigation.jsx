import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Calendar, Users, User, Archive, List, CheckSquare } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

const MemberNavigation = () => {
    const location = useLocation();
    const { user } = useAuth();

    // Vérifier si le lien est actif
    const isActive = (path) => location.pathname === path;

    // Navigation différente selon le rôle
    if (user?.role === 'admin') {
        return (
            <div className="bg-white shadow-sm rounded-lg p-4 mb-6">
                <nav className="flex flex-wrap gap-2">
                    <Link
                        to="/teams"
                        className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${isActive('/teams') ? 'bg-[#6B4EFF] text-white' : 'hover:bg-gray-100 text-gray-700'}`}
                    >
                        <Users className="w-4 h-4" />
                        <span>Me<PERSON>pes</span>
                    </Link>

                    <Link
                        to="/team-calendar"
                        className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${isActive('/team-calendar') ? 'bg-[#6B4EFF] text-white' : 'hover:bg-gray-100 text-gray-700'}`}
                    >
                        <Calendar className="w-4 h-4" />
                        <span>Calendrier d'équipes</span>
                    </Link>

                    <Link
                        to="/team-tasks"
                        className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${isActive('/team-tasks') ? 'bg-[#6B4EFF] text-white' : 'hover:bg-gray-100 text-gray-700'}`}
                    >
                        <CheckSquare className="w-4 h-4" />
                        <span>Tâches d'équipe</span>
                    </Link>

                    <Link
                        to="/profile"
                        className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${isActive('/profile') ? 'bg-[#6B4EFF] text-white' : 'hover:bg-gray-100 text-gray-700'}`}
                    >
                        <User className="w-4 h-4" />
                        <span>Mon Profil</span>
                    </Link>
                </nav>
            </div>
        );
    }

    // Navigation pour les employés
    return (
        <div className="bg-white shadow-sm rounded-lg p-4 mb-6">
            <nav className="flex flex-wrap gap-2">
                <Link
                    to="/employee-teams"
                    className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${isActive('/employee-teams') ? 'bg-[#6B4EFF] text-white' : 'hover:bg-gray-100 text-gray-700'}`}
                >
                    <Users className="w-4 h-4" />
                    <span>Mes Équipes</span>
                </Link>



                <Link
                    to="/member-calendar"
                    className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${isActive('/member-calendar') ? 'bg-[#6B4EFF] text-white' : 'hover:bg-gray-100 text-gray-700'}`}
                >
                    <Calendar className="w-4 h-4" />
                    <span>Mon Calendrier</span>
                </Link>

                <Link
                    to="/employee-team-tasks"
                    className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${isActive('/employee-team-tasks') ? 'bg-[#6B4EFF] text-white' : 'hover:bg-gray-100 text-gray-700'}`}
                >
                    <CheckSquare className="w-4 h-4" />
                    <span>Tâches d'équipe</span>
                </Link>

                <Link
                    to="/profile"
                    className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${isActive('/profile') ? 'bg-[#6B4EFF] text-white' : 'hover:bg-gray-100 text-gray-700'}`}
                >
                    <User className="w-4 h-4" />
                    <span>Mon Profil</span>
                </Link>
            </nav>
        </div>
    );
};

export default MemberNavigation;