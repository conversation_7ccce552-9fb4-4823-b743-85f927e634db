import React from 'react';

const NewLogo = ({ className = "", size = 48 }) => (
    <div className={`relative ${className}`} style={{ width: size, height: size }}>
        {/* Cercle principal avec dégradé violet-rose */}
        <div
            className="absolute inset-0 rounded-full shadow-lg"
            style={{
                background: 'linear-gradient(135deg, #8B5CF6 0%, #A855F7 50%, #EC4899 100%)',
            }}
        />

        {/* Carré blanc au centre */}
        <div
            className="absolute bg-white rounded-sm flex items-center justify-center"
            style={{
                width: size * 0.55,
                height: size * 0.55,
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
            }}
        >
            {/* Lettre N noire stylisée */}
            <div
                className="font-black text-black relative"
                style={{
                    fontSize: size * 0.35,
                    lineHeight: 1,
                    fontFamily: 'Arial, sans-serif',
                }}
            >
                N
                {/* Petit triangle décoratif sous le N */}
                <div
                    className="absolute bg-black"
                    style={{
                        width: 0,
                        height: 0,
                        borderLeft: `${size * 0.04}px solid transparent`,
                        borderRight: `${size * 0.04}px solid transparent`,
                        borderTop: `${size * 0.06}px solid black`,
                        bottom: `-${size * 0.08}px`,
                        left: '50%',
                        transform: 'translateX(-50%)',
                    }}
                />
            </div>
        </div>

        {/* Effet de brillance subtil */}
        <div
            className="absolute inset-0 rounded-full opacity-10"
            style={{
                background: 'linear-gradient(135deg, rgba(255,255,255,0.6) 0%, rgba(255,255,255,0) 50%)',
            }}
        />
    </div>
);

export default NewLogo;
