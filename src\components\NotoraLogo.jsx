import React from 'react';

const NotoraLogo = ({ className }) => (
    <svg 
        width="40" 
        height="40" 
        viewBox="0 0 40 40" 
        fill="none" 
        xmlns="http://www.w3.org/2000/svg"
        className={className}
    >
        {/* Fond principal avec dégradé */}
        <rect 
            x="2" 
            y="2" 
            width="36" 
            height="36" 
            rx="10" 
            fill="url(#notoraGradient)"
        />
        
        {/* Point décoratif en haut à droite */}
        <circle 
            cx="33" 
            cy="7" 
            r="3" 
            fill="#fff" 
            fillOpacity="0.8"
        />
        
        {/* Point décoratif en bas à gauche */}
        <circle 
            cx="7" 
            cy="33" 
            r="3" 
            fill="#fff" 
            fillOpacity="0.8"
        />
        
        {/* Lettre N */}
        <text 
            x="13" 
            y="28" 
            fontSize="22" 
            fontWeight="bold" 
            fill="white" 
            fontFamily="Arial, sans-serif"
        >
            N
        </text>

        {/* Définition du dégradé */}
        <defs>
            <linearGradient 
                id="notoraGradient" 
                x1="2" 
                y1="2" 
                x2="38" 
                y2="38" 
                gradientUnits="userSpaceOnUse"
            >
                <stop offset="0%" stopColor="#8B5CF6" />
                <stop offset="100%" stopColor="#7C3AED" />
            </linearGradient>
        </defs>
    </svg>
);

export default NotoraLogo; 