import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';

const PrivateRoute = ({ children, requiredRole, allowedRoles = [] }) => {
    const auth = useAuth();
    const location = useLocation();
    const isAuthenticated = auth.isAuthenticated();

    console.log('PrivateRoute - Auth State:', { isAuthenticated, user: auth.user, requiredRole, allowedRoles });

    if (!isAuthenticated) {
        console.log('PrivateRoute - Not authenticated, redirecting to login');
        return <Navigate to="/login" replace />;
    }

    // Vérifier si l'utilisateur a un rôle requis
    if (requiredRole) {
        const roles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];
        console.log('PrivateRoute - Checking roles:', { userRole: auth.user?.role, requiredRoles: roles });

        if (!roles.includes(auth.user?.role)) {
            console.log('PrivateRoute - Unauthorized role, redirecting to unauthorized');
            return <Navigate to="/unauthorized" replace />;
        }
    }

    // Vérifier si l'utilisateur a un rôle autorisé (si spécifié)
    if (allowedRoles.length > 0) {
        if (!allowedRoles.includes(auth.user?.role)) {
            console.log('PrivateRoute - Role not in allowed roles, redirecting');

            // Rediriger les super_admin vers leur dashboard
            if (auth.user?.role === 'super_admin') {
                return <Navigate to="/super-admin" replace />;
            }

            return <Navigate to="/unauthorized" replace />;
        }
    }

    // Restrictions spécifiques pour les super_admin
    if (auth.user?.role === 'super_admin') {
        // Bloquer l'accès aux routes d'équipes, d'événements et personnelles pour les super_admin
        const restrictedPaths = [
            '/teams',
            '/employee-teams',
            '/team-calendar',
            '/team-calendar-view',
            '/member-team-view',
            '/admin-calendar-view',
            '/member-calendar-view',
            '/events',
            '/personal-events',
            '/personal-tasks',
            '/client-notes',
            '/client-journals',
            '/client-pomodoro'
        ];

        // Vérifier si le chemin actuel est restreint
        if (restrictedPaths.some(path => location.pathname.startsWith(path))) {
            console.log('PrivateRoute - Super admin trying to access restricted path, redirecting');
            return <Navigate to="/super-admin" replace />;
        }
    }

    // Restrictions spécifiques pour les admins
    if (auth.user?.role === 'admin') {
        // Bloquer l'accès aux routes personnelles et spécifiques aux clients
        const restrictedPaths = [
            '/personal-events',
            '/personal-tasks',
            '/client-notes',
            '/client-journals',
            '/client-pomodoro'
        ];

        // Vérifier si le chemin actuel est restreint
        if (restrictedPaths.some(path => location.pathname.startsWith(path))) {
            console.log('PrivateRoute - Admin trying to access restricted path, redirecting');
            return <Navigate to="/dashboard" replace />;
        }
    }

    console.log('PrivateRoute - Access granted');
    return children;
};

export default PrivateRoute;