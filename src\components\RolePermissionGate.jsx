import React from 'react';
import { useAuth } from '@/contexts/AuthContext';

/**
 * Composant qui contrôle l'affichage des éléments UI en fonction du rôle de l'utilisateur
 * @param {Array|string} allowedRoles - Rôle(s) autorisé(s) à voir le contenu
 * @param {Array|string} excludedRoles - Rôle(s) non autorisé(s) à voir le contenu
 * @param {string} permission - Permission spécifique à vérifier (optionnel)
 * @param {React.ReactNode} children - Les éléments à afficher si l'utilisateur a le rôle autorisé
 * @param {React.ReactNode} fallback - Les éléments à afficher si l'utilisateur n'a pas le rôle autorisé
 */
const RolePermissionGate = ({ allowedRoles = [], excludedRoles = [], permission = null, children, fallback = null }) => {
  const { user } = useAuth();

  if (!user) return fallback;

  // Convertir en tableau si une chaîne est fournie
  const allowedRolesArray = Array.isArray(allowedRoles) ? allowedRoles : [allowedRoles];
  const excludedRolesArray = Array.isArray(excludedRoles) ? excludedRoles : [excludedRoles];

  // Règles spéciales pour les super_admin
  if (user.role === 'super_admin') {
    // Les super_admin ne peuvent pas gérer les équipes ou les événements
    if (permission === 'manage_teams' || permission === 'manage_events') {
      return fallback;
    }
  }

  // Si aucun rôle autorisé n'est spécifié, tous les rôles sont autorisés par défaut
  const isAllowed = allowedRolesArray.length === 0 || allowedRolesArray.includes(user.role);

  // Si le rôle de l'utilisateur est dans la liste des rôles exclus, il n'est pas autorisé
  const isExcluded = excludedRolesArray.includes(user.role);

  if (isAllowed && !isExcluded) {
    return <>{children}</>;
  }

  return fallback;
};

export default RolePermissionGate;
