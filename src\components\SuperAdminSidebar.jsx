import React from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
    Users,
    User,
    LogOut,
    LayoutDashboard,
    BarChart2
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'react-toastify';

const SuperAdminSidebar = () => {
    const { t } = useTranslation();
    const location = useLocation();
    const navigate = useNavigate();
    const { logout, user } = useAuth();

    const menuItems = [
        {
            name: t('sidebar.userManagement'),
            icon: <Users className="w-5 h-5" />,
            path: '/super-admin/users'
        },
        {
            name: 'Tableau de bord',
            icon: <BarChart2 className="w-5 h-5" />,
            path: '/super-admin/analytics'
        },
        {
            name: t('sidebar.profile'),
            icon: <User className="w-5 h-5" />,
            path: '/super-admin/profile'
        }
    ];

    const handleLogout = async () => {
        try {
            await logout();
            navigate('/login');
            toast.success('Déconnexion réussie');
        } catch (error) {
            console.error('Erreur lors de la déconnexion:', error);
            toast.error('Erreur lors de la déconnexion');
        }
    };

    const isActive = (path) => {
        // Si on est sur /super-admin exactement, activer /super-admin/users
        if (location.pathname === '/super-admin' && path === '/super-admin/users') {
            return true;
        }
        return location.pathname.startsWith(path);
    };

    return (
        <aside className="fixed inset-y-0 left-0 w-64 bg-[#6B4EFF] shadow-xl">
            {/* Header */}
            <div className="h-20 flex items-center px-4">
                <Link to="/super-admin" className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center shadow-lg">
                        <span className="text-xl font-bold text-white">N</span>
                    </div>
                    <div className="flex flex-col">
                        <span className="text-xl font-bold text-white tracking-tight">
                            Espace privé
                        </span>
                        <span className="text-xs text-white/70">
                            {user?.name}
                        </span>
                    </div>
                </Link>
            </div>

            {/* Navigation */}
            <nav className="mt-4">
                <div className="space-y-1 px-3">
                    {menuItems.map((item) => (
                        <Link
                            key={item.path}
                            to={item.path}
                            className={`flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-200 ${isActive(item.path)
                                ? 'bg-white/20 text-white shadow-lg'
                                : 'text-white/70 hover:bg-white/10 hover:text-white'
                                }`}
                        >
                            {item.icon}
                            <span className="text-sm font-medium">{item.name}</span>
                        </Link>
                    ))}
                </div>
            </nav>

            {/* Bouton de déconnexion */}
            <div className="absolute bottom-4 left-0 right-0 px-3">
                <button
                    onClick={handleLogout}
                    className="flex items-center gap-3 w-full px-4 py-3 text-white/70 hover:bg-white/10 hover:text-white rounded-lg transition-colors"
                >
                    <LogOut className="w-5 h-5" />
                    <span className="text-sm font-medium">Se déconnecter</span>
                </button>
            </div>
        </aside>
    );
};

export default SuperAdminSidebar;