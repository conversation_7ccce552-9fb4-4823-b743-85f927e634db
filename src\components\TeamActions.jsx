import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { permissionService } from '@/services/permissionService';
import TeamPermissionGate from './TeamPermissionGate';

/**
 * Composant qui affiche les actions disponibles pour une équipe en fonction des permissions de l'utilisateur
 * @param {Object} team - L'équipe pour laquelle afficher les actions
 * @param {Function} onEdit - Fonction appelée lors du clic sur le bouton d'édition
 * @param {Function} onDelete - Fonction appelée lors du clic sur le bouton de suppression
 * @param {Function} onAddMember - Fonction appelée lors du clic sur le bouton d'ajout de membre
 */
const TeamActions = ({ team, onEdit, onDelete, onAddMember }) => {
  const { user } = useAuth();
  const permissions = permissionService.checkTeamPermissions(user, team);

  // Vérification des permissions avant d'exécuter les actions
  const handleEditTeam = () => {
    if (!permissions.canManage) {
      alert("Vous n'avez pas les permissions nécessaires pour modifier cette équipe");
      return;
    }
    onEdit(team);
  };

  const handleDeleteTeam = () => {
    if (!permissions.canManage) {
      alert("Vous n'avez pas les permissions nécessaires pour supprimer cette équipe");
      return;
    }
    onDelete(team);
  };

  const handleAddMember = () => {
    if (!permissions.canAddMembers) {
      alert("Vous n'avez pas les permissions nécessaires pour ajouter des membres à cette équipe");
      return;
    }
    onAddMember(team);
  };

  return (
    <div className="team-actions">
      {/* Utilisation du composant TeamPermissionGate pour contrôler l'affichage des boutons */}
      <TeamPermissionGate team={team} permissionType="canManage">
        <button 
          className="btn btn-primary" 
          onClick={handleEditTeam}
        >
          Modifier
        </button>
      </TeamPermissionGate>

      <TeamPermissionGate team={team} permissionType="canManage">
        <button 
          className="btn btn-danger" 
          onClick={handleDeleteTeam}
        >
          Supprimer
        </button>
      </TeamPermissionGate>

      <TeamPermissionGate team={team} permissionType="canAddMembers">
        <button 
          className="btn btn-success" 
          onClick={handleAddMember}
        >
          Ajouter un membre
        </button>
      </TeamPermissionGate>

      {/* Afficher un message si l'utilisateur n'a aucune permission */}
      {!permissions.canManage && !permissions.canAddMembers && (
        <p className="text-muted">Vous n'avez pas les permissions nécessaires pour gérer cette équipe.</p>
      )}
    </div>
  );
};

export default TeamActions;