import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { permissionService } from '@/services/permissionService';

/**
 * Composant qui contrôle l'affichage des éléments UI en fonction des permissions de l'utilisateur
 * @param {Object} team - L'équipe pour laquelle vérifier les permissions
 * @param {string} permissionType - Le type de permission à vérifier (canView, canManage, canAddMembers, etc.)
 * @param {React.ReactNode} children - Les éléments à afficher si l'utilisateur a la permission
 * @param {React.ReactNode} fallback - Les éléments à afficher si l'utilisateur n'a pas la permission
 */
const TeamPermissionGate = ({ team, permissionType, children, fallback = null }) => {
  const { user } = useAuth();
  const permissions = permissionService.checkTeamPermissions(user, team);
  
  if (permissions[permissionType]) {
    return <>{children}</>;
  }
  
  return fallback;
};

export default TeamPermissionGate;