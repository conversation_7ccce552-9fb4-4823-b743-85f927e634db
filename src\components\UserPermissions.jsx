import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { userService } from '@/services/userService';
import { toast } from 'react-toastify';
import { ArrowLeft, Save, Shield, ShieldAlert, ShieldCheck, ShieldX } from 'lucide-react';

const UserPermissions = () => {
  const { userId } = useParams();
  const navigate = useNavigate();
  const [user, setUser] = useState(null);
  const [permissions, setPermissions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);

  // Liste des permissions possibles
  const availablePermissions = [
    { id: 'manage_users', name: 'Gérer les utilisateurs', description: 'Créer, modifier et supprimer des utilisateurs', icon: <Shield className="w-5 h-5" /> },
    { id: 'manage_teams', name: '<PERSON><PERSON><PERSON> les équipes', description: 'Créer, modifier et supprimer des équipes', icon: <Shield className="w-5 h-5" /> },
    { id: 'manage_events', name: 'Gérer les événements', description: 'Créer, modifier et supprimer des événements', icon: <Shield className="w-5 h-5" /> },
    { id: 'manage_tasks', name: 'Gérer les tâches', description: 'Créer, modifier et supprimer des tâches', icon: <Shield className="w-5 h-5" /> },
    { id: 'view_dashboard', name: 'Voir le tableau de bord', description: 'Accéder au tableau de bord', icon: <Shield className="w-5 h-5" /> },
    { id: 'manage_settings', name: 'Gérer les paramètres', description: 'Modifier les paramètres de l\'application', icon: <ShieldAlert className="w-5 h-5" /> },
    { id: 'view_reports', name: 'Voir les rapports', description: 'Accéder aux rapports', icon: <Shield className="w-5 h-5" /> },
    { id: 'admin_access', name: 'Accès administrateur', description: 'Accès complet à toutes les fonctionnalités d\'administration', icon: <ShieldAlert className="w-5 h-5" /> },
    { id: 'super_admin_access', name: 'Accès super administrateur', description: 'Accès complet à toutes les fonctionnalités', icon: <ShieldCheck className="w-5 h-5" /> }
  ];

  // Permissions par défaut selon le rôle
  const defaultPermissionsByRole = {
    super_admin: ['manage_users', 'manage_teams', 'manage_events', 'manage_tasks', 'view_dashboard', 'manage_settings', 'view_reports', 'admin_access', 'super_admin_access'],
    admin: ['manage_teams', 'manage_events', 'manage_tasks', 'view_dashboard', 'view_reports'],
    employee: ['view_dashboard'],
    client: ['view_dashboard']
  };

  useEffect(() => {
    const fetchUserData = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await userService.getUserById(userId);
        if (response.success) {
          setUser(response.data);
          
          // Si l'utilisateur a des permissions définies, les utiliser
          // Sinon, utiliser les permissions par défaut selon le rôle
          if (response.data.permissions && response.data.permissions.length > 0) {
            setPermissions(response.data.permissions);
          } else {
            const defaultPerms = defaultPermissionsByRole[response.data.role] || [];
            setPermissions(defaultPerms);
          }
        } else {
          throw new Error(response.error || 'Erreur lors de la récupération des données de l\'utilisateur');
        }
      } catch (err) {
        console.error('Erreur lors de la récupération des données de l\'utilisateur:', err);
        setError(err.message || 'Une erreur est survenue');
        toast.error(err.message || 'Erreur lors de la récupération des données de l\'utilisateur');
      } finally {
        setLoading(false);
      }
    };

    if (userId) {
      fetchUserData();
    }
  }, [userId]);

  const handlePermissionChange = (permissionId) => {
    setPermissions(prev => {
      if (prev.includes(permissionId)) {
        return prev.filter(p => p !== permissionId);
      } else {
        return [...prev, permissionId];
      }
    });
  };

  const handleSavePermissions = async () => {
    setSaving(true);
    try {
      const response = await userService.updateUserPermissions(userId, permissions);
      if (response.success) {
        toast.success('Permissions mises à jour avec succès');
      } else {
        throw new Error(response.error || 'Erreur lors de la mise à jour des permissions');
      }
    } catch (err) {
      console.error('Erreur lors de la mise à jour des permissions:', err);
      toast.error(err.message || 'Erreur lors de la mise à jour des permissions');
    } finally {
      setSaving(false);
    }
  };

  const handleGoBack = () => {
    navigate('/super-admin');
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 p-4 rounded-lg">
        <h3 className="text-lg font-semibold mb-2">Erreur</h3>
        <p>{error}</p>
        <button 
          onClick={handleGoBack}
          className="mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
        >
          Retour
        </button>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="text-center p-8">
        <p className="text-gray-500">Utilisateur non trouvé.</p>
        <button 
          onClick={handleGoBack}
          className="mt-4 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
        >
          Retour
        </button>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <button 
            onClick={handleGoBack}
            className="p-2 rounded-full hover:bg-gray-100 transition-colors"
          >
            <ArrowLeft className="w-5 h-5 text-gray-500" />
          </button>
          <h1 className="text-2xl font-bold text-gray-900">Permissions de l'utilisateur</h1>
        </div>
        <button
          onClick={handleSavePermissions}
          disabled={saving}
          className="flex items-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <Save className="w-4 h-4" />
          {saving ? 'Enregistrement...' : 'Enregistrer'}
        </button>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div className="flex items-center gap-4 mb-4">
          <div className="h-12 w-12 rounded-full bg-purple-100 flex items-center justify-center text-purple-600">
            {user.role === 'super_admin' ? (
              <ShieldCheck className="w-6 h-6" />
            ) : user.role === 'admin' ? (
              <Shield className="w-6 h-6" />
            ) : (
              <ShieldX className="w-6 h-6" />
            )}
          </div>
          <div>
            <h2 className="text-xl font-semibold">{user.firstName} {user.lastName}</h2>
            <p className="text-gray-500">{user.email}</p>
            <div className="mt-1 inline-block px-2 py-1 bg-purple-100 text-purple-800 text-xs font-medium rounded-full">
              {user.role === 'super_admin' ? 'Super Admin' : 
               user.role === 'admin' ? 'Admin' : 
               user.role === 'employee' ? 'Employé' : 'Client'}
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold mb-4">Liste des permissions</h3>
        <div className="space-y-4">
          {availablePermissions.map(permission => (
            <div key={permission.id} className="flex items-start gap-3 p-3 hover:bg-gray-50 rounded-lg transition-colors">
              <div className="flex-shrink-0 mt-0.5">
                <input
                  type="checkbox"
                  id={`permission-${permission.id}`}
                  checked={permissions.includes(permission.id)}
                  onChange={() => handlePermissionChange(permission.id)}
                  className="h-5 w-5 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                />
              </div>
              <div className="flex-1">
                <label htmlFor={`permission-${permission.id}`} className="flex items-center gap-2 font-medium text-gray-900 cursor-pointer">
                  {permission.icon}
                  {permission.name}
                </label>
                <p className="text-sm text-gray-500 mt-1">{permission.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default UserPermissions;
