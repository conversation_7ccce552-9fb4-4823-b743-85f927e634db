import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
    User,
    Calendar,
    Book,
    FileText,
    LogOut,
    Users,
    CheckSquare,
    CalendarDays,
    ListTodo,
    Clock
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import NotoraLogo from './NotoraLogo';
import RolePermissionGate from './RolePermissionGate';

const UserSidebar = () => {
    const { t } = useTranslation();
    const location = useLocation();
    const { logout, user } = useAuth();

    // Combiner les éléments de menu en fonction du rôle
    let menuItems = [];

    if (user?.role === 'super_admin') {
        menuItems = [
            {
                name: 'Tableau de bord',
                icon: <Calendar className="w-5 h-5" />,
                path: '/dashboard'
            },
            {
                name: 'Mon Profil',
                icon: <User className="w-5 h-5" />,
                path: '/profile'
            }
        ];
    } else if (user?.role === 'admin') {
        // Pour les admins : menu avec éléments d'équipe uniquement
        menuItems = [
            {
                name: 'Tableau de bord',
                icon: <Calendar className="w-5 h-5" />,
                path: '/dashboard'
            },
            {
                name: 'Mes Équipes',
                icon: <Users className="w-5 h-5" />,
                path: '/teams'
            },
            {
                name: 'Calendrier d\'équipes',
                icon: <Calendar className="w-5 h-5" />,
                path: '/team-calendar'
            },
            {
                name: 'Tâches d\'équipe',
                icon: <CheckSquare className="w-5 h-5" />,
                path: '/team-tasks'
            },
            {
                name: 'Mon Profil',
                icon: <User className="w-5 h-5" />,
                path: '/profile'
            }
        ];
    } else if (user?.role === 'client') {
        menuItems = [
            {
                name: 'Tableau de bord',
                icon: <Calendar className="w-5 h-5" />,
                path: '/dashboard'
            },
            {
                name: 'Mes Événements',
                icon: <CalendarDays className="w-5 h-5" />,
                path: '/personal-events'
            },
            {
                name: 'Mes Tâches',
                icon: <ListTodo className="w-5 h-5" />,
                path: '/personal-tasks'
            },
            {
                name: 'Notes',
                icon: <FileText className="w-5 h-5" />,
                path: '/client-notes'
            },
            {
                name: 'Journal',
                icon: <Book className="w-5 h-5" />,
                path: '/client-journals'
            },
            {
                name: 'Mode Pomodoro',
                icon: <Clock className="w-5 h-5" />,
                path: '/client-pomodoro'
            },
            {
                name: 'Mon Profil',
                icon: <User className="w-5 h-5" />,
                path: '/profile'
            }
        ];
    } else if (user?.role === 'employee') {
        // Pour les employés : menu de base + éléments d'équipe
        menuItems = [
            {
                name: 'Tableau de bord',
                icon: <Calendar className="w-5 h-5" />,
                path: '/dashboard'
            },
            {
                name: 'Mes Événements',
                icon: <CalendarDays className="w-5 h-5" />,
                path: '/personal-events'
            },
            {
                name: 'Mes Tâches',
                icon: <ListTodo className="w-5 h-5" />,
                path: '/personal-tasks'
            },
            {
                name: 'Calendrier D\'équipes',
                icon: <Calendar className="w-5 h-5" />,
                path: '/team-calendar'
            },
            {
                name: 'Mes Équipes',
                icon: <Users className="w-5 h-5" />,
                path: '/employee-teams'
            },
            {
                name: 'Tâches d\'équipe',
                icon: <CheckSquare className="w-5 h-5" />,
                path: '/employee-team-tasks'
            },
            {
                name: 'Événements d\'équipe',
                icon: <CalendarDays className="w-5 h-5" />,
                path: '/employee-events-list'
            },
            {
                name: 'Mon Profil',
                icon: <User className="w-5 h-5" />,
                path: '/profile'
            }
        ];
    }

    const handleLogout = async () => {
        try {
            await logout();
        } catch (error) {
            console.error('Erreur lors de la déconnexion:', error);
        }
    };

    return (
        <aside className="fixed inset-y-0 left-0 w-64 bg-[#6B4EFF] text-white">
            {/* Header */}
            <div className="h-20 flex items-center px-6 border-b border-white/10">
                <Link to="/" className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center shadow-lg">
                        <span className="text-xl font-bold">N</span>
                    </div>
                    <div className="flex flex-col">
                        <span className="text-xl font-bold tracking-tight">
                            Espace privé
                        </span>
                        <span className="text-xs text-white/70">

                        </span>
                    </div>
                </Link>
            </div>

            {/* Navigation */}
            <nav className="mt-4">
                <div className="space-y-1 px-4">
                    {menuItems.map((item) => (
                        <Link
                            key={item.path}
                            to={item.path}
                            className={`flex items-center gap-3 px-4 py-3 rounded-lg transition-colors ${location.pathname === item.path
                                ? 'bg-white/20 text-white'
                                : 'text-white/70 hover:bg-white/10 hover:text-white'
                                }`}
                        >
                            {item.icon}
                            <span className="text-sm font-medium">{item.name}</span>
                        </Link>
                    ))}
                </div>
            </nav>

            {/* Bouton de déconnexion */}
            <div className="absolute bottom-4 left-0 right-0 px-3">
                <button
                    onClick={handleLogout}
                    className="flex items-center gap-3 w-full px-4 py-3 text-white/70 hover:bg-white/10 hover:text-white rounded-lg transition-colors"
                >
                    <LogOut className="w-5 h-5" />
                    <span className="text-sm font-medium">Se déconnecter</span>
                </button>
            </div>
        </aside>
    );
};

export default UserSidebar;