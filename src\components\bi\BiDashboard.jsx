import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Setting<PERSON>, RefreshCw } from 'lucide-react';
import biService from '@/services/biService';
import UserActivityChart from './UserActivityChart';
import PlatformActivityChart from './PlatformActivityChart';

const BiDashboard = () => {
  const [metrics, setMetrics] = useState(null);
  const [dashboardConfig, setDashboardConfig] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showSettings, setShowSettings] = useState(false);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Récupérer les métriques
      const metricsResult = await biService.getMetrics();
      if (!metricsResult.success) {
        throw new Error(metricsResult.error || 'Erreur lors de la récupération des métriques');
      }
      
      // Récupérer la configuration du tableau de bord
      const configResult = await biService.getDashboardConfig();
      if (!configResult.success) {
        throw new Error(configResult.error || 'Erreur lors de la récupération de la configuration du tableau de bord');
      }
      
      setMetrics(metricsResult.data);
      setDashboardConfig(configResult.data);
    } catch (err) {
      console.error('Erreur lors du chargement des données BI:', err);
      setError(err.message || 'Une erreur est survenue lors du chargement des données');
      toast.error(err.message || 'Erreur lors du chargement des données BI');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    fetchData();
    toast.info('Actualisation des données en cours...');
  };

  const toggleSettings = () => {
    setShowSettings(!showSettings);
  };

  const updateDashboardLayout = async (newLayout) => {
    try {
      const result = await biService.updateDashboardConfig({
        ...dashboardConfig,
        layout: newLayout
      });
      
      if (result.success) {
        setDashboardConfig(result.data);
        toast.success('Configuration du tableau de bord mise à jour');
      } else {
        throw new Error(result.error || 'Erreur lors de la mise à jour de la configuration');
      }
    } catch (err) {
      console.error('Erreur lors de la mise à jour de la configuration:', err);
      toast.error(err.message || 'Erreur lors de la mise à jour de la configuration');
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 p-4 rounded-lg">
        <h3 className="text-lg font-semibold mb-2">Erreur</h3>
        <p>{error}</p>
        <button 
          onClick={fetchData}
          className="mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
        >
          Réessayer
        </button>
      </div>
    );
  }

  if (!metrics || !dashboardConfig) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 p-4 rounded-lg">
        <h3 className="text-lg font-semibold mb-2">Données non disponibles</h3>
        <p>Impossible de charger les données du tableau de bord.</p>
        <button 
          onClick={fetchData}
          className="mt-4 px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors"
        >
          Réessayer
        </button>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Tableau de bord BI</h2>
        <div className="flex gap-2">
          <button
            onClick={handleRefresh}
            className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full transition-colors"
            title="Actualiser les données"
          >
            <RefreshCw className="w-5 h-5" />
          </button>
          <button
            onClick={toggleSettings}
            className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full transition-colors"
            title="Paramètres du tableau de bord"
          >
            <Settings className="w-5 h-5" />
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <UserActivityChart data={metrics.user_activity} />
        <PlatformActivityChart data={metrics.platform_activity} />
      </div>

      {showSettings && (
        <div className="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
          <h3 className="text-lg font-semibold mb-4">Paramètres du tableau de bord</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium mb-2">Type d'affichage</h4>
              <div className="flex gap-2">
                <button
                  onClick={() => updateDashboardLayout({ columns: 2, rows: 1 })}
                  className={`flex items-center gap-2 px-3 py-2 rounded-lg border ${
                    dashboardConfig.layout.columns === 2 ? 'bg-purple-100 border-purple-300 text-purple-700' : 'border-gray-300'
                  }`}
                >
                  <BarChart className="w-4 h-4" />
                  <span>Côte à côte</span>
                </button>
                <button
                  onClick={() => updateDashboardLayout({ columns: 1, rows: 2 })}
                  className={`flex items-center gap-2 px-3 py-2 rounded-lg border ${
                    dashboardConfig.layout.columns === 1 ? 'bg-purple-100 border-purple-300 text-purple-700' : 'border-gray-300'
                  }`}
                >
                  <PieChart className="w-4 h-4" />
                  <span>Empilé</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BiDashboard;
