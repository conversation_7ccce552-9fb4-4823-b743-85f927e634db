import React from 'react';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from 'chart.js';
import { Bar } from 'react-chartjs-2';

// Enregistrer les composants nécessaires pour Chart.js
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

const PlatformActivityChart = ({ data }) => {
  if (!data) {
    return (
      <div className="flex justify-center items-center h-64 bg-gray-50 rounded-lg">
        <p className="text-gray-500">Aucune donnée disponible</p>
      </div>
    );
  }

  // Préparer les données pour le graphique
  const chartData = {
    labels: [
      'Équipes',
      'Événements d\'équipe',
      'Événements personnels',
      'Tâches d\'équipe',
      'Tâches personnelles'
    ],
    datasets: [
      {
        label: 'Nombre',
        data: [
          data.total_teams || 0,
          data.total_events || 0,
          data.total_personal_events || 0,
          data.total_team_tasks || 0,
          data.total_personal_tasks || 0
        ],
        backgroundColor: [
          'rgba(255, 99, 132, 0.6)',
          'rgba(54, 162, 235, 0.6)',
          'rgba(75, 192, 192, 0.6)',
          'rgba(153, 102, 255, 0.6)',
          'rgba(255, 159, 64, 0.6)'
        ],
        borderColor: [
          'rgba(255, 99, 132, 1)',
          'rgba(54, 162, 235, 1)',
          'rgba(75, 192, 192, 1)',
          'rgba(153, 102, 255, 1)',
          'rgba(255, 159, 64, 1)'
        ],
        borderWidth: 1,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const label = context.dataset.label || '';
            const value = context.raw || 0;
            return `${label}: ${value}`;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          precision: 0
        }
      }
    }
  };

  // Calculer le total des éléments
  const totalItems = 
    (data.total_teams || 0) + 
    (data.total_events || 0) + 
    (data.total_personal_events || 0) + 
    (data.total_team_tasks || 0) + 
    (data.total_personal_tasks || 0);

  return (
    <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
      <h3 className="text-lg font-semibold mb-4">Activité de la plateforme</h3>
      <div className="h-64">
        <Bar data={chartData} options={options} />
      </div>
      <div className="mt-4 grid grid-cols-3 gap-2">
        <div className="bg-gray-50 p-3 rounded-lg">
          <p className="text-sm text-gray-500">Total des équipes</p>
          <p className="text-2xl font-bold">{data.total_teams || 0}</p>
        </div>
        <div className="bg-gray-50 p-3 rounded-lg">
          <p className="text-sm text-gray-500">Total des événements</p>
          <p className="text-2xl font-bold">{(data.total_events || 0) + (data.total_personal_events || 0)}</p>
        </div>
        <div className="bg-gray-50 p-3 rounded-lg">
          <p className="text-sm text-gray-500">Total des tâches</p>
          <p className="text-2xl font-bold">{(data.total_team_tasks || 0) + (data.total_personal_tasks || 0)}</p>
        </div>
      </div>
    </div>
  );
};

export default PlatformActivityChart;
