import React from 'react';
import { Chart as ChartJS, ArcElement, <PERSON><PERSON><PERSON>, Legend } from 'chart.js';
import { Pie } from 'react-chartjs-2';

// Enregistrer les composants nécessaires pour Chart.js
ChartJS.register(ArcElement, Tooltip, Legend);

const UserActivityChart = ({ data }) => {
  if (!data || !data.users_by_role) {
    return (
      <div className="flex justify-center items-center h-64 bg-gray-50 rounded-lg">
        <p className="text-gray-500">Aucune donnée disponible</p>
      </div>
    );
  }

  // Préparer les données pour le graphique
  const chartData = {
    labels: [
      'Super Admin',
      'Admin',
      'Employé',
      'Client'
    ],
    datasets: [
      {
        data: [
          data.users_by_role.super_admin || 0,
          data.users_by_role.admin || 0,
          data.users_by_role.employee || 0,
          data.users_by_role.client || 0
        ],
        backgroundColor: [
          'rgba(255, 99, 132, 0.6)',
          'rgba(54, 162, 235, 0.6)',
          'rgba(75, 192, 192, 0.6)',
          'rgba(153, 102, 255, 0.6)'
        ],
        borderColor: [
          'rgba(255, 99, 132, 1)',
          'rgba(54, 162, 235, 1)',
          'rgba(75, 192, 192, 1)',
          'rgba(153, 102, 255, 1)'
        ],
        borderWidth: 1,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          font: {
            size: 12
          }
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const label = context.label || '';
            const value = context.raw || 0;
            const total = context.dataset.data.reduce((a, b) => a + b, 0);
            const percentage = Math.round((value / total) * 100);
            return `${label}: ${value} (${percentage}%)`;
          }
        }
      }
    }
  };

  return (
    <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
      <h3 className="text-lg font-semibold mb-4">Répartition des utilisateurs par rôle</h3>
      <div className="h-64">
        <Pie data={chartData} options={options} />
      </div>
      <div className="mt-4 grid grid-cols-2 gap-2">
        <div className="bg-gray-50 p-3 rounded-lg">
          <p className="text-sm text-gray-500">Total des utilisateurs</p>
          <p className="text-2xl font-bold">{data.total_users || 0}</p>
        </div>
        <div className="bg-gray-50 p-3 rounded-lg">
          <p className="text-sm text-gray-500">Utilisateurs actifs</p>
          <p className="text-2xl font-bold">{data.active_users || 0}</p>
          <p className="text-xs text-gray-400">({data.active_percentage || 0}%)</p>
        </div>
      </div>
    </div>
  );
};

export default UserActivityChart;
