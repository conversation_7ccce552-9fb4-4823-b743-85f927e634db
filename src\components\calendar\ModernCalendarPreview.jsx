import React from 'react';
import { MODERN_EVENT_COLORS } from '@/utils/modernCalendarHelper';
import '@/styles/modern-calendar.css';

const ModernCalendarPreview = ({ events = [], className = "" }) => {
  // Générer un calendrier de démonstration pour le mois actuel
  const today = new Date();
  const currentMonth = today.getMonth();
  const currentYear = today.getFullYear();
  
  // Premier jour du mois
  const firstDay = new Date(currentYear, currentMonth, 1);
  const lastDay = new Date(currentYear, currentMonth + 1, 0);
  
  // Jour de la semaine du premier jour (0 = dimanche, 1 = lundi, etc.)
  const startDay = (firstDay.getDay() + 6) % 7; // Convertir pour que lundi = 0
  
  // Générer les jours du calendrier
  const days = [];
  
  // Jours du mois précédent
  for (let i = startDay - 1; i >= 0; i--) {
    const date = new Date(currentYear, currentMonth, -i);
    days.push({
      date: date.getDate(),
      isCurrentMonth: false,
      isToday: false,
      fullDate: date
    });
  }
  
  // Jours du mois actuel
  for (let day = 1; day <= lastDay.getDate(); day++) {
    const date = new Date(currentYear, currentMonth, day);
    days.push({
      date: day,
      isCurrentMonth: true,
      isToday: day === today.getDate() && currentMonth === today.getMonth() && currentYear === today.getFullYear(),
      fullDate: date
    });
  }
  
  // Jours du mois suivant pour compléter la grille
  const remainingDays = 42 - days.length; // 6 semaines × 7 jours
  for (let day = 1; day <= remainingDays; day++) {
    const date = new Date(currentYear, currentMonth + 1, day);
    days.push({
      date: day,
      isCurrentMonth: false,
      isToday: false,
      fullDate: date
    });
  }

  // Événements de démonstration
  const demoEvents = [
    { id: 1, title: 'Réunion équipe', date: 3, color: MODERN_EVENT_COLORS.blue, status: 'pending' },
    { id: 2, title: 'Formation', date: 7, color: MODERN_EVENT_COLORS.purple, status: 'pending' },
    { id: 3, title: 'Présentation', date: 12, color: MODERN_EVENT_COLORS.green, status: 'completed' },
    { id: 4, title: 'Workshop', date: 15, color: MODERN_EVENT_COLORS.orange, status: 'pending' },
    { id: 5, title: 'Conférence', date: 20, color: MODERN_EVENT_COLORS.pink, status: 'pending' },
    { id: 6, title: 'Événement archivé', date: 25, color: MODERN_EVENT_COLORS.gray, status: 'archived' }
  ];

  // Combiner les événements de démonstration avec les événements fournis
  const allEvents = [...demoEvents, ...events];

  // Obtenir les événements pour un jour donné
  const getEventsForDay = (day) => {
    return allEvents.filter(event => {
      if (event.date) {
        return event.date === day && currentMonth === today.getMonth();
      }
      if (event.fullDate) {
        return event.fullDate.getDate() === day && 
               event.fullDate.getMonth() === currentMonth &&
               event.fullDate.getFullYear() === currentYear;
      }
      return false;
    });
  };

  const monthNames = [
    'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
    'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
  ];

  const dayNames = ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'];

  return (
    <div className={`modern-calendar-container ${className}`}>
      {/* En-tête du calendrier */}
      <div className="bg-white border-b border-gray-200 p-4">
        <h3 className="text-lg font-semibold text-gray-900">
          {monthNames[currentMonth]} {currentYear}
        </h3>
        <p className="text-sm text-gray-500 mt-1">
          Aperçu du nouveau design moderne
        </p>
      </div>

      {/* Grille du calendrier */}
      <div className="bg-white">
        {/* En-têtes des jours */}
        <div className="grid grid-cols-7 border-b border-gray-200">
          {dayNames.map((day) => (
            <div key={day} className="p-3 text-center text-xs font-semibold text-gray-500 uppercase tracking-wider bg-gray-50">
              {day}
            </div>
          ))}
        </div>

        {/* Grille des jours */}
        <div className="grid grid-cols-7">
          {days.map((day, index) => {
            const dayEvents = getEventsForDay(day.date);
            
            return (
              <div
                key={index}
                className={`min-h-[100px] border-r border-b border-gray-200 p-2 ${
                  day.isCurrentMonth 
                    ? day.isToday 
                      ? 'bg-blue-50' 
                      : 'bg-white hover:bg-gray-50' 
                    : 'bg-gray-50'
                } transition-colors cursor-pointer`}
              >
                {/* Numéro du jour */}
                <div className={`text-sm font-medium mb-1 ${
                  day.isCurrentMonth
                    ? day.isToday
                      ? 'text-blue-600 font-bold'
                      : 'text-gray-900'
                    : 'text-gray-400'
                }`}>
                  {day.date}
                </div>

                {/* Événements du jour */}
                <div className="space-y-1">
                  {dayEvents.slice(0, 3).map((event) => (
                    <div
                      key={event.id}
                      className={`text-xs px-2 py-1 rounded-md border-l-2 ${
                        event.status === 'archived' 
                          ? 'opacity-70 line-through italic' 
                          : ''
                      }`}
                      style={{
                        backgroundColor: event.color.background,
                        borderLeftColor: event.color.border,
                        color: event.color.text
                      }}
                    >
                      {event.title}
                    </div>
                  ))}
                  
                  {/* Indicateur d'événements supplémentaires */}
                  {dayEvents.length > 3 && (
                    <div className="text-xs text-gray-500 font-medium">
                      +{dayEvents.length - 3} autre(s)
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Légende des couleurs */}
      <div className="bg-gray-50 p-4 border-t border-gray-200">
        <h4 className="text-sm font-medium text-gray-700 mb-3">Légende des couleurs</h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {Object.entries(MODERN_EVENT_COLORS).slice(0, 8).map(([key, color]) => (
            <div key={key} className="flex items-center gap-2">
              <div 
                className="w-4 h-4 rounded border"
                style={{ 
                  backgroundColor: color.background,
                  borderColor: color.border 
                }}
              />
              <span className="text-xs text-gray-600">{color.name}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ModernCalendarPreview;
