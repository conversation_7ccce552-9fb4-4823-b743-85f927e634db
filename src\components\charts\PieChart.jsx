import React from 'react';
import { Pie } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  Title
} from 'chart.js';

// Enregistrer les composants Chart.js nécessaires
ChartJS.register(ArcElement, Tooltip, Legend, Title);

/**
 * Composant PieChart réutilisable pour les graphiques circulaires
 * @param {Object} props - Propriétés du composant
 * @param {Object} props.data - Données du graphique selon votre guide
 * @param {string} props.title - Titre du graphique
 * @param {string} props.subtitle - Sous-titre du graphique
 * @param {Array} props.legend - Légende personnalisée
 * @param {string} props.period - Période actuelle
 * @param {boolean} props.showPercentages - Afficher les pourcentages
 * @param {number} props.height - Hauteur du graphique
 * @returns {JSX.Element} - Composant <PERSON>
 */
const PieChart = ({
  data,
  title,
  subtitle,
  legend,
  period,
  showPercentages = true,
  height = 300
}) => {
  // Vérification des données
  if (!data || !Array.isArray(data) || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg">
        <div className="text-center">
          <div className="text-gray-400 text-lg mb-2">📊</div>
          <p className="text-gray-500 text-sm">Aucune donnée disponible</p>
        </div>
      </div>
    );
  }

  // Préparer les données pour Chart.js
  const chartData = {
    labels: data.map(item => item.name),
    datasets: [
      {
        data: data.map(item => item.value),
        backgroundColor: data.map(item => item.color),
        borderColor: data.map(item => item.color),
        borderWidth: 2,
        hoverBorderWidth: 3,
        hoverOffset: 8
      }
    ]
  };

  // Options du graphique
  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false // Nous utilisons une légende personnalisée
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#ffffff',
        bodyColor: '#ffffff',
        borderColor: '#ffffff',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: true,
        callbacks: {
          label: function (context) {
            const label = context.label || '';
            const value = context.parsed;
            const total = context.dataset.data.reduce((a, b) => a + b, 0);
            const percentage = total > 0 ? ((value * 100) / total).toFixed(1) : 0;

            if (showPercentages) {
              return `${label}: ${value} (${percentage}%)`;
            } else {
              return `${label}: ${value}`;
            }
          }
        }
      },
      title: {
        display: false // Nous gérons le titre manuellement
      }
    },
    animation: {
      animateRotate: true,
      animateScale: true,
      duration: 1000,
      easing: 'easeInOutQuart'
    },
    interaction: {
      intersect: false,
      mode: 'index'
    }
  };

  // Calculer le total pour les pourcentages
  const total = data.reduce((sum, item) => sum + item.value, 0);

  return (
    <div className="w-full">
      {/* En-tête avec titre et sous-titre */}
      <div className="mb-6">
        <div className="flex items-center space-x-2 mb-2">
          <div className={`w-3 h-3 rounded-full ${period === 'today' ? 'bg-green-500 animate-pulse' : 'bg-blue-500'}`}></div>
          <h3 className="text-lg font-semibold text-gray-900">
            {title}
          </h3>
        </div>
        {subtitle && (
          <p className="text-sm text-gray-500">{subtitle}</p>
        )}
        {period && (
          <p className="text-xs text-gray-400">
            Période: {period === 'today' ? 'Aujourd\'hui' : period === '1h' ? 'Dernière heure' : period === '24h' ? 'Dernières 24h' : period === '7d' ? 'Derniers 7 jours' : 'Derniers 30 jours'}
          </p>
        )}
      </div>

      {/* Graphique */}
      <div style={{ height: `${height}px` }} className="mb-6">
        <Pie data={chartData} options={options} />
      </div>

      {/* Légende personnalisée */}
      <div className="space-y-3">
        {data.map((item, index) => (
          <div key={index} className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div
                className="w-4 h-4 rounded-full flex-shrink-0"
                style={{ backgroundColor: item.color }}
              ></div>
              <span className="text-sm font-medium text-gray-700">
                {item.name}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm font-bold text-gray-900">
                {item.value}
              </span>
              {showPercentages && total > 0 && (
                <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                  {((item.value * 100) / total).toFixed(1)}%
                </span>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Résumé total */}
      <div className="mt-4 pt-4 border-t border-gray-200">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-600">Total</span>
          <span className="text-lg font-bold text-gray-900">{total}</span>
        </div>
      </div>
    </div>
  );
};

export default PieChart;
