import React from 'react';
import { Shield, RefreshCw, Bug, AlertTriangle } from 'lucide-react';
import { useAdminDashboard } from '@/hooks/useAdminDashboard';
import MetricCards from './MetricCards';
import PeriodFilter from './PeriodFilter';
import <PERSON><PERSON><PERSON> from '@/components/charts/PieChart';

/**
 * Composant principal du tableau de bord BI pour les administrateurs
 * Suit le guide détaillé pour l'implémentation complète avec analyses en temps réel
 */
const AdminBIDashboard = () => {
  // Utilisation du hook personnalisé pour gérer le dashboard
  const {
    dashboardData,
    loading,
    error,
    currentPeriod,
    lastUpdated,
    availablePeriods,
    changePeriod,
    refreshDashboard,
    fetchDebugData,
    metricData,
    chartsData,
    detailedStats,
    metadata,
    isTeamLeader,
    adminName,
    isRealTimeData
  } = useAdminDashboard('today');

  // Fonction pour gérer le débogage
  const handleDebug = async () => {
    const debugData = await fetchDebugData();
    if (debugData) {
      console.log('Données de débogage admin:', debugData);
    }
  };

  // Composant de chargement
  if (loading && !dashboardData) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="flex items-center space-x-2">
          <RefreshCw className="w-6 h-6 animate-spin text-blue-500" />
          <span className="text-lg text-gray-600">Chargement du tableau de bord admin...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      {/* En-tête avec informations admin et boutons d'action */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="bg-blue-500 p-3 rounded-lg">
              <Shield className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {metadata?.dashboard_title || 'Tableau de Bord Admin'}
              </h1>
              <p className="text-gray-600 text-sm">
                {metadata?.dashboard_subtitle || 'Analyses de vos activités d\'équipe'}
              </p>
              {adminName && (
                <p className="text-blue-600 text-sm font-medium">
                  👋 Bonjour {adminName} {isTeamLeader ? '(Responsable d\'équipe)' : ''}
                </p>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-3">
            {/* Bouton de débogage */}
            <button
              onClick={handleDebug}
              className="flex items-center space-x-2 px-3 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors"
              title="Données de débogage"
            >
              <Bug className="w-4 h-4" />
              <span className="hidden sm:inline">Debug</span>
            </button>

            {/* Bouton d'actualisation */}
            <button
              onClick={refreshDashboard}
              disabled={loading}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 transition-colors"
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              <span>Actualiser</span>
            </button>
          </div>
        </div>

        {/* Filtres de période */}
        <PeriodFilter
          availablePeriods={availablePeriods}
          currentPeriod={currentPeriod}
          onPeriodChange={changePeriod}
          loading={loading}
        />
      </div>

      {/* Cartes de métriques */}
      <MetricCards
        metricCards={dashboardData?.metric_cards}
        currentPeriod={currentPeriod}
        loading={loading}
      />

      {/* Graphiques - Distribution des événements et tâches */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Graphique des événements d'équipe */}
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
          {chartsData?.eventsDistribution ? (
            <PieChart
              data={chartsData.eventsDistribution.data}
              title={chartsData.eventsDistribution.title}
              subtitle={chartsData.eventsDistribution.subtitle}
              legend={chartsData.eventsDistribution.legend}
              period={currentPeriod}
              showPercentages={true}
              height={320}
            />
          ) : (
            <div className="flex items-center justify-center h-80 bg-gray-50 rounded-lg">
              <div className="text-center">
                <div className="text-gray-400 text-lg mb-2">📊</div>
                <p className="text-gray-500 text-sm">Données des événements non disponibles</p>
              </div>
            </div>
          )}
        </div>

        {/* Graphique des tâches d'équipe */}
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
          {chartsData?.tasksDistribution ? (
            <PieChart
              data={chartsData.tasksDistribution.data}
              title={chartsData.tasksDistribution.title}
              subtitle={chartsData.tasksDistribution.subtitle}
              legend={chartsData.tasksDistribution.legend}
              period={currentPeriod}
              showPercentages={true}
              height={320}
            />
          ) : (
            <div className="flex items-center justify-center h-80 bg-gray-50 rounded-lg">
              <div className="text-center">
                <div className="text-gray-400 text-lg mb-2">📊</div>
                <p className="text-gray-500 text-sm">Données des tâches non disponibles</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Statistiques détaillées */}
      {detailedStats && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          {/* Gestion d'équipes */}
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Gestion d'Équipes</h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Équipes gérées</span>
                <span className="font-bold text-gray-900">{detailedStats.team_management?.total_teams || 0}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Membres total</span>
                <span className="font-bold text-gray-900">{detailedStats.team_management?.total_team_members || 0}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Progression moyenne</span>
                <span className="font-bold text-green-600">{detailedStats.team_management?.average_progress || 0}%</span>
              </div>
              {detailedStats.team_management?.most_active_team && (
                <div className="pt-3 border-t border-gray-200">
                  <p className="text-xs text-gray-500 mb-1">Équipe la plus active</p>
                  <p className="font-medium text-blue-600">{detailedStats.team_management.most_active_team.name}</p>
                </div>
              )}
            </div>
          </div>

          {/* Activité des événements */}
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Activité Événements</h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Total événements</span>
                <span className="font-bold text-gray-900">{detailedStats.events_activity?.total || 0}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Créés (période)</span>
                <span className="font-bold text-blue-600">{detailedStats.events_activity?.created_in_period || 0}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Terminés (période)</span>
                <span className="font-bold text-green-600">{detailedStats.events_activity?.completed_in_period || 0}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">En attente</span>
                <span className="font-bold text-orange-600">{detailedStats.events_activity?.pending || 0}</span>
              </div>
              <div className="pt-3 border-t border-gray-200">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Taux de complétion</span>
                  <span className="font-bold text-purple-600">{detailedStats.events_activity?.completion_rate || 0}%</span>
                </div>
              </div>
            </div>
          </div>

          {/* Activité des tâches */}
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Activité Tâches</h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Total tâches</span>
                <span className="font-bold text-gray-900">{detailedStats.tasks_activity?.total || 0}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Créées (période)</span>
                <span className="font-bold text-blue-600">{detailedStats.tasks_activity?.created_in_period || 0}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Terminées (période)</span>
                <span className="font-bold text-green-600">{detailedStats.tasks_activity?.completed_in_period || 0}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">En attente</span>
                <span className="font-bold text-orange-600">{detailedStats.tasks_activity?.pending || 0}</span>
              </div>
              <div className="pt-3 border-t border-gray-200">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Taux de complétion</span>
                  <span className="font-bold text-purple-600">{detailedStats.tasks_activity?.completion_rate || 0}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Footer avec informations détaillées */}
      <div className="mt-8 flex justify-between items-center text-sm text-gray-500">
        <div>
          {lastUpdated && (
            <>
              Dernière mise à jour: {lastUpdated.toLocaleTimeString('fr-FR')}
              {metadata?.refresh_mode === 'manual' && (
                <span className="ml-2">• Mode manuel</span>
              )}
              {isRealTimeData && (
                <span className="ml-2 text-green-500">• Données en temps réel</span>
              )}
              {currentPeriod === 'today' && (
                <span className="ml-2 text-green-500">• AdminActivityTracker</span>
              )}
              {currentPeriod !== 'today' && (
                <span className="ml-2 text-orange-500">• Données historiques</span>
              )}
            </>
          )}
        </div>
        <div className="flex items-center space-x-4">
          <span>
            Source: {metadata?.data_source || 'AdminActivityTracker'}
          </span>
          <span>
            Période: {availablePeriods.find(p => p.value === currentPeriod)?.label || currentPeriod}
          </span>
        </div>
      </div>

      {/* Message d'erreur */}
      {error && (
        <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="w-5 h-5 text-red-500" />
            <p className="text-red-600 text-sm">{error}</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminBIDashboard;
