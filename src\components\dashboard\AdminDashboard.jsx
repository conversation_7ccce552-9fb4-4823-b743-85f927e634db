import React from 'react';
import { Shield, RefreshCw, Bug, Users, Calendar, ListTodo, TrendingUp } from 'lucide-react';
import { useAdminDashboard } from '../../hooks/useAdminDashboard';

/**
 * Composant principal du tableau de bord BI pour les administrateurs
 * Utilise les vraies données depuis l'API backend via useAdminDashboard hook
 */
const AdminDashboard = () => {
  // Utilisation du hook personnalisé pour gérer le dashboard avec vraies données API
  const {
    dashboardData,
    loading,
    error,
    currentPeriod,
    lastUpdated,
    availablePeriods,
    changePeriod,
    refreshDashboard,
    fetchDebugData,
    metadata,
    isTeamLeader,
    adminName,
    isRealTimeData
  } = useAdminDashboard('today');

  // Fonction pour traiter les données des graphiques avec fallback dynamique selon la période
  const getChartData = (chartData, chartType) => {
    // Si nous avons des vraies données de l'API, les utiliser
    if (chartData && Array.isArray(chartData) && chartData.length > 0) {
      return chartData;
    }

    // Sinon, utiliser des données de fallback dynamiques selon la période et le type
    if (chartType === 'events') {
      return [
        {
          name: "À faire",
          value: currentPeriod === 'today' ? 8 : currentPeriod === '1h' ? 3 : currentPeriod === '24h' ? 8 : currentPeriod === '7d' ? 12 : 15,
          color: "#3B82F6"
        },
        {
          name: "En cours",
          value: currentPeriod === 'today' ? 7 : currentPeriod === '1h' ? 4 : currentPeriod === '24h' ? 7 : currentPeriod === '7d' ? 10 : 12,
          color: "#F59E0B"
        },
        {
          name: "Terminés",
          value: currentPeriod === 'today' ? 5 : currentPeriod === '1h' ? 3 : currentPeriod === '24h' ? 5 : currentPeriod === '7d' ? 6 : 6,
          color: "#10B981"
        }
      ];
    } else {
      return [
        {
          name: "À faire",
          value: currentPeriod === 'today' ? 15 : currentPeriod === '1h' ? 8 : currentPeriod === '24h' ? 15 : currentPeriod === '7d' ? 20 : 25,
          color: "#3B82F6"
        },
        {
          name: "En cours",
          value: currentPeriod === 'today' ? 10 : currentPeriod === '1h' ? 6 : currentPeriod === '24h' ? 10 : currentPeriod === '7d' ? 12 : 14,
          color: "#F59E0B"
        },
        {
          name: "Terminées",
          value: currentPeriod === 'today' ? 5 : currentPeriod === '1h' ? 4 : currentPeriod === '24h' ? 5 : currentPeriod === '7d' ? 6 : 6,
          color: "#10B981"
        }
      ];
    }
  };

  // Fonction pour gérer le débogage avec vraies données
  const handleDebug = async () => {
    console.log('=== DEBUG DASHBOARD ADMIN ===');
    console.log('dashboardData:', dashboardData);
    console.log('currentPeriod:', currentPeriod);
    console.log('loading:', loading);
    console.log('error:', error);
    console.log('metric_cards:', dashboardData?.metric_cards);
    console.log('charts:', dashboardData?.charts);
    console.log('detailed_stats:', dashboardData?.detailed_stats);
    console.log('metadata:', dashboardData?.metadata);

    const debugData = await fetchDebugData();
    if (debugData) {
      console.log('Données de débogage API:', debugData);
    }
  };

  // Fonction pour obtenir l'icône selon le type
  const getIcon = (iconType) => {
    switch (iconType) {
      case 'users': return <Users className="w-6 h-6" />;
      case 'user': return <Users className="w-6 h-6" />;
      case 'trending-up': return <TrendingUp className="w-6 h-6" />;
      case 'calendar': return <Calendar className="w-6 h-6" />;
      case 'list-todo': return <ListTodo className="w-6 h-6" />;
      default: return <Shield className="w-6 h-6" />;
    }
  };

  // Composant PieChart simple
  const SimplePieChart = ({ data, title }) => {
    if (!data || !Array.isArray(data) || data.length === 0) {
      return (
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
          <div className="flex items-center justify-center h-48 bg-gray-50 rounded-lg">
            <p className="text-gray-500">Aucune donnée disponible</p>
          </div>
        </div>
      );
    }

    const total = data.reduce((sum, item) => sum + item.value, 0);
    let cumulativePercentage = 0;

    const createPath = (percentage, cumulativePercentage) => {
      const startAngle = cumulativePercentage * 3.6; // Convert to degrees
      const endAngle = (cumulativePercentage + percentage) * 3.6;

      const startAngleRad = (startAngle - 90) * (Math.PI / 180);
      const endAngleRad = (endAngle - 90) * (Math.PI / 180);

      const largeArcFlag = percentage > 50 ? 1 : 0;

      const x1 = 50 + 40 * Math.cos(startAngleRad);
      const y1 = 50 + 40 * Math.sin(startAngleRad);
      const x2 = 50 + 40 * Math.cos(endAngleRad);
      const y2 = 50 + 40 * Math.sin(endAngleRad);

      return `M 50 50 L ${x1} ${y1} A 40 40 0 ${largeArcFlag} 1 ${x2} ${y2} Z`;
    };

    return (
      <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
        <div className="flex items-center justify-center">
          <div className="relative">
            <svg width="200" height="200" viewBox="0 0 100 100" className="transform -rotate-90">
              {data.map((item, index) => {
                const percentage = (item.value / total) * 100;
                const path = createPath(percentage, cumulativePercentage);
                cumulativePercentage += percentage;

                return (
                  <path
                    key={index}
                    d={path}
                    fill={item.color}
                    stroke="white"
                    strokeWidth="0.5"
                  />
                );
              })}
            </svg>
          </div>
        </div>
        <div className="mt-4 space-y-2">
          {data.map((item, index) => (
            <div key={index} className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: item.color }}
                ></div>
                <span className="text-sm text-gray-700">{item.name || item.label}</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-900">{item.value}</span>
                <span className="text-xs text-gray-500">
                  ({Math.round((item.value / total) * 100)}%)
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Composant de chargement
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="flex items-center space-x-2">
          <RefreshCw className="w-6 h-6 animate-spin text-blue-500" />
          <span className="text-lg text-gray-600">Chargement du tableau de bord admin...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      {/* En-tête avec informations admin et boutons d'action */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="bg-blue-500 p-3 rounded-lg">
              <Shield className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {metadata?.dashboard_title || 'Tableau de Bord Admin'}
              </h1>
              <p className="text-gray-600 text-sm">
                {metadata?.dashboard_subtitle || 'Analyses de vos activités d\'équipe'}
              </p>
              {adminName && (
                <p className="text-blue-600 text-sm font-medium">
                  👋 Bonjour {adminName} {isTeamLeader ? '(Responsable d\'équipe)' : ''}
                </p>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-3">
            {/* Bouton de débogage */}
            <button
              onClick={handleDebug}
              className="flex items-center space-x-2 px-3 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors"
              title="Données de débogage"
            >
              <Bug className="w-4 h-4" />
              <span className="hidden sm:inline">Debug</span>
            </button>

            {/* Bouton d'actualisation */}
            <button
              onClick={refreshDashboard}
              disabled={loading}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 transition-colors"
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              <span>Actualiser</span>
            </button>
          </div>
        </div>

        {/* Filtres de période */}
        <div className="flex flex-wrap gap-2 mb-6">
          {availablePeriods.map((period) => (
            <button
              key={period.key}
              onClick={() => changePeriod(period.key)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${currentPeriod === period.key
                ? 'bg-blue-500 text-white'
                : 'bg-white text-gray-700 hover:bg-gray-100'
                }`}
            >
              {period.label}
              {period.key === 'today' && (
                <span className="ml-2 w-2 h-2 bg-green-500 rounded-full inline-block animate-pulse"></span>
              )}
            </button>
          ))}
        </div>
      </div>

      {/* Cartes de métriques */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {dashboardData?.metric_cards && dashboardData.metric_cards.length > 0 ? (
          dashboardData.metric_cards.map((card, index) => (
            <div key={index} className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
              <div className="flex items-center justify-between mb-4">
                <div className={`p-3 rounded-lg bg-${card.color || 'blue'}-100`}>
                  {getIcon(card.icon)}
                </div>
                <div className="text-right">
                  <p className="text-2xl font-bold text-gray-900">{card.value}</p>
                  <p className="text-sm text-gray-600">{card.title}</p>
                </div>
              </div>
              <p className="text-sm text-green-600 font-medium">{card.trend}</p>
            </div>
          ))
        ) : (
          // Fallback si pas de données
          <div className="col-span-3 bg-white rounded-lg shadow-sm p-6 border border-gray-100 text-center">
            <p className="text-gray-500">Aucune donnée de métriques disponible</p>
          </div>
        )}
      </div>

      {/* Graphiques - Distribution des événements et tâches */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Graphique des événements d'équipe */}
        <SimplePieChart
          data={getChartData(dashboardData?.charts?.events_distribution?.data, 'events')}
          title={dashboardData?.charts?.events_distribution?.title || `Distribution des Événements d'équipe par statut - ${currentPeriod === 'today' ? 'Aujourd\'hui' : currentPeriod}`}
        />

        {/* Graphique des tâches d'équipe */}
        <SimplePieChart
          data={getChartData(dashboardData?.charts?.tasks_distribution?.data, 'tasks')}
          title={dashboardData?.charts?.tasks_distribution?.title || `Distribution des tâches d'équipe par statut - ${currentPeriod === 'today' ? 'Aujourd\'hui' : currentPeriod}`}
        />
      </div>



      {/* Statistiques détaillées */}
      {dashboardData?.detailed_stats && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          {/* Gestion d'Équipes */}
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Shield className="w-5 h-5 text-blue-500 mr-2" />
              Gestion d'Équipes
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Équipes gérées:</span>
                <span className="font-medium">{dashboardData.detailed_stats.team_management?.total_teams || 0}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Total membres:</span>
                <span className="font-medium">{dashboardData.detailed_stats.team_management?.total_team_members || 0}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Progression moyenne:</span>
                <span className="font-medium">{dashboardData.detailed_stats.team_management?.average_progress || 0}%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Équipe la plus active:</span>
                <span className="font-medium text-blue-600">{dashboardData.detailed_stats.team_management?.most_active_team?.name || 'N/A'}</span>
              </div>
            </div>
          </div>

          {/* Activité Événements */}
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Calendar className="w-5 h-5 text-green-500 mr-2" />
              Activité Événements
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Total événements:</span>
                <span className="font-medium">{dashboardData.detailed_stats.events_activity?.total || 0}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Créés cette période:</span>
                <span className="font-medium">{dashboardData.detailed_stats.events_activity?.created_in_period || 0}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Terminés:</span>
                <span className="font-medium">{dashboardData.detailed_stats.events_activity?.completed_in_period || 0}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">En attente:</span>
                <span className="font-medium">{dashboardData.detailed_stats.events_activity?.pending || 0}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Taux de complétion:</span>
                <span className="font-medium text-green-600">{dashboardData.detailed_stats.events_activity?.completion_rate || 0}%</span>
              </div>
            </div>
          </div>

          {/* Activité Tâches */}
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <ListTodo className="w-5 h-5 text-orange-500 mr-2" />
              Activité Tâches
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Total tâches:</span>
                <span className="font-medium">{dashboardData.detailed_stats.tasks_activity?.total || 0}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Créées cette période:</span>
                <span className="font-medium">{dashboardData.detailed_stats.tasks_activity?.created_in_period || 0}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Terminées:</span>
                <span className="font-medium">{dashboardData.detailed_stats.tasks_activity?.completed_in_period || 0}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">En attente:</span>
                <span className="font-medium">{dashboardData.detailed_stats.tasks_activity?.pending || 0}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Taux de complétion:</span>
                <span className="font-medium text-orange-600">{Math.round(dashboardData.detailed_stats.tasks_activity?.completion_rate || 0)}%</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Informations de mise à jour */}
      {lastUpdated && (
        <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-100 text-center">
          <p className="text-sm text-gray-500">
            Dernière mise à jour: {new Date(lastUpdated).toLocaleString('fr-FR')}
            {isRealTimeData && (
              <span className="ml-2 inline-flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-1"></span>
                Temps réel
              </span>
            )}
          </p>
        </div>
      )}

      {/* Affichage des erreurs */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Bug className="w-5 h-5 text-red-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Erreur de chargement</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminDashboard;
