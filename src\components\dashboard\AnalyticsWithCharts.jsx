import React, { useEffect, useState } from 'react';
import { RefreshCw, Users, UserCheck, UserX, TrendingUp, TrendingDown } from 'lucide-react';
import { toast } from 'react-toastify';
import biService from '@/services/biService';

// Import Chart.js avec gestion d'erreur
let Chart, Doughnut, Bar;
try {
  const chartImports = require('chart.js');
  const reactChartImports = require('react-chartjs-2');

  Chart = chartImports.Chart;
  Doughnut = reactChartImports.Doughnut;
  Bar = reactChartImports.Bar;

  // Enregistrer les composants Chart.js
  Chart.register(
    chartImports.ArcElement,
    chartImports.CategoryScale,
    chartImports.LinearScale,
    chartImports.BarElement,
    chartImports.Title,
    chartImports.Tooltip,
    chartImports.Legend
  );
} catch (error) {
  console.warn('Chart.js non disponible:', error);
}

const AnalyticsWithCharts = () => {
  const [dashboardData, setDashboardData] = useState({
    metric_cards: [],
    charts: {},
    detailed_stats: {},
    metadata: {
      dashboard_title: 'Tableau de Bord Super Admin',
      dashboard_subtitle: 'Vue d\'ensemble des utilisateurs et analyses'
    },
    is_realtime: false
  });
  const [loading, setLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState(null);
  const [chartsAvailable, setChartsAvailable] = useState(false);

  // Vérifier si Chart.js est disponible
  useEffect(() => {
    setChartsAvailable(Chart && Doughnut && Bar);
  }, []);

  // Récupérer les données du tableau de bord
  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      console.log('AnalyticsWithCharts - Récupération des données...');

      const response = await biService.getSuperAdminDashboard();

      if (response.success && response.data) {
        setDashboardData(response.data);
        setLastUpdated(new Date());
        console.log('AnalyticsWithCharts - Données récupérées:', response.data);
      } else {
        console.error('AnalyticsWithCharts - Erreur de réponse:', response);
        toast.error(response.message || 'Erreur lors du chargement des données');
        // Utiliser des données de test
        setDashboardData({
          metric_cards: [
            {
              title: "Nombre total d'utilisateurs",
              value: 29,
              trend: "+100%",
              trend_period: "ce mois",
              icon: "users"
            },
            {
              title: "Utilisateurs actifs",
              value: 7,
              trend: "+150.0%",
              trend_period: "cette semaine",
              icon: "user-check"
            },
            {
              title: "Utilisateurs inactifs",
              value: 22,
              trend: "0%",
              trend_period: "ce mois",
              icon: "user-x"
            }
          ],
          charts: {
            active_vs_inactive: {
              type: "doughnut",
              title: "Utilisateurs Actifs vs Inactifs",
              data: [
                { "name": "Actifs", "value": 7, "color": "#10B981" },
                { "name": "Inactifs", "value": 22, "color": "#EF4444" }
              ]
            },
            role_distribution: {
              type: "bar",
              title: "Distribution des Utilisateurs par Rôle",
              data: [
                { "name": "Super Admin", "value": 2, "color": "#8B5CF6" },
                { "name": "Admin", "value": 7, "color": "#3B82F6" },
                { "name": "Employés", "value": 15, "color": "#10B981" },
                { "name": "Clients", "value": 5, "color": "#F59E0B" }
              ]
            }
          },
          detailed_stats: {
            users_by_role: {
              super_admin: 2,
              admin: 7,
              employee: 15,
              client: 5
            }
          },
          metadata: {
            dashboard_title: 'Tableau de Bord Super Admin',
            dashboard_subtitle: 'Vue d\'ensemble des utilisateurs et analyses'
          },
          is_realtime: false
        });
      }
    } catch (error) {
      console.error('AnalyticsWithCharts - Erreur:', error);
      toast.error('Erreur lors du chargement des données du tableau de bord');
    } finally {
      setLoading(false);
    }
  };

  // Effet pour charger les données au montage
  useEffect(() => {
    fetchDashboardData();
  }, []);

  // Préparer les données pour le graphique en donut (Actifs vs Inactifs)
  const prepareActiveInactiveData = () => {
    if (!chartsAvailable || !dashboardData?.charts?.active_vs_inactive?.data) {
      return null;
    }

    const chartData = dashboardData.charts.active_vs_inactive;
    return {
      labels: chartData.data.map(item => item.name || 'N/A'),
      datasets: [{
        data: chartData.data.map(item => item.value || 0),
        backgroundColor: chartData.data.map(item => item.color || '#gray'),
        borderWidth: 0,
        cutout: '60%'
      }]
    };
  };

  // Préparer les données pour le graphique en barres (Distribution par rôle)
  const prepareRoleDistributionData = () => {
    if (!chartsAvailable || !dashboardData?.charts?.role_distribution?.data) {
      return null;
    }

    const chartData = dashboardData.charts.role_distribution;
    return {
      labels: chartData.data.map(item => item.name || 'N/A'),
      datasets: [{
        label: 'Nombre d\'utilisateurs',
        data: chartData.data.map(item => item.value || 0),
        backgroundColor: chartData.data.map(item => item.color || '#gray'),
        borderRadius: 4,
        borderSkipped: false,
      }]
    };
  };

  // Options pour les graphiques
  const doughnutOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          padding: 20,
          usePointStyle: true,
          font: { size: 12 }
        }
      }
    }
  };

  const barOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { display: false }
    },
    scales: {
      y: {
        beginAtZero: true,
        max: 20, // Échelle adaptée aux vraies valeurs
        ticks: {
          stepSize: 5 // Étapes de 5 en 5
        },
        grid: { color: '#f3f4f6' }
      },
      x: {
        grid: { display: false }
      }
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center gap-2 py-8">
        <RefreshCw className="w-5 h-5 text-blue-600 animate-spin" />
        <span className="text-gray-600">Chargement des données en temps réel...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Section Activité des utilisateurs */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Activité des utilisateurs</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {dashboardData.metric_cards && dashboardData.metric_cards.map((card, index) => (
            <div key={index} className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    {card.icon === 'users' && <Users className="w-5 h-5 text-blue-500" />}
                    {card.icon === 'user-check' && <UserCheck className="w-5 h-5 text-green-500" />}
                    {card.icon === 'user-x' && <UserX className="w-5 h-5 text-red-500" />}
                    <span className="text-sm font-medium text-gray-600">{card.title}</span>
                  </div>
                  <p className="text-3xl font-bold text-gray-900 mb-1">{card.value}</p>
                  <p className="text-sm text-gray-500">{card.trend_period}</p>
                </div>
                <div className="text-right">
                  <div className={`flex items-center gap-1 text-sm font-medium ${card.trend && card.trend.includes('+') ? 'text-green-600' :
                    card.trend && card.trend.includes('-') ? 'text-red-600' : 'text-gray-600'
                    }`}>
                    {card.trend && card.trend.includes('+') && <TrendingUp className="w-4 h-4" />}
                    {card.trend && card.trend.includes('-') && <TrendingDown className="w-4 h-4" />}
                    <span>{card.trend}</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Section Répartition des utilisateurs par rôle */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Répartition des utilisateurs par rôle</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
            <div className="flex items-center gap-2 mb-2">
              <Users className="w-5 h-5 text-purple-500" />
              <span className="text-sm font-medium text-gray-600">Super Admin</span>
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {dashboardData.detailed_stats?.users_by_role?.super_admin || 0}
            </div>
          </div>
          <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
            <div className="flex items-center gap-2 mb-2">
              <Users className="w-5 h-5 text-blue-500" />
              <span className="text-sm font-medium text-gray-600">Admin</span>
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {dashboardData.detailed_stats?.users_by_role?.admin || 0}
            </div>
          </div>
          <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
            <div className="flex items-center gap-2 mb-2">
              <Users className="w-5 h-5 text-green-500" />
              <span className="text-sm font-medium text-gray-600">Employés</span>
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {dashboardData.detailed_stats?.users_by_role?.employee || 0}
            </div>
          </div>
          <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
            <div className="flex items-center gap-2 mb-2">
              <Users className="w-5 h-5 text-orange-500" />
              <span className="text-sm font-medium text-gray-600">Clients</span>
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {dashboardData.detailed_stats?.users_by_role?.client || 0}
            </div>
          </div>
        </div>
      </div>

      {/* Section Graphiques */}
      {chartsAvailable ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
            <h4 className="text-base font-medium text-gray-900 mb-4">
              {dashboardData.charts?.active_vs_inactive?.title || 'Utilisateurs Actifs vs Inactifs'}
            </h4>
            <div className="h-64 flex items-center justify-center">
              {prepareActiveInactiveData() ? (
                <Doughnut data={prepareActiveInactiveData()} options={doughnutOptions} />
              ) : (
                <p className="text-gray-500">Données non disponibles</p>
              )}
            </div>
          </div>
          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
            <h4 className="text-base font-medium text-gray-900 mb-4">
              {dashboardData.charts?.role_distribution?.title || 'Distribution des Utilisateurs par Rôle'}
            </h4>
            <div className="h-80 flex items-center justify-center">
              {prepareRoleDistributionData() ? (
                <Bar data={prepareRoleDistributionData()} options={barOptions} />
              ) : (
                <p className="text-gray-500">Données non disponibles</p>
              )}
            </div>
          </div>
        </div>
      ) : (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <p className="text-yellow-800 text-sm">
            <strong>Graphiques non disponibles:</strong> Chart.js n'est pas correctement installé ou configuré.
          </p>
        </div>
      )}

      {/* Bouton de rafraîchissement */}
      <div className="flex justify-center">
        <button
          onClick={fetchDashboardData}
          disabled={loading}
          className="flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
        >
          <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
          {loading ? 'Actualisation...' : 'Actualiser les données'}
        </button>
      </div>

      {/* Informations de mise à jour */}
      <div className="text-center text-sm text-gray-500">
        {lastUpdated && (
          <p>Dernière mise à jour: {lastUpdated.toLocaleString()}</p>
        )}
        {dashboardData.metadata?.data_source && (
          <p className="mt-1">
            Source: {dashboardData.metadata.data_source === 'real_time' ? 'Données en temps réel' : 'Données de démonstration'}
          </p>
        )}
      </div>
    </div>
  );
};

export default AnalyticsWithCharts;
