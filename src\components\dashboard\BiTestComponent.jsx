import React, { useState, useEffect } from 'react';
import { RefreshCw, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import biService from '@/services/biService';

const BiTestComponent = () => {
  const [testResult, setTestResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const [rawData, setRawData] = useState(null);

  const testEndpoint = async () => {
    setLoading(true);
    setTestResult(null);
    setRawData(null);

    try {
      console.log('🧪 Test de l\'endpoint BI...');

      // Test 1: Vérifier la connectivité de base
      console.log('🔍 Test 1: Vérification de la connectivité backend...');
      try {
        const healthResponse = await fetch('http://localhost:8000/api/health/', {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
          }
        });
        console.log('🏥 Health check status:', healthResponse.status);
      } catch (healthError) {
        console.error('❌ Backend non accessible:', healthError);
        setTestResult({
          status: 'error',
          message: 'Backend non accessible sur http://localhost:8000',
          error: 'Vérifiez que le serveur Django est démarré'
        });
        return;
      }

      // Test 2: Vérifier l'authentification
      console.log('🔍 Test 2: Vérification de l\'authentification...');
      const token = localStorage.getItem('authToken');
      if (!token) {
        setTestResult({
          status: 'error',
          message: 'Token d\'authentification manquant',
          error: 'Veuillez vous reconnecter'
        });
        return;
      }
      console.log('🔑 Token trouvé:', token.substring(0, 20) + '...');

      // Test 3: Test direct de l'endpoint BI
      console.log('🔍 Test 3: Test direct de l\'endpoint BI...');
      try {
        const directResponse = await fetch('http://localhost:8000/api/bi/super-admin/dashboard/', {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        console.log('📡 Réponse directe status:', directResponse.status);
        console.log('📡 Réponse directe headers:', Object.fromEntries(directResponse.headers.entries()));

        if (directResponse.ok) {
          const directData = await directResponse.json();
          console.log('✅ Données reçues directement:', directData);
          setTestResult({
            status: 'success',
            message: 'Endpoint BI fonctionne correctement !',
            data: directData
          });
          setRawData(directData);
          return;
        } else {
          const errorText = await directResponse.text();
          console.error('❌ Erreur directe:', directResponse.status, errorText);
          setTestResult({
            status: 'error',
            message: `Erreur HTTP ${directResponse.status}`,
            error: errorText || directResponse.statusText
          });
          return;
        }
      } catch (directError) {
        console.error('❌ Erreur lors du test direct:', directError);
      }

      // Test 4: Test via le service BI
      console.log('🔍 Test 4: Test via le service BI...');
      const response = await biService.getSuperAdminDashboard();

      console.log('📊 Réponse du service:', response);

      if (response.success) {
        setTestResult({
          status: 'success',
          message: 'Service BI fonctionne correctement !',
          data: response.data
        });
        setRawData(response.data);
      } else {
        setTestResult({
          status: 'warning',
          message: 'Service BI utilise des données mockées',
          data: response.data,
          error: response.error
        });
        setRawData(response.data);
      }
    } catch (error) {
      console.error('❌ Erreur lors du test:', error);
      setTestResult({
        status: 'error',
        message: 'Erreur lors du test de l\'endpoint BI',
        error: error.message
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    testEndpoint();
  }, []);

  const getStatusIcon = () => {
    if (loading) return <RefreshCw className="w-5 h-5 animate-spin text-blue-500" />;

    switch (testResult?.status) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'warning':
        return <AlertCircle className="w-5 h-5 text-yellow-500" />;
      case 'error':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <RefreshCw className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusColor = () => {
    switch (testResult?.status) {
      case 'success':
        return 'border-green-200 bg-green-50';
      case 'warning':
        return 'border-yellow-200 bg-yellow-50';
      case 'error':
        return 'border-red-200 bg-red-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900">Test de l'Endpoint BI</h2>
          <button
            onClick={testEndpoint}
            disabled={loading}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            {loading ? 'Test en cours...' : 'Retester'}
          </button>
        </div>

        {/* Résultat du test */}
        {testResult && (
          <div className={`border rounded-lg p-4 mb-6 ${getStatusColor()}`}>
            <div className="flex items-center gap-3 mb-2">
              {getStatusIcon()}
              <h3 className="text-lg font-semibold">{testResult.message}</h3>
            </div>

            {testResult.error && (
              <div className="mt-2 text-sm text-red-600">
                <strong>Erreur:</strong> {testResult.error}
              </div>
            )}
          </div>
        )}

        {/* Informations sur l'endpoint */}
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <h3 className="text-lg font-semibold mb-3">Informations sur l'Endpoint</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <strong>URL:</strong> <code className="bg-gray-200 px-2 py-1 rounded">GET /api/bi/super-admin/dashboard/</code>
            </div>
            <div>
              <strong>Base URL:</strong> <code className="bg-gray-200 px-2 py-1 rounded">http://localhost:8000</code>
            </div>
            <div>
              <strong>Authentification:</strong> Bearer Token (JWT)
            </div>
            <div>
              <strong>Rôle requis:</strong> super_admin
            </div>
          </div>
        </div>

        {/* Diagnostic du système */}
        <div className="bg-blue-50 rounded-lg p-4 mb-6">
          <h3 className="text-lg font-semibold mb-3">Diagnostic du Système</h3>
          <div className="space-y-2 text-sm">
            <div className="flex items-center gap-2">
              <span className="font-medium">Backend Django:</span>
              <span className="text-gray-600">Doit être démarré sur http://localhost:8000</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="font-medium">Frontend React:</span>
              <span className="text-gray-600">Démarré sur http://localhost:5173</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="font-medium">Token d'auth:</span>
              <span className="text-gray-600">
                {localStorage.getItem('authToken') ? '✅ Présent' : '❌ Manquant'}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <span className="font-medium">Rôle utilisateur:</span>
              <span className="text-gray-600">
                {JSON.parse(localStorage.getItem('user') || '{}')?.role || 'Non défini'}
              </span>
            </div>
          </div>
        </div>

        {/* Instructions de démarrage */}
        <div className="bg-yellow-50 rounded-lg p-4 mb-6">
          <h3 className="text-lg font-semibold mb-3">Instructions de Démarrage</h3>
          <div className="space-y-2 text-sm">
            <div>
              <strong>1. Démarrer le backend Django:</strong>
              <code className="block bg-gray-200 px-2 py-1 rounded mt-1">
                cd backend && python manage.py runserver
              </code>
            </div>
            <div>
              <strong>2. Vérifier l'endpoint manuellement:</strong>
              <code className="block bg-gray-200 px-2 py-1 rounded mt-1">
                curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:8000/api/bi/super-admin/dashboard/
              </code>
            </div>
            <div>
              <strong>3. Vérifier les logs Django:</strong>
              <span className="text-gray-600">Rechercher les erreurs 404 ou 500 dans la console Django</span>
            </div>
          </div>
        </div>

        {/* Données reçues */}
        {rawData && (
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-3">Données Reçues</h3>

            {/* Métriques principales */}
            {rawData.metric_cards && (
              <div className="mb-4">
                <h4 className="font-medium mb-2">Cartes de Métriques:</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  {rawData.metric_cards.map((card, index) => (
                    <div key={index} className="bg-white p-3 rounded border">
                      <div className="text-sm font-medium text-gray-600">{card.title}</div>
                      <div className="text-xl font-bold text-gray-900">{card.value}</div>
                      <div className="text-xs text-gray-500">{card.trend} {card.trend_period}</div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Statistiques détaillées */}
            {rawData.detailed_stats && (
              <div className="mb-4">
                <h4 className="font-medium mb-2">Statistiques Détaillées:</h4>
                <div className="bg-white p-3 rounded border">
                  <pre className="text-xs text-gray-600 overflow-x-auto">
                    {JSON.stringify(rawData.detailed_stats, null, 2)}
                  </pre>
                </div>
              </div>
            )}

            {/* Métadonnées */}
            {rawData.metadata && (
              <div className="mb-4">
                <h4 className="font-medium mb-2">Métadonnées:</h4>
                <div className="bg-white p-3 rounded border text-sm">
                  <div><strong>Dernière mise à jour:</strong> {rawData.metadata.last_updated}</div>
                  <div><strong>Source des données:</strong> {rawData.metadata.data_source}</div>
                  <div><strong>Intervalle de rafraîchissement:</strong> {rawData.metadata.refresh_interval}s</div>
                  <div><strong>Temps réel:</strong> {rawData.is_realtime ? 'Oui' : 'Non'}</div>
                </div>
              </div>
            )}

            {/* Données brutes complètes */}
            <details className="mt-4">
              <summary className="cursor-pointer font-medium text-gray-700 hover:text-gray-900">
                Voir les données brutes complètes
              </summary>
              <div className="mt-2 bg-white p-3 rounded border">
                <pre className="text-xs text-gray-600 overflow-x-auto max-h-96 overflow-y-auto">
                  {JSON.stringify(rawData, null, 2)}
                </pre>
              </div>
            </details>
          </div>
        )}
      </div>
    </div>
  );
};

export default BiTestComponent;
