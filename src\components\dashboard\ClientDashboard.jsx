import React, { useEffect, useState } from 'react';
import { useClient } from '@/contexts/ClientContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, CheckCircle, Clock, Calendar, ListTodo } from 'lucide-react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';

const ClientDashboard = () => {
  const { metrics, loading, fetchPersonalMetrics } = useClient();
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    fetchPersonalMetrics();
  }, [fetchPersonalMetrics]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
      </div>
    );
  }

  if (!metrics) {
    return (
      <div className="text-center p-8">
        <p className="text-gray-500">Aucune donnée disponible pour le moment.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Tableau de bord personnel</h2>
        <p className="text-sm text-gray-500">
          Dernière mise à jour: {format(new Date(), 'PPP', { locale: fr })}
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Vue d'ensemble</TabsTrigger>
          <TabsTrigger value="tasks">Tâches</TabsTrigger>
          <TabsTrigger value="events">Événements</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <StatCard
              title="Tâches personnelles"
              icon={<ListTodo className="h-5 w-5 text-indigo-600" />}
              value={metrics.personal_task_count || 0}
              description="Tâches en cours"
              progress={metrics.personal_task_completion_rate || 0}
              progressLabel="Taux de complétion"
            />
            <StatCard
              title="Événements personnels"
              icon={<Calendar className="h-5 w-5 text-green-600" />}
              value={metrics.personal_event_count || 0}
              description="Événements planifiés"
              progress={metrics.personal_event_completion_rate || 0}
              progressLabel="Taux de complétion"
            />
            <StatCard
              title="Sessions Pomodoro"
              icon={<Clock className="h-5 w-5 text-red-600" />}
              value={metrics.pomodoro_session_count || 0}
              description="Sessions complétées"
              progress={metrics.pomodoro_focus_rate || 0}
              progressLabel="Taux de concentration"
            />
          </div>
        </TabsContent>

        <TabsContent value="tasks" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Statut des tâches</CardTitle>
                <CardDescription>Répartition par statut</CardDescription>
              </CardHeader>
              <CardContent>
                {metrics.personal_task_status_distribution ? (
                  <div className="space-y-4">
                    <StatusBar
                      label="À faire"
                      value={metrics.personal_task_status_distribution.a_faire || 0}
                      total={metrics.personal_task_count || 1}
                      color="bg-blue-500"
                    />
                    <StatusBar
                      label="En cours"
                      value={metrics.personal_task_status_distribution.en_cours || 0}
                      total={metrics.personal_task_count || 1}
                      color="bg-yellow-500"
                    />
                    <StatusBar
                      label="Achevée"
                      value={metrics.personal_task_status_distribution.achevee || 0}
                      total={metrics.personal_task_count || 1}
                      color="bg-green-500"
                    />
                    <StatusBar
                      label="Archivée"
                      value={metrics.personal_task_status_distribution.archived || 0}
                      total={metrics.personal_task_count || 1}
                      color="bg-gray-500"
                    />
                  </div>
                ) : (
                  <p className="text-gray-500">Aucune donnée disponible</p>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Productivité</CardTitle>
                <CardDescription>Taux de complétion des tâches</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col items-center justify-center h-40">
                  <div className="text-5xl font-bold text-indigo-600 mb-2">
                    {Math.round(metrics.personal_task_completion_rate || 0)}%
                  </div>
                  <p className="text-gray-500">des tâches complétées</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="events" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Statut des événements</CardTitle>
                <CardDescription>Répartition par statut</CardDescription>
              </CardHeader>
              <CardContent>
                {metrics.personal_event_status_distribution ? (
                  <div className="space-y-4">
                    <StatusBar
                      label="À venir"
                      value={metrics.personal_event_status_distribution.pending || 0}
                      total={metrics.personal_event_count || 1}
                      color="bg-blue-500"
                    />
                    <StatusBar
                      label="Terminé"
                      value={metrics.personal_event_status_distribution.completed || 0}
                      total={metrics.personal_event_count || 1}
                      color="bg-green-500"
                    />
                    <StatusBar
                      label="Archivé"
                      value={metrics.personal_event_status_distribution.archived || 0}
                      total={metrics.personal_event_count || 1}
                      color="bg-gray-500"
                    />
                  </div>
                ) : (
                  <p className="text-gray-500">Aucune donnée disponible</p>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Planification</CardTitle>
                <CardDescription>Événements à venir</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col items-center justify-center h-40">
                  <div className="text-5xl font-bold text-green-600 mb-2">
                    {metrics.personal_event_status_distribution?.pending || 0}
                  </div>
                  <p className="text-gray-500">événements planifiés</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

// Composant pour afficher une carte de statistique
const StatCard = ({ title, icon, value, description, progress, progressLabel }) => (
  <Card>
    <CardHeader className="flex flex-row items-center justify-between pb-2">
      <CardTitle className="text-sm font-medium">{title}</CardTitle>
      {icon}
    </CardHeader>
    <CardContent>
      <div className="text-2xl font-bold">{value}</div>
      <p className="text-xs text-gray-500">{description}</p>
      <div className="mt-4">
        <div className="flex justify-between items-center mb-1">
          <span className="text-xs font-medium">{progressLabel}</span>
          <span className="text-xs font-medium">{Math.round(progress)}%</span>
        </div>
        <Progress value={progress} className="h-2" />
      </div>
    </CardContent>
  </Card>
);

// Composant pour afficher une barre de statut
const StatusBar = ({ label, value, total, color }) => (
  <div className="space-y-1">
    <div className="flex justify-between items-center">
      <span className="text-sm font-medium">{label}</span>
      <span className="text-sm font-medium">{value}</span>
    </div>
    <div className="h-2 bg-gray-200 rounded-full">
      <div
        className={`h-full rounded-full ${color}`}
        style={{ width: `${(value / total) * 100}%` }}
      ></div>
    </div>
  </div>
);

export default ClientDashboard;
