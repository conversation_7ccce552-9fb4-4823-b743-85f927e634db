import React, { useEffect, useState } from 'react';
import { useEmployee } from '@/contexts/EmployeeContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, Users, Calendar, ListTodo, Clock } from 'lucide-react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';

const EmployeeDashboard = () => {
  const { metrics, teams, loading, fetchEmployeeMetrics } = useEmployee();
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedTeam, setSelectedTeam] = useState('personal');

  useEffect(() => {
    fetchEmployeeMetrics();
  }, [fetchEmployeeMetrics]);

  useEffect(() => {
    if (teams && teams.length > 0 && selectedTeam === 'personal') {
      setSelectedTeam('personal');
    }
  }, [teams, selectedTeam]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
      </div>
    );
  }

  if (!metrics) {
    return (
      <div className="text-center p-8">
        <p className="text-gray-500">Aucune donnée disponible pour le moment.</p>
      </div>
    );
  }

  // Déterminer les métriques à afficher en fonction de l'onglet sélectionné
  const displayMetrics = selectedTeam === 'personal'
    ? (metrics?.personal_metrics || {})
    : (metrics?.team_metrics?.find(tm => String(tm.team_id) === String(selectedTeam)) || {});

  const teamName = selectedTeam === 'personal'
    ? 'Personnel'
    : teams?.find(t => String(t.id) === String(selectedTeam))?.name || 'Équipe';

  // Valeurs par défaut pour éviter les erreurs undefined
  const safeDisplayMetrics = {
    task_count: 0,
    event_count: 0,
    member_count: 0,
    pomodoro_session_count: 0,
    task_completion_rate: 0,
    event_completion_rate: 0,
    pomodoro_focus_rate: 0,
    task_status_distribution: {
      a_faire: 0,
      en_cours: 0,
      achevee: 0,
      archived: 0
    },
    event_status_distribution: {
      pending: 0,
      completed: 0,
      archived: 0
    },
    ...displayMetrics
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Tableau de bord employé</h2>
        <p className="text-sm text-gray-500">
          Dernière mise à jour: {format(new Date(), 'PPP', { locale: fr })}
        </p>
      </div>

      <div className="flex justify-between items-center mb-6">
        <div className="w-64">
          <Select value={selectedTeam} onValueChange={setSelectedTeam}>
            <SelectTrigger>
              <SelectValue placeholder="Sélectionner une vue" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="personal">Personnel</SelectItem>
              {teams && teams.map(team => (
                <SelectItem key={team.id} value={team.id}>{team.name}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full max-w-md grid-cols-3 mb-6">
          <TabsTrigger value="overview">Vue d'ensemble</TabsTrigger>
          <TabsTrigger value="tasks">Tâches</TabsTrigger>
          <TabsTrigger value="events">Événements</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {selectedTeam === 'personal' ? (
              <>
                <StatCard
                  title="Tâches personnelles"
                  icon={<ListTodo className="h-5 w-5 text-indigo-600" />}
                  value={safeDisplayMetrics.task_count}
                  description="Tâches en cours"
                  progress={safeDisplayMetrics.task_completion_rate}
                  progressLabel="Taux de complétion"
                />
                <StatCard
                  title="Événements personnels"
                  icon={<Calendar className="h-5 w-5 text-green-600" />}
                  value={safeDisplayMetrics.event_count}
                  description="Événements planifiés"
                  progress={safeDisplayMetrics.event_completion_rate}
                  progressLabel="Taux de complétion"
                />
                <StatCard
                  title="Sessions Pomodoro"
                  icon={<Clock className="h-5 w-5 text-red-600" />}
                  value={safeDisplayMetrics.pomodoro_session_count}
                  description="Sessions complétées"
                  progress={safeDisplayMetrics.pomodoro_focus_rate}
                  progressLabel="Taux de concentration"
                />
              </>
            ) : (
              <>
                <StatCard
                  title={`Membres de ${teamName}`}
                  icon={<Users className="h-5 w-5 text-indigo-600" />}
                  value={safeDisplayMetrics.member_count}
                  description="Membres actifs"
                />
                <StatCard
                  title="Tâches d'équipe"
                  icon={<ListTodo className="h-5 w-5 text-blue-600" />}
                  value={safeDisplayMetrics.task_count}
                  description="Tâches en cours"
                  progress={safeDisplayMetrics.task_completion_rate}
                  progressLabel="Taux de complétion"
                />
                <StatCard
                  title="Événements d'équipe"
                  icon={<Calendar className="h-5 w-5 text-green-600" />}
                  value={safeDisplayMetrics.event_count}
                  description="Événements planifiés"
                  progress={safeDisplayMetrics.event_completion_rate}
                  progressLabel="Taux de complétion"
                />
              </>
            )}
          </div>
        </TabsContent>

        <TabsContent value="tasks" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Statut des tâches</CardTitle>
                <CardDescription>Répartition par statut pour {teamName}</CardDescription>
              </CardHeader>
              <CardContent>
                {safeDisplayMetrics.task_status_distribution ? (
                  <div className="space-y-4">
                    <StatusBar
                      label="À faire"
                      value={safeDisplayMetrics.task_status_distribution.a_faire}
                      total={Math.max(safeDisplayMetrics.task_count, 1)}
                      color="bg-blue-500"
                    />
                    <StatusBar
                      label="En cours"
                      value={safeDisplayMetrics.task_status_distribution.en_cours}
                      total={Math.max(safeDisplayMetrics.task_count, 1)}
                      color="bg-yellow-500"
                    />
                    <StatusBar
                      label="Achevée"
                      value={safeDisplayMetrics.task_status_distribution.achevee}
                      total={Math.max(safeDisplayMetrics.task_count, 1)}
                      color="bg-green-500"
                    />
                    <StatusBar
                      label="Archivée"
                      value={safeDisplayMetrics.task_status_distribution.archived}
                      total={Math.max(safeDisplayMetrics.task_count, 1)}
                      color="bg-gray-500"
                    />
                  </div>
                ) : (
                  <p className="text-gray-500">Aucune donnée disponible</p>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Productivité</CardTitle>
                <CardDescription>Taux de complétion des tâches</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col items-center justify-center h-40">
                  <div className="text-5xl font-bold text-indigo-600 mb-2">
                    {Math.round(safeDisplayMetrics.task_completion_rate)}%
                  </div>
                  <p className="text-gray-500">des tâches complétées</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="events" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Statut des événements</CardTitle>
                <CardDescription>Répartition par statut pour {teamName}</CardDescription>
              </CardHeader>
              <CardContent>
                {safeDisplayMetrics.event_status_distribution ? (
                  <div className="space-y-4">
                    <StatusBar
                      label="À venir"
                      value={safeDisplayMetrics.event_status_distribution.pending}
                      total={Math.max(safeDisplayMetrics.event_count, 1)}
                      color="bg-blue-500"
                    />
                    <StatusBar
                      label="Terminé"
                      value={safeDisplayMetrics.event_status_distribution.completed}
                      total={Math.max(safeDisplayMetrics.event_count, 1)}
                      color="bg-green-500"
                    />
                    <StatusBar
                      label="Archivé"
                      value={safeDisplayMetrics.event_status_distribution.archived}
                      total={Math.max(safeDisplayMetrics.event_count, 1)}
                      color="bg-gray-500"
                    />
                  </div>
                ) : (
                  <p className="text-gray-500">Aucune donnée disponible</p>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Planification</CardTitle>
                <CardDescription>Événements à venir pour {teamName}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col items-center justify-center h-40">
                  <div className="text-5xl font-bold text-green-600 mb-2">
                    {safeDisplayMetrics.event_status_distribution?.pending || 0}
                  </div>
                  <p className="text-gray-500">événements planifiés</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

// Composant pour afficher une carte de statistique
const StatCard = ({ title, icon, value, description, progress, progressLabel }) => (
  <Card>
    <CardHeader className="flex flex-row items-center justify-between pb-2">
      <CardTitle className="text-sm font-medium">{title}</CardTitle>
      {icon}
    </CardHeader>
    <CardContent>
      <div className="text-2xl font-bold">{value}</div>
      <p className="text-xs text-gray-500">{description}</p>
      {progress !== undefined && (
        <div className="mt-4">
          <div className="flex justify-between items-center mb-1">
            <span className="text-xs font-medium">{progressLabel}</span>
            <span className="text-xs font-medium">{Math.round(progress)}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
      )}
    </CardContent>
  </Card>
);

// Composant pour afficher une barre de statut
const StatusBar = ({ label, value, total, color }) => (
  <div className="space-y-1">
    <div className="flex justify-between items-center">
      <span className="text-sm font-medium">{label}</span>
      <span className="text-sm font-medium">{value}</span>
    </div>
    <div className="h-2 bg-gray-200 rounded-full">
      <div
        className={`h-full rounded-full ${color}`}
        style={{ width: `${(value / total) * 100}%` }}
      ></div>
    </div>
  </div>
);

export default EmployeeDashboard;
