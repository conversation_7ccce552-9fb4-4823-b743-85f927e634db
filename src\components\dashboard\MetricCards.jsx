import React from 'react';
import {
  Users,
  UserCheck,
  UserX,
  TrendingUp,
  TrendingDown,
  Minus,
  Clock,
  Calendar,
  Target
} from 'lucide-react';

/**
 * Composant MetricCards pour afficher les cartes de métriques du dashboard admin
 * @param {Object} props - Propriétés du composant
 * @param {Array} props.metricCards - Données des cartes de métriques selon votre guide
 * @param {string} props.currentPeriod - Période actuelle
 * @param {boolean} props.loading - État de chargement
 * @returns {JSX.Element} - Composant MetricCards
 */
const MetricCards = ({ metricCards, currentPeriod, loading }) => {
  // Fonction pour obtenir l'icône appropriée
  const getIcon = (iconName, color) => {
    const iconProps = {
      className: "w-8 h-8",
      style: { color: color }
    };

    switch (iconName) {
      case 'users':
        return <Users {...iconProps} />;
      case 'user-group':
        return <Users {...iconProps} />;
      case 'user-check':
        return <UserCheck {...iconProps} />;
      case 'user-x':
        return <UserX {...iconProps} />;
      case 'trending-up':
        return <TrendingUp {...iconProps} />;
      case 'trending-down':
        return <TrendingDown {...iconProps} />;
      case 'clock':
        return <Clock {...iconProps} />;
      case 'calendar':
        return <Calendar {...iconProps} />;
      case 'target':
        return <Target {...iconProps} />;
      default:
        return <Users {...iconProps} />;
    }
  };

  // Fonction pour obtenir l'icône de tendance
  const getTrendIcon = (trend) => {
    if (!trend) return <Minus className="w-4 h-4 text-gray-400" />;

    const trendValue = parseFloat(trend.replace(/[^-\d.]/g, ''));

    if (trendValue > 0) {
      return <TrendingUp className="w-4 h-4 text-green-500" />;
    } else if (trendValue < 0) {
      return <TrendingDown className="w-4 h-4 text-red-500" />;
    } else {
      return <Minus className="w-4 h-4 text-gray-400" />;
    }
  };

  // Fonction pour obtenir la couleur de la tendance
  const getTrendColor = (trend) => {
    if (!trend) return 'text-gray-500';

    const trendValue = parseFloat(trend.replace(/[^-\d.]/g, ''));

    if (trendValue > 0) {
      return 'text-green-600';
    } else if (trendValue < 0) {
      return 'text-red-600';
    } else {
      return 'text-gray-500';
    }
  };

  // Fonction pour formater la valeur
  const formatValue = (value) => {
    if (typeof value === 'number') {
      return value.toLocaleString();
    }
    return value;
  };

  // Composant de carte de métrique individuelle
  const MetricCard = ({ card, index }) => (
    <div
      key={index}
      className="bg-white rounded-lg shadow-sm p-6 border border-gray-100 hover:shadow-md transition-shadow duration-200"
    >
      <div className="flex items-center justify-between">
        <div className="flex-1">
          {/* Titre principal */}
          <p className="text-gray-600 text-sm mb-2 font-medium">
            {card.title}
          </p>

          {/* Sous-titre si présent */}
          {card.subtitle && (
            <p className="text-gray-500 text-xs mb-3">
              {card.subtitle}
            </p>
          )}

          {/* Valeur principale */}
          <p className="text-4xl font-bold text-gray-900 mb-3">
            {formatValue(card.value)}
          </p>

          {/* Tendance et période */}
          <div className="flex items-center space-x-2">
            {getTrendIcon(card.trend)}
            <p className={`text-sm font-medium ${getTrendColor(card.trend)}`}>
              {card.trend} {card.trend_period}
            </p>
          </div>

          {/* Indicateurs temps réel */}
          {card.period === 'today' && currentPeriod === 'today' && (
            <div className="mt-2 flex items-center space-x-2">
              <span className="inline-block w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
              <span className="text-xs text-green-600 font-medium">En temps réel</span>
            </div>
          )}

          {/* Source de données */}
          {card.data_source && (
            <p className="text-xs text-gray-400 mt-1">
              Source: {card.data_source}
            </p>
          )}
        </div>

        {/* Icône avec fond coloré */}
        <div
          className="p-4 rounded-lg flex-shrink-0"
          style={{ backgroundColor: `${card.color}20` }}
        >
          {getIcon(card.icon, card.color)}
        </div>
      </div>

      {/* Dernière mise à jour */}
      {card.last_updated && (
        <div className="mt-4 pt-4 border-t border-gray-100">
          <p className="text-xs text-gray-400">
            Mis à jour: {new Date(card.last_updated).toLocaleTimeString('fr-FR')}
          </p>
        </div>
      )}
    </div>
  );

  // Composant de carte de chargement
  const LoadingCard = ({ index }) => (
    <div key={`loading-${index}`} className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <div className="h-4 bg-gray-200 rounded animate-pulse mb-2"></div>
          <div className="h-3 bg-gray-100 rounded animate-pulse mb-3 w-3/4"></div>
          <div className="h-10 bg-gray-200 rounded animate-pulse mb-3 w-1/2"></div>
          <div className="h-3 bg-gray-100 rounded animate-pulse w-2/3"></div>
        </div>
        <div className="w-16 h-16 bg-gray-200 rounded-lg animate-pulse flex-shrink-0"></div>
      </div>
    </div>
  );

  // Vérification des données
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {[0, 1, 2].map(index => (
          <LoadingCard key={index} index={index} />
        ))}
      </div>
    );
  }

  if (!metricCards || !Array.isArray(metricCards) || metricCards.length === 0) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="col-span-full bg-white rounded-lg shadow-sm p-8 border border-gray-100 text-center">
          <div className="text-gray-400 text-4xl mb-4">📊</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Aucune métrique disponible
          </h3>
          <p className="text-gray-500 text-sm">
            Les données de métriques ne sont pas disponibles pour le moment.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
      {metricCards.map((card, index) => (
        <MetricCard key={index} card={card} index={index} />
      ))}
    </div>
  );
};

export default MetricCards;
