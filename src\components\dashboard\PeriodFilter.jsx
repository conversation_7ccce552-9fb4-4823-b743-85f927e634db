import React from 'react';
import { Clock, Calendar, TrendingUp } from 'lucide-react';

/**
 * Composant PeriodFilter pour filtrer les données par période
 * @param {Object} props - Propriétés du composant
 * @param {Array} props.availablePeriods - Périodes disponibles selon votre guide
 * @param {string} props.currentPeriod - Période actuellement sélectionnée
 * @param {Function} props.onPeriodChange - Fonction appelée lors du changement de période
 * @param {boolean} props.loading - État de chargement
 * @returns {JSX.Element} - Composant PeriodFilter
 */
const PeriodFilter = ({ 
  availablePeriods, 
  currentPeriod, 
  onPeriodChange, 
  loading = false 
}) => {
  // Fonction pour obtenir l'icône appropriée selon la période
  const getPeriodIcon = (periodValue) => {
    switch (periodValue) {
      case 'today':
        return <Calendar className="w-4 h-4" />;
      case '1h':
        return <Clock className="w-4 h-4" />;
      case '24h':
        return <Clock className="w-4 h-4" />;
      case '7d':
        return <TrendingUp className="w-4 h-4" />;
      case '30d':
        return <TrendingUp className="w-4 h-4" />;
      default:
        return <Calendar className="w-4 h-4" />;
    }
  };

  // Fonction pour gérer le changement de période
  const handlePeriodChange = (periodValue) => {
    if (loading || periodValue === currentPeriod) return;
    
    console.log(`PeriodFilter - Changement de période vers: ${periodValue}`);
    onPeriodChange(periodValue);
  };

  // Fonction pour gérer les événements clavier (accessibilité)
  const handleKeyDown = (event, periodValue) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handlePeriodChange(periodValue);
    }
  };

  // Vérification des données
  if (!availablePeriods || !Array.isArray(availablePeriods) || availablePeriods.length === 0) {
    return (
      <div className="flex space-x-2 mb-4">
        <div className="px-4 py-2 bg-gray-100 text-gray-400 rounded-lg text-sm">
          Aucune période disponible
        </div>
      </div>
    );
  }

  return (
    <div className="mb-6">
      {/* Titre de la section */}
      <div className="mb-4">
        <h3 className="text-sm font-medium text-gray-700 mb-2">
          Filtrer par période
        </h3>
        <p className="text-xs text-gray-500">
          Sélectionnez une période pour analyser vos données d'équipe
        </p>
      </div>

      {/* Boutons de filtrage */}
      <div className="flex flex-wrap gap-2">
        {availablePeriods.map((period) => {
          const isSelected = currentPeriod === period.value;
          const isToday = period.value === 'today';
          
          return (
            <button
              key={period.value}
              onClick={() => handlePeriodChange(period.value)}
              onKeyDown={(e) => handleKeyDown(e, period.value)}
              disabled={loading}
              className={`
                flex items-center space-x-2 px-4 py-2 rounded-lg font-medium text-sm
                transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2
                ${isSelected
                  ? 'text-white shadow-lg transform scale-105'
                  : 'bg-white text-gray-700 border border-gray-200 hover:bg-gray-50 hover:border-gray-300'
                }
                ${loading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                ${isSelected ? 'focus:ring-white' : 'focus:ring-blue-500'}
              `}
              style={{
                backgroundColor: isSelected ? period.color : undefined,
                borderColor: isSelected ? period.color : undefined
              }}
              role="tab"
              aria-pressed={isSelected}
              aria-label={`Filtrer par ${period.label}`}
              tabIndex={0}
            >
              {/* Icône */}
              <span className={isSelected ? 'text-white' : 'text-gray-500'}>
                {getPeriodIcon(period.value)}
              </span>
              
              {/* Label */}
              <span>{period.label}</span>
              
              {/* Indicateur temps réel pour "today" */}
              {isToday && isSelected && (
                <span className="inline-block w-2 h-2 bg-white rounded-full animate-pulse ml-1"></span>
              )}
            </button>
          );
        })}
      </div>

      {/* Indicateur de période sélectionnée */}
      <div className="mt-3 flex items-center space-x-2 text-xs text-gray-500">
        <span>Période sélectionnée:</span>
        <span className="font-medium text-gray-700">
          {availablePeriods.find(p => p.value === currentPeriod)?.label || currentPeriod}
        </span>
        
        {/* Indicateur temps réel */}
        {currentPeriod === 'today' && (
          <div className="flex items-center space-x-1">
            <span className="inline-block w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
            <span className="text-green-600 font-medium">Temps réel</span>
          </div>
        )}
        
        {/* Indicateur de chargement */}
        {loading && (
          <div className="flex items-center space-x-1">
            <div className="w-3 h-3 border border-blue-500 border-t-transparent rounded-full animate-spin"></div>
            <span className="text-blue-600">Chargement...</span>
          </div>
        )}
      </div>

      {/* Description de la période */}
      <div className="mt-2">
        {currentPeriod === 'today' && (
          <p className="text-xs text-gray-500">
            📊 Données en temps réel depuis 00:00:00 aujourd'hui
          </p>
        )}
        {currentPeriod === '1h' && (
          <p className="text-xs text-gray-500">
            ⏰ Données de la dernière heure
          </p>
        )}
        {currentPeriod === '24h' && (
          <p className="text-xs text-gray-500">
            📅 Données des dernières 24 heures
          </p>
        )}
        {currentPeriod === '7d' && (
          <p className="text-xs text-gray-500">
            📈 Données des 7 derniers jours
          </p>
        )}
        {currentPeriod === '30d' && (
          <p className="text-xs text-gray-500">
            📊 Données des 30 derniers jours
          </p>
        )}
      </div>
    </div>
  );
};

export default PeriodFilter;
