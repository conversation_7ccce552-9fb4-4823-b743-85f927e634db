import React from 'react';
import { Users, UserCheck, UserX } from 'lucide-react';

const SimpleAnalytics = () => {
  // Données de test simples
  const testData = {
    metric_cards: [
      {
        title: "Nombre total d'utilisateurs",
        value: 29,
        trend: "+100%",
        trend_period: "ce mois",
        icon: "users"
      },
      {
        title: "Utilisateurs actifs",
        value: 7,
        trend: "+150.0%",
        trend_period: "cette semaine",
        icon: "user-check"
      },
      {
        title: "Utilisateurs inactifs",
        value: 22,
        trend: "0%",
        trend_period: "ce mois",
        icon: "user-x"
      }
    ],
    detailed_stats: {
      users_by_role: {
        super_admin: 2,
        admin: 7,
        employee: 15,
        client: 5
      }
    }
  };

  return (
    <div className="space-y-6">
      {/* Section Activité des utilisateurs */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Activité des utilisateurs</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {testData.metric_cards.map((card, index) => (
            <div key={index} className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    {card.icon === 'users' && <Users className="w-5 h-5 text-blue-500" />}
                    {card.icon === 'user-check' && <UserCheck className="w-5 h-5 text-green-500" />}
                    {card.icon === 'user-x' && <UserX className="w-5 h-5 text-red-500" />}
                    <span className="text-sm font-medium text-gray-600">{card.title}</span>
                  </div>
                  <p className="text-3xl font-bold text-gray-900 mb-1">{card.value}</p>
                  <p className="text-sm text-gray-500">{card.trend_period}</p>
                </div>
                <div className="text-right">
                  <div className={`flex items-center gap-1 text-sm font-medium ${
                    card.trend && card.trend.includes('+') ? 'text-green-600' : 
                    card.trend && card.trend.includes('-') ? 'text-red-600' : 'text-gray-600'
                  }`}>
                    <span>{card.trend}</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Section Répartition des utilisateurs par rôle */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Répartition des utilisateurs par rôle</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Super Admin */}
          <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
            <div className="flex items-center gap-2 mb-2">
              <Users className="w-5 h-5 text-purple-500" />
              <span className="text-sm font-medium text-gray-600">Super Admin</span>
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {testData.detailed_stats.users_by_role.super_admin}
            </div>
          </div>

          {/* Admin */}
          <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
            <div className="flex items-center gap-2 mb-2">
              <Users className="w-5 h-5 text-blue-500" />
              <span className="text-sm font-medium text-gray-600">Admin</span>
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {testData.detailed_stats.users_by_role.admin}
            </div>
          </div>

          {/* Employés */}
          <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
            <div className="flex items-center gap-2 mb-2">
              <Users className="w-5 h-5 text-green-500" />
              <span className="text-sm font-medium text-gray-600">Employés</span>
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {testData.detailed_stats.users_by_role.employee}
            </div>
          </div>

          {/* Clients */}
          <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
            <div className="flex items-center gap-2 mb-2">
              <Users className="w-5 h-5 text-orange-500" />
              <span className="text-sm font-medium text-gray-600">Clients</span>
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {testData.detailed_stats.users_by_role.client}
            </div>
          </div>
        </div>
      </div>

      {/* Message de test */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <p className="text-blue-800 text-sm">
          <strong>Mode Test:</strong> Interface simplifiée sans graphiques Chart.js pour éviter les erreurs.
        </p>
      </div>
    </div>
  );
};

export default SimpleAnalytics;
