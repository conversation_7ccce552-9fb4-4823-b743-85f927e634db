import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import { Users, Calendar, CheckSquare, Settings } from 'lucide-react';
import biService from '@/services/biService';

// Composant pour afficher une carte de statistique
const StatCard = ({ title, icon, count, percentage, status }) => {
  // Déterminer la couleur du pourcentage
  const getPercentageColor = () => {
    if (percentage > 0) return 'text-green-500';
    if (percentage < 0) return 'text-red-500';
    return 'text-gray-500';
  };

  // Déterminer la couleur de l'icône
  const getIconBgColor = () => {
    switch (title) {
      case 'Utilisateurs actifs':
        return 'bg-blue-100 text-blue-500';
      case 'Événements planifiés':
        return 'bg-green-100 text-green-500';
      case 'Tâches complétées':
        return 'bg-purple-100 text-purple-500';
      case 'Santé du système':
        return 'bg-yellow-100 text-yellow-500';
      default:
        return 'bg-gray-100 text-gray-500';
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
      <div className="flex items-start justify-between">
        <div className={`p-3 rounded-lg ${getIconBgColor()}`}>
          {icon}
        </div>
        {status ? (
          <div className="text-green-500 font-medium">{status}</div>
        ) : (
          <div className={`${getPercentageColor()} font-medium`}>
            {percentage > 0 ? `+${percentage}%` : `${percentage}%`}
          </div>
        )}
      </div>
      <h3 className="mt-4 text-gray-500 font-medium">{title}</h3>
      <div className="mt-1 text-3xl font-bold">
        {status ? `${percentage}%` : count}
      </div>
    </div>
  );
};

const SimpleSuperAdminDashboard = () => {
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    setLoading(true);
    setError(null);

    try {
      // Récupérer les statistiques du tableau de bord
      const statsResult = await biService.getDashboardStats();
      if (!statsResult.success) {
        throw new Error(statsResult.error || 'Erreur lors de la récupération des statistiques');
      }

      setStats(statsResult.data);
    } catch (err) {
      console.error('Erreur lors du chargement des données du tableau de bord:', err);
      setError(err.message || 'Une erreur est survenue lors du chargement des données');
      toast.error(err.message || 'Erreur lors du chargement des données du tableau de bord');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    fetchDashboardData();
    toast.info('Actualisation des données en cours...');
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 p-4 rounded-lg">
        <h3 className="text-lg font-semibold mb-2">Erreur</h3>
        <p>{error}</p>
        <button
          onClick={fetchDashboardData}
          className="mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
        >
          Réessayer
        </button>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Tableau de bord</h2>
        <button
          onClick={handleRefresh}
          className="flex items-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
        >
          Actualiser
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <StatCard
          title="Utilisateurs actifs"
          icon={<Users className="w-5 h-5" />}
          count={stats?.utilisateursActifs?.count || 0}
          percentage={stats?.utilisateursActifs?.percentage || 0}
        />
        <StatCard
          title="Événements planifiés"
          icon={<Calendar className="w-5 h-5" />}
          count={stats?.evenementsPlanifies?.count || 0}
          percentage={stats?.evenementsPlanifies?.percentage || 0}
        />
        <StatCard
          title="Tâches complétées"
          icon={<CheckSquare className="w-5 h-5" />}
          count={stats?.tachesCompletees?.count || 0}
          percentage={stats?.tachesCompletees?.percentage || 0}
        />
        <StatCard
          title="Santé du système"
          icon={<Settings className="w-5 h-5" />}
          percentage={stats?.santeSysteme?.percentage || 0}
          status={stats?.santeSysteme?.status || 'Optimal'}
        />
      </div>

      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 mb-6">
        <h3 className="text-lg font-semibold mb-4">Activité récente</h3>
        <div className="py-4 text-center text-gray-500">
          Aucune activité récente
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <h3 className="text-lg font-semibold mb-4">État du système</h3>
        <div className="py-4 text-center text-gray-500">
          Informations non disponibles
        </div>
      </div>
    </div>
  );
};

export default SimpleSuperAdminDashboard;
