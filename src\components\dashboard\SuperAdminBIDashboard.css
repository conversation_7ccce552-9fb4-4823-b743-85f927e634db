.dashboard-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  font-family: Arial, sans-serif;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.dashboard-header h1 {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin: 0;
}

.refresh-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #6c5ce7;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.refresh-button:hover {
  background-color: #5b4bc9;
}

.refresh-icon {
  width: 16px;
  height: 16px;
}

.dashboard-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #6c5ce7;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  background-color: #ffebee;
  border: 1px solid #ffcdd2;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
  color: #c62828;
}

.retry-button {
  background-color: #c62828;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  margin-top: 8px;
  cursor: pointer;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
  margin-bottom: 24px;
}

@media (max-width: 1024px) {
  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 640px) {
  .metrics-grid {
    grid-template-columns: 1fr;
  }
}

.metric-card {
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.metric-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin-bottom: 16px;
}

.user-icon {
  background-color: #e3f2fd;
  color: #2196f3;
}

.event-icon {
  background-color: #e8f5e9;
  color: #4caf50;
}

.task-icon {
  background-color: #f3e5f5;
  color: #9c27b0;
}

.system-icon {
  background-color: #fff8e1;
  color: #ffc107;
}

.metric-content h3 {
  font-size: 16px;
  color: #757575;
  margin: 0 0 8px 0;
  font-weight: 500;
}

.metric-value {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.metric-trend {
  font-size: 14px;
  font-weight: 500;
}

.positive {
  color: #4caf50;
}

.negative {
  color: #f44336;
}

.metric-status {
  font-size: 14px;
  font-weight: 500;
  color: #4caf50;
}

.detailed-metrics {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.metrics-section {
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.metrics-section h2 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;
  color: #9e9e9e;
  font-size: 14px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.metrics-table {
  width: 100%;
  border-collapse: collapse;
}

.metrics-row {
  display: flex;
  border-bottom: 1px solid #eee;
  padding: 12px 0;
}

.metrics-row:last-child {
  border-bottom: none;
}

.metrics-label {
  flex: 1;
  font-weight: 500;
  color: #616161;
}

.metrics-value {
  flex: 1;
  text-align: right;
  font-size: 16px;
  font-weight: 600;
  color: #2196f3;
}
