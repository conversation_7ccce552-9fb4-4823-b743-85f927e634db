import React, { useEffect, useState } from 'react';
import {
  RefreshCw,
  Users,
  Calendar,
  CheckSquare,
  Bar<PERSON>hart,
  Layers,
  Briefcase,
  User<PERSON>heck,
  UserPlus,
  PieChart,
  Activity,
  TrendingUp
} from 'lucide-react';
import { toast } from 'react-toastify';
import { Chart as ChartJS, ArcElement, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend, Filler } from 'chart.js';
import { Pie, Line } from 'react-chartjs-2';
import biService from '@/services/biService';

// Enregistrer les composants Chart.js
ChartJS.register(
  ArcElement,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

const SuperAdminBIDashboard = () => {
  const [metrics, setMetrics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // États pour les données des graphiques
  const [activeUsersData, setActiveUsersData] = useState(null);
  const [roleUsageData, setRoleUsageData] = useState(null);
  const [activeInactiveData, setActiveInactiveData] = useState(null);
  const [roleDistributionData, setRoleDistributionData] = useState(null);
  const [selectedPeriod, setSelectedPeriod] = useState('30d');

  // Couleurs personnalisables pour les graphiques
  const colors = {
    superAdmin: '#9C27B0',
    admin: '#2196F3',
    employee: '#4CAF50',
    client: '#FFC107',
    active: '#4CAF50',
    inactive: '#F44336'
  };

  const fetchMetrics = async () => {
    if (loading && metrics) return; // Éviter les requêtes multiples si déjà en cours

    setLoading(true);
    setError(null);

    try {
      console.log('SuperAdminBIDashboard - Récupération des métriques...');
      // ✅ CORRECT - Utiliser le bon endpoint
      const response = await biService.getSuperAdminDashboard();

      if (response.success) {
        console.log('SuperAdminBIDashboard - Métriques récupérées avec succès:', response.data);

        // Traiter les données pour s'assurer qu'aucun objet n'est rendu directement
        const processedData = { ...response.data };

        // Fonction pour convertir les objets en valeurs primitives pour le rendu React
        const processNestedObjects = (obj) => {
          // Si ce n'est pas un objet ou c'est null, retourner tel quel
          if (!obj || typeof obj !== 'object') return obj;

          // Si c'est un tableau, traiter chaque élément
          if (Array.isArray(obj)) {
            return obj.map(item => processNestedObjects(item));
          }

          const result = {};

          // Vérifier si l'objet a les clés temporelles spécifiques
          if (obj['24h'] !== undefined ||
            obj['7d'] !== undefined ||
            obj['30d'] !== undefined ||
            obj['90d'] !== undefined) {

            // Extraire les valeurs dans un format sûr pour le rendu
            if (obj['24h'] !== undefined) result.day = obj['24h'];
            if (obj['7d'] !== undefined) result.week = obj['7d'];
            if (obj['30d'] !== undefined) result.month = obj['30d'];
            if (obj['90d'] !== undefined) result.quarter = obj['90d'];

            return result;
          }

          // Traiter récursivement tous les champs de l'objet
          Object.keys(obj).forEach(key => {
            // Si la valeur est un objet, la traiter récursivement
            if (obj[key] && typeof obj[key] === 'object') {
              result[key] = processNestedObjects(obj[key]);
            } else {
              // Sinon, copier la valeur telle quelle
              result[key] = obj[key];
            }
          });

          return result;
        };

        // Traiter les données et les stocker dans l'état
        const processedMetrics = processNestedObjects(processedData);
        console.log('Métriques traitées:', processedMetrics);
        setMetrics(processedMetrics);

        // Récupérer les données pour les graphiques
        await fetchChartData();
      } else {
        // En cas d'erreur, utiliser les données mockées
        console.log('SuperAdminBIDashboard - Utilisation des données mockées:', response.data);
        setMetrics(response.data);

        // Récupérer les données mockées pour les graphiques
        await fetchChartData();

        // Afficher un message d'erreur si disponible
        if (response.error) {
          setError('Erreur lors du chargement des métriques: ' + response.error);
          toast.warning('Utilisation des données de démonstration en raison d\'une erreur de connexion');
        }
      }
    } catch (err) {
      console.error('Erreur lors du chargement des métriques:', err);
      setError('Erreur lors du chargement des métriques: ' + err.message);

      // Ne pas effacer les anciennes métriques en cas d'erreur
      if (!metrics) {
        // Utiliser les données mockées seulement si nous n'avons pas de données
        console.log('Utilisation des données mockées de secours');
        const fallbackResponse = await biService.getSuperAdminDashboard();
        setMetrics(fallbackResponse.data);
        await fetchChartData();
      }
    } finally {
      setLoading(false);
    }
  };

  // Fonction pour récupérer les données des graphiques
  const fetchChartData = async () => {
    try {
      console.log('SuperAdminBIDashboard - Récupération des données des graphiques...');

      // ❌ SUPPRIMÉ - getHistoricalData() utilisait l'ancien endpoint
      // Les données de graphiques sont maintenant dans getSuperAdminDashboard()
      console.log('SuperAdminBIDashboard - Utilisation des données mockées pour les graphiques');

      // ❌ SUPPRIMÉ - getHistoricalData() utilisait l'ancien endpoint
      // Les données de graphiques sont maintenant dans getSuperAdminDashboard()
      console.log('SuperAdminBIDashboard - Utilisation des données mockées pour les graphiques');

      // ❌ SUPPRIMÉ - getHistoricalData() utilisait l'ancien endpoint
      // Les données de distribution des rôles sont maintenant dans getSuperAdminDashboard()
      console.log('SuperAdminBIDashboard - Utilisation des données de distribution des rôles depuis le dashboard principal');

      console.log('SuperAdminBIDashboard - Récupération des données des graphiques terminée');
    } catch (err) {
      console.error('Erreur lors de la récupération des données des graphiques:', err);
      // Ne pas bloquer l'affichage des métriques principales en cas d'erreur avec les graphiques
      toast.warning('Certains graphiques utilisent des données de démonstration');
    }
  };

  // Fonction pour gérer le changement de période
  const handlePeriodChange = (period) => {
    setSelectedPeriod(period);
  };

  useEffect(() => {
    fetchMetrics();

    // Actualiser les données toutes les 60 secondes pour une mise à jour plus fréquente
    const intervalId = setInterval(() => {
      console.log('Actualisation automatique des métriques...');
      fetchMetrics();
    }, 60000); // Augmenté à 60 secondes pour réduire la charge

    // Nettoyer l'intervalle lors du démontage du composant
    return () => clearInterval(intervalId);
  }, []);

  // Récupérer les données des graphiques lorsque la période change
  useEffect(() => {
    if (metrics) {
      fetchChartData();
    }
  }, [selectedPeriod]);

  const handleRefresh = () => {
    fetchMetrics();
    toast.info('Actualisation des données en cours...');
  };

  // Données mockées pour le développement en cas d'erreur avec l'API
  const mockMetrics = {
    user_activity: {
      total_users: 29,
      active_users: {
        day: 12,
        week: 18,
        month: 22,
        quarter: 25
      },
      active_percentage: {
        day: 41.4,
        week: 62.1,
        month: 75.9,
        quarter: 86.2
      },
      users_by_role: {
        super_admin: 3,
        admin: 8,
        employee: 11,
        client: 7
      },
      active_by_role: {
        day: {
          super_admin: 2,
          admin: 5,
          employee: 3,
          client: 2
        },
        week: {
          super_admin: 3,
          admin: 6,
          employee: 6,
          client: 3
        },
        month: {
          super_admin: 3,
          admin: 7,
          employee: 8,
          client: 4
        }
      },
      activity_rate_by_role: {
        super_admin: 100.0,
        admin: 87.5,
        employee: 72.7,
        client: 57.1
      }
    },
    platform_activity: {
      total_teams: 5,
      total_events: 12,
      total_personal_events: 18,
      total_team_tasks: 25,
      total_personal_tasks: 32
    }
  };

  // Données mockées pour les graphiques
  const mockActiveUsersData = {
    labels: ["2023-05-01", "2023-05-02", "2023-05-03", "2023-05-04", "2023-05-05", "2023-05-06", "2023-05-07"],
    datasets: [
      {
        label: "Utilisateurs actifs",
        data: [12, 14, 15, 13, 16, 18, 17],
        borderColor: colors.admin,
        backgroundColor: `${colors.admin}33`,
        fill: true
      }
    ]
  };

  const mockRoleUsageData = {
    labels: ["2023-05-01", "2023-05-02", "2023-05-03", "2023-05-04", "2023-05-05", "2023-05-06", "2023-05-07"],
    datasets: [
      {
        label: "Super Admin",
        data: [100, 100, 100, 100, 100, 100, 100],
        borderColor: colors.superAdmin,
        backgroundColor: `${colors.superAdmin}33`,
        fill: false
      },
      {
        label: "Admin",
        data: [87.5, 87.5, 75, 87.5, 100, 87.5, 87.5],
        borderColor: colors.admin,
        backgroundColor: `${colors.admin}33`,
        fill: false
      },
      {
        label: "Employé",
        data: [72.7, 63.6, 72.7, 81.8, 72.7, 63.6, 72.7],
        borderColor: colors.employee,
        backgroundColor: `${colors.employee}33`,
        fill: false
      },
      {
        label: "Client",
        data: [57.1, 42.9, 57.1, 71.4, 57.1, 42.9, 57.1],
        borderColor: colors.client,
        backgroundColor: `${colors.client}33`,
        fill: false
      }
    ]
  };

  const mockActiveInactiveData = {
    labels: ["Actifs (30 jours)", "Inactifs"],
    datasets: [
      {
        data: [22, 7],
        backgroundColor: [colors.active, colors.inactive]
      }
    ]
  };

  const mockRoleDistributionData = {
    labels: ["Super Admin", "Admin", "Employé", "Client"],
    datasets: [
      {
        data: [3, 8, 11, 7],
        backgroundColor: [colors.superAdmin, colors.admin, colors.employee, colors.client]
      }
    ]
  };

  // Fonction pour vérifier si un objet contient des objets imbriqués qui ne peuvent pas être rendus
  const sanitizeForRendering = (obj) => {
    if (!obj || typeof obj !== 'object') return obj;

    if (Array.isArray(obj)) {
      return obj.map(item => sanitizeForRendering(item));
    }

    const result = {};

    Object.keys(obj).forEach(key => {
      const value = obj[key];

      if (value === null || value === undefined) {
        result[key] = '';
      } else if (typeof value === 'object') {
        // Vérifier si c'est un objet avec des clés temporelles (24h, 7d, 30d, 90d)
        if (value['24h'] !== undefined || value['7d'] !== undefined ||
          value['30d'] !== undefined || value['90d'] !== undefined) {

          // Convertir en format plus facile à utiliser
          result[key] = {
            day: value['24h'] !== undefined ? Number(value['24h']) || 0 : 0,
            week: value['7d'] !== undefined ? Number(value['7d']) || 0 : 0,
            month: value['30d'] !== undefined ? Number(value['30d']) || 0 : 0,
            quarter: value['90d'] !== undefined ? Number(value['90d']) || 0 : 0
          };
        } else if (key === 'periods') {
          // Pour les objets 'periods', extraire les valeurs numériques
          const safeValue = {};
          if (value.day !== undefined) safeValue.day = Number(value.day) || 0;
          if (value.week !== undefined) safeValue.week = Number(value.week) || 0;
          if (value.month !== undefined) safeValue.month = Number(value.month) || 0;
          if (value.quarter !== undefined) safeValue.quarter = Number(value.quarter) || 0;

          // Stocker directement l'objet avec les propriétés converties
          result[key] = safeValue;
        } else if (typeof value === 'object' && !Array.isArray(value)) {
          // Traiter récursivement les autres objets
          result[key] = sanitizeForRendering(value);
        } else {
          // Traiter les tableaux et autres valeurs
          result[key] = sanitizeForRendering(value);
        }
      } else {
        result[key] = value;
      }
    });

    return result;
  };

  // Utiliser les données mockées si l'API échoue, et s'assurer qu'elles sont sûres pour le rendu
  const displayMetrics = metrics || mockMetrics;

  // Utiliser les données mockées pour les graphiques si les données de l'API ne sont pas disponibles
  const displayActiveUsersData = activeUsersData || mockActiveUsersData;
  const displayRoleUsageData = roleUsageData || mockRoleUsageData;
  const displayActiveInactiveData = activeInactiveData || mockActiveInactiveData;
  const displayRoleDistributionData = roleDistributionData || mockRoleDistributionData;

  // Fonction pour obtenir la valeur appropriée en fonction de la période sélectionnée
  const getValueForPeriod = (obj) => {
    if (!obj || typeof obj !== 'object') return obj;

    if (selectedPeriod === '7d' && obj.week !== undefined) {
      return obj.week;
    } else if (selectedPeriod === '30d' && obj.month !== undefined) {
      return obj.month;
    } else if (selectedPeriod === '90d' && obj.quarter !== undefined) {
      return obj.quarter;
    } else if (obj.day !== undefined) {
      return obj.day;
    }

    return 0;
  };

  // Afficher les métriques après traitement pour le débogage
  console.log('Métriques après sanitization pour le rendu:', displayMetrics);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
      </div>
    );
  }



  // Fonction utilitaire pour vérifier si une valeur peut être rendue en toute sécurité
  const safeRender = (value) => {
    if (value === null || value === undefined) return 0;
    if (typeof value === 'number') return value;
    if (typeof value === 'string') return value;

    // Si c'est un objet, essayer d'extraire une valeur numérique
    if (typeof value === 'object' && value !== null) {
      // Vérifier les propriétés temporelles
      if (value.day !== undefined) return value.day;
      if (value.week !== undefined) return value.week;
      if (value.month !== undefined) return value.month;
      if (value.quarter !== undefined) return value.quarter;

      // Retourner 0 par défaut pour les objets
      return 0;
    }

    return value || 0;
  };

  // Options pour les graphiques
  const pieChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
      },
      tooltip: {
        callbacks: {
          label: function (context) {
            const label = context.label || '';
            const value = context.raw || 0;
            return `${label}: ${value}`;
          }
        }
      }
    }
  };

  const lineChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
      }
    },
    scales: {
      y: {
        beginAtZero: true
      }
    }
  };

  const roleUsageChartOptions = {
    ...lineChartOptions,
    scales: {
      ...lineChartOptions.scales,
      y: {
        beginAtZero: true,
        max: 100,
        title: {
          display: true,
          text: 'Taux d\'activité (%)'
        }
      }
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Tableau de bord</h2>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">Période:</span>
            <select
              id="period-selector"
              name="period-selector"
              value={selectedPeriod}
              onChange={(e) => handlePeriodChange(e.target.value)}
              className="px-2 py-1 border border-gray-300 rounded-md text-sm"
            >
              <option value="7d">7 jours</option>
              <option value="30d">30 jours</option>
              <option value="90d">90 jours</option>
              <option value="365d">1 an</option>
            </select>
          </div>
          <button
            onClick={handleRefresh}
            className="flex items-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            <RefreshCw className="w-4 h-4" />
            Actualiser
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 p-4 rounded-lg mb-6">
          <h3 className="text-lg font-semibold mb-2">Erreur</h3>
          <p>{error}</p>
          <button
            onClick={fetchMetrics}
            className="mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            Réessayer
          </button>
        </div>
      )}

      {/* Section 1: Activité des utilisateurs */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 mb-6">
        <h3 className="text-lg font-semibold mb-4">Activité des utilisateurs</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center gap-3 mb-2">
              <div className="p-2 rounded-lg bg-blue-100 text-blue-600">
                <Users className="w-5 h-5" />
              </div>
              <h4 className="font-medium">Total utilisateurs</h4>
            </div>
            <div className="text-2xl font-bold">{safeRender(displayMetrics?.user_activity?.total_users)}</div>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center gap-3 mb-2">
              <div className="p-2 rounded-lg bg-green-100 text-green-600">
                <UserCheck className="w-5 h-5" />
              </div>
              <h4 className="font-medium">Utilisateurs actifs</h4>
            </div>
            <div className="text-2xl font-bold">
              {getValueForPeriod(displayMetrics?.user_activity?.active_users)}
            </div>
            <div className="text-sm text-gray-500 mt-1">
              {getValueForPeriod(displayMetrics?.user_activity?.active_percentage)}% du total
            </div>
          </div>
        </div>
      </div>

      {/* Section 2: Répartition des utilisateurs par rôle */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 mb-6">
        <h3 className="text-lg font-semibold mb-4">Répartition des utilisateurs par rôle</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center gap-3 mb-2">
              <div className="p-2 rounded-lg bg-purple-100 text-purple-600">
                <UserPlus className="w-5 h-5" />
              </div>
              <h4 className="font-medium">Super Admin</h4>
            </div>
            <div className="text-2xl font-bold">{safeRender(displayMetrics?.user_activity?.users_by_role?.super_admin)}</div>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center gap-3 mb-2">
              <div className="p-2 rounded-lg bg-blue-100 text-blue-600">
                <UserPlus className="w-5 h-5" />
              </div>
              <h4 className="font-medium">Admin</h4>
            </div>
            <div className="text-2xl font-bold">{safeRender(displayMetrics?.user_activity?.users_by_role?.admin)}</div>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center gap-3 mb-2">
              <div className="p-2 rounded-lg bg-green-100 text-green-600">
                <UserPlus className="w-5 h-5" />
              </div>
              <h4 className="font-medium">Employé</h4>
            </div>
            <div className="text-2xl font-bold">{safeRender(displayMetrics?.user_activity?.users_by_role?.employee)}</div>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center gap-3 mb-2">
              <div className="p-2 rounded-lg bg-yellow-100 text-yellow-600">
                <UserPlus className="w-5 h-5" />
              </div>
              <h4 className="font-medium">Client</h4>
            </div>
            <div className="text-2xl font-bold">{safeRender(displayMetrics?.user_activity?.users_by_role?.client)}</div>
          </div>
        </div>
      </div>

      {/* Section 3: Activité de la plateforme */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 mb-6">
        <h3 className="text-lg font-semibold mb-4">Activité de la plateforme</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center gap-3 mb-2">
              <div className="p-2 rounded-lg bg-blue-100 text-blue-600">
                <Layers className="w-5 h-5" />
              </div>
              <h4 className="font-medium">Équipes</h4>
            </div>
            <div className="text-2xl font-bold">{safeRender(displayMetrics?.platform_activity?.total_teams)}</div>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center gap-3 mb-2">
              <div className="p-2 rounded-lg bg-purple-100 text-purple-600">
                <Calendar className="w-5 h-5" />
              </div>
              <h4 className="font-medium">Événements</h4>
            </div>
            <div className="text-2xl font-bold">{safeRender(displayMetrics?.platform_activity?.total_events)}</div>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center gap-3 mb-2">
              <div className="p-2 rounded-lg bg-green-100 text-green-600">
                <CheckSquare className="w-5 h-5" />
              </div>
              <h4 className="font-medium">Tâches d'équipe</h4>
            </div>
            <div className="text-2xl font-bold">{safeRender(displayMetrics?.platform_activity?.total_team_tasks)}</div>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center gap-3 mb-2">
              <div className="p-2 rounded-lg bg-indigo-100 text-indigo-600">
                <Calendar className="w-5 h-5" />
              </div>
              <h4 className="font-medium">Événements personnels</h4>
            </div>
            <div className="text-2xl font-bold">{safeRender(displayMetrics?.platform_activity?.total_personal_events)}</div>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center gap-3 mb-2">
              <div className="p-2 rounded-lg bg-amber-100 text-amber-600">
                <Briefcase className="w-5 h-5" />
              </div>
              <h4 className="font-medium">Tâches personnelles</h4>
            </div>
            <div className="text-2xl font-bold">{safeRender(displayMetrics?.platform_activity?.total_personal_tasks)}</div>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center gap-3 mb-2">
              <div className="p-2 rounded-lg bg-rose-100 text-rose-600">
                <PieChart className="w-5 h-5" />
              </div>
              <h4 className="font-medium">Statistiques</h4>
            </div>
            <div className="text-2xl font-bold">
              {safeRender(
                Number(displayMetrics?.platform_activity?.total_events || 0) +
                Number(displayMetrics?.platform_activity?.total_personal_events || 0) +
                Number(displayMetrics?.platform_activity?.total_team_tasks || 0) +
                Number(displayMetrics?.platform_activity?.total_personal_tasks || 0)
              )}
            </div>
            <div className="text-sm text-gray-500 mt-1">Total des activités</div>
          </div>
        </div>
      </div>

      {/* Section 4: Graphiques */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        {/* Graphique circulaire des utilisateurs actifs/inactifs */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold mb-4">Utilisateurs actifs vs inactifs</h3>
          <div className="h-64">
            {displayActiveInactiveData && (
              <Pie data={displayActiveInactiveData} options={pieChartOptions} />
            )}
          </div>
        </div>

        {/* Graphique circulaire de la distribution des rôles */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold mb-4">Distribution des rôles</h3>
          <div className="h-64">
            {displayRoleDistributionData && (
              <Pie data={displayRoleDistributionData} options={pieChartOptions} />
            )}
          </div>
        </div>
      </div>

      {/* Section 5: Graphiques de tendance */}
      <div className="grid grid-cols-1 gap-6 mb-6">
        {/* Graphique de tendance des utilisateurs actifs */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold mb-4">Tendance des utilisateurs actifs</h3>
          <div className="h-80">
            {displayActiveUsersData && (
              <Line data={displayActiveUsersData} options={lineChartOptions} />
            )}
          </div>
        </div>

        {/* Graphique de taux d'utilisation par rôle */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold mb-4">Taux d'utilisation par rôle</h3>
          <div className="h-80">
            {displayRoleUsageData && (
              <Line data={displayRoleUsageData} options={roleUsageChartOptions} />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SuperAdminBIDashboard;
