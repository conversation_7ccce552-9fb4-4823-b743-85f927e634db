import React, { useState, useEffect } from 'react';
import { Users, UserCheck, UserX, Shield, RefreshCw } from 'lucide-react';
import { Doughn<PERSON>, Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { showSuccessToast, showErrorToast, showWarningToast } from '../../utils/toastUtils';
import biService from '@/services/biService';

// Enregistrer les composants Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const SuperAdminBIDashboardFinal = () => {
  const [metrics, setMetrics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);
  const [currentPeriod, setCurrentPeriod] = useState('today');

  // Périodes disponibles selon la documentation backend
  const availablePeriods = [
    { value: 'today', label: 'Aujourd\'hui', color: '#10B981' },
    { value: '1h', label: '1h', color: '#3B82F6' },
    { value: '24h', label: '24h', color: '#8B5CF6' },
    { value: '7d', label: '7j', color: '#F59E0B' },
    { value: '30d', label: '30j', color: '#EF4444' }
  ];

  // Fonction pour récupérer les métriques avec support des filtres de période
  const fetchMetrics = async (period = 'today', manualRefresh = true) => {
    try {
      setLoading(true);
      setError(null);

      console.log(`SuperAdminBIDashboard - Récupération des métriques (période: ${period}, manuel: ${manualRefresh})...`);
      const response = await biService.getSuperAdminDashboard(period, manualRefresh);

      if (response.success) {
        console.log('SuperAdminBIDashboard - Métriques récupérées avec succès:', response.data);
        setMetrics(response.data);
        setLastUpdated(new Date());

        // Afficher le message de succès seulement pour les refresh manuels
        if (manualRefresh && response.data.is_realtime) {
          showSuccessToast('Données en temps réel chargées avec succès');
        } else if (manualRefresh) {
          showSuccessToast('Données mises à jour avec succès');
        }
      } else {
        console.log('SuperAdminBIDashboard - Utilisation des données mockées:', response.data);
        setMetrics(response.data);
        setLastUpdated(new Date());

        if (response.error) {
          setError('Erreur lors du chargement des métriques: ' + response.error);
          if (manualRefresh) {
            showWarningToast('Utilisation des données de démonstration en raison d\'une erreur de connexion');
          }
        }
      }
    } catch (err) {
      console.error('Erreur lors du chargement des métriques:', err);
      setError('Erreur lors du chargement des métriques: ' + err.message);

      // Utiliser les données mockées en cas d'erreur
      if (!metrics) {
        console.log('Utilisation des données mockées de secours');
        try {
          const fallbackResponse = await biService.getSuperAdminDashboard(period, manualRefresh);
          if (fallbackResponse.data) {
            setMetrics(fallbackResponse.data);
            setLastUpdated(new Date());
          }
        } catch (fallbackError) {
          console.error('Erreur lors du chargement des données de secours:', fallbackError);
        }
      }

      if (manualRefresh) {
        showErrorToast('Erreur lors du chargement des données');
      }
    } finally {
      setLoading(false);
    }
  };

  // Fonction pour changer de période
  const handlePeriodChange = (newPeriod) => {
    if (newPeriod !== currentPeriod) {
      console.log(`Changement de période: ${currentPeriod} -> ${newPeriod}`);
      setCurrentPeriod(newPeriod);
      // Récupérer les données pour la nouvelle période
      fetchMetrics(newPeriod, true);
    }
  };

  // Fonction pour rafraîchir manuellement
  const handleRefresh = () => {
    console.log('Rafraîchissement manuel des données');
    fetchMetrics(currentPeriod, true);
  };

  useEffect(() => {
    // Chargement initial avec la période par défaut
    console.log(`Chargement initial avec la période: ${currentPeriod}`);
    fetchMetrics(currentPeriod, true);
  }, []); // Dépendance vide pour ne s'exécuter qu'une fois

  useEffect(() => {
    // Auto-refresh seulement pour la période "today" toutes les 60 secondes (1 minute)
    let interval = null;
    if (currentPeriod === 'today') {
      console.log('Activation de l\'auto-refresh pour la période "today"');
      interval = setInterval(() => {
        console.log('Actualisation automatique des métriques (today)...');
        fetchMetrics('today', false); // false = pas manuel pour l'auto-refresh
      }, 60000); // 60 secondes au lieu de 30
    } else {
      console.log(`Pas d'auto-refresh pour la période "${currentPeriod}"`);
    }

    return () => {
      if (interval) {
        console.log('Nettoyage de l\'auto-refresh');
        clearInterval(interval);
      }
    };
  }, [currentPeriod]);

  // Couleurs exactes de la maquette
  const colors = {
    turquoise: '#17A2B8',  // Couleur turquoise principale des graphiques
    gray: '#6C757D',       // Gris pour les données inactives
    blue: '#3B82F6',       // Bleu pour les icônes
    green: '#10B981',      // Vert pour les icônes
    red: '#EF4444'         // Rouge pour les icônes
  };

  // Extraction des données selon la nouvelle structure API
  const getMetricData = () => {
    if (metrics?.metric_cards) {
      // Utiliser les données des metric_cards de l'API
      const totalUsersCard = metrics.metric_cards.find(card => card.title.includes('total'));
      const activeUsersCard = metrics.metric_cards.find(card => card.title.includes('actifs'));
      const inactiveUsersCard = metrics.metric_cards.find(card => card.title.includes('inactifs'));

      return {
        totalUsers: totalUsersCard?.value || 0,
        activeUsers: activeUsersCard?.value || 0,
        inactiveUsers: inactiveUsersCard?.value || 0,
        totalUsersTrend: totalUsersCard?.trend || '+0%',
        activeUsersTrend: activeUsersCard?.trend || '+0%',
        inactiveUsersTrend: inactiveUsersCard?.trend || '0%'
      };
    }

    // Fallback vers l'ancienne structure
    const totalUsers = metrics?.detailed_stats?.activity_stats?.total_users || 0;
    const activeUsers = metrics?.detailed_stats?.engagement_metrics?.users_logged_today || 0;
    const inactiveUsers = totalUsers - activeUsers;

    return {
      totalUsers,
      activeUsers,
      inactiveUsers,
      totalUsersTrend: metrics?.detailed_stats?.trends?.total_users_trend || '+0%',
      activeUsersTrend: metrics?.detailed_stats?.trends?.active_users_trend || '+0%',
      inactiveUsersTrend: metrics?.detailed_stats?.trends?.inactive_users_trend || '0%'
    };
  };

  const metricData = getMetricData();

  // Extraction des données des graphiques selon la nouvelle structure API
  const getChartData = () => {
    if (metrics?.charts) {
      // Utiliser les données des charts de l'API
      const activeVsInactive = metrics.charts.active_vs_inactive;
      const roleDistribution = metrics.charts.role_distribution;

      const doughnutData = activeVsInactive ? {
        labels: activeVsInactive.data.map(item => item.name),
        datasets: [{
          data: activeVsInactive.data.map(item => item.value),
          backgroundColor: activeVsInactive.data.map(item => item.color),
          borderWidth: 0,
          cutout: '65%'
        }]
      } : {
        labels: ['Connectés', 'Non connectés'],
        datasets: [{
          data: [metricData.activeUsers, metricData.inactiveUsers],
          backgroundColor: [colors.turquoise, colors.gray],
          borderWidth: 0,
          cutout: '65%'
        }]
      };

      const barData = roleDistribution ? {
        labels: roleDistribution.data.map(item => item.name),
        datasets: [{
          data: roleDistribution.data.map(item => item.value),
          backgroundColor: roleDistribution.data.map(item => item.color),
          borderRadius: 4,
          barThickness: 60
        }]
      } : {
        labels: ['Super Admin', 'Admin', 'Employés', 'Clients'],
        datasets: [{
          data: [
            metrics?.detailed_stats?.users_by_role?.super_admin || 0,
            metrics?.detailed_stats?.users_by_role?.admin || 0,
            metrics?.detailed_stats?.users_by_role?.employee || 0,
            metrics?.detailed_stats?.users_by_role?.client || 0
          ],
          backgroundColor: colors.turquoise,
          borderRadius: 4,
          barThickness: 60
        }]
      };

      return { doughnutData, barData, activeVsInactive, roleDistribution };
    }

    // Fallback vers l'ancienne structure
    const doughnutData = {
      labels: ['Connectés', 'Non connectés'],
      datasets: [{
        data: [metricData.activeUsers, metricData.inactiveUsers],
        backgroundColor: [colors.turquoise, colors.gray],
        borderWidth: 0,
        cutout: '65%'
      }]
    };

    const barData = {
      labels: ['Super Admin', 'Admin', 'Employés', 'Clients'],
      datasets: [{
        data: [
          metrics?.detailed_stats?.users_by_role?.super_admin || 0,
          metrics?.detailed_stats?.users_by_role?.admin || 0,
          metrics?.detailed_stats?.users_by_role?.employee || 0,
          metrics?.detailed_stats?.users_by_role?.client || 0
        ],
        backgroundColor: colors.turquoise,
        borderRadius: 4,
        barThickness: 60
      }]
    };

    return { doughnutData, barData, activeVsInactive: null, roleDistribution: null };
  };

  const { doughnutData, barData, activeVsInactive, roleDistribution } = getChartData();

  // Options pour le graphique Doughnut
  const doughnutOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          usePointStyle: true,
          padding: 20,
          font: {
            size: 14
          },
          generateLabels: function (chart) {
            const data = chart.data;
            if (data.labels.length && data.datasets.length) {
              return data.labels.map((label, i) => {
                const dataset = data.datasets[0];
                const value = dataset.data[i];
                return {
                  text: `${label}`,
                  fillStyle: dataset.backgroundColor[i],
                  strokeStyle: dataset.backgroundColor[i],
                  lineWidth: 0,
                  pointStyle: 'circle'
                };
              });
            }
            return [];
          }
        }
      },
      tooltip: {
        callbacks: {
          label: function (context) {
            const total = context.dataset.data.reduce((a, b) => a + b, 0);
            const percentage = total > 0 ? ((context.parsed * 100) / total).toFixed(1) : 0;
            return `${context.label}: ${context.parsed} (${percentage}%)`;
          }
        }
      }
    }
  };

  // Options pour le graphique en barres
  const barOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        callbacks: {
          label: function (context) {
            return `${context.label}: ${context.parsed.y} utilisateurs`;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        max: 20, // Échelle adaptée aux vraies valeurs (2, 7, 15, 5)
        ticks: {
          stepSize: 5, // Étapes de 5 en 5
          font: {
            size: 12
          }
        },
        grid: {
          color: '#E5E7EB'
        }
      },
      x: {
        grid: {
          display: false
        },
        ticks: {
          font: {
            size: 12
          }
        }
      }
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="flex items-center space-x-2">
          <RefreshCw className="w-6 h-6 animate-spin text-blue-500" />
          <span className="text-lg text-gray-600">Chargement des données...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      {/* En-tête avec filtres de période et bouton refresh */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="bg-blue-500 p-3 rounded-lg">
              <Shield className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {metrics?.metadata?.dashboard_title || 'Tableau de Bord Super Admin'}
              </h1>
              <p className="text-gray-600 text-sm">
                {metrics?.metadata?.dashboard_subtitle || 'Vue d\'ensemble des utilisateurs et analyses'}
              </p>
            </div>
          </div>

          <button
            onClick={handleRefresh}
            disabled={loading}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 transition-colors"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            <span>Actualiser</span>
          </button>
        </div>

        {/* Boutons de filtrage par période */}
        <div className="flex space-x-2 mb-4">
          {availablePeriods.map(period => (
            <button
              key={period.value}
              onClick={() => handlePeriodChange(period.value)}
              disabled={loading}
              className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${currentPeriod === period.value
                ? 'text-white shadow-lg'
                : 'bg-white text-gray-700 border border-gray-200 hover:bg-gray-50'
                }`}
              style={{
                backgroundColor: currentPeriod === period.value ? period.color : undefined,
                borderColor: currentPeriod === period.value ? period.color : undefined
              }}
            >
              {period.label}
              {period.value === 'today' && currentPeriod === 'today' && (
                <span className="ml-2 inline-block w-2 h-2 bg-white rounded-full animate-pulse"></span>
              )}
            </button>
          ))}
        </div>
      </div>

      {/* Cartes de métriques - Design exact de la maquette */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {/* Cartes de métriques dynamiques selon l'API */}
        {metrics?.metric_cards ? (
          // Utiliser les données des metric_cards de l'API
          metrics.metric_cards.map((card, index) => (
            <div key={index} className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-gray-600 text-sm mb-2">{card.title}</p>
                  {card.subtitle && (
                    <p className="text-gray-500 text-xs mb-1">{card.subtitle}</p>
                  )}
                  <p className="text-4xl font-bold text-gray-900 mb-2">
                    {typeof card.value === 'number' ? card.value.toLocaleString() : card.value}
                  </p>
                  <p className="text-sm font-medium" style={{ color: card.color }}>
                    {card.trend} {card.trend_period}
                    {card.period === 'today' && currentPeriod === 'today' && (
                      <span className="ml-2">
                        <span className="inline-block w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
                        <span className="ml-1 text-green-500">En temps réel</span>
                      </span>
                    )}
                  </p>
                </div>
                <div className="p-4 rounded-lg" style={{ backgroundColor: `${card.color}20` }}>
                  {card.icon === 'users' && <Users className="w-8 h-8" style={{ color: card.color }} />}
                  {card.icon === 'user-check' && <UserCheck className="w-8 h-8" style={{ color: card.color }} />}
                  {card.icon === 'user-x' && <UserX className="w-8 h-8" style={{ color: card.color }} />}
                </div>
              </div>
            </div>
          ))
        ) : (
          // Fallback vers l'affichage statique
          <>
            {/* Nombre total d'utilisateurs */}
            <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-gray-600 text-sm mb-2">Nombre total d'utilisateurs</p>
                  <p className="text-4xl font-bold text-gray-900 mb-2">{metricData.totalUsers.toLocaleString()}</p>
                  <p className="text-green-500 text-sm font-medium">
                    {metricData.totalUsersTrend} ce mois
                  </p>
                </div>
                <div className="bg-blue-100 p-4 rounded-lg">
                  <Users className="w-8 h-8 text-blue-500" />
                </div>
              </div>
            </div>

            {/* Utilisateurs actifs */}
            <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-gray-600 text-sm mb-2">Utilisateurs actifs</p>
                  <p className="text-4xl font-bold text-gray-900 mb-2">{metricData.activeUsers.toLocaleString()}</p>
                  <p className="text-green-500 text-sm font-medium">
                    {currentPeriod === 'today' ? 'Connectés aujourd\'hui' : `Période: ${currentPeriod}`}
                    {currentPeriod === 'today' && (
                      <span className="ml-2">
                        <span className="inline-block w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
                        <span className="ml-1">En temps réel</span>
                      </span>
                    )}
                  </p>
                </div>
                <div className="bg-green-100 p-4 rounded-lg">
                  <UserCheck className="w-8 h-8 text-green-500" />
                </div>
              </div>
            </div>

            {/* Utilisateurs inactifs */}
            <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-gray-600 text-sm mb-2">Utilisateurs inactifs</p>
                  <p className="text-4xl font-bold text-gray-900 mb-2">{metricData.inactiveUsers.toLocaleString()}</p>
                  <p className="text-red-500 text-sm font-medium">
                    {metricData.inactiveUsersTrend} ce mois
                  </p>
                </div>
                <div className="bg-red-100 p-4 rounded-lg">
                  <UserX className="w-8 h-8 text-red-500" />
                </div>
              </div>
            </div>
          </>
        )}
      </div>

      {/* Graphiques - Layout exact de la maquette */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Graphique Doughnut - Utilisateurs Actifs vs Inactifs */}
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
          <div className="flex items-center space-x-2 mb-6">
            <div className={`w-3 h-3 rounded-full ${currentPeriod === 'today' ? 'bg-green-500 animate-pulse' : 'bg-blue-500'}`}></div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {activeVsInactive?.title || 'Utilisateurs Actifs vs Inactifs'}
              </h3>
              {activeVsInactive?.subtitle && (
                <p className="text-sm text-gray-500">{activeVsInactive.subtitle}</p>
              )}
              {activeVsInactive?.period_name && (
                <p className="text-xs text-gray-400">Période: {activeVsInactive.period_name}</p>
              )}
            </div>
          </div>
          <div className="h-80">
            <Doughnut data={doughnutData} options={doughnutOptions} />
          </div>
        </div>

        {/* Graphique en Barres - Distribution par Rôle */}
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
          <div className="flex items-center space-x-2 mb-6">
            <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {roleDistribution?.title || 'Distribution des Utilisateurs par Rôle'}
              </h3>
              {roleDistribution?.subtitle && (
                <p className="text-sm text-gray-500">{roleDistribution.subtitle}</p>
              )}
            </div>
          </div>
          <div className="h-96">
            <Bar data={barData} options={barOptions} />
          </div>
        </div>
      </div>

      {/* Footer avec informations détaillées */}
      <div className="mt-8 flex justify-between items-center text-sm text-gray-500">
        <div>
          {lastUpdated && (
            <>
              Dernière mise à jour: {lastUpdated.toLocaleTimeString('fr-FR')}
              {metrics?.metadata?.refresh_mode === 'manual' && (
                <span className="ml-2">• Mode manuel</span>
              )}
              {metrics?.is_realtime && (
                <span className="ml-2 text-green-500">• Données en temps réel</span>
              )}
              {currentPeriod === 'today' && (
                <span className="ml-2 text-green-500">• DailyLoginTracker</span>
              )}
              {currentPeriod !== 'today' && (
                <span className="ml-2 text-orange-500">• Approximation (User.last_login)</span>
              )}
            </>
          )}
        </div>
        <div className="flex items-center space-x-4">
          <span>
            Source: {metrics?.metadata?.data_source || 'API Backend'}
          </span>
          <span>
            Période: {availablePeriods.find(p => p.value === currentPeriod)?.label || currentPeriod}
          </span>
        </div>
      </div>

      {/* Message d'erreur */}
      {error && (
        <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}
    </div>
  );
};

export default SuperAdminBIDashboardFinal;
