import React, { useState, useEffect } from 'react';
import { Users, UserCheck, UserX, Shield, RefreshCw } from 'lucide-react';
import { <PERSON>hn<PERSON>, Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { showSuccessToast, showErrorToast, showWarningToast } from '../../utils/toastUtils';
import biService from '@/services/biService';

// Enregistrer les composants Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const SuperAdminBIDashboardNew = () => {
  const [metrics, setMetrics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);

  // Fonction pour récupérer les métriques
  const fetchMetrics = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('SuperAdminBIDashboard - Récupération des métriques...');
      const response = await biService.getSuperAdminDashboard();

      if (response.success) {
        console.log('SuperAdminBIDashboard - Métriques récupérées avec succès:', response.data);
        setMetrics(response.data);
        setLastUpdated(new Date());

        if (response.data.is_realtime) {
          showSuccessToast('Données en temps réel chargées avec succès');
        }
      } else {
        console.log('SuperAdminBIDashboard - Utilisation des données mockées:', response.data);
        setMetrics(response.data);
        setLastUpdated(new Date());

        if (response.error) {
          setError('Erreur lors du chargement des métriques: ' + response.error);
          showWarningToast('Utilisation des données de démonstration en raison d\'une erreur de connexion');
        }
      }
    } catch (err) {
      console.error('Erreur lors du chargement des métriques:', err);
      setError('Erreur lors du chargement des métriques: ' + err.message);

      // Utiliser les données mockées en cas d'erreur
      if (!metrics) {
        console.log('Utilisation des données mockées de secours');
        const fallbackResponse = await biService.getSuperAdminDashboard();
        setMetrics(fallbackResponse.data);
        setLastUpdated(new Date());
      }

      showErrorToast('Erreur lors du chargement des données');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMetrics();

    // Auto-refresh toutes les 30 secondes
    const interval = setInterval(() => {
      console.log('Actualisation automatique des métriques...');
      fetchMetrics();
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  // Configuration des couleurs exactes de la maquette
  const colors = {
    primary: '#3B82F6',    // Bleu pour total utilisateurs
    success: '#10B981',    // Vert pour utilisateurs actifs
    danger: '#EF4444',     // Rouge pour utilisateurs inactifs
    cyan: '#17A2B8',       // Cyan turquoise pour les graphiques (comme dans la maquette)
    gray: '#6C757D'        // Gris pour les inactifs
  };

  // Données pour le graphique Doughnut (Actifs vs Inactifs)
  const doughnutData = {
    labels: ['Actifs', 'Inactifs'],
    datasets: [
      {
        data: metrics ? [
          metrics.detailed_stats?.activity_stats?.active_users?.last_30_days || 0,
          metrics.detailed_stats?.activity_stats?.inactive_users || 0
        ] : [0, 0],
        backgroundColor: [colors.cyan, colors.gray],
        borderWidth: 0,
        cutout: '65%'
      }
    ]
  };

  // Données pour le graphique en barres (Distribution par rôle)
  const barData = {
    labels: ['Super Admin', 'Admin', 'Employés', 'Clients'],
    datasets: [
      {
        data: metrics ? [
          metrics.detailed_stats?.users_by_role?.super_admin || 0,
          metrics.detailed_stats?.users_by_role?.admin || 0,
          metrics.detailed_stats?.users_by_role?.employee || 0,
          metrics.detailed_stats?.users_by_role?.client || 0
        ] : [0, 0, 0, 0],
        backgroundColor: colors.cyan,
        borderRadius: 4,
        barThickness: 40
      }
    ]
  };

  // Options pour le graphique Doughnut
  const doughnutOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          usePointStyle: true,
          padding: 20,
          font: {
            size: 12
          }
        }
      },
      tooltip: {
        callbacks: {
          label: function (context) {
            const total = context.dataset.data.reduce((a, b) => a + b, 0);
            const percentage = ((context.parsed * 100) / total).toFixed(1);
            return `${context.label}: ${context.parsed} (${percentage}%)`;
          }
        }
      }
    }
  };

  // Options pour le graphique en barres
  const barOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        callbacks: {
          label: function (context) {
            return `${context.label}: ${context.parsed.y} utilisateurs`;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        max: 20, // Échelle adaptée aux vraies valeurs
        ticks: {
          stepSize: 5, // Étapes de 5 en 5
        },
        grid: {
          color: '#F3F4F6'
        }
      },
      x: {
        grid: {
          display: false
        }
      }
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex items-center space-x-2">
          <RefreshCw className="w-6 h-6 animate-spin text-blue-500" />
          <span className="text-lg text-gray-600">Chargement des données...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      {/* En-tête */}
      <div className="mb-8">
        <div className="flex items-center space-x-3 mb-2">
          <div className="bg-blue-500 p-2 rounded-lg">
            <Shield className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Tableau de Bord Super Admin</h1>
            <p className="text-gray-600">Vue d'ensemble des utilisateurs et analyses</p>
          </div>
        </div>

        {/* Bouton de rafraîchissement */}
        <div className="flex justify-end">
          <button
            onClick={fetchMetrics}
            disabled={loading}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            <span>Actualiser</span>
          </button>
        </div>
      </div>

      {/* Cartes de métriques */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {/* Nombre total d'utilisateurs */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm mb-1">Nombre total d'utilisateurs</p>
              <p className="text-3xl font-bold text-gray-900">
                {metrics?.detailed_stats?.activity_stats?.total_users || 0}
              </p>
              <p className="text-green-500 text-sm mt-1">
                +{metrics?.detailed_stats?.trends?.total_users_trend || '0%'} ce mois
              </p>
            </div>
            <div className="bg-blue-100 p-3 rounded-lg">
              <Users className="w-6 h-6 text-blue-500" />
            </div>
          </div>
        </div>

        {/* Utilisateurs actifs */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm mb-1">Utilisateurs actifs</p>
              <p className="text-3xl font-bold text-gray-900">
                {metrics?.detailed_stats?.activity_stats?.active_users?.last_30_days || 0}
              </p>
              <p className="text-green-500 text-sm mt-1">
                +{metrics?.detailed_stats?.trends?.active_users_trend || '0%'} cette semaine
              </p>
            </div>
            <div className="bg-green-100 p-3 rounded-lg">
              <UserCheck className="w-6 h-6 text-green-500" />
            </div>
          </div>
        </div>

        {/* Utilisateurs inactifs */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm mb-1">Utilisateurs inactifs</p>
              <p className="text-3xl font-bold text-gray-900">
                {metrics?.detailed_stats?.activity_stats?.inactive_users || 0}
              </p>
              <p className="text-red-500 text-sm mt-1">
                {metrics?.detailed_stats?.trends?.inactive_users_trend || '0%'} ce mois
              </p>
            </div>
            <div className="bg-red-100 p-3 rounded-lg">
              <UserX className="w-6 h-6 text-red-500" />
            </div>
          </div>
        </div>
      </div>

      {/* Graphiques */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Graphique Doughnut - Utilisateurs Actifs vs Inactifs */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center space-x-2 mb-4">
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <h3 className="text-lg font-semibold text-gray-900">Utilisateurs Actifs vs Inactifs</h3>
          </div>
          <div className="h-80">
            <Doughnut data={doughnutData} options={doughnutOptions} />
          </div>
        </div>

        {/* Graphique en Barres - Distribution par Rôle */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center space-x-2 mb-4">
            <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
            <h3 className="text-lg font-semibold text-gray-900">Distribution des Utilisateurs par Rôle</h3>
          </div>
          <div className="h-96">
            <Bar data={barData} options={barOptions} />
          </div>
        </div>
      </div>

      {/* Informations de mise à jour */}
      {lastUpdated && (
        <div className="mt-6 text-center text-sm text-gray-500">
          Dernière mise à jour: {lastUpdated.toLocaleTimeString('fr-FR')}
          {metrics?.is_realtime && (
            <span className="ml-2 text-green-500">• Données en temps réel</span>
          )}
        </div>
      )}

      {/* Message d'erreur */}
      {error && (
        <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}
    </div>
  );
};

export default SuperAdminBIDashboardNew;
