import React, { useState, useEffect, useCallback } from 'react';
import { RefreshCw, Shield, Users, UserCheck, UserX } from 'lucide-react';
import { toast } from 'react-toastify';
import { <PERSON>hn<PERSON>, Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
} from 'chart.js';
import biService from '@/services/biService';

// Enregistrer les composants Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const SuperAdminBIDashboardWithFilters = () => {
  const [dashboardData, setDashboardData] = useState(null);
  const [currentPeriod, setCurrentPeriod] = useState('today');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);

  // Périodes disponibles selon votre documentation
  const availablePeriods = [
    { value: 'today', label: 'Aujourd\'hui', color: '#10B981' },
    { value: '1h', label: '1h', color: '#3B82F6' },
    { value: '24h', label: '24h', color: '#8B5CF6' },
    { value: '7d', label: '7j', color: '#F59E0B' },
    { value: '30d', label: '30j', color: '#EF4444' }
  ];

  // Fonction pour récupérer les données du dashboard
  const fetchDashboardData = useCallback(async (period = currentPeriod, manualRefresh = true) => {
    setIsLoading(true);
    setError(null);

    try {
      console.log(`Récupération des données pour la période: ${period}`);
      const response = await biService.getSuperAdminDashboard(period, manualRefresh);
      
      if (response.success && response.data) {
        setDashboardData(response.data);
        setCurrentPeriod(period);
        setLastUpdated(new Date());
        console.log('Données du dashboard récupérées:', response.data);
      } else {
        throw new Error(response.error || 'Erreur lors de la récupération des données');
      }
    } catch (err) {
      console.error('Erreur lors du chargement des données:', err);
      setError(err.message || 'Une erreur est survenue lors du chargement des données');
      toast.error(err.message || 'Erreur lors du chargement des données du tableau de bord');
    } finally {
      setIsLoading(false);
    }
  }, [currentPeriod]);

  // Fonction pour rafraîchir manuellement
  const handleRefresh = useCallback(() => {
    fetchDashboardData(currentPeriod, true);
    toast.info('Actualisation des données en cours...');
  }, [fetchDashboardData, currentPeriod]);

  // Fonction pour changer de période
  const handlePeriodChange = useCallback((newPeriod) => {
    if (newPeriod !== currentPeriod) {
      fetchDashboardData(newPeriod, true);
    }
  }, [fetchDashboardData, currentPeriod]);

  // Chargement initial
  useEffect(() => {
    fetchDashboardData('today', true);
  }, []);

  // Extraction des données pour les graphiques
  const getChartData = () => {
    if (!dashboardData?.charts) return { doughnutData: null, barData: null };

    // Données pour le graphique Doughnut
    const activeVsInactive = dashboardData.charts.active_vs_inactive;
    const doughnutData = activeVsInactive ? {
      labels: activeVsInactive.data.map(item => item.name),
      datasets: [{
        data: activeVsInactive.data.map(item => item.value),
        backgroundColor: activeVsInactive.data.map(item => item.color),
        borderWidth: 0,
        cutout: '65%'
      }]
    } : null;

    // Données pour le graphique en barres
    const roleDistribution = dashboardData.charts.role_distribution;
    const barData = roleDistribution ? {
      labels: roleDistribution.data.map(item => item.name),
      datasets: [{
        data: roleDistribution.data.map(item => item.value),
        backgroundColor: roleDistribution.data.map(item => item.color),
        borderRadius: 4,
        barThickness: 60
      }]
    } : null;

    return { doughnutData, barData };
  };

  // Options pour les graphiques
  const chartOptions = {
    doughnut: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'bottom',
          labels: {
            usePointStyle: true,
            padding: 20,
            font: { size: 14 }
          }
        },
        tooltip: {
          callbacks: {
            label: function (context) {
              const total = context.dataset.data.reduce((a, b) => a + b, 0);
              const percentage = total > 0 ? ((context.parsed * 100) / total).toFixed(1) : 0;
              return `${context.label}: ${context.parsed} (${percentage}%)`;
            }
          }
        }
      }
    },
    bar: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: { display: false },
        tooltip: {
          callbacks: {
            label: function (context) {
              return `${context.label}: ${context.parsed.y} utilisateurs`;
            }
          }
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          ticks: { font: { size: 12 } },
          grid: { color: '#E5E7EB' }
        },
        x: {
          grid: { display: false },
          ticks: { font: { size: 12 } }
        }
      }
    }
  };

  if (error && !dashboardData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-lg mb-4">Erreur: {error}</div>
          <button
            onClick={() => fetchDashboardData('today', true)}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
          >
            Réessayer
          </button>
        </div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <RefreshCw className="w-6 h-6 animate-spin text-blue-500" />
          <span className="text-lg text-gray-600">Chargement des données...</span>
        </div>
      </div>
    );
  }

  const { doughnutData, barData } = getChartData();

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      {/* En-tête avec filtres de période et bouton refresh */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="bg-blue-500 p-3 rounded-lg">
              <Shield className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {dashboardData.metadata?.dashboard_title || 'Tableau de Bord Super Admin'}
              </h1>
              <p className="text-gray-600 text-sm">
                {dashboardData.metadata?.dashboard_subtitle || 'Vue d\'ensemble des utilisateurs et analyses'}
              </p>
            </div>
          </div>

          <button
            onClick={handleRefresh}
            disabled={isLoading}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 transition-colors"
          >
            <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
            <span>Actualiser</span>
          </button>
        </div>

        {/* Boutons de filtrage par période */}
        <div className="flex space-x-2 mb-4">
          {availablePeriods.map(period => (
            <button
              key={period.value}
              onClick={() => handlePeriodChange(period.value)}
              disabled={isLoading}
              className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                currentPeriod === period.value
                  ? 'text-white shadow-lg'
                  : 'bg-white text-gray-700 border border-gray-200 hover:bg-gray-50'
              }`}
              style={{
                backgroundColor: currentPeriod === period.value ? period.color : undefined,
                borderColor: currentPeriod === period.value ? period.color : undefined
              }}
            >
              {period.label}
            </button>
          ))}
        </div>
      </div>

      {/* Cartes de métriques */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {dashboardData.metric_cards?.map((card, index) => (
          <div key={index} className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <p className="text-gray-600 text-sm mb-2">{card.title}</p>
                {card.subtitle && (
                  <p className="text-gray-500 text-xs mb-1">{card.subtitle}</p>
                )}
                <p className="text-4xl font-bold text-gray-900 mb-2">
                  {typeof card.value === 'number' ? card.value.toLocaleString() : card.value}
                </p>
                <p className="text-sm font-medium" style={{ color: card.color }}>
                  {card.trend} {card.trend_period}
                  {card.period === 'today' && (
                    <span className="ml-2">
                      <span className="inline-block w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
                      <span className="ml-1 text-green-500">En temps réel</span>
                    </span>
                  )}
                </p>
              </div>
              <div className="p-4 rounded-lg" style={{ backgroundColor: `${card.color}20` }}>
                {card.icon === 'users' && <Users className="w-8 h-8" style={{ color: card.color }} />}
                {card.icon === 'user-check' && <UserCheck className="w-8 h-8" style={{ color: card.color }} />}
                {card.icon === 'user-x' && <UserX className="w-8 h-8" style={{ color: card.color }} />}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Graphiques */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Graphique Doughnut */}
        {doughnutData && (
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
            <div className="flex items-center space-x-2 mb-6">
              <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
              <h3 className="text-lg font-semibold text-gray-900">
                {dashboardData.charts.active_vs_inactive?.title || 'Connexions'}
              </h3>
              {dashboardData.charts.active_vs_inactive?.subtitle && (
                <p className="text-sm text-gray-500">
                  {dashboardData.charts.active_vs_inactive.subtitle}
                </p>
              )}
            </div>
            <div className="h-80">
              <Doughnut data={doughnutData} options={chartOptions.doughnut} />
            </div>
          </div>
        )}

        {/* Graphique en Barres */}
        {barData && (
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
            <div className="flex items-center space-x-2 mb-6">
              <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
              <h3 className="text-lg font-semibold text-gray-900">
                {dashboardData.charts.role_distribution?.title || 'Distribution par Rôle'}
              </h3>
            </div>
            <div className="h-96">
              <Bar data={barData} options={chartOptions.bar} />
            </div>
          </div>
        )}
      </div>

      {/* Footer avec informations */}
      <div className="mt-8 flex justify-between items-center text-sm text-gray-500">
        <div>
          {lastUpdated && (
            <>
              Dernière mise à jour: {lastUpdated.toLocaleTimeString('fr-FR')}
              {dashboardData.metadata?.refresh_mode === 'manual' && (
                <span className="ml-2">• Mode manuel</span>
              )}
              {dashboardData.is_realtime && (
                <span className="ml-2 text-green-500">• Données en temps réel</span>
              )}
            </>
          )}
        </div>
        <div>
          Source: {dashboardData.metadata?.data_source || 'API Backend'}
        </div>
      </div>

      {/* Message d'erreur */}
      {error && (
        <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}
    </div>
  );
};

export default SuperAdminBIDashboardWithFilters;
