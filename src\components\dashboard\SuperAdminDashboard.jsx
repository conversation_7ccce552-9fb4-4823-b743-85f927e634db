import React, { useEffect, useState } from 'react';
import { useSuperAdmin } from '@/contexts/SuperAdminContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, Users, UserCheck, UserPlus, UserX, Calendar, ListTodo, BarChart2, TrendingUp, TrendingDown, RefreshCw, Layers } from 'lucide-react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import biService from '@/services/biService';

const SuperAdminDashboard = () => {
  const { metrics, loading, fetchSuperAdminMetrics } = useSuperAdmin();
  const [activeTab, setActiveTab] = useState('overview');
  const [biData, setBiData] = useState(null);
  const [biLoading, setBiLoading] = useState(false);

  useEffect(() => {
    fetchSuperAdminMetrics();
  }, [fetchSuperAdminMetrics]);

  // Fonction pour récupérer les données BI
  const fetchBiData = async () => {
    setBiLoading(true);
    try {
      const response = await biService.getSuperAdminDashboard();
      if (response.success && response.data) {
        setBiData(response.data);
      } else {
        // Données de fallback
        setBiData({
          metric_cards: [
            { title: "Nombre total d'utilisateurs", value: 29, trend: "+100%", trend_period: "ce mois", icon: "users" },
            { title: "Utilisateurs actifs", value: 7, trend: "+150.0%", trend_period: "cette semaine", icon: "user-check" },
            { title: "Utilisateurs inactifs", value: 22, trend: "0%", trend_period: "ce mois", icon: "user-x" }
          ],
          detailed_stats: {
            users_by_role: { super_admin: 2, admin: 7, employee: 15, client: 5 }
          }
        });
      }
    } catch (error) {
      console.error('Erreur lors du chargement des données BI:', error);
      // Données de fallback en cas d'erreur
      setBiData({
        metric_cards: [
          { title: "Nombre total d'utilisateurs", value: 29, trend: "+100%", trend_period: "ce mois", icon: "users" },
          { title: "Utilisateurs actifs", value: 7, trend: "+150.0%", trend_period: "cette semaine", icon: "user-check" },
          { title: "Utilisateurs inactifs", value: 22, trend: "0%", trend_period: "ce mois", icon: "user-x" }
        ],
        detailed_stats: {
          users_by_role: { super_admin: 2, admin: 7, employee: 15, client: 5 }
        }
      });
    } finally {
      setBiLoading(false);
    }
  };

  // Charger les données BI quand l'onglet analytics est sélectionné
  useEffect(() => {
    if (activeTab === 'analytics' && !biData) {
      fetchBiData();
    }
  }, [activeTab, biData]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
      </div>
    );
  }

  if (!metrics) {
    return (
      <div className="text-center p-8">
        <p className="text-gray-500">Aucune donnée disponible pour le moment.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Tableau de bord super administrateur</h2>
        <p className="text-sm text-gray-500">
          Dernière mise à jour: {format(new Date(), 'PPP', { locale: fr })}
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full max-w-md grid-cols-4">
          <TabsTrigger value="overview">Vue d'ensemble</TabsTrigger>
          <TabsTrigger value="users">Utilisateurs</TabsTrigger>
          <TabsTrigger value="activity">Activité</TabsTrigger>
          <TabsTrigger value="analytics">
            <BarChart2 className="h-4 w-4 mr-2" />
            Analyse
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <StatCard
              title="Utilisateurs"
              icon={<Users className="h-5 w-5 text-indigo-600" />}
              value={metrics.user_count || 0}
              description="Utilisateurs enregistrés"
            />
            <StatCard
              title="Équipes"
              icon={<Users className="h-5 w-5 text-blue-600" />}
              value={metrics.team_count || 0}
              description="Équipes actives"
            />
            <StatCard
              title="Taux d'activité"
              icon={<UserCheck className="h-5 w-5 text-green-600" />}
              value={`${Math.round(metrics.active_user_rate || 0)}%`}
              description="Utilisateurs actifs ce mois-ci"
              progress={metrics.active_user_rate || 0}
              progressLabel="Taux d'activité"
            />
          </div>
        </TabsContent>

        <TabsContent value="users" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Répartition des utilisateurs</CardTitle>
                <CardDescription>Par rôle</CardDescription>
              </CardHeader>
              <CardContent>
                {metrics.user_role_distribution ? (
                  <div className="space-y-4">
                    <StatusBar
                      label="Super Admin"
                      value={metrics.user_role_distribution.super_admin || 0}
                      total={metrics.user_count || 1}
                      color="bg-purple-500"
                    />
                    <StatusBar
                      label="Admin"
                      value={metrics.user_role_distribution.admin || 0}
                      total={metrics.user_count || 1}
                      color="bg-indigo-500"
                    />
                    <StatusBar
                      label="Employé"
                      value={metrics.user_role_distribution.employee || 0}
                      total={metrics.user_count || 1}
                      color="bg-blue-500"
                    />
                    <StatusBar
                      label="Client"
                      value={metrics.user_role_distribution.client || 0}
                      total={metrics.user_count || 1}
                      color="bg-green-500"
                    />
                  </div>
                ) : (
                  <p className="text-gray-500">Aucune donnée disponible</p>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Croissance des utilisateurs</CardTitle>
                <CardDescription>Nouveaux utilisateurs par mois</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col items-center justify-center h-40">
                  <div className="text-5xl font-bold text-indigo-600 mb-2">
                    {metrics.new_users_this_month || 0}
                  </div>
                  <p className="text-gray-500">nouveaux utilisateurs ce mois-ci</p>
                  <div className="mt-4 w-full">
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-xs font-medium">Croissance mensuelle</span>
                      <span className="text-xs font-medium">
                        {metrics.user_growth_rate ? `+${Math.round(metrics.user_growth_rate)}%` : '0%'}
                      </span>
                    </div>
                    <Progress value={metrics.user_growth_rate || 0} className="h-2" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="activity" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Activité de la plateforme</CardTitle>
                <CardDescription>Statistiques globales</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-sm font-medium">Tâches créées</span>
                      <span className="text-sm font-semibold">{metrics.total_tasks || 0}</span>
                    </div>
                    <Progress value={100} className="h-2" />
                  </div>
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-sm font-medium">Événements créés</span>
                      <span className="text-sm font-semibold">{metrics.total_events || 0}</span>
                    </div>
                    <Progress value={100} className="h-2" />
                  </div>
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-sm font-medium">Sessions Pomodoro</span>
                      <span className="text-sm font-semibold">{metrics.total_pomodoro_sessions || 0}</span>
                    </div>
                    <Progress value={100} className="h-2" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Activité récente</CardTitle>
                <CardDescription>Derniers 30 jours</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-sm font-medium">Utilisateurs actifs</span>
                      <span className="text-sm font-semibold">{metrics.active_users || 0}</span>
                    </div>
                    <div className="h-2 bg-gray-200 rounded-full">
                      <div
                        className="h-full bg-indigo-600 rounded-full"
                        style={{ width: `${(metrics.active_users / metrics.user_count) * 100 || 0}%` }}
                      ></div>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-sm font-medium">Tâches complétées</span>
                      <span className="text-sm font-semibold">{metrics.completed_tasks_this_month || 0}</span>
                    </div>
                    <div className="h-2 bg-gray-200 rounded-full">
                      <div
                        className="h-full bg-green-600 rounded-full"
                        style={{ width: `${(metrics.completed_tasks_this_month / metrics.total_tasks) * 100 || 0}%` }}
                      ></div>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-sm font-medium">Événements terminés</span>
                      <span className="text-sm font-semibold">{metrics.completed_events_this_month || 0}</span>
                    </div>
                    <div className="h-2 bg-gray-200 rounded-full">
                      <div
                        className="h-full bg-blue-600 rounded-full"
                        style={{ width: `${(metrics.completed_events_this_month / metrics.total_events) * 100 || 0}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="mt-6">
          {biLoading ? (
            <div className="flex items-center justify-center gap-2 py-8">
              <RefreshCw className="w-5 h-5 text-blue-600 animate-spin" />
              <span className="text-gray-600">Chargement des données d'analyse...</span>
            </div>
          ) : biData ? (
            <div className="space-y-6">
              {/* Section Activité des utilisateurs */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Activité des utilisateurs</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {biData.metric_cards && biData.metric_cards.map((card, index) => (
                    <Card key={index}>
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              {card.icon === 'users' && <Users className="w-5 h-5 text-blue-500" />}
                              {card.icon === 'user-check' && <UserCheck className="w-5 h-5 text-green-500" />}
                              {card.icon === 'user-x' && <UserX className="w-5 h-5 text-red-500" />}
                              <span className="text-sm font-medium text-gray-600">{card.title}</span>
                            </div>
                            <p className="text-3xl font-bold text-gray-900 mb-1">{card.value}</p>
                            <p className="text-sm text-gray-500">{card.trend_period}</p>
                          </div>
                          <div className="text-right">
                            <div className={`flex items-center gap-1 text-sm font-medium ${card.trend && card.trend.includes('+') ? 'text-green-600' :
                              card.trend && card.trend.includes('-') ? 'text-red-600' : 'text-gray-600'
                              }`}>
                              {card.trend && card.trend.includes('+') && <TrendingUp className="w-4 h-4" />}
                              {card.trend && card.trend.includes('-') && <TrendingDown className="w-4 h-4" />}
                              <span>{card.trend}</span>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>

              {/* Section Répartition des utilisateurs par rôle */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Répartition des utilisateurs par rôle</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <Users className="w-5 h-5 text-purple-500" />
                        <span className="text-sm font-medium text-gray-600">Super Admin</span>
                      </div>
                      <div className="text-2xl font-bold text-gray-900">
                        {biData.detailed_stats?.users_by_role?.super_admin || 0}
                      </div>
                      {biData.detailed_stats?.users_with_permissions?.super_admin && (
                        <p className="text-xs text-gray-500 mt-1">
                          {biData.detailed_stats.users_with_permissions.super_admin.percentage?.toFixed(1)}% du total
                        </p>
                      )}
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <Users className="w-5 h-5 text-blue-500" />
                        <span className="text-sm font-medium text-gray-600">Admin</span>
                      </div>
                      <div className="text-2xl font-bold text-gray-900">
                        {biData.detailed_stats?.users_by_role?.admin || 0}
                      </div>
                      {biData.detailed_stats?.users_with_permissions?.admin && (
                        <p className="text-xs text-gray-500 mt-1">
                          {biData.detailed_stats.users_with_permissions.admin.percentage?.toFixed(1)}% du total
                        </p>
                      )}
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <Users className="w-5 h-5 text-green-500" />
                        <span className="text-sm font-medium text-gray-600">Employés</span>
                      </div>
                      <div className="text-2xl font-bold text-gray-900">
                        {biData.detailed_stats?.users_by_role?.employee || 0}
                      </div>
                      {biData.detailed_stats?.users_with_permissions?.employee && (
                        <p className="text-xs text-gray-500 mt-1">
                          {biData.detailed_stats.users_with_permissions.employee.percentage?.toFixed(1)}% du total
                        </p>
                      )}
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <Users className="w-5 h-5 text-orange-500" />
                        <span className="text-sm font-medium text-gray-600">Clients</span>
                      </div>
                      <div className="text-2xl font-bold text-gray-900">
                        {biData.detailed_stats?.users_by_role?.client || 0}
                      </div>
                      {biData.detailed_stats?.users_with_permissions?.client && (
                        <p className="text-xs text-gray-500 mt-1">
                          {biData.detailed_stats.users_with_permissions.client.percentage?.toFixed(1)}% du total
                        </p>
                      )}
                    </CardContent>
                  </Card>
                </div>
              </div>

              {/* Section Métriques d'engagement */}
              {biData.detailed_stats?.engagement_metrics && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Métriques d'engagement</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <UserPlus className="w-5 h-5 text-blue-500" />
                          <span className="text-sm font-medium text-gray-600">Nouveaux (7j)</span>
                        </div>
                        <div className="text-2xl font-bold text-gray-900">
                          {biData.detailed_stats.engagement_metrics.new_users_7d || 0}
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <UserPlus className="w-5 h-5 text-green-500" />
                          <span className="text-sm font-medium text-gray-600">Nouveaux (30j)</span>
                        </div>
                        <div className="text-2xl font-bold text-gray-900">
                          {biData.detailed_stats.engagement_metrics.new_users_30d || 0}
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <UserCheck className="w-5 h-5 text-purple-500" />
                          <span className="text-sm font-medium text-gray-600">Connectés aujourd'hui</span>
                        </div>
                        <div className="text-2xl font-bold text-gray-900">
                          {biData.detailed_stats.engagement_metrics.users_logged_today || 0}
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <TrendingUp className="w-5 h-5 text-indigo-500" />
                          <span className="text-sm font-medium text-gray-600">Taux de rétention</span>
                        </div>
                        <div className="text-2xl font-bold text-gray-900">
                          {biData.detailed_stats.engagement_metrics.retention_rate?.toFixed(1) || 0}%
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              )}

              {/* Informations système et statut */}
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      <div className={`w-3 h-3 rounded-full ${biData.is_realtime ? 'bg-green-500' : 'bg-yellow-500'}`}></div>
                      <span className="text-sm font-medium text-gray-700">
                        {biData.is_realtime ? 'Données en temps réel' : 'Données de démonstration'}
                      </span>
                    </div>
                    {biData.metadata?.last_updated && (
                      <div className="text-sm text-gray-500">
                        Dernière mise à jour: {new Date(biData.metadata.last_updated).toLocaleString()}
                      </div>
                    )}
                  </div>
                  <button
                    onClick={fetchBiData}
                    disabled={biLoading}
                    className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
                  >
                    <RefreshCw className={`w-4 h-4 ${biLoading ? 'animate-spin' : ''}`} />
                    {biLoading ? 'Actualisation...' : 'Actualiser'}
                  </button>
                </div>
                {biData.metadata?.refresh_interval && (
                  <div className="mt-2 text-xs text-gray-500">
                    Actualisation automatique toutes les {biData.metadata.refresh_interval} secondes
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500 mb-4">Aucune donnée d'analyse disponible</p>
              <button
                onClick={fetchBiData}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
              >
                Charger les données
              </button>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

// Composant pour afficher une carte de statistique
const StatCard = ({ title, icon, value, description, progress, progressLabel }) => (
  <Card>
    <CardHeader className="flex flex-row items-center justify-between pb-2">
      <CardTitle className="text-sm font-medium">{title}</CardTitle>
      {icon}
    </CardHeader>
    <CardContent>
      <div className="text-2xl font-bold">{value}</div>
      <p className="text-xs text-gray-500">{description}</p>
      {progress !== undefined && (
        <div className="mt-4">
          <div className="flex justify-between items-center mb-1">
            <span className="text-xs font-medium">{progressLabel}</span>
            <span className="text-xs font-medium">{Math.round(progress)}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
      )}
    </CardContent>
  </Card>
);

// Composant pour afficher une barre de statut
const StatusBar = ({ label, value, total, color }) => (
  <div className="space-y-1">
    <div className="flex justify-between items-center">
      <span className="text-sm font-medium">{label}</span>
      <span className="text-sm font-medium">{value}</span>
    </div>
    <div className="h-2 bg-gray-200 rounded-full">
      <div
        className={`h-full rounded-full ${color}`}
        style={{ width: `${(value / total) * 100}%` }}
      ></div>
    </div>
  </div>
);

export default SuperAdminDashboard;
