/* Styles pour le tableau de bord Super Admin en temps réel */

.dashboard-container {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: 100vh;
  padding: 1.5rem;
}

.dashboard-header {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  border-left: 4px solid #6B4EFF;
}

.dashboard-title {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.dashboard-icon {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, #6B4EFF 0%, #8B5CF6 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.metric-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--card-color, #6B4EFF);
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.metric-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a202c;
  margin: 0.5rem 0;
  line-height: 1;
}

.metric-trend {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.metric-trend.positive {
  color: #059669;
}

.metric-trend.negative {
  color: #dc2626;
}

.metric-icon-container {
  width: 3rem;
  height: 3rem;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  background: var(--card-color, #6B4EFF);
}

.chart-container {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  height: 100%;
}

.chart-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  font-size: 1.125rem;
  font-weight: 600;
  color: #1a202c;
}

.chart-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.chart-indicator.green {
  background-color: #10b981;
}

.chart-indicator.blue {
  background-color: #3b82f6;
}

.chart-content {
  height: 16rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-error {
  text-align: center;
  color: #6b7280;
}

.chart-error button {
  margin-top: 0.5rem;
  padding: 0.5rem 1rem;
  background: #6B4EFF;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.chart-error button:hover {
  background: #5B3EEF;
}

.refresh-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #6B4EFF 0%, #8B5CF6 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.refresh-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(107, 78, 255, 0.3);
}

.refresh-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 4rem;
  color: #6b7280;
}

.loading-spinner svg {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-indicator.realtime {
  background-color: #dcfce7;
  color: #166534;
}

.status-indicator.offline {
  background-color: #fef2f2;
  color: #991b1b;
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
}

.status-dot.green {
  background-color: #22c55e;
}

.status-dot.red {
  background-color: #ef4444;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 1rem;
  }
  
  .dashboard-header {
    padding: 1.5rem;
  }
  
  .metric-value {
    font-size: 2rem;
  }
  
  .chart-content {
    height: 12rem;
  }
}

@media (max-width: 640px) {
  .dashboard-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  .metric-card {
    padding: 1rem;
  }
  
  .metric-value {
    font-size: 1.75rem;
  }
  
  .chart-container {
    padding: 1rem;
  }
  
  .chart-content {
    height: 10rem;
  }
}

/* Animation pour les cartes de métriques */
.metric-card {
  animation: fadeInUp 0.6s ease-out;
}

.metric-card:nth-child(1) {
  animation-delay: 0.1s;
}

.metric-card:nth-child(2) {
  animation-delay: 0.2s;
}

.metric-card:nth-child(3) {
  animation-delay: 0.3s;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Animation pour les graphiques */
.chart-container {
  animation: fadeIn 0.8s ease-out;
}

.chart-container:nth-child(1) {
  animation-delay: 0.4s;
}

.chart-container:nth-child(2) {
  animation-delay: 0.5s;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Styles pour les tooltips des graphiques */
.chartjs-tooltip {
  background: rgba(0, 0, 0, 0.8) !important;
  border-radius: 6px !important;
  color: white !important;
  font-size: 12px !important;
  padding: 8px 12px !important;
}

/* Amélioration de l'accessibilité */
.metric-card:focus,
.refresh-button:focus,
.chart-error button:focus {
  outline: 2px solid #6B4EFF;
  outline-offset: 2px;
}

/* Styles pour le mode sombre (optionnel) */
@media (prefers-color-scheme: dark) {
  .dashboard-container {
    background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
  }
  
  .metric-card,
  .chart-container,
  .dashboard-header {
    background: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;
  }
  
  .metric-value {
    color: #f7fafc;
  }
  
  .chart-title {
    color: #f7fafc;
  }
}
