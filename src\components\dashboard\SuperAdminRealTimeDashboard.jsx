import React, { useEffect, useState } from 'react';
import {
  RefreshCw,
  Users,
  UserCheck,
  UserX,
  TrendingUp,
  TrendingDown,
  Activity,
  Clock,
  Wifi,
  WifiOff
} from 'lucide-react';
import { toast } from 'react-toastify';
import {
  Chart as ChartJS,
  ArcElement,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js';
import { Doughnut, Bar } from 'react-chartjs-2';
import biService from '@/services/biService';

// Enregistrer les composants Chart.js
ChartJS.register(
  ArcElement,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

const SuperAdminRealTimeDashboard = () => {
  const [dashboardData, setDashboardData] = useState({
    metric_cards: [],
    charts: {},
    detailed_stats: {},
    metadata: {
      dashboard_title: 'Tableau de Bord Super Admin',
      dashboard_subtitle: 'Vue d\'ensemble des utilisateurs et analyses'
    },
    is_realtime: false
  });
  const [loading, setLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState(null);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [error, setError] = useState(null);

  // Récupérer les données du tableau de bord
  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('SuperAdminRealTimeDashboard - Récupération des données...');

      const response = await biService.getSuperAdminDashboard();

      if (response.success && response.data) {
        setDashboardData(response.data);
        setLastUpdated(new Date());
        console.log('SuperAdminRealTimeDashboard - Données récupérées:', response.data);
      } else {
        console.error('SuperAdminRealTimeDashboard - Erreur de réponse:', response);
        setError(response.message || 'Erreur lors du chargement des données');
        toast.error(response.message || 'Erreur lors du chargement des données');
        // Définir des données par défaut pour éviter les erreurs d'affichage
        setDashboardData({
          metric_cards: [
            {
              title: "Nombre total d'utilisateurs",
              value: 1247,
              trend: "+12% ce mois",
              icon: "users",
              color: "#3B82F6"
            },
            {
              title: "Utilisateurs actifs",
              value: 1089,
              trend: "+5% cette semaine",
              icon: "user-check",
              color: "#10B981"
            },
            {
              title: "Utilisateurs inactifs",
              value: 158,
              trend: "-3% ce mois",
              icon: "user-x",
              color: "#EF4444"
            }
          ],
          charts: {
            active_vs_inactive: {
              type: "doughnut",
              title: "Utilisateurs Actifs vs Inactifs",
              data: [
                { "name": "Actifs", "value": 1089, "color": "#10B981" },
                { "name": "Inactifs", "value": 158, "color": "#EF4444" }
              ]
            },
            role_distribution: {
              type: "bar",
              title: "Distribution des Utilisateurs par Rôle",
              data: [
                { "name": "Super Admin", "value": 50, "color": "#8B5CF6" },
                { "name": "Admin", "value": 100, "color": "#3B82F6" },
                { "name": "Employés", "value": 800, "color": "#10B981" },
                { "name": "Clients", "value": 297, "color": "#F59E0B" }
              ]
            }
          },
          detailed_stats: {},
          metadata: {
            dashboard_title: 'Tableau de Bord Super Admin',
            dashboard_subtitle: 'Vue d\'ensemble des utilisateurs et analyses'
          },
          is_realtime: false
        });
      }
    } catch (error) {
      console.error('SuperAdminRealTimeDashboard - Erreur:', error);
      setError('Erreur lors du chargement des données du tableau de bord');
      toast.error('Erreur lors du chargement des données du tableau de bord');
      // Utiliser les données par défaut en cas d'erreur
      setDashboardData({
        metric_cards: [
          {
            title: "Nombre total d'utilisateurs",
            value: 1247,
            trend: "+12% ce mois",
            icon: "users",
            color: "#3B82F6"
          },
          {
            title: "Utilisateurs actifs",
            value: 1089,
            trend: "+5% cette semaine",
            icon: "user-check",
            color: "#10B981"
          },
          {
            title: "Utilisateurs inactifs",
            value: 158,
            trend: "-3% ce mois",
            icon: "user-x",
            color: "#EF4444"
          }
        ],
        charts: {
          active_vs_inactive: {
            type: "doughnut",
            title: "Utilisateurs Actifs vs Inactifs",
            data: [
              { "name": "Actifs", "value": 1089, "color": "#10B981" },
              { "name": "Inactifs", "value": 158, "color": "#EF4444" }
            ]
          },
          role_distribution: {
            type: "bar",
            title: "Distribution des Utilisateurs par Rôle",
            data: [
              { "name": "Super Admin", "value": 50, "color": "#8B5CF6" },
              { "name": "Admin", "value": 100, "color": "#3B82F6" },
              { "name": "Employés", "value": 800, "color": "#10B981" },
              { "name": "Clients", "value": 297, "color": "#F59E0B" }
            ]
          }
        },
        detailed_stats: {},
        metadata: {
          dashboard_title: 'Tableau de Bord Super Admin',
          dashboard_subtitle: 'Erreur de connexion'
        },
        is_realtime: false
      });
    } finally {
      setLoading(false);
    }
  };

  // Effet pour charger les données au montage
  useEffect(() => {
    fetchDashboardData();
  }, []);

  // Effet pour le rafraîchissement automatique
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      fetchDashboardData();
    }, 30000); // Rafraîchir toutes les 30 secondes

    return () => clearInterval(interval);
  }, [autoRefresh]);



  // Préparer les données pour le graphique en donut (Actifs vs Inactifs)
  const prepareActiveInactiveData = () => {
    if (!dashboardData?.charts?.active_vs_inactive?.data || !Array.isArray(dashboardData.charts.active_vs_inactive.data)) {
      return null;
    }

    const chartData = dashboardData.charts.active_vs_inactive;

    return {
      labels: chartData.data.map(item => item.name || 'N/A'),
      datasets: [{
        data: chartData.data.map(item => item.value || 0),
        backgroundColor: chartData.data.map(item => item.color || '#gray'),
        borderWidth: 0,
        cutout: '60%'
      }]
    };
  };

  // Préparer les données pour le graphique en barres (Distribution par rôle)
  const prepareRoleDistributionData = () => {
    if (!dashboardData?.charts?.role_distribution?.data || !Array.isArray(dashboardData.charts.role_distribution.data)) {
      return null;
    }

    const chartData = dashboardData.charts.role_distribution;

    return {
      labels: chartData.data.map(item => item.name || 'N/A'),
      datasets: [{
        label: 'Nombre d\'utilisateurs',
        data: chartData.data.map(item => item.value || 0),
        backgroundColor: chartData.data.map(item => item.color || '#gray'),
        borderRadius: 4,
        borderSkipped: false,
      }]
    };
  };

  // Options pour le graphique en donut
  const doughnutOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          padding: 20,
          usePointStyle: true,
          font: {
            size: 12
          }
        }
      },
      tooltip: {
        callbacks: {
          label: function (context) {
            const label = context.label || '';
            const value = context.parsed || 0;
            const total = context.dataset.data.reduce((a, b) => a + b, 0);
            const percentage = ((value / total) * 100).toFixed(1);
            return `${label}: ${value} (${percentage}%)`;
          }
        }
      }
    }
  };

  // Options pour le graphique en barres
  const barOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        callbacks: {
          label: function (context) {
            return `${context.parsed.y} utilisateurs`;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        max: 20, // Échelle adaptée aux vraies valeurs
        grid: {
          color: '#f3f4f6'
        },
        ticks: {
          stepSize: 5, // Étapes de 5 en 5
          font: {
            size: 11
          }
        }
      },
      x: {
        grid: {
          display: false
        },
        ticks: {
          font: {
            size: 11
          }
        }
      }
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center gap-2 py-8">
        <RefreshCw className="w-5 h-5 text-blue-600 animate-spin" />
        <span className="text-gray-600">Chargement des données en temps réel...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Section Activité des utilisateurs */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Activité des utilisateurs</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Cartes de métriques */}
          {dashboardData.metric_cards && Array.isArray(dashboardData.metric_cards) && dashboardData.metric_cards.length > 0 ? (
            dashboardData.metric_cards.map((card, index) => (
              <div key={index} className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      {card.icon === 'users' && <Users className="w-5 h-5 text-blue-500" />}
                      {card.icon === 'user-check' && <UserCheck className="w-5 h-5 text-green-500" />}
                      {card.icon === 'user-x' && <UserX className="w-5 h-5 text-red-500" />}
                      <span className="text-sm font-medium text-gray-600">{card.title}</span>
                    </div>
                    <p className="text-3xl font-bold text-gray-900 mb-1">{card.value}</p>
                    <p className="text-sm text-gray-500">{card.trend_period}</p>
                  </div>
                  <div className="text-right">
                    <div className={`flex items-center gap-1 text-sm font-medium ${card.trend && card.trend.includes('+') ? 'text-green-600' :
                      card.trend && card.trend.includes('-') ? 'text-red-600' : 'text-gray-600'
                      }`}>
                      {card.trend && card.trend.includes('+') && <TrendingUp className="w-4 h-4" />}
                      {card.trend && card.trend.includes('-') && <TrendingDown className="w-4 h-4" />}
                      <span>{card.trend}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="col-span-2 text-center py-8">
              <p className="text-gray-500">Aucune donnée de métrique disponible</p>
              <button
                onClick={fetchDashboardData}
                className="mt-2 px-4 py-2 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
              >
                Réessayer
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Section Répartition des utilisateurs par rôle */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Répartition des utilisateurs par rôle</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Super Admin */}
          <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
            <div className="flex items-center gap-2 mb-2">
              <Users className="w-5 h-5 text-purple-500" />
              <span className="text-sm font-medium text-gray-600">Super Admin</span>
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {dashboardData.detailed_stats?.users_by_role?.super_admin || 0}
            </div>
          </div>

          {/* Admin */}
          <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
            <div className="flex items-center gap-2 mb-2">
              <Users className="w-5 h-5 text-blue-500" />
              <span className="text-sm font-medium text-gray-600">Admin</span>
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {dashboardData.detailed_stats?.users_by_role?.admin || 0}
            </div>
          </div>

          {/* Employés */}
          <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
            <div className="flex items-center gap-2 mb-2">
              <Users className="w-5 h-5 text-green-500" />
              <span className="text-sm font-medium text-gray-600">Employés</span>
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {dashboardData.detailed_stats?.users_by_role?.employee || 0}
            </div>
          </div>

          {/* Clients */}
          <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
            <div className="flex items-center gap-2 mb-2">
              <Users className="w-5 h-5 text-orange-500" />
              <span className="text-sm font-medium text-gray-600">Clients</span>
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {dashboardData.detailed_stats?.users_by_role?.client || 0}
            </div>
          </div>
        </div>
      </div>

      {/* Section Graphiques */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Graphique Actifs vs Inactifs */}
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <h4 className="text-base font-medium text-gray-900 mb-4">
            {dashboardData.charts?.active_vs_inactive?.title || 'Utilisateurs Actifs vs Inactifs'}
          </h4>
          <div className="h-64 flex items-center justify-center">
            {prepareActiveInactiveData() ? (
              <Doughnut data={prepareActiveInactiveData()} options={doughnutOptions} />
            ) : (
              <div className="text-center">
                <p className="text-gray-500 mb-2">Données non disponibles</p>
                <button
                  onClick={fetchDashboardData}
                  className="px-4 py-2 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                >
                  Réessayer
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Graphique Distribution par rôle */}
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <h4 className="text-base font-medium text-gray-900 mb-4">
            {dashboardData.charts?.role_distribution?.title || 'Distribution des Utilisateurs par Rôle'}
          </h4>
          <div className="h-80 flex items-center justify-center">
            {prepareRoleDistributionData() ? (
              <Bar data={prepareRoleDistributionData()} options={barOptions} />
            ) : (
              <div className="text-center">
                <p className="text-gray-500 mb-2">Données non disponibles</p>
                <button
                  onClick={fetchDashboardData}
                  className="px-4 py-2 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                >
                  Réessayer
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Bouton de rafraîchissement */}
      <div className="flex justify-center">
        <button
          onClick={fetchDashboardData}
          disabled={loading}
          className="flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
        >
          <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
          {loading ? 'Actualisation...' : 'Actualiser les données'}
        </button>
      </div>

      {/* Informations de mise à jour */}
      <div className="text-center text-sm text-gray-500">
        {lastUpdated && (
          <p>Dernière mise à jour: {lastUpdated.toLocaleString()}</p>
        )}
        {dashboardData.metadata?.data_source && (
          <p className="mt-1">
            Source: {dashboardData.metadata.data_source === 'real_time' ? 'Données en temps réel' : 'Données de démonstration'}
          </p>
        )}
      </div>
    </div>
  );
};

export default SuperAdminRealTimeDashboard;
