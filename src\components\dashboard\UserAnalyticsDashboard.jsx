import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Pie } from 'react-chartjs-2';
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js';
import { Users, UserCheck, Shield } from 'lucide-react';
import axios from 'axios';
import { API_URL } from '@/config/constants';
import { useAuth } from '@/contexts/AuthContext';

// Enregistrer les composants Chart.js nécessaires
ChartJS.register(ArcElement, Tooltip, Legend);

const UserAnalyticsDashboard = () => {
  const { getAuthHeader } = useAuth();
  const [metrics, setMetrics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [refreshInterval, setRefreshInterval] = useState(30000); // 30 secondes par défaut

  // Couleurs pour les graphiques (basées sur l'image fournie)
  const colors = {
    active: '#8B5CF6', // Violet pour utilisateurs actifs
    inactive: '#D1D5DB', // Gris pour utilisateurs inactifs
    admin: '#8B5CF6', // Violet pour administrateurs
    editor: '#EC4899', // Rose pour éditeurs
    observer: '#3B82F6', // Bleu pour observateurs
    guest: '#10B981', // Vert pour invités
  };

  // Fonction pour récupérer les métriques
  const fetchMetrics = async () => {
    setLoading(true);
    try {
      const headers = await getAuthHeader();
      const response = await axios.get(`${API_URL}/bi/metrics/`, { headers });
      
      console.log('Métriques récupérées:', response.data);
      setMetrics(response.data);
      setError(null);
    } catch (err) {
      console.error('Erreur lors de la récupération des métriques:', err);
      setError('Impossible de charger les données. Veuillez réessayer plus tard.');
    } finally {
      setLoading(false);
    }
  };

  // Charger les métriques au montage du composant
  useEffect(() => {
    fetchMetrics();
    
    // Configurer un intervalle pour rafraîchir les données
    const intervalId = setInterval(() => {
      fetchMetrics();
    }, refreshInterval);
    
    // Nettoyer l'intervalle lors du démontage du composant
    return () => clearInterval(intervalId);
  }, [refreshInterval]);

  // Si les données sont en cours de chargement
  if (loading && !metrics) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  // Si une erreur s'est produite
  if (error) {
    return (
      <div className="bg-red-50 p-4 rounded-lg text-red-800">
        <p className="font-medium">Erreur</p>
        <p>{error}</p>
        <button 
          onClick={fetchMetrics}
          className="mt-2 px-4 py-2 bg-red-100 hover:bg-red-200 rounded-md transition-colors"
        >
          Réessayer
        </button>
      </div>
    );
  }

  // Utiliser des données mockées si les métriques ne sont pas disponibles
  const data = metrics || {
    user_activity: {
      total_users: 12,
      active_users: {
        day: 7,
        week: 9,
        month: 10
      },
      active_percentage: {
        day: 58,
        week: 75,
        month: 83
      },
      users_by_role: {
        admin: 2,
        editor: 3,
        observer: 4,
        guest: 3
      }
    }
  };

  // Préparer les données pour le graphique de statut des utilisateurs
  const userStatusData = {
    labels: ['Utilisateurs Actifs', 'Utilisateurs Inactifs'],
    datasets: [
      {
        data: [
          data.user_activity.active_users.day || 0,
          (data.user_activity.total_users || 0) - (data.user_activity.active_users.day || 0)
        ],
        backgroundColor: [colors.active, colors.inactive],
        borderColor: ['#FFFFFF', '#FFFFFF'],
        borderWidth: 2,
      },
    ],
  };

  // Préparer les données pour le graphique de distribution des rôles
  const roleDistributionData = {
    labels: ['Administrateur', 'Éditeur', 'Observateur', 'Invité'],
    datasets: [
      {
        data: [
          data.user_activity.users_by_role.admin || 0,
          data.user_activity.users_by_role.editor || 0,
          data.user_activity.users_by_role.observer || 0,
          data.user_activity.users_by_role.guest || 0
        ],
        backgroundColor: [colors.admin, colors.editor, colors.observer, colors.guest],
        borderColor: ['#FFFFFF', '#FFFFFF', '#FFFFFF', '#FFFFFF'],
        borderWidth: 2,
      },
    ],
  };

  // Options communes pour les graphiques
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          usePointStyle: true,
          padding: 20,
          font: {
            size: 12
          }
        }
      }
    }
  };

  // Calculer le nombre total de permissions (exemple simple)
  const totalPermissions = Object.values(data.user_activity.users_by_role).reduce(
    (sum, count) => sum + count * 4, // Supposons 4 permissions par rôle en moyenne
    0
  );

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold text-center mb-6">Tableau de Bord d'Analyse des Utilisateurs</h1>
      
      {/* Cartes de statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Total Utilisateurs</CardTitle>
            <Users className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.user_activity.total_users || 0}</div>
            <p className="text-xs text-gray-500 mt-1">
              {data.user_activity.active_users.week || 0} actifs, {data.user_activity.active_users.month || 0} mensuels
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Utilisateurs Actifs</CardTitle>
            <UserCheck className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.user_activity.active_users.day || 0}</div>
            <p className="text-xs text-gray-500 mt-1">
              {data.user_activity.active_percentage.day || 0}% du total des utilisateurs
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Total Permissions</CardTitle>
            <Shield className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalPermissions}</div>
            <p className="text-xs text-gray-500 mt-1">
              Pour tous les rôles
            </p>
          </CardContent>
        </Card>
      </div>
      
      {/* Graphiques */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Statut des Utilisateurs</CardTitle>
            <p className="text-sm text-gray-500">Répartition des utilisateurs actifs et inactifs</p>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <Pie data={userStatusData} options={chartOptions} />
            </div>
            <div className="mt-4 text-center">
              <div className="inline-block mr-4">
                <span className="inline-block w-3 h-3 rounded-full bg-purple-500 mr-1"></span>
                <span className="text-sm">Utilisateurs Actifs {data.user_activity.active_percentage.day || 0}%</span>
              </div>
              <div className="inline-block">
                <span className="inline-block w-3 h-3 rounded-full bg-gray-300 mr-1"></span>
                <span className="text-sm">Utilisateurs Inactifs {100 - (data.user_activity.active_percentage.day || 0)}%</span>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Distribution des Rôles</CardTitle>
            <p className="text-sm text-gray-500">Nombre d'utilisateurs par rôle</p>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <Pie data={roleDistributionData} options={chartOptions} />
            </div>
            <div className="mt-4 text-center text-sm grid grid-cols-2 gap-2">
              <div>
                <span className="inline-block w-3 h-3 rounded-full bg-purple-500 mr-1"></span>
                <span>Administrateur {Math.round(data.user_activity.users_by_role.admin / data.user_activity.total_users * 100) || 0}%</span>
              </div>
              <div>
                <span className="inline-block w-3 h-3 rounded-full bg-pink-500 mr-1"></span>
                <span>Éditeur {Math.round(data.user_activity.users_by_role.editor / data.user_activity.total_users * 100) || 0}%</span>
              </div>
              <div>
                <span className="inline-block w-3 h-3 rounded-full bg-blue-500 mr-1"></span>
                <span>Observateur {Math.round(data.user_activity.users_by_role.observer / data.user_activity.total_users * 100) || 0}%</span>
              </div>
              <div>
                <span className="inline-block w-3 h-3 rounded-full bg-green-500 mr-1"></span>
                <span>Invité {Math.round(data.user_activity.users_by_role.guest / data.user_activity.total_users * 100) || 0}%</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Bouton de rafraîchissement manuel */}
      <div className="flex justify-center mt-6">
        <button 
          onClick={fetchMetrics}
          className="px-4 py-2 bg-purple-100 hover:bg-purple-200 text-purple-800 rounded-md transition-colors flex items-center"
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          Rafraîchir les données
        </button>
      </div>
    </div>
  );
};

export default UserAnalyticsDashboard;
