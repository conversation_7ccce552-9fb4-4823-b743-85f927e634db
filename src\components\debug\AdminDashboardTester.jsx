import React, { useState, useEffect } from 'react';
import { AlertTriangle, CheckCircle, RefreshCw, Shield, Bug } from 'lucide-react';
import { toast } from 'react-toastify';
import adminDashboardService from '@/services/adminDashboardService';

/**
 * Composant de test pour valider l'implémentation du dashboard admin
 * Suit les tests définis dans votre guide
 */
const AdminDashboardTester = () => {
  const [testResults, setTestResults] = useState({});
  const [isRunning, setIsRunning] = useState(false);
  const [currentTest, setCurrentTest] = useState('');

  const tests = [
    {
      id: 'service_connection',
      name: 'Connexion au service Admin BI',
      description: 'Teste la connexion au service adminDashboardService'
    },
    {
      id: 'dashboard_today',
      name: 'Dashboard Admin - Aujourd\'hui',
      description: 'Teste les données pour la période aujourd\'hui'
    },
    {
      id: 'dashboard_1h',
      name: 'Dashboard Admin - 1 heure',
      description: 'Teste les données pour la période 1 heure'
    },
    {
      id: 'dashboard_24h',
      name: 'Dashboard Admin - 24 heures',
      description: 'Teste les données pour la période 24 heures'
    },
    {
      id: 'dashboard_7d',
      name: 'Dashboard Admin - 7 jours',
      description: 'Teste les données pour la période 7 jours'
    },
    {
      id: 'dashboard_30d',
      name: 'Dashboard Admin - 30 jours',
      description: 'Teste les données pour la période 30 jours'
    },
    {
      id: 'data_structure',
      name: 'Structure des données',
      description: 'Vérifie que la structure des données est conforme au guide'
    },
    {
      id: 'metric_cards',
      name: 'Cartes de métriques',
      description: 'Vérifie les 3 cartes de métriques (équipes, membres, progression)'
    },
    {
      id: 'charts_data',
      name: 'Données des graphiques',
      description: 'Vérifie les graphiques circulaires (événements et tâches)'
    },
    {
      id: 'detailed_stats',
      name: 'Statistiques détaillées',
      description: 'Vérifie les statistiques détaillées selon le guide'
    },
    {
      id: 'debug_data',
      name: 'Données de débogage',
      description: 'Teste l\'endpoint de débogage admin'
    }
  ];

  const runTest = async (testId) => {
    setCurrentTest(testId);
    
    try {
      let result = { success: false, message: '', data: null };

      switch (testId) {
        case 'service_connection':
          try {
            const response = await adminDashboardService.getAdminDashboard('today', true);
            result = {
              success: true,
              message: 'Service Admin BI accessible',
              data: { hasData: !!response.data, success: response.success }
            };
          } catch (error) {
            result = {
              success: false,
              message: `Erreur de connexion: ${error.message}`,
              data: { error: error.message }
            };
          }
          break;

        case 'dashboard_today':
        case 'dashboard_1h':
        case 'dashboard_24h':
        case 'dashboard_7d':
        case 'dashboard_30d':
          const period = testId.replace('dashboard_', '');
          try {
            const response = await adminDashboardService.getAdminDashboard(period, true);
            if (response.data) {
              result = {
                success: true,
                message: `Données récupérées pour ${period}`,
                data: {
                  period: period,
                  adminId: response.data.admin_id,
                  adminName: response.data.admin_name,
                  isTeamLeader: response.data.is_team_leader,
                  hasMetricCards: !!response.data.metric_cards,
                  hasCharts: !!response.data.charts,
                  hasDetailedStats: !!response.data.detailed_stats,
                  metricCardsCount: response.data.metric_cards?.length || 0,
                  chartsCount: Object.keys(response.data.charts || {}).length
                }
              };
            } else {
              result = {
                success: false,
                message: `Pas de données pour ${period}`,
                data: { period: period, response: response }
              };
            }
          } catch (error) {
            result = {
              success: false,
              message: `Erreur pour ${period}: ${error.message}`,
              data: { period: period, error: error.message }
            };
          }
          break;

        case 'data_structure':
          try {
            const response = await adminDashboardService.getAdminDashboard('today', true);
            const data = response.data;
            const hasRequiredFields = !!(
              data &&
              data.timestamp &&
              data.admin_id &&
              data.admin_name &&
              typeof data.is_team_leader === 'boolean' &&
              data.metric_cards &&
              data.charts &&
              data.detailed_stats &&
              data.metadata
            );
            
            result = {
              success: hasRequiredFields,
              message: hasRequiredFields ? 'Structure des données conforme au guide' : 'Structure des données incorrecte',
              data: {
                hasTimestamp: !!data?.timestamp,
                hasAdminId: !!data?.admin_id,
                hasAdminName: !!data?.admin_name,
                hasIsTeamLeader: typeof data?.is_team_leader === 'boolean',
                hasMetricCards: !!data?.metric_cards,
                hasCharts: !!data?.charts,
                hasDetailedStats: !!data?.detailed_stats,
                hasMetadata: !!data?.metadata
              }
            };
          } catch (error) {
            result = {
              success: false,
              message: `Erreur de structure: ${error.message}`,
              data: { error: error.message }
            };
          }
          break;

        case 'metric_cards':
          try {
            const response = await adminDashboardService.getAdminDashboard('today', true);
            const metricCards = response.data?.metric_cards;
            const isValid = Array.isArray(metricCards) && metricCards.length === 3;
            
            const expectedTitles = ['Équipes gérées', 'Membres d\'équipe', 'Progression moyenne'];
            const hasCorrectTitles = expectedTitles.every(title => 
              metricCards?.some(card => card.title.includes(title.split(' ')[0]))
            );
            
            result = {
              success: isValid && hasCorrectTitles,
              message: isValid && hasCorrectTitles ? '3 cartes de métriques conformes trouvées' : 'Cartes de métriques incorrectes',
              data: {
                count: metricCards?.length || 0,
                expectedCount: 3,
                cards: metricCards?.map(card => ({
                  title: card.title,
                  value: card.value,
                  hasIcon: !!card.icon,
                  hasColor: !!card.color,
                  hasTrend: !!card.trend
                })) || []
              }
            };
          } catch (error) {
            result = {
              success: false,
              message: `Erreur cartes métriques: ${error.message}`,
              data: { error: error.message }
            };
          }
          break;

        case 'charts_data':
          try {
            const response = await adminDashboardService.getAdminDashboard('today', true);
            const charts = response.data?.charts;
            const hasEventsChart = charts?.events_distribution;
            const hasTasksChart = charts?.tasks_distribution;
            const isValid = hasEventsChart && hasTasksChart;
            
            result = {
              success: isValid,
              message: isValid ? '2 graphiques circulaires trouvés (événements et tâches)' : 'Graphiques manquants ou incorrects',
              data: {
                hasEventsDistribution: !!hasEventsChart,
                hasTasksDistribution: !!hasTasksChart,
                eventsData: hasEventsChart ? {
                  type: hasEventsChart.type,
                  dataCount: hasEventsChart.data?.length || 0,
                  hasLegend: !!hasEventsChart.legend
                } : null,
                tasksData: hasTasksChart ? {
                  type: hasTasksChart.type,
                  dataCount: hasTasksChart.data?.length || 0,
                  hasLegend: !!hasTasksChart.legend
                } : null
              }
            };
          } catch (error) {
            result = {
              success: false,
              message: `Erreur graphiques: ${error.message}`,
              data: { error: error.message }
            };
          }
          break;

        case 'detailed_stats':
          try {
            const response = await adminDashboardService.getAdminDashboard('today', true);
            const detailedStats = response.data?.detailed_stats;
            const hasTeamManagement = detailedStats?.team_management;
            const hasEventsActivity = detailedStats?.events_activity;
            const hasTasksActivity = detailedStats?.tasks_activity;
            const isValid = hasTeamManagement && hasEventsActivity && hasTasksActivity;
            
            result = {
              success: isValid,
              message: isValid ? 'Statistiques détaillées conformes au guide' : 'Statistiques détaillées manquantes',
              data: {
                hasTeamManagement: !!hasTeamManagement,
                hasEventsActivity: !!hasEventsActivity,
                hasTasksActivity: !!hasTasksActivity,
                teamManagement: hasTeamManagement ? {
                  totalTeams: hasTeamManagement.total_teams,
                  totalMembers: hasTeamManagement.total_team_members,
                  averageProgress: hasTeamManagement.average_progress,
                  hasMostActiveTeam: !!hasTeamManagement.most_active_team
                } : null
              }
            };
          } catch (error) {
            result = {
              success: false,
              message: `Erreur statistiques détaillées: ${error.message}`,
              data: { error: error.message }
            };
          }
          break;

        case 'debug_data':
          try {
            const response = await adminDashboardService.getAdminDebugData();
            result = {
              success: response.success,
              message: response.success ? 'Données de débogage récupérées' : 'Erreur lors du débogage',
              data: {
                success: response.success,
                hasData: !!response.data,
                error: response.error || null
              }
            };
          } catch (error) {
            result = {
              success: false,
              message: `Erreur débogage: ${error.message}`,
              data: { error: error.message }
            };
          }
          break;

        default:
          result = {
            success: false,
            message: 'Test non reconnu',
            data: { testId: testId }
          };
      }

      setTestResults(prev => ({
        ...prev,
        [testId]: result
      }));

      return result;
    } catch (error) {
      const errorResult = {
        success: false,
        message: `Erreur inattendue: ${error.message}`,
        data: { error: error.message }
      };
      
      setTestResults(prev => ({
        ...prev,
        [testId]: errorResult
      }));

      return errorResult;
    }
  };

  const runAllTests = async () => {
    setIsRunning(true);
    setTestResults({});
    
    toast.info('🧪 Démarrage des tests du dashboard admin...');
    
    for (const test of tests) {
      setCurrentTest(test.id);
      await runTest(test.id);
      // Petite pause entre les tests
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    setCurrentTest('');
    setIsRunning(false);
    
    const successCount = Object.values(testResults).filter(r => r.success).length;
    const totalCount = tests.length;
    
    if (successCount === totalCount) {
      toast.success(`✅ Tous les tests admin réussis (${successCount}/${totalCount})`);
    } else {
      toast.warning(`⚠️ ${successCount}/${totalCount} tests admin réussis`);
    }
  };

  const getStatusIcon = (testId) => {
    const result = testResults[testId];
    if (!result) {
      if (currentTest === testId) {
        return <RefreshCw className="w-4 h-4 animate-spin text-blue-500" />;
      }
      return <div className="w-4 h-4 bg-gray-300 rounded-full"></div>;
    }
    
    return result.success 
      ? <CheckCircle className="w-4 h-4 text-green-500" />
      : <AlertTriangle className="w-4 h-4 text-red-500" />;
  };

  const getStatusColor = (testId) => {
    const result = testResults[testId];
    if (!result) return 'border-gray-200';
    return result.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50';
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        {/* En-tête */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <div className="bg-blue-500 p-3 rounded-lg">
              <Shield className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Testeur Dashboard Admin BI</h1>
              <p className="text-gray-600 text-sm">Validation de l'implémentation selon le guide détaillé</p>
            </div>
          </div>

          <button
            onClick={runAllTests}
            disabled={isRunning}
            className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 transition-colors"
          >
            {isRunning ? 'Tests en cours...' : 'Lancer tous les tests'}
          </button>
        </div>

        {/* Résultats des tests */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {tests.map(test => (
            <div key={test.id} className={`border rounded-lg p-4 ${getStatusColor(test.id)}`}>
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  {getStatusIcon(test.id)}
                  <h3 className="font-medium text-gray-900">{test.name}</h3>
                </div>
                <button
                  onClick={() => runTest(test.id)}
                  disabled={isRunning}
                  className="text-xs px-2 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 disabled:opacity-50"
                >
                  Tester
                </button>
              </div>
              
              <p className="text-sm text-gray-600 mb-2">{test.description}</p>
              
              {testResults[test.id] && (
                <div className="text-xs">
                  <p className={`font-medium ${testResults[test.id].success ? 'text-green-700' : 'text-red-700'}`}>
                    {testResults[test.id].message}
                  </p>
                  
                  {testResults[test.id].data && (
                    <details className="mt-2">
                      <summary className="cursor-pointer text-blue-600 hover:text-blue-800">
                        Voir les détails
                      </summary>
                      <pre className="mt-1 bg-gray-100 p-2 rounded text-xs overflow-auto max-h-32">
                        {JSON.stringify(testResults[test.id].data, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Résumé */}
        {Object.keys(testResults).length > 0 && (
          <div className="mt-8 bg-white rounded-lg shadow-sm p-6 border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Résumé des Tests Admin</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-500">
                  {Object.values(testResults).filter(r => r.success).length}
                </div>
                <div className="text-sm text-gray-600">Réussis</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-500">
                  {Object.values(testResults).filter(r => !r.success).length}
                </div>
                <div className="text-sm text-gray-600">Échoués</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-500">
                  {Object.keys(testResults).length}
                </div>
                <div className="text-sm text-gray-600">Total</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-500">
                  {Math.round((Object.values(testResults).filter(r => r.success).length / Object.keys(testResults).length) * 100)}%
                </div>
                <div className="text-sm text-gray-600">Succès</div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminDashboardTester;
