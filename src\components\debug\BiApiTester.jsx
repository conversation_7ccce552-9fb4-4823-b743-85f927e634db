import React, { useState } from 'react';
import { Refresh<PERSON><PERSON>, Bug, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { toast } from 'react-toastify';
import biService from '@/services/biService';

/**
 * Composant de test pour vérifier tous les endpoints BI selon la documentation backend
 */
const BiApiTester = () => {
  const [results, setResults] = useState({});
  const [loading, setLoading] = useState({});
  const [selectedPeriod, setSelectedPeriod] = useState('today');

  const periods = ['today', '1h', '24h', '7d', '30d'];

  // Test du tableau de bord principal avec filtres
  const testDashboard = async (period) => {
    const testKey = `dashboard_${period}`;
    setLoading(prev => ({ ...prev, [testKey]: true }));

    try {
      console.log(`Test du tableau de bord pour la période: ${period}`);
      const response = await biService.getSuperAdminDashboard(period, true);
      
      setResults(prev => ({
        ...prev,
        [testKey]: {
          success: response.success,
          data: response.data,
          error: response.error,
          timestamp: new Date().toISOString(),
          endpoint: `/api/bi/super-admin/dashboard/?period=${period}&manual_refresh=true`
        }
      }));

      if (response.success) {
        toast.success(`✅ Dashboard ${period} - OK`);
      } else {
        toast.error(`❌ Dashboard ${period} - Erreur: ${response.error}`);
      }
    } catch (error) {
      setResults(prev => ({
        ...prev,
        [testKey]: {
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
          endpoint: `/api/bi/super-admin/dashboard/?period=${period}&manual_refresh=true`
        }
      }));
      toast.error(`❌ Dashboard ${period} - Exception: ${error.message}`);
    } finally {
      setLoading(prev => ({ ...prev, [testKey]: false }));
    }
  };

  // Test des statistiques en temps réel
  const testRealTimeStats = async () => {
    const testKey = 'realtime_stats';
    setLoading(prev => ({ ...prev, [testKey]: true }));

    try {
      console.log('Test des statistiques en temps réel');
      const response = await biService.getRealTimeLoginStats();
      
      setResults(prev => ({
        ...prev,
        [testKey]: {
          success: response.success,
          data: response.data,
          error: response.error,
          timestamp: new Date().toISOString(),
          endpoint: '/api/bi/realtime/login-stats/'
        }
      }));

      if (response.success) {
        toast.success('✅ Statistiques temps réel - OK');
      } else {
        toast.error(`❌ Statistiques temps réel - Erreur: ${response.error}`);
      }
    } catch (error) {
      setResults(prev => ({
        ...prev,
        [testKey]: {
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
          endpoint: '/api/bi/realtime/login-stats/'
        }
      }));
      toast.error(`❌ Statistiques temps réel - Exception: ${error.message}`);
    } finally {
      setLoading(prev => ({ ...prev, [testKey]: false }));
    }
  };

  // Test des données de débogage
  const testDebugData = async () => {
    const testKey = 'debug_data';
    setLoading(prev => ({ ...prev, [testKey]: true }));

    try {
      console.log('Test des données de débogage');
      const response = await biService.getDebugLoginData();
      
      setResults(prev => ({
        ...prev,
        [testKey]: {
          success: response.success,
          data: response.data,
          error: response.error,
          timestamp: new Date().toISOString(),
          endpoint: '/api/bi/debug/login-data/'
        }
      }));

      if (response.success) {
        toast.success('✅ Données de débogage - OK');
      } else {
        toast.error(`❌ Données de débogage - Erreur: ${response.error}`);
      }
    } catch (error) {
      setResults(prev => ({
        ...prev,
        [testKey]: {
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
          endpoint: '/api/bi/debug/login-data/'
        }
      }));
      toast.error(`❌ Données de débogage - Exception: ${error.message}`);
    } finally {
      setLoading(prev => ({ ...prev, [testKey]: false }));
    }
  };

  // Test des métriques générales
  const testMetrics = async () => {
    const testKey = 'metrics';
    setLoading(prev => ({ ...prev, [testKey]: true }));

    try {
      console.log('Test des métriques générales');
      const response = await biService.getMetrics();
      
      setResults(prev => ({
        ...prev,
        [testKey]: {
          success: response.success,
          data: response.data,
          error: response.error,
          timestamp: new Date().toISOString(),
          endpoint: '/api/bi/metrics/'
        }
      }));

      if (response.success) {
        toast.success('✅ Métriques générales - OK');
      } else {
        toast.error(`❌ Métriques générales - Erreur: ${response.error}`);
      }
    } catch (error) {
      setResults(prev => ({
        ...prev,
        [testKey]: {
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
          endpoint: '/api/bi/metrics/'
        }
      }));
      toast.error(`❌ Métriques générales - Exception: ${error.message}`);
    } finally {
      setLoading(prev => ({ ...prev, [testKey]: false }));
    }
  };

  // Test des données historiques
  const testHistoricalData = async () => {
    const testKey = 'historical_data';
    setLoading(prev => ({ ...prev, [testKey]: true }));

    try {
      console.log('Test des données historiques');
      const response = await biService.getHistoricalData('active_users', '7d');
      
      setResults(prev => ({
        ...prev,
        [testKey]: {
          success: response.success,
          data: response.data,
          error: response.error,
          timestamp: new Date().toISOString(),
          endpoint: '/api/bi/historical-data/?data_type=active_users&period=7d'
        }
      }));

      if (response.success) {
        toast.success('✅ Données historiques - OK');
      } else {
        toast.error(`❌ Données historiques - Erreur: ${response.error}`);
      }
    } catch (error) {
      setResults(prev => ({
        ...prev,
        [testKey]: {
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
          endpoint: '/api/bi/historical-data/?data_type=active_users&period=7d'
        }
      }));
      toast.error(`❌ Données historiques - Exception: ${error.message}`);
    } finally {
      setLoading(prev => ({ ...prev, [testKey]: false }));
    }
  };

  // Test de toutes les périodes
  const testAllPeriods = async () => {
    toast.info('🧪 Test de toutes les périodes en cours...');
    for (const period of periods) {
      await testDashboard(period);
      // Attendre un peu entre chaque test
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    toast.success('✅ Test de toutes les périodes terminé');
  };

  // Test de tous les endpoints
  const testAllEndpoints = async () => {
    toast.info('🧪 Test de tous les endpoints en cours...');
    await testDashboard('today');
    await testRealTimeStats();
    await testDebugData();
    await testMetrics();
    await testHistoricalData();
    toast.success('✅ Test de tous les endpoints terminé');
  };

  const getStatusIcon = (result) => {
    if (!result) return <AlertCircle className="w-4 h-4 text-gray-400" />;
    if (result.success) return <CheckCircle className="w-4 h-4 text-green-500" />;
    return <XCircle className="w-4 h-4 text-red-500" />;
  };

  const getStatusColor = (result) => {
    if (!result) return 'border-gray-200';
    if (result.success) return 'border-green-200 bg-green-50';
    return 'border-red-200 bg-red-50';
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* En-tête */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <div className="bg-purple-500 p-3 rounded-lg">
              <Bug className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Testeur API BI</h1>
              <p className="text-gray-600 text-sm">Test des endpoints selon la documentation backend</p>
            </div>
          </div>

          {/* Boutons de test */}
          <div className="flex flex-wrap gap-2 mb-4">
            <button
              onClick={testAllEndpoints}
              className="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors"
            >
              Tester tous les endpoints
            </button>
            <button
              onClick={testAllPeriods}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              Tester toutes les périodes
            </button>
            <button
              onClick={() => testDashboard(selectedPeriod)}
              className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
            >
              Tester période sélectionnée
            </button>
          </div>

          {/* Sélecteur de période */}
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-gray-700">Période:</label>
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm"
            >
              {periods.map(period => (
                <option key={period} value={period}>{period}</option>
              ))}
            </select>
          </div>
        </div>

        {/* Résultats des tests */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {Object.entries(results).map(([testKey, result]) => (
            <div key={testKey} className={`border rounded-lg p-4 ${getStatusColor(result)}`}>
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                  {getStatusIcon(result)}
                  <h3 className="font-medium text-gray-900">{testKey.replace('_', ' ').toUpperCase()}</h3>
                  {loading[testKey] && <RefreshCw className="w-4 h-4 animate-spin text-blue-500" />}
                </div>
                <span className="text-xs text-gray-500">
                  {result?.timestamp && new Date(result.timestamp).toLocaleTimeString('fr-FR')}
                </span>
              </div>

              <div className="space-y-2">
                <div className="text-xs font-mono bg-gray-100 p-2 rounded">
                  {result?.endpoint || 'Endpoint non défini'}
                </div>

                {result?.error && (
                  <div className="text-sm text-red-600 bg-red-100 p-2 rounded">
                    Erreur: {result.error}
                  </div>
                )}

                {result?.data && (
                  <div className="text-xs">
                    <details>
                      <summary className="cursor-pointer text-blue-600 hover:text-blue-800">
                        Voir les données ({Object.keys(result.data).length} propriétés)
                      </summary>
                      <pre className="mt-2 bg-gray-100 p-2 rounded text-xs overflow-auto max-h-40">
                        {JSON.stringify(result.data, null, 2)}
                      </pre>
                    </details>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Instructions */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="font-medium text-blue-900 mb-2">Instructions d'utilisation</h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• <strong>Tester tous les endpoints</strong> : Teste tous les endpoints disponibles</li>
            <li>• <strong>Tester toutes les périodes</strong> : Teste le dashboard pour toutes les périodes</li>
            <li>• <strong>Tester période sélectionnée</strong> : Teste seulement la période choisie</li>
            <li>• Les résultats s'affichent en temps réel avec les codes de statut</li>
            <li>• Cliquez sur "Voir les données" pour examiner la réponse complète</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default BiApiTester;
