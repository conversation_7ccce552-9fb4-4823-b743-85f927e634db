import React, { useState, useEffect } from 'react';
import { AlertTriangle, CheckCircle, RefreshCw, Bug } from 'lucide-react';
import { toast } from 'react-toastify';
import biService from '@/services/biService';

/**
 * Composant de débogage pour identifier et corriger les erreurs du dashboard
 */
const DashboardErrorFixer = () => {
  const [testResults, setTestResults] = useState({});
  const [isRunning, setIsRunning] = useState(false);
  const [currentTest, setCurrentTest] = useState('');

  const tests = [
    {
      id: 'service_connection',
      name: 'Connexion au service BI',
      description: 'Teste la connexion au service biService'
    },
    {
      id: 'period_today',
      name: 'Données période "today"',
      description: 'Teste les données pour la période aujourd\'hui'
    },
    {
      id: 'period_1h',
      name: '<PERSON>n<PERSON> période "1h"',
      description: 'Teste les données pour la période 1 heure'
    },
    {
      id: 'period_24h',
      name: 'Donn<PERSON> période "24h"',
      description: 'Teste les données pour la période 24 heures'
    },
    {
      id: 'period_7d',
      name: 'Données période "7d"',
      description: 'Teste les données pour la période 7 jours'
    },
    {
      id: 'period_30d',
      name: 'Données période "30d"',
      description: 'Teste les données pour la période 30 jours'
    },
    {
      id: 'data_structure',
      name: 'Structure des données',
      description: 'Vérifie que la structure des données est correcte'
    },
    {
      id: 'metric_cards',
      name: 'Cartes de métriques',
      description: 'Vérifie que les cartes de métriques sont présentes'
    },
    {
      id: 'charts_data',
      name: 'Données des graphiques',
      description: 'Vérifie que les données des graphiques sont correctes'
    }
  ];

  const runTest = async (testId) => {
    setCurrentTest(testId);
    
    try {
      let result = { success: false, message: '', data: null };

      switch (testId) {
        case 'service_connection':
          try {
            const response = await biService.getSuperAdminDashboard('today', true);
            result = {
              success: true,
              message: 'Service BI accessible',
              data: { hasData: !!response.data, success: response.success }
            };
          } catch (error) {
            result = {
              success: false,
              message: `Erreur de connexion: ${error.message}`,
              data: { error: error.message }
            };
          }
          break;

        case 'period_today':
        case 'period_1h':
        case 'period_24h':
        case 'period_7d':
        case 'period_30d':
          const period = testId.replace('period_', '');
          try {
            const response = await biService.getSuperAdminDashboard(period, true);
            if (response.data) {
              result = {
                success: true,
                message: `Données récupérées pour ${period}`,
                data: {
                  period: period,
                  isRealtime: response.data.is_realtime,
                  hasMetricCards: !!response.data.metric_cards,
                  hasCharts: !!response.data.charts,
                  metricCardsCount: response.data.metric_cards?.length || 0,
                  chartsCount: Object.keys(response.data.charts || {}).length
                }
              };
            } else {
              result = {
                success: false,
                message: `Pas de données pour ${period}`,
                data: { period: period, response: response }
              };
            }
          } catch (error) {
            result = {
              success: false,
              message: `Erreur pour ${period}: ${error.message}`,
              data: { period: period, error: error.message }
            };
          }
          break;

        case 'data_structure':
          try {
            const response = await biService.getSuperAdminDashboard('today', true);
            const data = response.data;
            const hasRequiredFields = !!(
              data &&
              data.timestamp &&
              typeof data.is_realtime === 'boolean' &&
              data.metric_cards &&
              data.charts &&
              data.metadata
            );
            
            result = {
              success: hasRequiredFields,
              message: hasRequiredFields ? 'Structure des données correcte' : 'Structure des données incorrecte',
              data: {
                hasTimestamp: !!data?.timestamp,
                hasIsRealtime: typeof data?.is_realtime === 'boolean',
                hasMetricCards: !!data?.metric_cards,
                hasCharts: !!data?.charts,
                hasMetadata: !!data?.metadata
              }
            };
          } catch (error) {
            result = {
              success: false,
              message: `Erreur de structure: ${error.message}`,
              data: { error: error.message }
            };
          }
          break;

        case 'metric_cards':
          try {
            const response = await biService.getSuperAdminDashboard('today', true);
            const metricCards = response.data?.metric_cards;
            const isValid = Array.isArray(metricCards) && metricCards.length > 0;
            
            result = {
              success: isValid,
              message: isValid ? `${metricCards.length} cartes de métriques trouvées` : 'Aucune carte de métrique trouvée',
              data: {
                count: metricCards?.length || 0,
                cards: metricCards?.map(card => ({
                  title: card.title,
                  value: card.value,
                  hasIcon: !!card.icon,
                  hasColor: !!card.color
                })) || []
              }
            };
          } catch (error) {
            result = {
              success: false,
              message: `Erreur cartes métriques: ${error.message}`,
              data: { error: error.message }
            };
          }
          break;

        case 'charts_data':
          try {
            const response = await biService.getSuperAdminDashboard('today', true);
            const charts = response.data?.charts;
            const hasCharts = charts && Object.keys(charts).length > 0;
            
            result = {
              success: hasCharts,
              message: hasCharts ? `${Object.keys(charts).length} graphiques trouvés` : 'Aucun graphique trouvé',
              data: {
                chartNames: Object.keys(charts || {}),
                charts: charts ? Object.entries(charts).map(([name, chart]) => ({
                  name: name,
                  type: chart.type,
                  hasData: !!chart.data,
                  dataCount: Array.isArray(chart.data) ? chart.data.length : 0
                })) : []
              }
            };
          } catch (error) {
            result = {
              success: false,
              message: `Erreur graphiques: ${error.message}`,
              data: { error: error.message }
            };
          }
          break;

        default:
          result = {
            success: false,
            message: 'Test non reconnu',
            data: { testId: testId }
          };
      }

      setTestResults(prev => ({
        ...prev,
        [testId]: result
      }));

      return result;
    } catch (error) {
      const errorResult = {
        success: false,
        message: `Erreur inattendue: ${error.message}`,
        data: { error: error.message }
      };
      
      setTestResults(prev => ({
        ...prev,
        [testId]: errorResult
      }));

      return errorResult;
    }
  };

  const runAllTests = async () => {
    setIsRunning(true);
    setTestResults({});
    
    toast.info('🧪 Démarrage des tests de diagnostic...');
    
    for (const test of tests) {
      setCurrentTest(test.id);
      await runTest(test.id);
      // Petite pause entre les tests
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    setCurrentTest('');
    setIsRunning(false);
    
    const successCount = Object.values(testResults).filter(r => r.success).length;
    const totalCount = tests.length;
    
    if (successCount === totalCount) {
      toast.success(`✅ Tous les tests réussis (${successCount}/${totalCount})`);
    } else {
      toast.warning(`⚠️ ${successCount}/${totalCount} tests réussis`);
    }
  };

  const getStatusIcon = (testId) => {
    const result = testResults[testId];
    if (!result) {
      if (currentTest === testId) {
        return <RefreshCw className="w-4 h-4 animate-spin text-blue-500" />;
      }
      return <div className="w-4 h-4 bg-gray-300 rounded-full"></div>;
    }
    
    return result.success 
      ? <CheckCircle className="w-4 h-4 text-green-500" />
      : <AlertTriangle className="w-4 h-4 text-red-500" />;
  };

  const getStatusColor = (testId) => {
    const result = testResults[testId];
    if (!result) return 'border-gray-200';
    return result.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50';
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        {/* En-tête */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <div className="bg-orange-500 p-3 rounded-lg">
              <Bug className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Correcteur d'Erreurs Dashboard</h1>
              <p className="text-gray-600 text-sm">Diagnostic et correction des erreurs du tableau de bord</p>
            </div>
          </div>

          <button
            onClick={runAllTests}
            disabled={isRunning}
            className="px-6 py-3 bg-orange-500 text-white rounded-lg hover:bg-orange-600 disabled:opacity-50 transition-colors"
          >
            {isRunning ? 'Tests en cours...' : 'Lancer tous les tests'}
          </button>
        </div>

        {/* Résultats des tests */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {tests.map(test => (
            <div key={test.id} className={`border rounded-lg p-4 ${getStatusColor(test.id)}`}>
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  {getStatusIcon(test.id)}
                  <h3 className="font-medium text-gray-900">{test.name}</h3>
                </div>
                <button
                  onClick={() => runTest(test.id)}
                  disabled={isRunning}
                  className="text-xs px-2 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 disabled:opacity-50"
                >
                  Tester
                </button>
              </div>
              
              <p className="text-sm text-gray-600 mb-2">{test.description}</p>
              
              {testResults[test.id] && (
                <div className="text-xs">
                  <p className={`font-medium ${testResults[test.id].success ? 'text-green-700' : 'text-red-700'}`}>
                    {testResults[test.id].message}
                  </p>
                  
                  {testResults[test.id].data && (
                    <details className="mt-2">
                      <summary className="cursor-pointer text-blue-600 hover:text-blue-800">
                        Voir les détails
                      </summary>
                      <pre className="mt-1 bg-gray-100 p-2 rounded text-xs overflow-auto max-h-32">
                        {JSON.stringify(testResults[test.id].data, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Résumé */}
        {Object.keys(testResults).length > 0 && (
          <div className="mt-8 bg-white rounded-lg shadow-sm p-6 border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Résumé des Tests</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-500">
                  {Object.values(testResults).filter(r => r.success).length}
                </div>
                <div className="text-sm text-gray-600">Réussis</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-500">
                  {Object.values(testResults).filter(r => !r.success).length}
                </div>
                <div className="text-sm text-gray-600">Échoués</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-500">
                  {Object.keys(testResults).length}
                </div>
                <div className="text-sm text-gray-600">Total</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-500">
                  {Math.round((Object.values(testResults).filter(r => r.success).length / Object.keys(testResults).length) * 100)}%
                </div>
                <div className="text-sm text-gray-600">Succès</div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DashboardErrorFixer;
