import React from 'react';

/**
 * Composant de modèle d'email pour l'envoi de mot de passe temporaire
 * Ce composant n'est pas rendu dans l'interface utilisateur mais sert de modèle pour générer le contenu HTML des emails
 */
const TempPasswordEmail = ({ name, tempPassword, appUrl }) => {
  return (
    <div style={{ fontFamily: 'Arial, sans-serif', maxWidth: '600px', margin: '0 auto', padding: '20px', border: '1px solid #e0e0e0', borderRadius: '5px' }}>
      <div style={{ textAlign: 'center', marginBottom: '20px' }}>
        <h1 style={{ color: '#6b46c1', margin: '0' }}>Bienvenue dans notre application</h1>
      </div>
      
      <div style={{ marginBottom: '20px' }}>
        <p>Bonjour <strong>{name}</strong>,</p>
        <p>Votre compte a été créé avec succès. Voici vos informations de connexion :</p>
      </div>
      
      <div style={{ background: '#f9f9f9', padding: '15px', borderRadius: '5px', marginBottom: '20px' }}>
        <p><strong>Mot de passe temporaire :</strong> {tempPassword}</p>
      </div>
      
      <div style={{ marginBottom: '20px' }}>
        <p>Pour des raisons de sécurité, vous devrez changer ce mot de passe temporaire lors de votre première connexion.</p>
        <p>Veuillez vous connecter en utilisant le lien ci-dessous :</p>
        <p style={{ textAlign: 'center' }}>
          <a 
            href={appUrl} 
            style={{ 
              display: 'inline-block', 
              padding: '10px 20px', 
              background: '#6b46c1', 
              color: 'white', 
              textDecoration: 'none', 
              borderRadius: '5px',
              fontWeight: 'bold'
            }}
          >
            Se connecter
          </a>
        </p>
      </div>
      
      <div style={{ borderTop: '1px solid #e0e0e0', paddingTop: '20px', fontSize: '12px', color: '#666' }}>
        <p>Si vous n'avez pas demandé la création de ce compte, veuillez ignorer cet email ou contacter l'administrateur.</p>
        <p>Cet email a été envoyé automatiquement, merci de ne pas y répondre.</p>
      </div>
    </div>
  );
};

export default TempPasswordEmail;