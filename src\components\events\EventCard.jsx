import React, { useState } from 'react';
import { Edit, Trash2, CheckCircle, Clock, Calendar, User, Users, MoreVertical, Circle, AlertCircle, Archive } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from '@/components/ui/dropdown-menu';
import EventPermissionGate from './EventPermissionGate';
import { toast } from 'react-toastify';
import eventService from '@/services/eventService';

const EventCard = ({ event, onEdit, onDelete, onUpdateStatus }) => {
    const [loading, setLoading] = useState(false);

    // Fonction pour formater les dates
    const formatDate = (dateString) => {
        if (!dateString) return 'Date inconnue';
        try {
            return new Date(dateString).toLocaleDateString('fr-FR', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        } catch (error) {
            console.error('Erreur de formatage de date:', error, dateString);
            return 'Date inconnue';
        }
    };

    // Fonction pour obtenir la couleur en fonction du statut
    const getStatusColor = (status) => {
        switch (status) {
            case 'completed':
                return 'bg-green-500';
            case 'pending':
                return 'bg-yellow-500';
            case 'archived':
                return 'bg-gray-500';
            default:
                return 'bg-blue-500';
        }
    };

    // Fonction pour obtenir le libellé du statut
    const getStatusLabel = (status) => {
        switch (status) {
            case 'completed':
                return 'Terminé';
            case 'pending':
                return 'En attente';
            case 'archived':
                return 'Archivé';
            default:
                return 'Inconnu';
        }
    };

    // Fonction pour obtenir l'icône du statut
    const getStatusIcon = (status) => {
        switch (status) {
            case 'completed':
                return <CheckCircle className="h-4 w-4 text-green-600" />;
            case 'pending':
                return <Clock className="h-4 w-4 text-yellow-600" />;
            case 'archived':
                return <AlertCircle className="h-4 w-4 text-gray-600" />;
            default:
                return <Circle className="h-4 w-4 text-blue-600" />;
        }
    };

    // Gérer l'archivage d'un événement
    const handleArchive = async () => {
        if (!window.confirm(`Êtes-vous sûr de vouloir archiver l'événement "${event.title}" ?`)) return;

        try {
            setLoading(true);
            await eventService.archiveEvent(event.id);
            toast.success('Événement archivé avec succès');
        } catch (error) {
            console.error('Erreur lors de l\'archivage de l\'événement:', error);
            toast.error(error.message || 'Erreur lors de l\'archivage de l\'événement');
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100">
            {/* En-tête de la carte avec un bandeau de couleur selon le statut */}
            <div className={`${getStatusColor(event.status)} h-2 w-full`}></div>

            <div className="p-6 space-y-4">
                <div className="flex justify-between items-start">
                    <div>
                        <h3 className="text-xl font-semibold text-gray-800 hover:text-indigo-600 transition-colors">{event.title}</h3>
                        <p className="text-sm text-gray-600 mt-1">{event.description || 'Aucune description'}</p>
                    </div>
                </div>

                <div className="pt-4 border-t border-gray-200">
                    <div className="flex items-center space-x-2 mb-3">
                        <div className={`flex-shrink-0 p-1 rounded-full ${getStatusColor(event.status)} bg-opacity-20`}>
                            {getStatusIcon(event.status)}
                        </div>
                        <span className="text-sm font-medium">
                            Statut: {getStatusLabel(event.status)}
                        </span>
                        <div className="ml-auto">
                            {/* Indicateur visuel pour montrer que l'événement est cliquable */}
                            <span className="text-xs text-gray-500">Cliquer pour voir les détails</span>
                        </div>
                    </div>

                    <div className="space-y-3">
                        <div className="flex items-center text-sm text-gray-600">
                            <Calendar className="h-4 w-4 mr-2 text-indigo-500" />
                            <span className="font-medium">Date:</span>
                            <span className="ml-1">
                                {formatDate(event.start_date)} {event.start_date !== event.end_date && ` - ${formatDate(event.end_date)}`}
                            </span>
                        </div>
                        <div className="flex items-center text-sm text-gray-600">
                            <Clock className="h-4 w-4 mr-2 text-indigo-500" />
                            <span className="font-medium">Heure:</span>
                            <span className="ml-1">
                                {event.start_time} - {event.end_time}
                            </span>
                        </div>
                        {event.team_name && (
                            <div className="flex items-center text-sm text-gray-600">
                                <Users className="h-4 w-4 mr-2 text-indigo-500" />
                                <span className="font-medium">Équipe:</span>
                                <span className="ml-1">{event.team_name}</span>
                            </div>
                        )}
                        {event.member_id && (
                            <div className="flex items-center text-sm text-gray-600">
                                <User className="h-4 w-4 mr-2 text-indigo-500" />
                                <span className="font-medium">Assigné à:</span>
                                <span className="ml-1">{event.member_name || event.member_id}</span>
                            </div>
                        )}
                    </div>
                </div>

                {event.note && (
                    <div className="pt-4 border-t border-gray-200">
                        <h4 className="text-sm font-semibold text-gray-700 mb-2">Notes</h4>
                        <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">{event.note}</p>
                    </div>
                )}

                <div className="pt-4 border-t border-gray-200 bg-gray-50 -mx-6 -mb-6 p-6 rounded-b-lg">
                    <div className="flex items-center text-sm text-gray-600 mb-2">
                        <User className="h-4 w-4 mr-2 text-indigo-500" />
                        <span className="font-medium">Créé par:</span>
                        <span className="ml-1">{event.created_by_name || 'Administrateur'}</span>
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                        <Circle className="h-4 w-4 mr-2 text-green-500" />
                        <span className="font-medium">Créé le</span>
                        <span className="ml-1">{formatDate(event.created_at)}</span>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default EventCard;
