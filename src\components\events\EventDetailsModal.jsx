import React from 'react';
import { X, Calendar, Clock, User, Users, Circle, CheckCircle, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import EventPermissionGate from './EventPermissionGate';

const EventDetailsModal = ({ event, onClose, onUpdateStatus }) => {
  // Fonction pour formater les dates
  const formatDate = (dateString) => {
    if (!dateString) return 'Date inconnue';
    try {
      return new Date(dateString).toLocaleDateString('fr-FR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (error) {
      console.error('Erreur de formatage de date:', error, dateString);
      return 'Date inconnue';
    }
  };

  // Fonction pour obtenir la couleur en fonction du statut
  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'bg-green-500';
      case 'pending':
        return 'bg-yellow-500';
      case 'archived':
        return 'bg-gray-500';
      default:
        return 'bg-blue-500';
    }
  };

  // Fonction pour obtenir le libellé du statut
  const getStatusLabel = (status) => {
    switch (status) {
      case 'completed':
        return 'Terminé';
      case 'pending':
        return 'En attente';
      case 'archived':
        return 'Archivé';
      default:
        return 'Inconnu';
    }
  };

  // Fonction pour obtenir l'icône du statut
  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'archived':
        return <AlertCircle className="h-4 w-4 text-gray-600" />;
      default:
        return <Circle className="h-4 w-4 text-blue-600" />;
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg p-6 max-w-md w-full">
        <div className="flex justify-between items-start mb-4">
          <h3 className="text-xl font-bold text-gray-800">{event.title}</h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <div className="space-y-4">
          <div className="bg-gray-50 p-4 rounded-lg">
            <p className="text-gray-600">{event.description || 'Aucune description'}</p>
          </div>

          <div className="flex items-center space-x-2">
            <div className={`flex-shrink-0 p-1 rounded-full ${getStatusColor(event.status)} bg-opacity-20`}>
              {getStatusIcon(event.status)}
            </div>
            <span className="text-sm font-medium">
              Statut: {getStatusLabel(event.status)}
            </span>
            <EventPermissionGate event={event} permissionType="canUpdateStatus">
              <div className="ml-auto">
                {event.status === 'pending' ? (
                  <Button 
                    size="sm" 
                    className="bg-green-600 hover:bg-green-700 text-white"
                    onClick={() => onUpdateStatus(event.id, 'completed')}
                  >
                    Marquer comme terminé
                  </Button>
                ) : event.status === 'completed' ? (
                  <Button 
                    size="sm" 
                    className="bg-yellow-600 hover:bg-yellow-700 text-white"
                    onClick={() => onUpdateStatus(event.id, 'pending')}
                  >
                    Marquer comme en attente
                  </Button>
                ) : null}
              </div>
            </EventPermissionGate>
          </div>

          <div className="space-y-3">
            <div className="flex items-center text-sm text-gray-600">
              <Calendar className="h-4 w-4 mr-2 text-indigo-500" />
              <span className="font-medium">Date:</span>
              <span className="ml-1">
                {formatDate(event.start_date)} {event.start_date !== event.end_date && ` - ${formatDate(event.end_date)}`}
              </span>
            </div>
            <div className="flex items-center text-sm text-gray-600">
              <Clock className="h-4 w-4 mr-2 text-indigo-500" />
              <span className="font-medium">Heure:</span>
              <span className="ml-1">
                {event.start_time} - {event.end_time}
              </span>
            </div>
            {event.team_name && (
              <div className="flex items-center text-sm text-gray-600">
                <Users className="h-4 w-4 mr-2 text-indigo-500" />
                <span className="font-medium">Équipe:</span>
                <span className="ml-1">{event.team_name}</span>
              </div>
            )}
            {event.member_id && (
              <div className="flex items-center text-sm text-gray-600">
                <User className="h-4 w-4 mr-2 text-indigo-500" />
                <span className="font-medium">Assigné à:</span>
                <span className="ml-1">{event.member_name || event.member_id}</span>
              </div>
            )}
          </div>

          {event.note && (
            <div className="pt-4 border-t border-gray-200">
              <h4 className="text-sm font-semibold text-gray-700 mb-2">Notes</h4>
              <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">{event.note}</p>
            </div>
          )}

          <div className="pt-4 border-t border-gray-200">
            <div className="flex items-center text-sm text-gray-600 mb-2">
              <User className="h-4 w-4 mr-2 text-indigo-500" />
              <span className="font-medium">Créé par:</span>
              <span className="ml-1">{event.created_by_name || 'Administrateur'}</span>
            </div>
            <div className="flex items-center text-sm text-gray-600">
              <Circle className="h-4 w-4 mr-2 text-green-500" />
              <span className="font-medium">Créé le</span>
              <span className="ml-1">{formatDate(event.created_at)}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EventDetailsModal;
