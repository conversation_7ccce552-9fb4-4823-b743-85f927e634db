import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useTeam } from '@/contexts/TeamContext';
import { useEvent } from '@/contexts/EventContext';
import { Calendar, Clock, Users, User, FileText, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import SimpleColorPicker from '@/components/ui/SimpleColorPicker';

const EventForm = ({ event, onSubmit, onCancel, isLoading }) => {
    const { user } = useAuth();
    const { teams } = useTeam();
    const { events } = useEvent();

    // État initial du formulaire
    const [formData, setFormData] = useState({
        title: '',
        description: '',
        start_date: '',
        end_date: '',
        start_time: '',
        end_time: '',
        note: '',
        team_id: '',
        member_id: '',
        color: '#CDB4DB' // Couleur par défaut (violet pastel)
    });

    // État pour les erreurs de validation
    const [errors, setErrors] = useState({});

    // État pour les membres de l'équipe sélectionnée
    const [teamMembers, setTeamMembers] = useState([]);

    // État pour vérifier si le titre est en cours de validation
    const [checkingTitle, setCheckingTitle] = useState(false);

    // Charger les données de l'événement si disponible (mode édition)
    useEffect(() => {
        if (event) {
            console.log('EventForm - Chargement des données de l\'événement:', event);

            // Formater les dates pour l'input date
            const formatDateForInput = (dateString) => {
                if (!dateString) return '';
                try {
                    // Vérifier si la date est déjà au format YYYY-MM-DD
                    if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
                        return dateString;
                    }

                    const date = new Date(dateString);
                    if (isNaN(date.getTime())) {
                        console.error('Date invalide:', dateString);
                        return '';
                    }

                    // Format YYYY-MM-DD
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    return `${year}-${month}-${day}`;
                } catch (error) {
                    console.error('Erreur de formatage de date:', error, dateString);
                    return '';
                }
            };

            // Formater l'heure pour l'input time
            const formatTimeForInput = (timeString) => {
                if (!timeString) return '';

                // Si déjà au format HH:MM, retourner tel quel
                if (/^\d{2}:\d{2}$/.test(timeString)) {
                    return timeString;
                }

                try {
                    // Essayer de parser l'heure
                    const [hours, minutes] = timeString.split(':');
                    return `${hours.padStart(2, '0')}:${minutes.padStart(2, '0')}`;
                } catch (error) {
                    console.error('Erreur de formatage d\'heure:', error, timeString);
                    return '';
                }
            };

            const formattedData = {
                title: event.title || '',
                description: event.description || '',
                start_date: formatDateForInput(event.start_date),
                end_date: formatDateForInput(event.end_date),
                start_time: formatTimeForInput(event.start_time),
                end_time: formatTimeForInput(event.end_time),
                note: event.note || '',
                team_id: event.team_id || '',
                member_id: event.member_id || '',
                color: event.color || '#BDE0FE' // Utiliser la couleur de l'événement ou la couleur par défaut
            };

            console.log('EventForm - Données formatées pour le formulaire:', formattedData);
            setFormData(formattedData);
        }
    }, [event]);

    // Mettre à jour les membres disponibles lorsque l'équipe change
    useEffect(() => {
        if (formData.team_id) {
            console.log('EventForm - Chargement des membres pour l\'équipe:', formData.team_id);
            const selectedTeam = teams.find(team => team.id === formData.team_id);
            if (selectedTeam && selectedTeam.members) {
                // Convertir les membres en tableau si nécessaire
                const membersList = Array.isArray(selectedTeam.members)
                    ? selectedTeam.members
                    : Object.entries(selectedTeam.members).map(([id, info]) => ({
                        id,
                        name: typeof info === 'object' ? info.name : info,
                        role: typeof info === 'object' ? info.role : 'employee'
                    }));
                console.log('EventForm - Membres récupérés:', membersList);
                setTeamMembers(membersList);
            } else {
                console.log('EventForm - Aucun membre trouvé pour l\'équipe:', formData.team_id);
                setTeamMembers([]);
            }
        } else {
            setTeamMembers([]);
        }
    }, [formData.team_id, teams]);

    // Effet pour afficher les équipes disponibles au chargement
    useEffect(() => {
        console.log('EventForm - Utilisateur:', user);
        console.log('EventForm - Équipes disponibles:', teams);

        if (teams && teams.length > 0) {
            // Filtrer les équipes selon le rôle de l'utilisateur
            const filteredTeams = teams.filter(team => {
                if (user.role === 'admin') {
                    const isResponsable = team.is_responsable === true || team.is_responsable === 'true';
                    console.log(`EventForm - Équipe ${team.name} (${team.id}), l'utilisateur est responsable: ${isResponsable}`);
                    return isResponsable;
                }
                const isMember = team.is_member === true || team.is_member === 'true';
                console.log(`EventForm - Équipe ${team.name} (${team.id}), l'utilisateur est membre: ${isMember}`);
                return isMember;
            });

            console.log('EventForm - Équipes filtrées pour l\'utilisateur:', filteredTeams);
        }
    }, [teams, user]);

    // Gérer les changements dans le formulaire
    const handleChange = (e) => {
        const { name, value } = e.target;
        console.log(`EventForm - Changement détecté: champ ${name}, valeur: ${value}`);

        setFormData(prev => ({ ...prev, [name]: value }));

        // Effacer l'erreur pour ce champ
        if (errors[name]) {
            setErrors(prev => ({ ...prev, [name]: null }));
        }

        // Log spécifique pour le changement d'équipe
        if (name === 'team_id') {
            console.log(`EventForm - Équipe sélectionnée: ID=${value}`);
            const selectedTeam = teams.find(team => team.id === value);
            console.log('EventForm - Détails de l\'équipe sélectionnée:', selectedTeam);
        }

        // Si l'équipe change, réinitialiser le membre sélectionné
        if (name === 'team_id') {
            setFormData(prev => ({ ...prev, member_id: '' }));

            // Vérifier les chevauchements potentiels avec la nouvelle équipe
            if (value && formData.start_date && formData.end_date && formData.start_time && formData.end_time) {
                setTimeout(() => {
                    const result = checkEventExists(
                        formData.title.trim(),
                        formData.start_date,
                        formData.end_date,
                        formData.start_time,
                        formData.end_time,
                        value
                    );

                    if (result.exists && result.reason === 'overlap') {
                        setErrors(prev => ({ ...prev, team_id: result.message }));
                    }
                }, 300);
            }
        }

        // Vérifier les chevauchements si les dates ou heures changent
        if (['start_date', 'end_date', 'start_time', 'end_time'].includes(name) && formData.team_id) {
            const updatedFormData = { ...formData, [name]: value };

            // Vérifier seulement si toutes les valeurs de date/heure sont remplies
            if (updatedFormData.start_date && updatedFormData.end_date &&
                updatedFormData.start_time && updatedFormData.end_time) {
                setTimeout(() => {
                    const result = checkEventExists(
                        updatedFormData.title.trim(),
                        updatedFormData.start_date,
                        updatedFormData.end_date,
                        updatedFormData.start_time,
                        updatedFormData.end_time,
                        updatedFormData.team_id
                    );

                    if (result.exists && result.reason === 'overlap') {
                        setErrors(prev => ({ ...prev, [name]: result.message }));
                    }
                }, 300);
            }
        }

        // Vérifier le titre en temps réel pour les doublons
        if (name === 'title' && value.trim() !== '') {
            setCheckingTitle(true);
            // Vérifier après un court délai pour éviter trop de vérifications pendant la frappe
            setTimeout(() => {
                const result = checkEventExists(
                    value.trim(),
                    formData.start_date,
                    formData.end_date,
                    formData.start_time,
                    formData.end_time,
                    formData.team_id
                );
                if (result.exists && result.reason === 'title') {
                    setErrors(prev => ({ ...prev, title: result.message }));
                }
                setCheckingTitle(false);
            }, 500);
        }
    };

    // Vérifier si l'événement existe déjà (titre identique dans la même équipe ou chevauchement)
    const checkEventExists = (title, start_date, end_date, start_time, end_time, team_id) => {
        // Si nous sommes en mode édition, exclure l'événement actuel de la vérification
        const existingEvents = event ? events.filter(e => e.id !== event.id) : events;

        // Vérifier si un événement avec le même titre existe déjà DANS LA MÊME ÉQUIPE
        const titleExistsInSameTeam = existingEvents.some(e =>
            e.title.toLowerCase() === title.toLowerCase() && e.team_id === team_id
        );
        if (titleExistsInSameTeam) return {
            exists: true,
            reason: 'title',
            message: 'Cette équipe a déjà un événement avec ce titre'
        };

        // Vérifier si un événement avec les mêmes dates/heures/équipe existe déjà
        const duplicateEvent = existingEvents.find(e => {
            // Même équipe
            const sameTeam = e.team_id === team_id;
            if (!sameTeam) return false;

            // Vérifier si les périodes se chevauchent
            const eventStart = new Date(`${e.start_date}T${e.start_time}`);
            const eventEnd = new Date(`${e.end_date}T${e.end_time}`);
            const newEventStart = new Date(`${start_date}T${start_time}`);
            const newEventEnd = new Date(`${end_date}T${end_time}`);

            // Vérifier si c'est exactement le même événement (mêmes dates et heures)
            const exactSameTime = eventStart.getTime() === newEventStart.getTime() &&
                eventEnd.getTime() === newEventEnd.getTime();

            if (exactSameTime) {
                return true;
            }

            // Chevauchement si:
            // (début1 <= fin2) ET (fin1 >= début2)
            return (eventStart <= newEventEnd) && (eventEnd >= newEventStart);
        });

        if (duplicateEvent) {
            return {
                exists: true,
                reason: 'overlap',
                message: `Un événement existe déjà pour cette équipe aux mêmes dates/heures (${duplicateEvent.title})`
            };
        }

        return { exists: false };
    };


    // Valider le formulaire
    const validateForm = () => {
        const newErrors = {};

        if (!formData.title.trim()) {
            newErrors.title = 'Le titre est requis';
        } else {
            // Vérifier si l'événement existe déjà (titre ou chevauchement)
            const result = checkEventExists(
                formData.title.trim(),
                formData.start_date,
                formData.end_date,
                formData.start_time,
                formData.end_time,
                formData.team_id
            );

            if (result.exists) {
                if (result.reason === 'title') {
                    newErrors.title = result.message;
                } else if (result.reason === 'overlap') {
                    newErrors.team_id = result.message;
                }
            }
        }

        if (!formData.start_date) {
            newErrors.start_date = 'La date de début est requise';
        }

        if (!formData.end_date) {
            newErrors.end_date = 'La date de fin est requise';
        } else if (formData.start_date && formData.end_date && new Date(formData.end_date) < new Date(formData.start_date)) {
            newErrors.end_date = 'La date de fin doit être postérieure à la date de début';
        }

        if (!formData.start_time) {
            newErrors.start_time = 'L\'heure de début est requise';
        }

        if (!formData.end_time) {
            newErrors.end_time = 'L\'heure de fin est requise';
        } else if (formData.start_date === formData.end_date && formData.start_time && formData.end_time) {
            // Vérifier que l'heure de fin est après l'heure de début si c'est le même jour
            if (formData.end_time <= formData.start_time) {
                newErrors.end_time = 'L\'heure de fin doit être postérieure à l\'heure de début';
            }
        }

        // Vérifier qu'une équipe est sélectionnée
        if (!formData.team_id) {
            newErrors.team_id = 'L\'événement doit être assigné à une équipe';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    // Soumettre le formulaire
    const handleSubmit = (e) => {
        e.preventDefault();

        console.log('EventForm - Soumission du formulaire avec les données:', formData);

        if (validateForm()) {
            // Vérifier une dernière fois s'il y a des chevauchements
            const result = checkEventExists(
                formData.title.trim(),
                formData.start_date,
                formData.end_date,
                formData.start_time,
                formData.end_time,
                formData.team_id
            );

            if (result.exists) {
                if (result.reason === 'title' && !event) { // Ne pas vérifier le titre en mode édition
                    setErrors(prev => ({ ...prev, title: result.message }));
                    return;
                } else if (result.reason === 'overlap') {
                    setErrors(prev => ({ ...prev, team_id: result.message }));
                    return;
                }
            }

            // Vérifier si l'utilisateur est admin et s'il a le droit de créer un événement pour cette équipe
            if (user.role === 'admin' && !event) { // Ne pas vérifier en mode édition
                const selectedTeam = teams.find(team => team.id === formData.team_id);
                if (selectedTeam && !(selectedTeam.is_responsable === true || selectedTeam.is_responsable === 'true')) {
                    setErrors(prev => ({ ...prev, team_id: "Vous ne pouvez créer des événements que pour les équipes dont vous êtes responsable" }));
                    return;
                }
            }

            // Vérifier que les dates sont au bon format
            const formatDate = (dateStr) => {
                if (!dateStr) return '';
                // Si déjà au format YYYY-MM-DD, retourner tel quel
                if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
                    return dateStr;
                }
                try {
                    const date = new Date(dateStr);
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    return `${year}-${month}-${day}`;
                } catch (error) {
                    console.error('Erreur de formatage de date:', error);
                    return dateStr;
                }
            };

            // Vérifier que les heures sont au bon format
            const formatTime = (timeStr) => {
                if (!timeStr) return '';
                // Si déjà au format HH:MM, retourner tel quel
                if (/^\d{2}:\d{2}$/.test(timeStr)) {
                    return timeStr;
                }
                try {
                    const [hours, minutes] = timeStr.split(':');
                    return `${hours.padStart(2, '0')}:${minutes.padStart(2, '0')}`;
                } catch (error) {
                    console.error('Erreur de formatage d\'heure:', error);
                    return timeStr;
                }
            };

            // Ajouter les informations supplémentaires nécessaires
            const eventData = {
                ...formData,
                // Formater correctement les dates et heures
                start_date: formatDate(formData.start_date),
                end_date: formatDate(formData.end_date),
                start_time: formatTime(formData.start_time),
                end_time: formatTime(formData.end_time),
                // Conserver le statut existant en mode édition, sinon 'pending'
                status: event ? event.status : 'pending',
                created_by: user.id,
                // Si une équipe est sélectionnée, ajouter son nom
                team_name: formData.team_id ?
                    teams.find(team => team.id === formData.team_id)?.name : undefined,
                // Si un membre est sélectionné, ajouter son nom
                member_name: formData.member_id && formData.team_id ?
                    teamMembers.find(member => member.id === formData.member_id)?.name : undefined,
                // S'assurer que la couleur est incluse
                color: formData.color || '#BDE0FE'
            };

            // S'assurer que les champs sont correctement formatés
            if (eventData.team_id) {
                eventData.team_id = String(eventData.team_id);
            }

            if (eventData.member_id) {
                eventData.member_id = String(eventData.member_id);
            }

            // En mode édition, s'assurer que l'ID est inclus
            if (event && event.id) {
                eventData.id = event.id;
            }

            // Nettoyer les données avant l'envoi
            // Supprimer les propriétés undefined ou null, mais garder la couleur et member_id même si vides
            Object.keys(eventData).forEach(key => {
                if (key !== 'color' && key !== 'member_id' && (eventData[key] === undefined || eventData[key] === null || eventData[key] === '')) {
                    delete eventData[key];
                }
            });

            // S'assurer que la couleur est toujours définie avec une valeur hexadécimale valide
            if (!eventData.color || typeof eventData.color !== 'string' || !eventData.color.startsWith('#')) {
                console.warn('EventForm - Couleur invalide détectée, utilisation de la couleur par défaut');
                eventData.color = '#BDE0FE'; // Couleur par défaut si aucune couleur n'est spécifiée
            }

            // Mettre à jour la variable globale avec la couleur finale
            if (event && event.id) {
                if (typeof window.eventColors === 'undefined') {
                    window.eventColors = {};
                    console.log('EventForm - Variable globale eventColors initialisée');
                }

                // Vérifier que la couleur est valide
                if (eventData.color && typeof eventData.color === 'string' && eventData.color.startsWith('#')) {
                    window.eventColors[event.id] = eventData.color;
                    console.log(`EventForm - Couleur finale mise à jour dans la variable globale pour l'événement ${event.id}:`, eventData.color);
                } else {
                    console.warn('EventForm - Format de couleur invalide lors de la soumission:', eventData.color);
                    // Utiliser une des couleurs de la palette
                    const defaultColors = [
                        '#CDB4DB', // Violet pastel
                        '#FFC8DD', // Rose clair pastel
                        '#FFAFCC', // Rose vif pastel
                        '#BDE0FE', // Bleu clair pastel
                        '#A2D2FF'  // Bleu pastel
                    ];
                    // Utiliser l'ID de l'événement pour choisir une couleur de manière déterministe
                    const colorIndex = event.id ? Math.abs(event.id.toString().split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)) % defaultColors.length : 0;
                    eventData.color = defaultColors[colorIndex];
                    window.eventColors[event.id] = eventData.color;
                }
            }

            // S'assurer que member_id est explicitement défini
            // Si c'est une chaîne vide, cela signifie "tous les membres de l'équipe"
            if (eventData.member_id === undefined) {
                eventData.member_id = '';
            }

            // Forcer la couleur à être une propriété explicite de l'objet
            Object.defineProperty(eventData, 'color', {
                value: eventData.color,
                enumerable: true,
                writable: true,
                configurable: true
            });

            // Log spécifique pour les propriétés problématiques
            console.log('EventForm - Couleur finale (après validation):', eventData.color);
            console.log('EventForm - Member ID final:', eventData.member_id);

            console.log('EventForm - Données de l\'événement avant soumission:', eventData);
            console.log('EventForm - Mode:', event ? 'édition' : 'création');

            // Déclencher un événement pour informer les autres composants de la mise à jour de couleur
            if (event && event.id) {
                try {
                    if (window.dispatchEvent) {
                        console.log(`EventForm - Déclenchement de l'événement eventColorUpdated avant soumission pour ${event.id} avec la couleur ${eventData.color}`);
                        window.dispatchEvent(new CustomEvent('eventColorUpdated', {
                            detail: {
                                eventId: event.id,
                                color: eventData.color,
                                timestamp: Date.now(),
                                source: 'EventForm-beforeSubmit'
                            }
                        }));
                    }
                } catch (error) {
                    console.error('Erreur lors du déclenchement de l\'événement:', error);
                }
            }

            // Appeler la fonction de soumission
            onSubmit(eventData);

            // Émettre un événement après la soumission pour s'assurer que tous les composants sont mis à jour
            if (event && event.id) {
                setTimeout(() => {
                    try {
                        if (window.dispatchEvent) {
                            console.log(`EventForm - Déclenchement de l'événement eventColorUpdated après soumission pour ${event.id} avec la couleur ${eventData.color}`);
                            window.dispatchEvent(new CustomEvent('eventColorUpdated', {
                                detail: {
                                    eventId: event.id,
                                    color: eventData.color,
                                    timestamp: Date.now(),
                                    source: 'EventForm-afterSubmit'
                                }
                            }));

                            // Forcer un rafraîchissement du composant parent
                            setTimeout(() => {
                                if (window.dispatchEvent) {
                                    window.dispatchEvent(new Event('refreshCalendar'));
                                }
                            }, 200);
                        }
                    } catch (dispatchError) {
                        console.error('EventForm - Erreur lors de l\'émission de l\'événement après soumission:', dispatchError);
                    }
                }, 100);
            }
        }
    };

    return (
        <form onSubmit={handleSubmit} className="space-y-4 bg-gradient-to-br from-white to-indigo-50 p-4 rounded-lg shadow-sm max-h-[70vh] overflow-y-auto">
            <div className="space-y-3">
                <div>
                    <Label htmlFor="title" className="flex items-center gap-2 text-indigo-700 font-medium mb-1">
                        <FileText className="h-4 w-4 text-indigo-600" />
                        Titre de l'événement
                    </Label>
                    <Input
                        id="title"
                        name="title"
                        value={formData.title}
                        onChange={handleChange}
                        placeholder="Réunion d'équipe, Formation, etc."
                        className={`transition-all duration-200 border-2 focus:ring-2 focus:ring-indigo-400 focus:border-indigo-400 ${errors.title ? 'border-red-400 bg-red-50' : 'border-indigo-200 hover:border-indigo-300'}`}
                    />
                    {errors.title && (
                        <p className="text-red-500 text-xs mt-1 flex items-center bg-red-50 p-1 rounded-md">
                            <AlertCircle className="h-3 w-3 mr-1 flex-shrink-0" />
                            {errors.title}
                        </p>
                    )}
                </div>

                <div>
                    <Label htmlFor="description" className="flex items-center gap-2 text-indigo-700 font-medium mb-1">
                        <FileText className="h-4 w-4 text-indigo-600" />
                        Description
                    </Label>
                    <textarea
                        id="description"
                        name="description"
                        value={formData.description}
                        onChange={handleChange}
                        placeholder="Description détaillée de l'événement"
                        className="w-full px-3 py-2 border-2 border-indigo-200 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-400 focus:border-indigo-400 hover:border-indigo-300 transition-all duration-200"
                        rows="2"
                    />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <div className="bg-white p-2 rounded-md shadow-sm">
                        <Label htmlFor="start_date" className="flex items-center gap-2 text-indigo-700 font-medium mb-1">
                            <Calendar className="h-4 w-4 text-indigo-600" />
                            Date de début
                        </Label>
                        <Input
                            type="date"
                            id="start_date"
                            name="start_date"
                            value={formData.start_date}
                            onChange={handleChange}
                            className={`transition-all duration-200 border-2 focus:ring-2 focus:ring-indigo-400 focus:border-indigo-400 ${errors.start_date ? 'border-red-400 bg-red-50' : 'border-indigo-200 hover:border-indigo-300'}`}
                        />
                        {errors.start_date && (
                            <p className="text-red-500 text-xs mt-1 flex items-center bg-red-50 p-1 rounded-md">
                                <AlertCircle className="h-3 w-3 mr-1 flex-shrink-0" />
                                {errors.start_date}
                            </p>
                        )}
                    </div>

                    <div className="bg-white p-2 rounded-md shadow-sm">
                        <Label htmlFor="end_date" className="flex items-center gap-2 text-indigo-700 font-medium mb-1">
                            <Calendar className="h-4 w-4 text-indigo-600" />
                            Date de fin
                        </Label>
                        <Input
                            type="date"
                            id="end_date"
                            name="end_date"
                            value={formData.end_date}
                            onChange={handleChange}
                            className={`transition-all duration-200 border-2 focus:ring-2 focus:ring-indigo-400 focus:border-indigo-400 ${errors.end_date ? 'border-red-400 bg-red-50' : 'border-indigo-200 hover:border-indigo-300'}`}
                        />
                        {errors.end_date && (
                            <p className="text-red-500 text-xs mt-1 flex items-center bg-red-50 p-1 rounded-md">
                                <AlertCircle className="h-3 w-3 mr-1 flex-shrink-0" />
                                {errors.end_date}
                            </p>
                        )}
                    </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <div className="bg-white p-2 rounded-md shadow-sm">
                        <Label htmlFor="start_time" className="flex items-center gap-2 text-indigo-700 font-medium mb-1">
                            <Clock className="h-4 w-4 text-indigo-600" />
                            Heure de début
                        </Label>
                        <Input
                            type="time"
                            id="start_time"
                            name="start_time"
                            value={formData.start_time}
                            onChange={handleChange}
                            className={`transition-all duration-200 border-2 focus:ring-2 focus:ring-indigo-400 focus:border-indigo-400 ${errors.start_time ? 'border-red-400 bg-red-50' : 'border-indigo-200 hover:border-indigo-300'}`}
                        />
                        {errors.start_time && (
                            <p className="text-red-500 text-xs mt-1 flex items-center bg-red-50 p-1 rounded-md">
                                <AlertCircle className="h-3 w-3 mr-1 flex-shrink-0" />
                                {errors.start_time}
                            </p>
                        )}
                    </div>

                    <div className="bg-white p-2 rounded-md shadow-sm">
                        <Label htmlFor="end_time" className="flex items-center gap-2 text-indigo-700 font-medium mb-1">
                            <Clock className="h-4 w-4 text-indigo-600" />
                            Heure de fin
                        </Label>
                        <Input
                            type="time"
                            id="end_time"
                            name="end_time"
                            value={formData.end_time}
                            onChange={handleChange}
                            className={`transition-all duration-200 border-2 focus:ring-2 focus:ring-indigo-400 focus:border-indigo-400 ${errors.end_time ? 'border-red-400 bg-red-50' : 'border-indigo-200 hover:border-indigo-300'}`}
                        />
                        {errors.end_time && (
                            <p className="text-red-500 text-xs mt-1 flex items-center bg-red-50 p-1 rounded-md">
                                <AlertCircle className="h-3 w-3 mr-1 flex-shrink-0" />
                                {errors.end_time}
                            </p>
                        )}
                    </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <div className="bg-white p-2 rounded-md shadow-sm">
                        <Label htmlFor="team_id" className="flex items-center gap-2 text-indigo-700 font-medium mb-1">
                            <Users className="h-4 w-4 text-indigo-600" />
                            Équipe <span className="text-red-500 ml-1">*</span>
                        </Label>
                        <select
                            id="team_id"
                            name="team_id"
                            value={formData.team_id}
                            onChange={handleChange}
                            className={`w-full px-3 py-2 transition-all duration-200 border-2 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-400 focus:border-indigo-400 appearance-none bg-white ${errors.team_id ? 'border-red-400 bg-red-50' : 'border-indigo-200 hover:border-indigo-300'}`}
                            required
                            style={{ backgroundImage: "url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e\")", backgroundPosition: "right 0.5rem center", backgroundRepeat: "no-repeat", backgroundSize: "1.5em 1.5em", paddingRight: "2.5rem" }}
                        >
                            <option value="">Sélectionner une équipe</option>
                            {teams && teams.length > 0 ? (
                                teams
                                    .filter(team => {
                                        // Si l'utilisateur est admin, montrer uniquement les équipes dont il est responsable
                                        if (user.role === 'admin') {
                                            return team.is_responsable === true || team.is_responsable === 'true';
                                        }
                                        // Pour les autres rôles, montrer les équipes dont ils sont membres
                                        return team.is_member === true || team.is_member === 'true';
                                    })
                                    .map(team => (
                                        <option key={team.id} value={team.id}>
                                            {team.name}
                                        </option>
                                    ))
                            ) : (
                                <option value="" disabled>
                                    Aucune équipe disponible
                                </option>
                            )}
                        </select>
                        {errors.team_id && (
                            <p className="text-red-500 text-xs mt-1 flex items-center bg-red-50 p-1 rounded-md">
                                <AlertCircle className="h-3 w-3 mr-1 flex-shrink-0" />
                                {errors.team_id}
                            </p>
                        )}
                    </div>

                    {formData.team_id && teamMembers.length > 0 && (
                        <div className="bg-white p-2 rounded-md shadow-sm">
                            <Label htmlFor="member_id" className="flex items-center gap-2 text-indigo-700 font-medium mb-1">
                                <User className="h-4 w-4 text-indigo-600" />
                                Membre spécifique (optionnel)
                            </Label>
                            <select
                                id="member_id"
                                name="member_id"
                                value={formData.member_id || ''}
                                onChange={(e) => {
                                    // Gérer explicitement le changement de membre
                                    const memberId = e.target.value;
                                    console.log('EventForm - Membre sélectionné:', memberId);
                                    setFormData(prev => ({
                                        ...prev,
                                        member_id: memberId,
                                        // Si un membre est sélectionné, ajouter son nom
                                        member_name: memberId ?
                                            teamMembers.find(m => m.id === memberId)?.name : undefined
                                    }));
                                    // Effacer l'erreur pour ce champ si elle existe
                                    if (errors.member_id) {
                                        setErrors(prev => ({ ...prev, member_id: null }));
                                    }
                                }}
                                className="w-full px-3 py-2 border-2 border-indigo-200 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-400 focus:border-indigo-400 hover:border-indigo-300 transition-all duration-200"
                            >
                                <option value="">Tous les membres de l'équipe</option>
                                {teamMembers.map(member => (
                                    <option key={member.id} value={member.id}>{member.name}</option>
                                ))}
                            </select>
                        </div>
                    )}
                </div>

                <div className="bg-white p-2 rounded-md shadow-sm">
                    <Label htmlFor="note" className="flex items-center gap-2 text-indigo-700 font-medium mb-1">
                        <FileText className="h-4 w-4 text-indigo-600" />
                        Notes (optionnel)
                    </Label>
                    <textarea
                        id="note"
                        name="note"
                        value={formData.note}
                        onChange={handleChange}
                        placeholder="Informations supplémentaires, instructions, etc."
                        className="w-full px-3 py-2 border-2 border-indigo-200 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-400 focus:border-indigo-400 hover:border-indigo-300 transition-all duration-200"
                        rows="2"
                    />
                </div>

                {/* Sélecteur de couleur simple */}
                <div className="bg-white p-2 rounded-md shadow-sm">
                    <SimpleColorPicker
                        selectedColor={formData.color}
                        onColorChange={(color) => {
                            console.log('EventForm - Couleur sélectionnée via SimpleColorPicker:', color);
                            setFormData(prev => ({ ...prev, color }));

                            // Mettre à jour la variable globale si on est en mode édition
                            if (event && event.id) {
                                if (typeof window.eventColors === 'undefined') {
                                    window.eventColors = {};
                                }
                                window.eventColors[event.id] = color;
                                console.log(`EventForm - Couleur mise à jour dans la variable globale pour l'événement ${event.id}:`, color);
                            }
                        }}
                        label="Couleur de l'événement"
                        className="space-y-2"
                    />
                </div>
            </div>

            <div className="flex justify-end space-x-3 pt-3 border-t border-indigo-100 sticky bottom-0 bg-indigo-50 pb-2">
                <Button
                    type="button"
                    variant="outline"
                    onClick={onCancel}
                    disabled={isLoading}
                    className="border-2 border-gray-300 hover:bg-gray-100 hover:text-gray-800 transition-all duration-200"
                >
                    Annuler
                </Button>
                <Button
                    type="submit"
                    disabled={isLoading}
                    className="bg-indigo-600 hover:bg-indigo-700 text-white transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5"
                >
                    {isLoading ? (
                        <span className="flex items-center">
                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Chargement...
                        </span>
                    ) : event ? 'Mettre à jour' : 'Créer'}
                </Button>
            </div>
        </form>
    );
};

export default EventForm;