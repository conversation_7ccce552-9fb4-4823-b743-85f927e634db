import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useEvent } from '@/contexts/EventContext';
import { useTeam } from '@/contexts/TeamContext';
import { permissionService } from '@/services/permissionService';

/**
 * Composant qui contrôle l'affichage des éléments UI en fonction des permissions de l'utilisateur pour les événements
 * @param {Object} event - L'événement pour lequel vérifier les permissions
 * @param {string} action - L'action à vérifier ('view', 'manage', 'updateStatus', ou permissionType pour compatibilité)
 * @param {React.ReactNode} children - Les éléments à afficher si l'utilisateur a la permission
 * @param {React.ReactNode} fallback - Les éléments à afficher si l'utilisateur n'a pas la permission
 */
const EventPermissionGate = ({ event, action, permissionType, children, fallback = null }) => {
  // Pour la compatibilité avec l'ancien code
  const effectivePermissionType = action || permissionType;
  const { user } = useAuth();

  // Récupérer les équipes pour vérifier les permissions
  const { teams } = useTeam();

  // Vérification de sécurité pour éviter les erreurs si event est null ou undefined
  if (!event) {
    console.log('EventPermissionGate: Aucun événement fourni');
    return fallback;
  }

  // Les super_admin n'ont pas accès aux événements d'équipe
  if (user && user.role === 'super_admin') {
    return fallback;
  }

  // Convertir l'action en type de permission
  let permissionTypeToCheck = effectivePermissionType;

  // Mapper les actions aux types de permission
  if (effectivePermissionType === 'view') permissionTypeToCheck = 'canView';
  if (effectivePermissionType === 'manage') permissionTypeToCheck = 'canManage';
  if (effectivePermissionType === 'updateStatus') permissionTypeToCheck = 'canUpdateStatus';
  if (effectivePermissionType === 'archive') permissionTypeToCheck = 'canArchive';
  if (effectivePermissionType === 'delete') permissionTypeToCheck = 'canDelete';

  // Vérifier d'abord si les propriétés can_manage, can_update_status, etc. existent déjà dans l'événement
  if (event[permissionTypeToCheck] === true) {
    console.log(`Permission ${permissionTypeToCheck} accordée via event.${permissionTypeToCheck}`);
    return <>{children}</>;
  }

  // Trouver l'équipe associée à l'événement si elle existe
  let team = null;
  if (event.team_id && teams.length > 0) {
    team = teams.find(t => t.id === event.team_id);
    console.log('EventPermissionGate - Équipe trouvée:', team ? team.id : 'aucune');
  }

  // Si l'utilisateur est un employé, vérifier s'il est membre de l'équipe
  if (user && user.role === 'employee' && !team && event.team_id) {
    // Vérifier si l'utilisateur a des équipes dans ses données
    if (user.teams && Array.isArray(user.teams) && user.teams.includes(event.team_id)) {
      console.log('EventPermissionGate - Utilisateur est membre de l\'équipe via user.teams:', event.team_id);
      // Créer un objet team minimal avec l'ID de l'équipe
      team = { id: event.team_id, members: [{ id: user.id }] };
    } else {
      console.log('EventPermissionGate - Utilisateur n\'est pas membre de l\'équipe:', event.team_id);
      console.log('EventPermissionGate - Équipes de l\'utilisateur:', user.teams);
    }
  }

  // Utiliser le service de permissions pour vérifier les permissions
  const permissions = permissionService.checkEventPermissions(user, event, team);

  // Logging détaillé pour le débogage
  console.log('EventPermissionGate - Vérification des permissions:', {
    eventId: event.id,
    action: effectivePermissionType,
    permissionType: permissionTypeToCheck,
    permissions
  });

  if (permissions && permissions[permissionTypeToCheck]) {
    console.log(`Permission ${permissionTypeToCheck} accordée via permissionService.checkEventPermissions`);
    return <>{children}</>;
  }

  console.log(`Permission ${permissionTypeToCheck} refusée pour l'événement:`, event.id);
  return fallback;
};

export default EventPermissionGate;