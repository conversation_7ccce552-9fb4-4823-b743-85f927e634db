import React from 'react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { ArrowLeft, Edit, Trash2, Archive, RotateCcw, CheckCircle, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';

const PersonalEventAgenda = ({ events, onEdit, onDelete, onArchive, onUnarchive, onReturnToCalendar, onUpdateStatus }) => {
  // Filtrer et valider les événements avant le tri
  const validEvents = (events || []).filter(event => {
    // Validation plus permissive - on accepte les événements avec au minimum un id et un titre
    if (!event || !event.id || !event.title) {
      console.warn('🚨 Event filtered out due to missing required data:', {
        event: event,
        hasId: !!event?.id,
        hasTitle: !!event?.title,
        hasStartDate: !!event?.start_date,
        startDateValue: event?.start_date,
        hasEndDate: !!event?.end_date,
        endDateValue: event?.end_date
      });
      return false;
    }

    // Si pas de start_date, essayer end_date ou date
    if (!event.start_date && !event.end_date && !event.date) {
      console.warn('🚨 Event filtered out - no date field found:', {
        event: event,
        start_date: event.start_date,
        end_date: event.end_date,
        date: event.date
      });
      return false;
    }

    return true;
  });

  console.log('🔍 DEBUG - PersonalEventAgenda - Événements valides:', validEvents.map(e => ({
    id: e.id,
    title: e.title,
    status: e.status,
    is_archived: e.is_archived,
    start_date: e.start_date
  })));

  // Trier les événements par date de début avec validation
  const sortedEvents = [...validEvents].sort((a, b) => {
    try {
      // Utiliser start_date, end_date ou date selon ce qui est disponible
      const dateA = new Date(a.start_date || a.end_date || a.date);
      const dateB = new Date(b.start_date || b.end_date || b.date);

      // Vérifier si les dates sont valides
      if (isNaN(dateA.getTime()) || isNaN(dateB.getTime())) {
        console.warn('Invalid dates found during sorting:', {
          a: { start_date: a.start_date, end_date: a.end_date, date: a.date },
          b: { start_date: b.start_date, end_date: b.end_date, date: b.date }
        });
        return 0;
      }

      return dateA - dateB;
    } catch (error) {
      console.error('Error sorting events by date:', error);
      return 0;
    }
  });

  // Formater la date pour l'affichage avec validation stricte
  const formatDate = (event) => {
    try {
      // Essayer différents champs de date
      const dateString = event.start_date || event.end_date || event.date;

      if (!dateString) {
        return 'Date non définie';
      }

      // Parser la date de manière sécurisée
      let date;
      if (typeof dateString === 'string' && dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
        // Format YYYY-MM-DD - créer une date locale
        const [year, month, day] = dateString.split('-').map(Number);
        date = new Date(year, month - 1, day);
      } else {
        // Autres formats
        date = new Date(dateString);
      }

      // Vérifier si la date est valide
      if (isNaN(date.getTime())) {
        console.error('Invalid date value:', dateString);
        return 'Date invalide';
      }

      return format(date, 'dd MMMM yyyy', { locale: fr });
    } catch (error) {
      console.error('Error formatting date:', error, 'for event:', event);
      return 'Erreur de date';
    }
  };

  // Formater le statut pour l'affichage
  const formatStatus = (status, isArchived) => {
    // Prioriser le champ status du backend
    if (status === 'archived' || isArchived) {
      return <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded-full text-xs font-medium">Archivé</span>;
    }

    switch (status) {
      case 'pending':
        return <span className="px-2 py-1 bg-yellow-100 text-yellow-600 rounded-full text-xs font-medium">En attente</span>;
      case 'completed':
        return <span className="px-2 py-1 bg-green-100 text-green-600 rounded-full text-xs font-medium">Terminé</span>;
      default:
        return <span className="px-2 py-1 bg-blue-100 text-blue-600 rounded-full text-xs font-medium">Actif</span>;
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm">
      <div className="p-4 border-b border-gray-200 flex justify-between items-center">
        <h2 className="text-lg font-semibold text-gray-800">Agenda des événements</h2>
        <Button
          variant="outline"
          size="sm"
          onClick={onReturnToCalendar}
          className="flex items-center gap-1 text-indigo-600"
        >
          <ArrowLeft className="h-4 w-4" />
          Retour au calendrier
        </Button>
      </div>

      <div className="p-4">
        <p className="text-sm text-gray-500 mb-4">Liste de tous les événements à venir</p>

        {sortedEvents.length === 0 ? (
          <div className="text-center py-8 bg-gray-50 rounded-lg">
            <p className="text-gray-500">Aucun événement trouvé</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    TITRE
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    DATE
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    STATUT
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    ACTIONS
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {sortedEvents.map((event, index) => (
                  <tr key={event.id || `event-${index}`} className={`hover:bg-gray-50 ${(event.status === 'archived' || event.is_archived) ? 'text-gray-400' : ''}`}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900" style={{ textDecoration: (event.status === 'archived' || event.is_archived) ? 'line-through' : 'none' }}>
                        {event.title}
                      </div>
                      {event.description && (
                        <div className="text-xs text-gray-500 mt-1 truncate max-w-xs">
                          {event.description}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(event)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {formatStatus(event.status, event.is_archived)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        {/* Bouton de changement de statut */}
                        {!(event.status === 'archived' || event.is_archived) && onUpdateStatus && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              const newStatus = event.status === 'completed' ? 'pending' : 'completed';
                              onUpdateStatus(event.id, newStatus);
                            }}
                            className={`${event.status === 'completed'
                              ? 'text-yellow-600 hover:text-yellow-800'
                              : 'text-green-600 hover:text-green-800'
                              }`}
                            title={event.status === 'completed' ? 'Marquer comme en attente' : 'Marquer comme terminé'}
                          >
                            {event.status === 'completed' ? (
                              <Clock className="h-4 w-4" />
                            ) : (
                              <CheckCircle className="h-4 w-4" />
                            )}
                          </Button>
                        )}

                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onEdit(event)}
                          className="text-blue-600 hover:text-blue-800"
                          disabled={event.status === 'archived' || event.is_archived}
                          title="Modifier"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>

                        {(event.status === 'archived' || event.is_archived) ? (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onUnarchive(event.id)}
                            className="text-green-600 hover:text-green-800"
                            title="Désarchiver"
                          >
                            <RotateCcw className="h-4 w-4" />
                          </Button>
                        ) : (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onArchive(event.id)}
                            className="text-yellow-600 hover:text-yellow-800"
                            title="Archiver"
                          >
                            <Archive className="h-4 w-4" />
                          </Button>
                        )}

                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onDelete(event.id)}
                          className="text-red-600 hover:text-red-800"
                          title="Supprimer"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default PersonalEventAgenda;
