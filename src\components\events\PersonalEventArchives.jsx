import React from 'react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { ArrowLef<PERSON>, Trash2, RotateCcw, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';

const PersonalEventArchives = ({ events, onDelete, onUnarchive, onReturnToCalendar, loading = false }) => {
  console.log('📁 PersonalEventArchives - Received events:', events?.length || 0, events);

  // Les événements sont déjà filtrés par le backend, on les utilise directement
  const validArchivedEvents = (events || []).filter(event => {
    if (!event || !event.id || !event.title) {
      console.warn('Event filtered out due to missing required data:', event);
      return false;
    }
    return true;
  });

  console.log('📊 PersonalEventArchives - Valid archived events:', validArchivedEvents.length);

  // Trier les événements par date de début avec validation
  const sortedEvents = [...validArchivedEvents].sort((a, b) => {
    try {
      const dateA = new Date(a.start_date);
      const dateB = new Date(b.start_date);

      // Vérifier si les dates sont valides
      if (isNaN(dateA.getTime()) || isNaN(dateB.getTime())) {
        console.warn('Invalid dates found during sorting:', { a: a.start_date, b: b.start_date });
        return 0;
      }

      return dateA - dateB;
    } catch (error) {
      console.error('Error sorting events by date:', error);
      return 0;
    }
  });

  // Formater la date pour l'affichage avec validation stricte
  const formatDate = (dateString) => {
    try {
      if (!dateString) {
        return 'Date non définie';
      }

      // Parser la date de manière sécurisée
      let date;
      if (typeof dateString === 'string' && dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
        // Format YYYY-MM-DD - créer une date locale
        const [year, month, day] = dateString.split('-').map(Number);
        date = new Date(year, month - 1, day);
      } else {
        // Autres formats
        date = new Date(dateString);
      }

      // Vérifier si la date est valide
      if (isNaN(date.getTime())) {
        console.error('Invalid date value:', dateString);
        return 'Date invalide';
      }

      return format(date, 'dd MMMM yyyy', { locale: fr });
    } catch (error) {
      console.error('Error formatting date:', error, 'for value:', dateString);
      return 'Erreur de date';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm">
      <div className="p-4 border-b border-gray-200 flex justify-between items-center">
        <h2 className="text-lg font-semibold text-gray-800">Archives</h2>
        <Button
          variant="outline"
          size="sm"
          onClick={onReturnToCalendar}
          className="flex items-center gap-1 text-indigo-600"
        >
          <ArrowLeft className="h-4 w-4" />
          Retour au calendrier
        </Button>
      </div>

      <div className="p-4">
        <p className="text-sm text-gray-500 mb-4">Liste des événements archivés</p>

        {loading ? (
          <div className="text-center py-8 bg-gray-50 rounded-lg">
            <Loader2 className="h-8 w-8 animate-spin mx-auto text-indigo-600 mb-2" />
            <p className="text-gray-500">Chargement des événements archivés...</p>
          </div>
        ) : sortedEvents.length === 0 ? (
          <div className="text-center py-8 bg-gray-50 rounded-lg">
            <p className="text-gray-500">Aucun événement archivé trouvé</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    TITRE
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    DATE
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    ACTIONS
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {sortedEvents.map((event, index) => (
                  <tr key={event.id || `archived-event-${index}`} className="hover:bg-gray-50 text-gray-400">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-500" style={{ textDecoration: 'line-through' }}>
                        {event.title}
                      </div>
                      {event.description && (
                        <div className="text-xs text-gray-400 mt-1 truncate max-w-xs">
                          {event.description}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(event.start_date)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onUnarchive(event.id)}
                          disabled={loading}
                          className="text-green-600 hover:text-green-800 hover:bg-green-50"
                          title="Désarchiver"
                        >
                          <RotateCcw className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onDelete(event.id)}
                          disabled={loading}
                          className="text-red-600 hover:text-red-800 hover:bg-red-50"
                          title="Supprimer définitivement"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default PersonalEventArchives;
