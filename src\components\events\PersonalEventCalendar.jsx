import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { usePersonalEvent } from '@/contexts/PersonalEventContext';
import { toast } from 'react-toastify';
import { Calendar, momentLocalizer } from 'react-big-calendar';
import moment from 'moment';
import 'moment/locale/fr';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import { getEventStyle } from '@/utils/eventStyleHelper';
import {
  getModernEventStyle,
  FRENCH_CALENDAR_MESSAGES,
  FRENCH_CALENDAR_FORMATS,
  formatEventsForCalendar
} from '@/utils/modernCalendarHelper';

import '@/styles/modern-calendar.css';

// Configuration de moment en français
moment.locale('fr');
const localizer = momentLocalizer(moment);

// Messages personnalisés en français pour le calendrier
const messages = {
  allDay: 'Journée',
  previous: 'Précédent',
  next: 'Suivant',
  today: 'Aujourd\'hui',
  month: 'Mois',
  week: 'Semaine',
  day: 'Jour',
  agenda: 'Agenda',
  date: 'Date',
  time: 'Heure',
  event: 'Événement',
  noEventsInRange: 'Aucun événement dans cette période',
  showMore: total => `+ ${total} événement(s) supplémentaire(s)`
};

const PersonalEventCalendar = ({
  events,
  onEdit,
  onDelete,
  onArchive,
  onUnarchive,
  calendarView = 'month',
  date = new Date()
}) => {
  const { user } = useAuth();
  const [refreshKey, setRefreshKey] = useState(Date.now());
  // Plus besoin de ces états car on ouvre directement le formulaire de modification

  // Forcer un rafraîchissement du composant quand les événements changent
  useEffect(() => {
    if (events && events.length > 0) {
      setRefreshKey(Date.now());
    }
  }, [events]);

  // Note: Suppression des écouteurs d'événements de couleur pour éviter les conflits
  // Les couleurs seront mises à jour directement via les props

  // Formater les événements pour le calendrier avec validation stricte
  const calendarEvents = (events || [])
    .filter(event => {
      // Filtrer AVANT le mapping pour éviter les erreurs
      if (!event || !event.id || !event.title || !event.start_date || !event.end_date) {
        console.warn('❌ Event filtered out due to missing required data:', event);
        return false;
      }



      return true;
    })
    .map(event => {
      // Créer des objets Date avec validation
      try {
        // Parser les dates de manière sécurisée
        const parseEventDate = (dateString) => {
          if (!dateString) throw new Error('Date string is empty');

          // Si c'est déjà un objet Date, le retourner
          if (dateString instanceof Date) return dateString;

          // Si c'est une chaîne au format YYYY-MM-DD, créer une date locale
          if (typeof dateString === 'string' && dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
            const [year, month, day] = dateString.split('-').map(Number);
            return new Date(year, month - 1, day);
          }

          // Sinon, utiliser le constructeur Date normal
          const date = new Date(dateString);
          if (isNaN(date.getTime())) {
            throw new Error('Invalid date');
          }
          return date;
        };

        const startDate = parseEventDate(event.start_date);
        const endDate = parseEventDate(event.end_date);

        // Validation des dates
        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
          throw new Error('Invalid date objects');
        }

        // Ajouter un jour à la date de fin pour l'affichage correct dans le calendrier
        const displayEndDate = new Date(endDate);
        displayEndDate.setDate(displayEndDate.getDate() + 1);

        return {
          id: event.id,
          title: event.title,
          start: startDate,
          end: displayEndDate,
          description: event.description || '',
          status: event.status || 'pending',
          is_archived: event.is_archived || false,
          color: event.color || '#FFAFCC',
          allDay: true,
          resource: {
            ...event,
            // S'assurer que le statut et is_archived sont correctement transmis
            status: event.status || 'pending',
            is_archived: event.is_archived || false
          }
        };
      } catch (error) {
        console.error('Error formatting event dates for event:', event.id, error);
        return null;
      }
    })
    .filter(Boolean); // Filtrer les événements null après le mapping

  console.log('📅 PersonalEventCalendar - Rendering with:', {
    eventsCount: events?.length || 0,
    calendarEventsCount: calendarEvents.length,
    calendarView,
    date
  });

  // Debug détaillé des événements reçus
  console.log('📅 DEBUG - Événements reçus dans PersonalEventCalendar:', events?.map(e => ({
    id: e.id,
    title: e.title,
    status: e.status,
    is_archived: e.is_archived,
    start_date: e.start_date,
    end_date: e.end_date
  })));



  // Debug détaillé des événements formatés pour le calendrier
  console.log('📅 DEBUG - Événements formatés pour le calendrier:', calendarEvents.map(e => ({
    id: e.id,
    title: e.title,
    status: e.status,
    is_archived: e.is_archived,
    resource_status: e.resource?.status,
    resource_is_archived: e.resource?.is_archived
  })));

  // Gérer le clic sur un événement - ouvrir directement le formulaire de modification
  const handleEventClick = (event) => {
    if (onEdit && event.resource) {
      onEdit(event.resource);
    }
  };

  // Personnaliser l'apparence des événements avec les couleurs simples
  const eventStyleGetter = (event) => {
    // Déterminer si l'événement est archivé
    const isArchived = event.resource && (
      event.resource.status === 'archived' ||
      event.resource.is_archived === true
    );

    console.log(`Event ${event.id} - isArchived:`, isArchived, 'status:', event.resource?.status, 'is_archived:', event.resource?.is_archived);

    // Récupérer la couleur de l'événement
    const eventColor = event.color || event.resource?.color || '#3B82F6';

    // Style simple et moderne
    const style = {
      backgroundColor: eventColor,
      border: `2px solid ${eventColor}`,
      borderRadius: '6px',
      color: '#ffffff',
      fontWeight: '600',
      fontSize: '0.75rem',
      cursor: 'pointer',
      padding: '2px 6px',
      textDecoration: isArchived ? 'line-through' : 'none',
      opacity: isArchived ? 0.7 : 1,
      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
    };

    // Ajouter les attributs pour le CSS
    const attributes = {
      'data-color-id': eventColor,
      'data-event-id': event.id
    };

    if (isArchived) {
      attributes['data-archived'] = 'true';
    }

    return {
      style: style,
      className: `personal-event ${isArchived ? 'event-archived' : ''}`.trim(),
      attributes
    };
  };

  // Ces fonctions ne sont plus nécessaires car on ouvre directement le formulaire de modification

  // Gérer le changement de vue du calendrier
  const handleViewChange = (newView) => {
    // Cette fonction sera appelée lorsque l'utilisateur change de vue dans le calendrier
    console.log('Vue du calendrier changée :', newView);
    // Vous pouvez ajouter ici une logique pour informer le composant parent du changement
  };

  // Gérer la navigation dans le calendrier
  const handleNavigate = (newDate) => {
    // Cette fonction sera appelée lorsque l'utilisateur navigue dans le calendrier
    console.log('Navigation du calendrier :', newDate);
    // Vous pouvez ajouter ici une logique pour informer le composant parent du changement
  };

  return (
    <div className="modern-calendar-container">
      <div className="bg-white rounded-xl shadow-lg overflow-hidden">
        <Calendar
          key={`personal-calendar-${refreshKey}`}
          localizer={localizer}
          events={calendarEvents}
          startAccessor="start"
          endAccessor="end"
          style={{ height: 650 }}
          messages={FRENCH_CALENDAR_MESSAGES}
          formats={FRENCH_CALENDAR_FORMATS}
          view={calendarView}
          date={date}
          onView={handleViewChange}
          onNavigate={handleNavigate}
          onSelectEvent={handleEventClick}
          eventPropGetter={eventStyleGetter}
          className="modern-calendar-style hide-toolbar"
          culture="fr"
          toolbar={false}
          popup={true}
          popupOffset={{ x: 30, y: 20 }}
          showMultiDayTimes={true}
          step={30}
          timeslots={2}
        />
      </div>
    </div>
  );
};

export default PersonalEventCalendar;
