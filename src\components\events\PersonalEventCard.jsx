import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Calendar, Clock, MapPin, MoreVertical, Edit, Trash2, Archive, RotateCcw } from 'lucide-react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';

const PersonalEventCard = ({ event, onEdit, onDelete, onArchive, onUnarchive }) => {
  // Formater les dates pour l'affichage
  const formatDate = (dateString) => {
    try {
      const date = new Date(dateString);
      return format(date, 'PPP', { locale: fr });
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Date invalide';
    }
  };

  // Déterminer si l'événement est archivé
  const isArchived = event.is_archived;

  return (
    <Card className={`overflow-hidden ${isArchived ? 'bg-gray-50 border-gray-200' : 'bg-white'}`}>
      <CardHeader className="p-4 pb-2">
        <div className="flex justify-between items-start">
          <CardTitle className={`text-lg font-semibold ${isArchived ? 'text-gray-500 line-through' : 'text-gray-800'}`}>
            {event.title}
          </CardTitle>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <MoreVertical className="h-4 w-4" aria-hidden="false" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {!isArchived && (
                <DropdownMenuItem onClick={() => onEdit(event)} className="text-blue-600 hover:bg-blue-50">
                  <Edit className="mr-2 h-4 w-4" aria-hidden="false" />
                  Modifier
                </DropdownMenuItem>
              )}
              
              <DropdownMenuItem onClick={() => onDelete(event.id)} className="text-red-600 hover:bg-red-50">
                <Trash2 className="mr-2 h-4 w-4" aria-hidden="false" />
                Supprimer
              </DropdownMenuItem>
              
              {!isArchived ? (
                <DropdownMenuItem onClick={() => onArchive(event.id)} className="text-gray-600 hover:bg-gray-50">
                  <Archive className="mr-2 h-4 w-4" aria-hidden="false" />
                  Archiver
                </DropdownMenuItem>
              ) : (
                <DropdownMenuItem onClick={() => onUnarchive(event.id)} className="text-green-600 hover:bg-green-50">
                  <RotateCcw className="mr-2 h-4 w-4" aria-hidden="false" />
                  Désarchiver
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      
      <CardContent className="p-4 pt-2">
        {event.description && (
          <p className={`text-sm mb-3 ${isArchived ? 'text-gray-400 line-through' : 'text-gray-600'}`}>
            {event.description}
          </p>
        )}
        
        <div className="space-y-2">
          <div className={`flex items-center text-xs ${isArchived ? 'text-gray-400' : 'text-gray-600'}`}>
            <Calendar className="h-3 w-3 mr-2 text-gray-400" aria-hidden="false" />
            <span className={isArchived ? 'line-through' : ''}>
              {formatDate(event.start_date)}
              {event.end_date && event.start_date !== event.end_date && ` - ${formatDate(event.end_date)}`}
            </span>
            {event.is_all_day && (
              <Badge variant="outline" className="ml-2 text-xs">Toute la journée</Badge>
            )}
          </div>
          
          {event.location && (
            <div className={`flex items-center text-xs ${isArchived ? 'text-gray-400' : 'text-gray-600'}`}>
              <MapPin className="h-3 w-3 mr-2 text-gray-400" aria-hidden="false" />
              <span className={isArchived ? 'line-through' : ''}>{event.location}</span>
            </div>
          )}
        </div>
      </CardContent>
      
      <CardFooter className="p-4 pt-0 flex justify-between items-center">
        <div className="flex items-center text-xs text-gray-500">
          <Clock className="h-3 w-3 mr-1" aria-hidden="false" />
          <span>Créé le {formatDate(event.created_at)}</span>
        </div>
        
        {isArchived && (
          <Badge variant="outline" className="text-xs bg-gray-100">
            Archivé
          </Badge>
        )}
      </CardFooter>
    </Card>
  );
};

export default PersonalEventCard;
