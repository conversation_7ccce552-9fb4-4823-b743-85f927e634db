import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { CalendarIcon, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'react-toastify';
import BackendColorPicker from '@/components/ui/BackendColorPicker';
import { getColorHex } from '@/utils/backendColors';

const PersonalEventForm = ({ event, onSubmit, onCancel, isSubmitting, personalEvents = [] }) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    start_date: new Date(),
    end_date: new Date(),
    start_time: '09:00',
    end_time: '10:30',
    location: '',
    note: '',
    color: 'bleu_personnel', // Utiliser l'ID de couleur backend par défaut
    is_all_day: false
  });

  const [errors, setErrors] = useState({});
  const [isFormSubmitting, setIsFormSubmitting] = useState(false);

  // Fonction pour parser une date sans problème de timezone
  const parseDate = (dateString) => {
    if (!dateString) return new Date();

    // Si c'est déjà un objet Date, le retourner tel quel
    if (dateString instanceof Date) return dateString;

    // Si c'est une chaîne au format YYYY-MM-DD, créer une date locale
    if (typeof dateString === 'string' && dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
      const [year, month, day] = dateString.split('-').map(Number);
      return new Date(year, month - 1, day); // month - 1 car les mois commencent à 0
    }

    // Sinon, utiliser le constructeur Date normal
    return new Date(dateString);
  };

  // Initialiser le formulaire avec les données de l'événement si disponible
  useEffect(() => {
    if (event) {
      setFormData({
        title: event.title || '',
        description: event.description || '',
        start_date: parseDate(event.start_date),
        end_date: parseDate(event.end_date),
        start_time: event.start_time || '09:00',
        end_time: event.end_time || '10:30',
        location: event.location || '',
        note: event.note || '',
        color: event.color || 'bleu_personnel', // Utiliser l'ID de couleur backend par défaut
        is_all_day: event.is_all_day || false
      });
    }
  }, [event]);

  // Gérer les changements dans les champs du formulaire
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));

    // Effacer l'erreur pour ce champ
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: null }));
    }
  };

  // Gérer les changements de date
  const handleDateChange = (date, field, closePopover = false) => {
    console.log('🗓️ handleDateChange appelé:', { date, field, closePopover });

    if (!date) {
      console.log('🗓️ Date null ou undefined, arrêt');
      return; // Ne rien faire si la date est null ou undefined
    }

    console.log('🗓️ Mise à jour de la date dans le state:', { field, date });
    setFormData(prev => ({
      ...prev,
      [field]: date
    }));

    // Effacer l'erreur pour ce champ
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }

    console.log('🗓️ handleDateChange terminé - PAS de soumission');
  };

  // Vérifier si un événement personnel avec le même titre existe déjà
  const checkPersonalEventTitleExists = (title) => {
    if (!personalEvents || !Array.isArray(personalEvents)) return false;

    // Si nous sommes en mode édition, exclure l'événement actuel de la vérification
    const existingEvents = event ? personalEvents.filter(e => e.id !== event.id) : personalEvents;

    // Vérifier si un événement avec le même titre existe déjà pour cet utilisateur
    return existingEvents.some(e =>
      e.title && e.title.toLowerCase().trim() === title.toLowerCase().trim()
    );
  };

  // Valider le formulaire
  const validateForm = () => {
    const newErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = "Le titre est requis";
    } else if (formData.title.length > 100) {
      newErrors.title = "Le titre ne peut pas dépasser 100 caractères";
    } else if (checkPersonalEventTitleExists(formData.title.trim())) {
      newErrors.title = "Vous avez déjà un événement avec ce titre";
    }

    if (formData.description && formData.description.length > 500) {
      newErrors.description = "La description ne peut pas dépasser 500 caractères";
    }

    if (!formData.start_date) {
      newErrors.start_date = "La date de début est requise";
    }

    if (!formData.end_date) {
      newErrors.end_date = "La date de fin est requise";
    } else if (formData.end_date < formData.start_date) {
      newErrors.end_date = "La date de fin doit être après la date de début";
    }

    setErrors(newErrors);
    return { isValid: Object.keys(newErrors).length === 0, errors: newErrors };
  };

  // État pour suivre si la soumission est intentionnelle
  const [isIntentionalSubmit, setIsIntentionalSubmit] = useState(false);

  // Soumettre le formulaire
  const handleSubmit = (e) => {
    console.log('🚨 handleSubmit appelé!', {
      event: e,
      eventType: e?.type,
      target: e?.target?.tagName,
      submitter: e?.submitter?.tagName,
      submitterType: e?.submitter?.type,
      isIntentionalSubmit: isIntentionalSubmit,
      stack: new Error().stack
    });

    e.preventDefault();

    // Bloquer les soumissions automatiques (pas intentionnelles)
    if (!isIntentionalSubmit) {
      console.log('🚫 Soumission bloquée - soumission automatique détectée');
      setIsIntentionalSubmit(false); // Reset pour la prochaine fois
      return;
    }

    // Reset le flag
    setIsIntentionalSubmit(false);

    // Empêcher les soumissions multiples
    if (isSubmitting || isFormSubmitting) {
      console.log('Soumission bloquée - formulaire déjà en cours de soumission');
      return;
    }

    // Vérifier si le formulaire est valide
    const validationResult = validateForm();
    if (!validationResult.isValid) {
      // Vérifier s'il y a une erreur de duplication de titre
      if (validationResult.errors.title && validationResult.errors.title.includes("déjà")) {
        // Ne pas afficher de toast pour les erreurs de duplication,
        // le message est déjà affiché sous le champ
        console.log('Erreur de duplication détectée:', validationResult.errors.title);
      } else {
        // Pour les autres erreurs, afficher le message générique
        toast.error("Veuillez remplir tous les champs obligatoires correctement");
      }
      return;
    }

    // Vérifier si le titre est vide (double vérification)
    if (!formData.title.trim()) {
      setErrors(prev => ({ ...prev, title: "Le titre est requis" }));
      toast.error("Le titre est requis");
      return;
    }

    // Marquer le formulaire comme en cours de soumission
    setIsFormSubmitting(true);

    // Convertir les dates au format correct pour l'API (YYYY-MM-DD) sans problème de timezone
    const formatDateToLocal = (date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    };

    const formattedData = {
      ...formData,
      start_date: formatDateToLocal(formData.start_date), // Format YYYY-MM-DD local
      end_date: formatDateToLocal(formData.end_date),     // Format YYYY-MM-DD local
      start_time: formData.start_time,
      end_time: formData.end_time,
      note: formData.note,
      color: formData.color
    };

    // Note: Suppression de l'événement eventColorUpdated pour éviter les conflits
    // La couleur sera mise à jour directement via le rafraîchissement des données

    // Soumettre le formulaire de manière sécurisée
    try {
      console.log('Soumission du formulaire avec les données:', formattedData);
      onSubmit(formattedData);
    } catch (error) {
      console.error('Erreur lors de la soumission:', error);
      setIsFormSubmitting(false);
    }
  };

  // Réinitialiser l'état de soumission quand isSubmitting change
  useEffect(() => {
    if (!isSubmitting) {
      setIsFormSubmitting(false);
    }
  }, [isSubmitting]);

  // Empêcher la soumission du formulaire par les touches Enter dans les calendriers
  const handleFormKeyDown = (e) => {
    // Si Enter est pressé et que nous sommes dans un popover de calendrier, empêcher la soumission
    if (e.key === 'Enter') {
      const target = e.target;
      const isInCalendar = target.closest('[role="dialog"]') || target.closest('.calendar') || target.closest('[data-radix-popper-content-wrapper]');

      if (isInCalendar) {
        console.log('🚫 Touche Enter bloquée dans le calendrier');
        e.preventDefault();
        e.stopPropagation();
        return false;
      }
    }
  };

  return (
    <form onSubmit={handleSubmit} onKeyDown={handleFormKeyDown} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="title" className="text-sm font-medium">
          Titre <span className="text-red-500">*</span>
        </Label>
        <Input
          id="title"
          name="title"
          value={formData.title}
          onChange={handleChange}
          placeholder="Titre de l'événement"
          className={cn(errors.title && "border-red-500")}
          disabled={isSubmitting}
        />
        {errors.title && <p className="text-sm text-red-500">{errors.title}</p>}
      </div>

      <div className="space-y-2">
        <Label htmlFor="description" className="text-sm font-medium">
          Description
        </Label>
        <Textarea
          id="description"
          name="description"
          value={formData.description}
          onChange={handleChange}
          placeholder="Description de l'événement"
          className={cn(errors.description && "border-red-500")}
          disabled={isSubmitting}
          rows={3}
        />
        {errors.description && <p className="text-sm text-red-500">{errors.description}</p>}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="start_date_button" className="text-sm font-medium">
            Date de début <span className="text-red-500">*</span>
          </Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                id="start_date_button"
                type="button" // Ajout de type="button" pour éviter la soumission du formulaire
                variant="outline"
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !formData.start_date && "text-muted-foreground",
                  errors.start_date && "border-red-500"
                )}
                disabled={isSubmitting}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {formData.start_date ? (
                  format(formData.start_date, "PPP", { locale: fr })
                ) : (
                  <span>Sélectionner une date</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent
              className="w-auto p-0"
              onOpenAutoFocus={(e) => e.preventDefault()}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  console.log('🚫 Enter bloqué dans PopoverContent start_date');
                  e.preventDefault();
                  e.stopPropagation();
                }
              }}
            >
              <Calendar
                mode="single"
                selected={formData.start_date}
                onSelect={(date) => {
                  console.log('🗓️ Calendar onSelect appelé pour start_date:', date);
                  if (date) {
                    handleDateChange(date, 'start_date');
                  }
                }}
                initialFocus
                locale={fr}
              />
            </PopoverContent>
          </Popover>
          {errors.start_date && <p className="text-sm text-red-500">{errors.start_date}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="end_date_button" className="text-sm font-medium">
            Date de fin <span className="text-red-500">*</span>
          </Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                id="end_date_button"
                type="button" // Ajout de type="button" pour éviter la soumission du formulaire
                variant="outline"
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !formData.end_date && "text-muted-foreground",
                  errors.end_date && "border-red-500"
                )}
                disabled={isSubmitting}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {formData.end_date ? (
                  format(formData.end_date, "PPP", { locale: fr })
                ) : (
                  <span>Sélectionner une date</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent
              className="w-auto p-0"
              onOpenAutoFocus={(e) => e.preventDefault()}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  console.log('🚫 Enter bloqué dans PopoverContent end_date');
                  e.preventDefault();
                  e.stopPropagation();
                }
              }}
            >
              <Calendar
                mode="single"
                selected={formData.end_date}
                onSelect={(date) => {
                  console.log('🗓️ Calendar onSelect appelé pour end_date:', date);
                  if (date) {
                    handleDateChange(date, 'end_date');
                  }
                }}
                initialFocus
                locale={fr}
                disabled={(date) => date < formData.start_date}
              />
            </PopoverContent>
          </Popover>
          {errors.end_date && <p className="text-sm text-red-500">{errors.end_date}</p>}
        </div>
      </div>

      {/* Champs d'heure */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="start_time" className="text-sm font-medium">
            Heure de début
          </Label>
          <Input
            id="start_time"
            name="start_time"
            type="time"
            value={formData.start_time}
            onChange={handleChange}
            disabled={isSubmitting || formData.is_all_day}
            className={cn(errors.start_time && "border-red-500")}
          />
          {errors.start_time && <p className="text-sm text-red-500">{errors.start_time}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="end_time" className="text-sm font-medium">
            Heure de fin
          </Label>
          <Input
            id="end_time"
            name="end_time"
            type="time"
            value={formData.end_time}
            onChange={handleChange}
            disabled={isSubmitting || formData.is_all_day}
            className={cn(errors.end_time && "border-red-500")}
          />
          {errors.end_time && <p className="text-sm text-red-500">{errors.end_time}</p>}
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="note" className="text-sm font-medium">
          Notes (optionnel)
        </Label>
        <Textarea
          id="note"
          name="note"
          value={formData.note}
          onChange={handleChange}
          placeholder="Informations supplémentaires, instructions, etc."
          disabled={isSubmitting}
          rows={3}
        />
      </div>

      {/* Sélecteur de couleur backend amélioré */}
      <BackendColorPicker
        selectedColor={formData.color}
        onColorChange={(color) => {
          setFormData(prev => ({ ...prev, color }));
        }}
        label="Couleur de l'événement"
        type="personal"
        size="default"
        showCustomColor={true}
        className="space-y-2"
      />

      <div className="space-y-2">
        <Label htmlFor="location" className="text-sm font-medium">
          Lieu
        </Label>
        <Input
          id="location"
          name="location"
          value={formData.location}
          onChange={handleChange}
          placeholder="Lieu de l'événement"
          disabled={isSubmitting}
        />
      </div>

      <div className="flex items-center space-x-2">
        <Checkbox
          id="is_all_day"
          name="is_all_day"
          checked={formData.is_all_day}
          onChange={(e) => {
            setFormData(prev => ({ ...prev, is_all_day: e.target.checked }))
          }}
          disabled={isSubmitting}
        />
        <Label
          htmlFor="is_all_day"
          className="text-sm font-medium cursor-pointer"
        >
          Événement sur toute la journée
        </Label>
      </div>

      <div className="flex justify-end space-x-2 pt-4">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isSubmitting || isFormSubmitting}
        >
          Annuler
        </Button>
        <Button
          type="submit"
          disabled={isSubmitting || isFormSubmitting}
          className="bg-indigo-600 hover:bg-indigo-700 text-white"
          onClick={() => {
            console.log('🎯 Bouton submit cliqué - soumission intentionnelle');
            setIsIntentionalSubmit(true);
          }}
        >
          {(isSubmitting || isFormSubmitting) ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {event ? 'Mise à jour...' : 'Création...'}
            </>
          ) : (
            event ? 'Mettre à jour' : 'Créer'
          )}
        </Button>
      </div>
    </form>
  );
};

export default PersonalEventForm;
