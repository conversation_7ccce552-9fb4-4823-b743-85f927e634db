import React from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Edit, Trash2, Archive, RotateCcw, Calendar, MapPin } from 'lucide-react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';

const PersonalEventList = ({ events, onEdit, onDelete, onArchive, onUnarchive }) => {
  // Filtrer et valider les événements
  const validEvents = (events || []).filter(event => {
    if (!event || !event.id || !event.title) {
      console.warn('Event filtered out due to missing required data:', event);
      return false;
    }
    return true;
  });

  // Formater les dates pour l'affichage avec validation stricte
  const formatDate = (dateString) => {
    try {
      if (!dateString) {
        return 'Date non définie';
      }

      // Parser la date de manière sécurisée
      let date;
      if (typeof dateString === 'string' && dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
        // Format YYYY-MM-DD - créer une date locale
        const [year, month, day] = dateString.split('-').map(Number);
        date = new Date(year, month - 1, day);
      } else {
        // Autres formats
        date = new Date(dateString);
      }

      // Vérifier si la date est valide
      if (isNaN(date.getTime())) {
        console.error('Invalid date value:', dateString);
        return 'Date invalide';
      }

      return format(date, 'PPP', { locale: fr });
    } catch (error) {
      console.error('Error formatting date:', error, 'for value:', dateString);
      return 'Erreur de date';
    }
  };

  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[300px]">Titre</TableHead>
            <TableHead>Date</TableHead>
            <TableHead>Lieu</TableHead>
            <TableHead>Statut</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {validEvents.length === 0 ? (
            <TableRow>
              <TableCell colSpan={5} className="text-center py-8 text-gray-500">
                Aucun événement personnel trouvé
              </TableCell>
            </TableRow>
          ) : (
            validEvents.map((event, index) => (
              <TableRow
                key={event.id || `event-${index}`}
                className={event.is_archived ? 'bg-gray-50' : ''}
              >
                <TableCell className="font-medium">
                  <div className={`${event.is_archived ? 'text-gray-500 line-through' : 'text-gray-900'}`}>
                    {event.title}
                  </div>
                  {event.description && (
                    <div className={`text-xs mt-1 line-clamp-1 ${event.is_archived ? 'text-gray-400 line-through' : 'text-gray-500'}`}>
                      {event.description}
                    </div>
                  )}
                </TableCell>

                <TableCell>
                  <div className={`flex items-center ${event.is_archived ? 'text-gray-400 line-through' : 'text-gray-700'}`}>
                    <Calendar className="h-3 w-3 mr-2 text-gray-400" aria-hidden="false" />
                    <span>
                      {formatDate(event.start_date)}
                      {event.end_date && event.start_date !== event.end_date && (
                        <span className="text-gray-400"> - {formatDate(event.end_date)}</span>
                      )}
                    </span>
                  </div>
                  {event.is_all_day && (
                    <Badge variant="outline" className="mt-1 text-xs">
                      Toute la journée
                    </Badge>
                  )}
                </TableCell>

                <TableCell>
                  {event.location ? (
                    <div className={`flex items-center ${event.is_archived ? 'text-gray-400 line-through' : 'text-gray-700'}`}>
                      <MapPin className="h-3 w-3 mr-2 text-gray-400" aria-hidden="true" />
                      <span>{event.location}</span>
                    </div>
                  ) : (
                    <span className="text-gray-400 text-sm">-</span>
                  )}
                </TableCell>

                <TableCell>
                  {event.is_archived ? (
                    <Badge variant="outline" className="bg-gray-100 text-gray-600">
                      Archivé
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200">
                      Actif
                    </Badge>
                  )}
                </TableCell>

                <TableCell className="text-right">
                  <div className="flex justify-end space-x-1">
                    {/* Bouton Modifier - Uniquement pour les événements non archivés */}
                    {!event.is_archived && (
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => onEdit(event)}
                        className="h-8 w-8 text-blue-600 hover:text-blue-800 hover:bg-blue-50"
                        title="Modifier"
                      >
                        <Edit className="h-4 w-4" aria-hidden="false" />
                      </Button>
                    )}

                    {/* Bouton Supprimer - Disponible pour tous les événements */}
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => onDelete(event.id)}
                      className={`h-8 w-8 ${event.is_archived ? 'text-red-700' : 'text-red-600'} hover:text-red-800 hover:bg-red-50`}
                      title="Supprimer"
                    >
                      <Trash2 className="h-4 w-4" aria-hidden="false" />
                    </Button>

                    {/* Bouton Archiver/Désarchiver selon le statut */}
                    {!event.is_archived ? (
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => onArchive(event.id)}
                        className="h-8 w-8 text-gray-600 hover:text-gray-800 hover:bg-gray-50"
                        title="Archiver"
                      >
                        <Archive className="h-4 w-4" aria-hidden="true" />
                      </Button>
                    ) : (
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => onUnarchive(event.id)}
                        className="h-8 w-8 text-green-600 hover:text-green-800 hover:bg-green-50"
                        title="Désarchiver"
                      >
                        <RotateCcw className="h-4 w-4" aria-hidden="true" />
                      </Button>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
};

export default PersonalEventList;
