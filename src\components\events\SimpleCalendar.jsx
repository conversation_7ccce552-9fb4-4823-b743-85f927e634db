import React from 'react';
import { Calendar, momentLocalizer } from 'react-big-calendar';
import moment from 'moment';
import 'moment/locale/fr';
import 'react-big-calendar/lib/css/react-big-calendar.css';

// Configuration de moment en français
moment.locale('fr');
const localizer = momentLocalizer(moment);

// Messages personnalisés en français pour le calendrier
const messages = {
  allDay: 'Journée',
  previous: 'Précédent',
  next: 'Suivant',
  today: 'Aujourd\'hui',
  month: 'Mois',
  week: '<PERSON><PERSON><PERSON>',
  day: 'Jour',
  agenda: 'Agenda',
  date: 'Date',
  time: 'Heure',
  event: 'Événement',
  noEventsInRange: 'Aucun événement dans cette période',
  showMore: total => `+ ${total} événement(s) supplémentaire(s)`
};

const SimpleCalendar = () => {
  // Événements de test
  const events = [
    {
      id: 1,
      title: 'Réunion importante',
      start: new Date(new Date().getFullYear(), new Date().getMonth(), 15),
      end: new Date(new Date().getFullYear(), new Date().getMonth(), 15),
      allDay: true
    },
    {
      id: 2,
      title: 'Rendez-vous médical',
      start: new Date(new Date().getFullYear(), new Date().getMonth(), 20),
      end: new Date(new Date().getFullYear(), new Date().getMonth(), 20),
      allDay: true
    },
    {
      id: 3,
      title: 'Anniversaire',
      start: new Date(new Date().getFullYear(), new Date().getMonth(), 25),
      end: new Date(new Date().getFullYear(), new Date().getMonth(), 25),
      allDay: true
    }
  ];

  return (
    <div className="calendar-container">
      <h2 className="text-xl font-bold mb-4">Calendrier Simple</h2>
      <Calendar
        localizer={localizer}
        events={events}
        startAccessor="start"
        endAccessor="end"
        style={{ height: 600 }}
        messages={messages}
        views={['month', 'week', 'day']}
        defaultView="month"
        className="notion-calendar-style"
      />
    </div>
  );
};

export default SimpleCalendar;
