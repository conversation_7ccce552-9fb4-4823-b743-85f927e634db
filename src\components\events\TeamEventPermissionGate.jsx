import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useEvent } from '@/contexts/EventContext';
import eventService from '@/services/eventService';

/**
 * Composant qui contrôle l'affichage des éléments UI en fonction des permissions de l'utilisateur
 * pour les événements liés à une équipe spécifique
 * 
 * @param {Object} team - L'équipe pour laquelle vérifier les permissions
 * @param {string} permissionType - Le type de permission à vérifier (canView, canManage, canUpdateStatus)
 * @param {React.ReactNode} children - Les éléments à afficher si l'utilisateur a la permission
 * @param {React.ReactNode} fallback - Les éléments à afficher si l'utilisateur n'a pas la permission
 */
const TeamEventPermissionGate = ({ team, permissionType, children, fallback = null }) => {
  const { user } = useAuth();
  
  // Vérifier les permissions pour les événements liés à cette équipe
  const hasPermission = () => {
    if (!user || !team) return false;
    
    const isAdmin = user.role === 'admin';
    const isEmployee = user.role === 'employee';
    
    // Créer un événement fictif lié à cette équipe pour vérifier les permissions
    const mockEvent = {
      team_id: team.id,
      team_name: team.name
    };
    
    const permissions = eventService.checkEventPermissions(user, mockEvent);
    return permissions[permissionType] || false;
  };
  
  if (hasPermission()) {
    return <>{children}</>;
  }
  
  return fallback;
};

export default TeamEventPermissionGate;