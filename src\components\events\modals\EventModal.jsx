import React, { useState } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { AlertCircle } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import EventForm from '../EventForm';

/**
 * Composant modal pour la création ou l'édition d'un événement
 * 
 * @param {boolean} isOpen - État d'ouverture du modal
 * @param {function} onClose - Fonction appelée à la fermeture du modal
 * @param {Object} event - Événement à éditer (null pour la création)
 * @param {function} onSubmit - Fonction appelée à la soumission du formulaire
 * @param {boolean} isLoading - État de chargement
 * @param {string} title - Titre du modal
 * @param {string} description - Description du modal
 */
const EventModal = ({ isOpen, onClose, event, onSubmit, isLoading, title, description }) => {
  const [error, setError] = useState(null);
  // Fonction pour gérer la soumission du formulaire avec gestion d'erreur
  const handleSubmit = async (eventData) => {
    try {
      setError(null);
      await onSubmit(eventData);
    } catch (err) {
      // Vérifier si l'erreur concerne un titre dupliqué
      if (err.message && (err.message.includes('titre existe déjà') || err.message.includes('duplicate') || err.message.includes('already exists'))) {
        setError('Un événement avec ce titre existe déjà. Veuillez choisir un titre différent.');
      } else {
        setError(err.message || 'Une erreur est survenue lors de la création de l\'événement');
      }
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={() => {
      setError(null);
      onClose();
    }}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>
            {description}
          </DialogDescription>
        </DialogHeader>
        
        {error && (
          <Alert variant="destructive" className="mt-2">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        <EventForm
          event={event}
          onSubmit={handleSubmit}
          onCancel={() => {
            setError(null);
            onClose();
          }}
          isLoading={isLoading}
        />
      </DialogContent>
    </Dialog>
  );
};

export default EventModal;