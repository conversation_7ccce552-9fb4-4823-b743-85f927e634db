import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Calendar, Clock, MoreVertical, Edit, Trash2, Archive, RotateCcw, AlertCircle } from 'lucide-react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';

const PersonalTaskCard = ({ task, onEdit, onDelete, onArchive, onUnarchive, onUpdateStatus }) => {
  // Formater les dates pour l'affichage
  const formatDate = (dateString) => {
    try {
      const date = new Date(dateString);
      return format(date, 'PPP', { locale: fr });
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Date invalide';
    }
  };

  // Déterminer si la tâche est archivée
  const isArchived = task.status === 'archived';

  // Obtenir la couleur et le libellé du statut
  const getStatusInfo = (status) => {
    switch (status) {
      case 'a_faire':
        return { color: 'bg-blue-100 text-blue-800 border-blue-200', label: 'À faire' };
      case 'en_cours':
        return { color: 'bg-amber-100 text-amber-800 border-amber-200', label: 'En cours' };
      case 'en_revision':
        return { color: 'bg-purple-100 text-purple-800 border-purple-200', label: 'En révision' };
      case 'achevee':
        return { color: 'bg-green-100 text-green-800 border-green-200', label: 'Achevée' };
      case 'archived':
        return { color: 'bg-gray-100 text-gray-800 border-gray-200', label: 'Archivée' };
      default:
        return { color: 'bg-gray-100 text-gray-800 border-gray-200', label: 'Inconnu' };
    }
  };

  // Obtenir la couleur et le libellé de la priorité
  const getPriorityInfo = (priority) => {
    switch (priority) {
      case 'faible':
        return { color: 'bg-green-100 text-green-800 border-green-200', label: 'Faible' };
      case 'moyenne':
        return { color: 'bg-blue-100 text-blue-800 border-blue-200', label: 'Moyenne' };
      case 'haute':
        return { color: 'bg-red-100 text-red-800 border-red-200', label: 'Haute' };
      default:
        return { color: 'bg-gray-100 text-gray-800 border-gray-200', label: 'Inconnue' };
    }
  };

  const statusInfo = getStatusInfo(task.status);
  const priorityInfo = getPriorityInfo(task.priority);

  // Gérer le changement de statut
  const handleStatusChange = (newStatus) => {
    if (onUpdateStatus && !isArchived) {
      onUpdateStatus(task.id, newStatus);
    }
  };

  return (
    <Card className={`overflow-hidden ${isArchived ? 'bg-gray-50 border-gray-200' : 'bg-white'}`}>
      <CardHeader className="p-4 pb-2">
        <div className="flex justify-between items-start">
          <CardTitle className={`text-lg font-semibold ${isArchived ? 'text-gray-500 line-through' : 'text-gray-800'}`}>
            {task.title}
          </CardTitle>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {!isArchived && (
                <DropdownMenuItem onClick={() => onEdit(task)} className="text-blue-600 hover:bg-blue-50">
                  <Edit className="mr-2 h-4 w-4" />
                  Modifier
                </DropdownMenuItem>
              )}

              <DropdownMenuItem onClick={() => onDelete(task.id)} className="text-red-600 hover:bg-red-50">
                <Trash2 className="mr-2 h-4 w-4" />
                Supprimer
              </DropdownMenuItem>

              {!isArchived ? (
                <DropdownMenuItem onClick={() => onArchive(task.id)} className="text-gray-600 hover:bg-gray-50">
                  <Archive className="mr-2 h-4 w-4" />
                  Archiver
                </DropdownMenuItem>
              ) : (
                <DropdownMenuItem onClick={() => onUnarchive(task.id)} className="text-green-600 hover:bg-green-50">
                  <RotateCcw className="mr-2 h-4 w-4" />
                  Désarchiver
                </DropdownMenuItem>
              )}

              {!isArchived && (
                <>
                  <DropdownMenuItem
                    onClick={() => handleStatusChange('a_faire')}
                    disabled={task.status === 'a_faire'}
                    className="text-blue-600 hover:bg-blue-50"
                  >
                    Marquer comme à faire
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => handleStatusChange('en_cours')}
                    disabled={task.status === 'en_cours'}
                    className="text-amber-600 hover:bg-amber-50"
                  >
                    Marquer comme en cours
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => handleStatusChange('en_revision')}
                    disabled={task.status === 'en_revision'}
                    className="text-purple-600 hover:bg-purple-50"
                  >
                    Marquer comme en révision
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => handleStatusChange('achevee')}
                    disabled={task.status === 'achevee'}
                    className="text-green-600 hover:bg-green-50"
                  >
                    Marquer comme achevée
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="p-4 pt-2">
        {task.description && (
          <p className={`text-sm mb-3 ${isArchived ? 'text-gray-400 line-through' : 'text-gray-600'}`}>
            {task.description}
          </p>
        )}

        <div className="flex flex-wrap gap-2 mb-3">
          <Badge variant="outline" className={statusInfo.color}>
            {statusInfo.label}
          </Badge>
          <Badge variant="outline" className={priorityInfo.color}>
            {priorityInfo.label}
          </Badge>
        </div>

        <div className="space-y-2">
          <div className={`flex items-center text-xs ${isArchived ? 'text-gray-400' : 'text-gray-600'}`}>
            <Calendar className="h-3 w-3 mr-2 text-gray-400" />
            <span className={isArchived ? 'line-through' : ''}>
              {formatDate(task.start_date)} - {formatDate(task.end_date)}
            </span>
          </div>
        </div>
      </CardContent>

      <CardFooter className="p-4 pt-0 flex justify-between items-center">
        <div className="flex items-center text-xs text-gray-500">
          <Clock className="h-3 w-3 mr-1" />
          <span>Créée le {formatDate(task.created_at)}</span>
        </div>

        {task.end_date && new Date(task.end_date) < new Date() && task.status !== 'achevee' && task.status !== 'archived' && (
          <Badge variant="outline" className="text-xs bg-red-100 text-red-800 border-red-200">
            <AlertCircle className="h-3 w-3 mr-1" />
            En retard
          </Badge>
        )}
      </CardFooter>
    </Card>
  );
};

export default PersonalTaskCard;
