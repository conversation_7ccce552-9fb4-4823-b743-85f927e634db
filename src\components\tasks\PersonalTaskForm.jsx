import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { CalendarIcon, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'react-toastify';

const PersonalTaskForm = ({ task, onSubmit, onCancel, isSubmitting, onQuickStatusChange, personalTasks = [] }) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    start_date: new Date(),
    end_date: new Date(),
    status: 'a_faire',
    priority: 'moyenne'
  });

  const [errors, setErrors] = useState({});

  // Fonction utilitaire pour l'affichage des valeurs sélectionnées
  const getPriorityLabel = (priority) => {
    const priorityLabels = {
      'faible': 'Faible',
      'moyenne': 'Moyenne',
      'haute': 'Haute'
    };
    return priorityLabels[priority] || 'Sélectionner une priorité';
  };

  // Initialiser le formulaire avec les données de la tâche si disponible
  useEffect(() => {
    if (task) {
      setFormData({
        title: task.title || '',
        description: task.description || '',
        start_date: task.start_date ? new Date(task.start_date) : new Date(),
        end_date: task.end_date ? new Date(task.end_date) : new Date(),
        status: task.status || 'a_faire',
        priority: task.priority || 'moyenne'
      });
    }
  }, [task]);

  // Gérer les changements dans les champs du formulaire
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Effacer l'erreur pour ce champ
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: null }));
    }
  };

  // Gérer les changements de date
  const handleDateChange = (date, field, closePopover = false) => {
    console.log('🗓️ PersonalTask handleDateChange appelé:', { date, field, closePopover });

    if (!date) {
      console.log('🗓️ PersonalTask Date null ou undefined, arrêt');
      return; // Ne rien faire si la date est null ou undefined
    }

    console.log('🗓️ PersonalTask Mise à jour de la date dans le state:', { field, date });
    setFormData(prev => ({
      ...prev,
      [field]: date
    }));

    // Effacer l'erreur pour ce champ
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }

    console.log('🗓️ PersonalTask handleDateChange terminé - PAS de soumission');
  };

  // Gérer les changements de sélection
  const handleSelectChange = (value, field) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Effacer l'erreur pour ce champ
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };

  // Vérifier si une tâche personnelle avec le même titre existe déjà
  const checkPersonalTaskTitleExists = (title) => {
    if (!personalTasks || !Array.isArray(personalTasks)) return false;

    // Si nous sommes en mode édition, exclure la tâche actuelle de la vérification
    const existingTasks = task ? personalTasks.filter(t => t.id !== task.id) : personalTasks;

    // Vérifier si une tâche avec le même titre existe déjà pour cet utilisateur
    return existingTasks.some(t =>
      t.title && t.title.toLowerCase().trim() === title.toLowerCase().trim()
    );
  };

  // Valider le formulaire
  const validateForm = () => {
    const newErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = "Le titre est requis";
    } else if (formData.title.length > 100) {
      newErrors.title = "Le titre ne peut pas dépasser 100 caractères";
    } else if (checkPersonalTaskTitleExists(formData.title.trim())) {
      newErrors.title = "Vous avez déjà une tâche avec ce titre";
    }

    if (formData.description && formData.description.length > 500) {
      newErrors.description = "La description ne peut pas dépasser 500 caractères";
    }

    if (!formData.start_date) {
      newErrors.start_date = "La date de début est requise";
    }

    if (!formData.end_date) {
      newErrors.end_date = "La date de fin est requise";
    } else if (formData.start_date && formData.end_date < formData.start_date) {
      newErrors.end_date = "La date de fin doit être après la date de début";
    }

    // Le statut et la priorité ont des valeurs par défaut, donc pas besoin de les valider

    setErrors(newErrors);
    return { isValid: Object.keys(newErrors).length === 0, errors: newErrors };
  };

  // Fonction pour gérer le changement rapide de statut
  const handleQuickStatusChange = async (taskId, newStatus) => {
    // Vérifier que la fonction est disponible
    if (onQuickStatusChange && typeof onQuickStatusChange === 'function') {
      // Mettre à jour le formulaire immédiatement pour un feedback visuel
      setFormData(prev => ({ ...prev, status: newStatus }));

      // Appeler la fonction de changement de statut
      await onQuickStatusChange(taskId, newStatus);
    } else {
      console.warn('onQuickStatusChange function not provided');
    }
  };

  // État pour suivre si la soumission est intentionnelle
  const [isIntentionalSubmit, setIsIntentionalSubmit] = useState(false);

  // Soumettre le formulaire
  const handleSubmit = (e) => {
    console.log('🚨 PersonalTask handleSubmit appelé!', {
      event: e,
      eventType: e?.type,
      target: e?.target?.tagName,
      submitter: e?.submitter?.tagName,
      submitterType: e?.submitter?.type,
      isIntentionalSubmit: isIntentionalSubmit,
      stack: new Error().stack
    });

    e.preventDefault();

    // Bloquer les soumissions automatiques (pas intentionnelles)
    if (!isIntentionalSubmit) {
      console.log('🚫 PersonalTask Soumission bloquée - soumission automatique détectée');
      setIsIntentionalSubmit(false); // Reset pour la prochaine fois
      return;
    }

    // Reset le flag
    setIsIntentionalSubmit(false);

    // Vérifier si le formulaire est valide
    const validationResult = validateForm();
    if (!validationResult.isValid) {
      // Vérifier s'il y a une erreur de duplication de titre
      if (validationResult.errors.title && validationResult.errors.title.includes("déjà")) {
        // Ne pas afficher de toast pour les erreurs de duplication,
        // le message est déjà affiché sous le champ
        console.log('Erreur de duplication détectée:', validationResult.errors.title);
      } else {
        // Pour les autres erreurs, afficher le message générique
        toast.error("Veuillez remplir tous les champs obligatoires correctement");
      }
      return;
    }

    // Convertir les dates en format YYYY-MM-DD pour l'API
    try {
      const formattedData = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        priority: formData.priority,
        start_date: formData.start_date instanceof Date ? formData.start_date.toISOString().split('T')[0] : formData.start_date,
        end_date: formData.end_date instanceof Date ? formData.end_date.toISOString().split('T')[0] : formData.end_date
        // Note: Le statut n'est pas inclus car il est géré par les boutons de changement rapide
      };

      console.log('Données formatées pour l\'API (sans statut):', formattedData);

      // Soumettre le formulaire
      onSubmit(formattedData);
    } catch (error) {
      console.error('Erreur lors du formatage des données:', error);
      toast.error('Erreur lors du formatage des dates');
    }
  };

  // Empêcher la soumission du formulaire par les touches Enter dans les calendriers
  const handleFormKeyDown = (e) => {
    // Si Enter est pressé et que nous sommes dans un popover de calendrier, empêcher la soumission
    if (e.key === 'Enter') {
      const target = e.target;
      const isInCalendar = target.closest('[role="dialog"]') || target.closest('.calendar') || target.closest('[data-radix-popper-content-wrapper]');

      if (isInCalendar) {
        console.log('🚫 PersonalTask Touche Enter bloquée dans le calendrier');
        e.preventDefault();
        e.stopPropagation();
        return false;
      }
    }
  };

  return (
    <form onSubmit={handleSubmit} onKeyDown={handleFormKeyDown} className="space-y-6">
      <div className="space-y-3">
        <Label htmlFor="title" className="text-sm font-semibold text-gray-700">
          Titre <span className="text-red-500">*</span>
        </Label>
        <Input
          id="title"
          name="title"
          value={formData.title}
          onChange={handleChange}
          placeholder="Titre de la tâche"
          className={cn(
            "h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500",
            errors.title && "border-red-500 focus:border-red-500 focus:ring-red-500"
          )}
          disabled={isSubmitting}
        />
        {errors.title && <p className="text-sm text-red-500">{errors.title}</p>}
      </div>

      <div className="space-y-3">
        <Label htmlFor="description" className="text-sm font-semibold text-gray-700">
          Description
        </Label>
        <Textarea
          id="description"
          name="description"
          value={formData.description}
          onChange={handleChange}
          placeholder="Description de la tâche"
          className={cn(
            "min-h-[80px] border-gray-300 focus:border-blue-500 focus:ring-blue-500 resize-none",
            errors.description && "border-red-500 focus:border-red-500 focus:ring-red-500"
          )}
          disabled={isSubmitting}
          rows={3}
        />
        {errors.description && <p className="text-sm text-red-500">{errors.description}</p>}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-3">
          <Label htmlFor="start_date_button" className="text-sm font-semibold text-gray-700">
            Date de début <span className="text-red-500">*</span>
          </Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                id="start_date_button"
                type="button"
                variant="outline"
                className={cn(
                  "w-full h-11 justify-start text-left font-normal border-gray-300 hover:border-gray-400",
                  !formData.start_date && "text-muted-foreground",
                  errors.start_date && "border-red-500 hover:border-red-500"
                )}
                disabled={isSubmitting}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {formData.start_date ? (
                  format(formData.start_date, "PPP", { locale: fr })
                ) : (
                  <span>Sélectionner une date</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent
              className="w-auto p-0"
              onOpenAutoFocus={(e) => e.preventDefault()}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  console.log('🚫 PersonalTask Enter bloqué dans PopoverContent start_date');
                  e.preventDefault();
                  e.stopPropagation();
                }
              }}
            >
              <div className="p-2">
                <Calendar
                  mode="single"
                  selected={formData.start_date}
                  onSelect={(date) => {
                    console.log('🗓️ PersonalTask Calendar onSelect appelé pour start_date:', date);
                    if (date) {
                      handleDateChange(date, 'start_date');
                    }
                  }}
                  initialFocus
                  locale={fr}
                />
                <div className="mt-2 flex justify-end">
                  <Button
                    type="button"
                    size="sm"
                    className="bg-indigo-600 hover:bg-indigo-700 text-white"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      document.body.click(); // Ferme le popover
                    }}
                  >
                    Confirmer
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>
          {errors.start_date && <p className="text-sm text-red-500">{errors.start_date}</p>}
        </div>

        <div className="space-y-3">
          <Label htmlFor="end_date_button" className="text-sm font-semibold text-gray-700">
            Date de fin <span className="text-red-500">*</span>
          </Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                id="end_date_button"
                type="button"
                variant="outline"
                className={cn(
                  "w-full h-11 justify-start text-left font-normal border-gray-300 hover:border-gray-400",
                  !formData.end_date && "text-muted-foreground",
                  errors.end_date && "border-red-500 hover:border-red-500"
                )}
                disabled={isSubmitting}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {formData.end_date ? (
                  format(formData.end_date, "PPP", { locale: fr })
                ) : (
                  <span>Sélectionner une date</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent
              className="w-auto p-0"
              onOpenAutoFocus={(e) => e.preventDefault()}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  console.log('🚫 PersonalTask Enter bloqué dans PopoverContent end_date');
                  e.preventDefault();
                  e.stopPropagation();
                }
              }}
            >
              <div className="p-2">
                <Calendar
                  mode="single"
                  selected={formData.end_date}
                  onSelect={(date) => {
                    console.log('🗓️ PersonalTask Calendar onSelect appelé pour end_date:', date);
                    if (date) {
                      handleDateChange(date, 'end_date');
                    }
                  }}
                  initialFocus
                  locale={fr}
                  disabled={(date) => date < formData.start_date}
                />
                <div className="mt-2 flex justify-end">
                  <Button
                    type="button"
                    size="sm"
                    className="bg-indigo-600 hover:bg-indigo-700 text-white"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      document.body.click(); // Ferme le popover
                    }}
                  >
                    Confirmer
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>
          {errors.end_date && <p className="text-sm text-red-500">{errors.end_date}</p>}
        </div>
      </div>

      <div className="space-y-3">
        <Label htmlFor="priority_select" className="text-sm font-semibold text-gray-700">
          Priorité
        </Label>
        <Select
          value={formData.priority}
          onValueChange={(value) => handleSelectChange(value, 'priority')}
          disabled={isSubmitting}
        >
          <SelectTrigger id="priority_select" className="w-full h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500">
            <SelectValue placeholder="Sélectionner une priorité">
              {getPriorityLabel(formData.priority)}
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="faible">Faible</SelectItem>
            <SelectItem value="moyenne">Moyenne</SelectItem>
            <SelectItem value="haute">Haute</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Boutons de changement rapide de statut */}
      {task && onQuickStatusChange && (
        <div className="mt-6 p-5 bg-blue-50 rounded-xl border border-blue-200">
          <h3 className="text-base font-semibold text-blue-900 mb-4">
            Changer le statut de la tâche :
          </h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            <Button
              type="button"
              variant="outline"
              size="default"
              onClick={() => handleQuickStatusChange(task.id, 'a_faire')}
              disabled={formData.status === 'a_faire'}
              className={`h-12 justify-start text-left font-medium transition-all duration-200 ${formData.status === 'a_faire'
                ? 'bg-gray-100 text-gray-500 border-gray-300 cursor-not-allowed'
                : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50 hover:border-gray-400 hover:shadow-sm'
                }`}
            >
              <span className="mr-3 text-lg">📋</span>
              Marquer comme à faire
            </Button>
            <Button
              type="button"
              variant="outline"
              size="default"
              onClick={() => handleQuickStatusChange(task.id, 'en_cours')}
              disabled={formData.status === 'en_cours'}
              className={`h-12 justify-start text-left font-medium transition-all duration-200 ${formData.status === 'en_cours'
                ? 'bg-blue-100 text-blue-500 border-blue-300 cursor-not-allowed'
                : 'bg-white text-blue-600 border-blue-300 hover:bg-blue-50 hover:border-blue-400 hover:shadow-sm'
                }`}
            >
              <span className="mr-3 text-lg">🔄</span>
              Marquer comme en cours
            </Button>
            <Button
              type="button"
              variant="outline"
              size="default"
              onClick={() => handleQuickStatusChange(task.id, 'en_revision')}
              disabled={formData.status === 'en_revision'}
              className={`h-12 justify-start text-left font-medium transition-all duration-200 ${formData.status === 'en_revision'
                ? 'bg-orange-100 text-orange-500 border-orange-300 cursor-not-allowed'
                : 'bg-white text-orange-600 border-orange-300 hover:bg-orange-50 hover:border-orange-400 hover:shadow-sm'
                }`}
            >
              <span className="mr-3 text-lg">🔍</span>
              Marquer comme en révision
            </Button>
            <Button
              type="button"
              variant="outline"
              size="default"
              onClick={() => handleQuickStatusChange(task.id, 'achevee')}
              disabled={formData.status === 'achevee'}
              className={`h-12 justify-start text-left font-medium transition-all duration-200 ${formData.status === 'achevee'
                ? 'bg-green-100 text-green-500 border-green-300 cursor-not-allowed'
                : 'bg-white text-green-600 border-green-300 hover:bg-green-50 hover:border-green-400 hover:shadow-sm'
                }`}
            >
              <span className="mr-3 text-lg">✅</span>
              Marquer comme achevée
            </Button>
          </div>
        </div>
      )}

      <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isSubmitting}
          className="h-11 px-6 border-gray-300 hover:border-gray-400 hover:bg-gray-50"
        >
          Annuler
        </Button>
        <Button
          type="submit"
          disabled={isSubmitting}
          className="h-11 px-6 bg-blue-600 hover:bg-blue-700 text-white font-medium shadow-sm"
          onClick={() => {
            console.log('🎯 PersonalTask Bouton submit cliqué - soumission intentionnelle');
            setIsIntentionalSubmit(true);
          }}
        >
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {task ? 'Mise à jour...' : 'Création...'}
            </>
          ) : (
            task ? 'Mettre à jour' : 'Créer'
          )}
        </Button>
      </div>
    </form>
  );
};

export default PersonalTaskForm;
