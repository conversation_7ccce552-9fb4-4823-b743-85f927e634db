import React, { useState, useEffect, useMemo } from 'react';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Edit, Trash2, Archive, Calendar, AlertCircle, RotateCcw } from 'lucide-react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';

// Composant pour gérer le mode strict de React avec react-beautiful-dnd
const StrictModeDroppable = ({ children, ...props }) => {
  const [enabled, setEnabled] = useState(false);

  React.useEffect(() => {
    const animation = requestAnimationFrame(() => setEnabled(true));
    return () => {
      cancelAnimationFrame(animation);
      setEnabled(false);
    };
  }, []);

  if (!enabled) {
    return null;
  }

  return <Droppable {...props}>{children}</Droppable>;
};

const PersonalTaskKanban = ({ tasks = [], onEdit, onDelete, onArchive, onUnarchive, onUpdateStatus }) => {
  const [isDragging, setIsDragging] = useState(false);

  // Définir les colonnes du Kanban
  const columns = [
    { id: 'a_faire', title: 'À faire', color: 'bg-blue-500' },
    { id: 'en_cours', title: 'En cours', color: 'bg-amber-500' },
    { id: 'achevee', title: 'Achevée', color: 'bg-green-500' },
    { id: 'archived', title: 'Archivée', color: 'bg-gray-500' }
  ];

  // Regrouper les tâches par statut (calculé à chaque rendu)
  const tasksByStatus = useMemo(() => {
    const grouped = columns.reduce((acc, column) => {
      acc[column.id] = tasks.filter(task => task.status === column.id);
      return acc;
    }, {});
    console.log('Regroupement des tâches par statut:', grouped);
    return grouped;
  }, [tasks]);

  // Log pour debug
  useEffect(() => {
    console.log('PersonalTaskKanban - Mise à jour avec les tâches:', tasks);
    console.log('PersonalTaskKanban - Tâches regroupées par statut:', tasksByStatus);
  }, [tasks, tasksByStatus]);

  // Formater les dates pour l'affichage
  const formatDate = (dateString) => {
    try {
      if (!dateString) return 'Date non définie';

      // Vérifier si la date est valide
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return 'Date invalide';
      }

      return format(date, 'PPP', { locale: fr });
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Date invalide';
    }
  };

  // Obtenir le libellé de la priorité
  const getPriorityLabel = (priority) => {
    switch (priority) {
      case 'faible':
        return <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">Faible</Badge>;
      case 'moyenne':
        return <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200">Moyenne</Badge>;
      case 'haute':
        return <Badge variant="outline" className="bg-red-100 text-red-800 border-red-200">Haute</Badge>;
      default:
        return <Badge variant="outline" className="bg-gray-100 text-gray-800 border-gray-200">Inconnue</Badge>;
    }
  };



  // Gérer le début du glisser-déposer
  const handleDragStart = () => {
    setIsDragging(true);
  };

  // Gérer la fin du glisser-déposer
  const handleDragEnd = (result) => {
    setIsDragging(false);

    const { destination, source, draggableId } = result;

    // Si pas de destination ou si la destination est la même que la source, ne rien faire
    if (!destination || (destination.droppableId === source.droppableId)) {
      return;
    }

    // Récupérer la tâche déplacée
    const task = tasks.find(t => t.id === draggableId);
    if (!task) return;

    // Si la tâche est archivée, ne pas permettre de la déplacer
    if (task.status === 'archived') {
      return;
    }

    // Mettre à jour le statut de la tâche
    onUpdateStatus(task.id, destination.droppableId);
  };

  // Rendu d'une carte de tâche
  const renderTaskCard = (task, index) => {
    const isArchived = task.status === 'archived';
    const isLate = task.end_date && new Date(task.end_date) < new Date() &&
      task.status !== 'achevee' && task.status !== 'archived';

    return (
      <Draggable
        key={task.id}
        draggableId={task.id}
        index={index}
        isDragDisabled={isArchived}
      >
        {(provided, snapshot) => (
          <div
            ref={provided.innerRef}
            {...provided.draggableProps}
            {...provided.dragHandleProps}
            className={`p-3 mb-2 rounded-md shadow-sm ${isArchived
              ? 'bg-gray-50 border border-gray-200'
              : snapshot.isDragging
                ? 'bg-white shadow-md border border-indigo-200 ring-2 ring-indigo-100'
                : 'bg-white border border-gray-200 hover:border-indigo-200'
              }`}
          >
            <div className="flex justify-between items-start mb-2">
              <div className="flex items-center gap-2">
                {getPriorityLabel(task.priority || 'moyenne')}
              </div>
              <div className="flex space-x-1">
                {/* Bouton Modifier - Uniquement pour les tâches non archivées */}
                {!isArchived && (
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => onEdit(task)}
                    className="h-6 w-6 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-full"
                    title="Modifier"
                  >
                    <Edit className="h-3 w-3" aria-hidden="true" />
                  </Button>
                )}

                {/* Bouton Supprimer - Disponible pour toutes les tâches */}
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => onDelete(task.id)}
                  className={`h-6 w-6 ${isArchived ? 'text-red-700' : 'text-red-600'} hover:text-red-800 hover:bg-red-50 rounded-full`}
                  title="Supprimer"
                >
                  <Trash2 className="h-3 w-3" aria-hidden="true" />
                </Button>

                {/* Bouton Archiver/Désarchiver selon le statut */}
                {!isArchived ? (
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => onArchive(task.id)}
                    className="h-6 w-6 text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-full"
                    title="Archiver"
                  >
                    <Archive className="h-3 w-3" aria-hidden="true" />
                  </Button>
                ) : (
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => onUnarchive(task.id)}
                    className="h-6 w-6 text-green-600 hover:text-green-800 hover:bg-green-50 rounded-full"
                    title="Désarchiver"
                  >
                    <RotateCcw className="h-3 w-3" aria-hidden="true" />
                  </Button>
                )}
              </div>
            </div>

            <h3 className={`font-medium mb-1 ${isArchived ? 'line-through text-gray-500' : 'text-gray-900'}`}>
              {task.title}
            </h3>
            <p className={`text-sm mb-3 line-clamp-2 ${isArchived ? 'line-through text-gray-400' : 'text-gray-500'}`}>
              {task.description || 'Aucune description'}
            </p>

            <div className={`flex items-center text-xs ${isArchived ? 'text-gray-400' : 'text-gray-600'} mb-2`}>
              <Calendar className="h-3 w-3 mr-1 text-gray-400" />
              <span className={isArchived ? 'line-through' : ''}>
                {formatDate(task.end_date)}
              </span>
            </div>

            {isLate && !isArchived && (
              <div className="mt-2 text-xs text-red-500 flex items-center">
                <AlertCircle className="h-3 w-3 mr-1" />
                En retard
              </div>
            )}

            {isArchived && (
              <div className="mt-2 text-xs text-gray-500 italic bg-gray-100 p-1 rounded-md">
                Tâche archivée
              </div>
            )}
          </div>
        )}
      </Draggable>
    );
  };

  return (
    <DragDropContext onDragEnd={handleDragEnd} onDragStart={handleDragStart}>
      <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md text-blue-700 text-sm">
        <p className="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
          </svg>
          Glissez-déposez les tâches entre les colonnes pour changer leur statut. Les tâches archivées ne peuvent pas être déplacées.
        </p>
      </div>
      <div className={`grid grid-cols-1 md:grid-cols-4 gap-6 ${isDragging ? 'opacity-95 bg-gray-50 p-2 rounded-lg transition-all duration-300' : ''}`}>
        {columns.map(column => (
          <div key={column.id} className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-medium text-gray-700 mb-3 flex items-center">
              <span className={`w-3 h-3 rounded-full ${column.color} mr-2`}></span>
              {column.title}
              <span className="ml-2 text-xs bg-gray-200 text-gray-700 px-2 py-0.5 rounded-full">
                {(tasksByStatus[column.id] || []).length}
              </span>
            </h3>
            <StrictModeDroppable droppableId={column.id}>
              {(provided, snapshot) => (
                <div
                  ref={provided.innerRef}
                  {...provided.droppableProps}
                  className={`space-y-3 min-h-[200px] transition-colors ${snapshot.isDraggingOver
                    ? `bg-gray-100 rounded-md p-2 ring-2 ring-${column.color.replace('bg-', '')} ring-opacity-50`
                    : ''
                    }`}
                  aria-label={`Colonne ${column.title} contenant ${(tasksByStatus[column.id] || []).length} tâches`}
                  aria-roledescription="Zone de dépôt pour les tâches"
                >
                  {(tasksByStatus[column.id] || []).map((task, index) => renderTaskCard(task, index))}
                  {provided.placeholder}
                </div>
              )}
            </StrictModeDroppable>
          </div>
        ))}
      </div>
    </DragDropContext>
  );
};

export default PersonalTaskKanban;
