import React, { useEffect } from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Edit, Trash2, Archive, RotateCcw, Calendar, AlertCircle, MoreVertical } from 'lucide-react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';

const PersonalTaskList = ({ tasks, onEdit, onDelete, onArchive, onUnarchive, onUpdateStatus }) => {
  // Log pour debug - surveiller les changements de tâches
  useEffect(() => {
    console.log('PersonalTaskList - Tâches mises à jour:', tasks);
  }, [tasks]);

  // Formater les dates pour l'affichage
  const formatDate = (dateString) => {
    try {
      if (!dateString) return 'Date non définie';

      // Vérifier si la date est valide
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return 'Date invalide';
      }

      return format(date, 'PPP', { locale: fr });
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Date invalide';
    }
  };

  // Obtenir la couleur et le libellé du statut
  const getStatusInfo = (status) => {
    switch (status) {
      case 'a_faire':
        return { color: 'bg-blue-100 text-blue-800 border-blue-200', label: 'À faire' };
      case 'en_cours':
        return { color: 'bg-amber-100 text-amber-800 border-amber-200', label: 'En cours' };
      case 'en_revision':
        return { color: 'bg-purple-100 text-purple-800 border-purple-200', label: 'En révision' };
      case 'achevee':
        return { color: 'bg-green-100 text-green-800 border-green-200', label: 'Achevée' };
      case 'archived':
        return { color: 'bg-gray-100 text-gray-800 border-gray-200', label: 'Archivée' };
      default:
        return { color: 'bg-gray-100 text-gray-800 border-gray-200', label: 'Inconnu' };
    }
  };

  // Obtenir la couleur et le libellé de la priorité
  const getPriorityInfo = (priority) => {
    switch (priority) {
      case 'faible':
        return { color: 'bg-green-100 text-green-800 border-green-200', label: 'Faible' };
      case 'moyenne':
        return { color: 'bg-blue-100 text-blue-800 border-blue-200', label: 'Moyenne' };
      case 'haute':
        return { color: 'bg-red-100 text-red-800 border-red-200', label: 'Haute' };
      default:
        return { color: 'bg-gray-100 text-gray-800 border-gray-200', label: 'Inconnue' };
    }
  };

  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[300px]">Titre</TableHead>
            <TableHead>Dates</TableHead>
            <TableHead>Statut</TableHead>
            <TableHead>Priorité</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {tasks.length === 0 && (
            <TableRow key="no-tasks">
              <TableCell colSpan={5} className="text-center py-8 text-gray-500">
                Aucune tâche personnelle trouvée
              </TableCell>
            </TableRow>
          )}
          {tasks.length > 0 && tasks.map((task) => {
            const isArchived = task.status === 'archived';
            const statusInfo = getStatusInfo(task.status);
            const priorityInfo = getPriorityInfo(task.priority);
            const isLate = task.end_date && new Date(task.end_date) < new Date() &&
              task.status !== 'achevee' && task.status !== 'archived';

            return (
              <TableRow
                key={task.id}
                className={isArchived ? 'bg-gray-50' : ''}
              >
                <TableCell className="font-medium">
                  <div className={`${isArchived ? 'text-gray-500 line-through' : 'text-gray-900'}`}>
                    {task.title}
                  </div>
                  {task.description && (
                    <div className={`text-xs mt-1 line-clamp-1 ${isArchived ? 'text-gray-400 line-through' : 'text-gray-500'}`}>
                      {task.description}
                    </div>
                  )}
                </TableCell>

                <TableCell>
                  <div className={`flex items-center ${isArchived ? 'text-gray-400 line-through' : 'text-gray-700'}`}>
                    <Calendar className="h-3 w-3 mr-2 text-gray-400" />
                    <span>
                      {formatDate(task.start_date)}
                      {task.end_date && task.start_date !== task.end_date && (
                        <span key={`end-date-${task.id}`} className="text-gray-400"> - {formatDate(task.end_date)}</span>
                      )}
                    </span>
                  </div>
                  {isLate && (
                    <Badge variant="outline" className="mt-1 text-xs bg-red-100 text-red-800 border-red-200">
                      <AlertCircle className="h-3 w-3 mr-1" />
                      En retard
                    </Badge>
                  )}
                </TableCell>

                <TableCell>
                  <Badge variant="outline" className={statusInfo.color}>
                    {statusInfo.label}
                  </Badge>
                </TableCell>

                <TableCell>
                  <Badge variant="outline" className={priorityInfo.color}>
                    {priorityInfo.label}
                  </Badge>
                </TableCell>

                <TableCell className="text-right">
                  <div className="flex justify-end">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <MoreVertical className="h-4 w-4" />
                          <span className="sr-only">Actions pour la tâche {task.title}</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {/* Générer les éléments du menu de façon explicite */}
                        {(() => {
                          const menuItems = [];

                          // Option Modifier - Uniquement pour les tâches non archivées
                          if (!isArchived) {
                            menuItems.push(
                              <DropdownMenuItem
                                key={`edit-${task.id}`}
                                onClick={() => onEdit(task)}
                                className="text-blue-600 hover:bg-blue-50"
                              >
                                <Edit className="mr-2 h-4 w-4" />
                                Modifier
                              </DropdownMenuItem>
                            );
                          }

                          // Option Supprimer - Disponible pour toutes les tâches
                          menuItems.push(
                            <DropdownMenuItem
                              key={`delete-${task.id}`}
                              onClick={() => onDelete(task.id)}
                              className="text-red-600 hover:bg-red-50"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Supprimer
                            </DropdownMenuItem>
                          );

                          // Option Archiver/Désarchiver selon le statut
                          if (!isArchived) {
                            menuItems.push(
                              <DropdownMenuItem
                                key={`archive-${task.id}`}
                                onClick={() => onArchive(task.id)}
                                className="text-gray-600 hover:bg-gray-50"
                              >
                                <Archive className="mr-2 h-4 w-4" />
                                Archiver
                              </DropdownMenuItem>
                            );
                          } else {
                            menuItems.push(
                              <DropdownMenuItem
                                key={`unarchive-${task.id}`}
                                onClick={() => onUnarchive(task.id)}
                                className="text-green-600 hover:bg-green-50"
                              >
                                <RotateCcw className="mr-2 h-4 w-4" />
                                Désarchiver
                              </DropdownMenuItem>
                            );
                          }

                          // Options pour changer le statut - Uniquement pour les tâches non archivées
                          if (!isArchived) {
                            menuItems.push(
                              <DropdownMenuItem
                                key={`status-a-faire-${task.id}`}
                                onClick={() => onUpdateStatus(task.id, 'a_faire')}
                                disabled={task.status === 'a_faire'}
                                className="text-blue-600 hover:bg-blue-50"
                              >
                                Marquer comme à faire
                              </DropdownMenuItem>
                            );

                            menuItems.push(
                              <DropdownMenuItem
                                key={`status-en-cours-${task.id}`}
                                onClick={() => onUpdateStatus(task.id, 'en_cours')}
                                disabled={task.status === 'en_cours'}
                                className="text-amber-600 hover:bg-amber-50"
                              >
                                Marquer comme en cours
                              </DropdownMenuItem>
                            );

                            menuItems.push(
                              <DropdownMenuItem
                                key={`status-achevee-${task.id}`}
                                onClick={() => onUpdateStatus(task.id, 'achevee')}
                                disabled={task.status === 'achevee'}
                                className="text-green-600 hover:bg-green-50"
                              >
                                Marquer comme achevée
                              </DropdownMenuItem>
                            );
                          }

                          return menuItems;
                        })()}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
};

export default PersonalTaskList;
