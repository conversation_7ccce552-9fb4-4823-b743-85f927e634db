import { useEffect, useState } from 'react';
import { Droppable } from 'react-beautiful-dnd';

// Ce composant est un wrapper pour Droppable qui résout les problèmes de compatibilité
// entre React 18 (StrictMode) et react-beautiful-dnd
export const StrictModeDroppable = ({ children, ...props }) => {
    const [enabled, setEnabled] = useState(false);
    
    useEffect(() => {
        // Petit délai pour éviter les problèmes avec StrictMode
        const animation = requestAnimationFrame(() => setEnabled(true));
        
        return () => {
            cancelAnimationFrame(animation);
            setEnabled(false);
        };
    }, []);
    
    if (!enabled) {
        return null;
    }
    
    return <Droppable {...props}>{children}</Droppable>;
};

export default StrictModeDroppable;
