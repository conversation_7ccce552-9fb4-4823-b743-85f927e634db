import React, { useState, useEffect } from 'react';
import { Edit, Trash2, CheckCircle, Clock, Calendar, User, Users, MoreVertical, Circle, AlertCircle, Archive, FileCheck } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from '@/components/ui/dropdown-menu';
import { toast } from 'react-toastify';
import { formatMemberAssignment } from '@/utils/memberDisplayUtils';
// Suppression de l'import Badge qui n'existe pas

const TeamTaskCard = ({ task, onEdit, onDelete, onUpdateStatus, onArchive, onUnarchive, modalOpen }) => {
    const [loading, setLoading] = useState(false);
    const [actionsMenuOpen, setActionsMenuOpen] = useState(false);
    const [statusMenuOpen, setStatusMenuOpen] = useState(false);

    // Fermer tous les menus quand une modale s'ouvre
    useEffect(() => {
        if (modalOpen) {
            setActionsMenuOpen(false);
            setStatusMenuOpen(false);
        }
    }, [modalOpen]);

    // Fonction pour formater les dates
    const formatDate = (dateString) => {
        if (!dateString) return 'Date inconnue';
        try {
            return new Date(dateString).toLocaleDateString('fr-FR', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        } catch (error) {
            console.error('Erreur de formatage de date:', error, dateString);
            return 'Date inconnue';
        }
    };

    // Fonction pour obtenir la couleur en fonction du statut
    const getStatusColor = (status) => {
        switch (status) {
            case 'achevee':
                return 'bg-green-500';
            case 'en_cours':
                return 'bg-blue-500';
            case 'a_faire':
                return 'bg-yellow-500';
            // Option "en révision" temporairement désactivée
            // case 'en_revision':
            //    return 'bg-purple-500';
            case 'archived':
                return 'bg-gray-500';
            default:
                return 'bg-blue-500';
        }
    };

    // Fonction pour obtenir le libellé du statut
    const getStatusLabel = (status) => {
        switch (status) {
            case 'achevee':
                return 'Achevée';
            case 'en_cours':
                return 'En cours';
            case 'a_faire':
                return 'À faire';
            // Option "en révision" temporairement désactivée
            // case 'en_revision':
            //    return 'En révision';
            case 'archived':
                return 'Archivée';
            default:
                return 'Inconnu';
        }
    };

    // Fonction pour obtenir l'icône du statut
    const getStatusIcon = (status) => {
        switch (status) {
            case 'achevee':
                return <CheckCircle className="h-4 w-4 text-green-600" aria-hidden="false" />;
            case 'en_cours':
                return <Circle className="h-4 w-4 text-blue-600" aria-hidden="false" />;
            case 'a_faire':
                return <Clock className="h-4 w-4 text-yellow-600" aria-hidden="false" />;
            // Option "en révision" temporairement désactivée
            // case 'en_revision':
            //    return <FileCheck className="h-4 w-4 text-purple-600" aria-hidden="false" />;
            case 'archived':
                return <AlertCircle className="h-4 w-4 text-gray-600" aria-hidden="false" />;
            default:
                return <Circle className="h-4 w-4 text-blue-600" aria-hidden="false" />;
        }
    };

    // Vérifier si l'utilisateur peut gérer cette tâche
    const canManage = task.permissions?.canManage;

    // Vérifier si l'utilisateur peut mettre à jour le statut de cette tâche
    const canUpdateStatus = task.permissions?.canUpdateStatus;

    // Logs de débogage pour les permissions
    console.log(`TeamTaskCard - Task ${task.id} permissions:`, {
        canManage,
        canUpdateStatus,
        taskStatus: task.status,
        taskTitle: task.title,
        taskMemberId: task.member_id,
        taskMemberName: task.member_name,
        allPermissions: task.permissions
    });

    return (
        <div className={`rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border ${task.status === 'archived' ? 'bg-gray-100 border-gray-300' : 'bg-white border-gray-100'}`}>
            {/* En-tête de la carte avec un bandeau de couleur selon le statut */}
            <div className={`${getStatusColor(task.status)} h-2 w-full`}></div>

            <div className="p-6 space-y-4">
                <div className="flex justify-between items-start">
                    <div>
                        <h3 className={`text-xl font-semibold ${task.status === 'archived' ? 'line-through text-gray-500' : 'text-gray-800 hover:text-indigo-600'} transition-colors`}>
                            {task.title}
                        </h3>
                        <p className={`text-sm ${task.status === 'archived' ? 'line-through text-gray-400' : 'text-gray-600'} mt-1`}>
                            {task.description || 'Aucune description'}
                        </p>
                    </div>
                    {canManage && (
                        <div className="relative">
                            <DropdownMenu open={actionsMenuOpen} onOpenChange={setActionsMenuOpen}>
                                <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" size="icon" className="hover:bg-gray-100 rounded-full">
                                        <MoreVertical className="h-5 w-5 text-gray-500" aria-hidden="true" />
                                    </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end" className="shadow-lg rounded-lg border border-gray-200">
                                    {/* Bouton Modifier - Uniquement pour les tâches non archivées */}
                                    {task.status !== 'archived' && (
                                        <DropdownMenuItem onClick={() => { setActionsMenuOpen(false); onEdit(task); }} className="hover:bg-gray-50">
                                            <Edit className="mr-2 h-4 w-4 text-blue-500" aria-hidden="true" />
                                            Modifier
                                        </DropdownMenuItem>
                                    )}

                                    {/* Bouton Supprimer - Disponible pour toutes les tâches */}
                                    <DropdownMenuItem
                                        onClick={() => { setActionsMenuOpen(false); onDelete(task.id); }}
                                        className={`${task.status === 'archived' ? 'text-red-700' : 'text-red-600'} hover:bg-red-50`}
                                    >
                                        <Trash2 className="mr-2 h-4 w-4" aria-hidden="true" />
                                        Supprimer
                                    </DropdownMenuItem>

                                    {/* Bouton Archiver/Désarchiver selon le statut - Uniquement pour les admins */}
                                    {task.permissions?.canArchive && task.status !== 'archived' && (
                                        <DropdownMenuItem onClick={() => { setActionsMenuOpen(false); onArchive(task.id); }} className="text-gray-600 hover:bg-gray-50">
                                            <Archive className="mr-2 h-4 w-4" aria-hidden="true" />
                                            Archiver
                                        </DropdownMenuItem>
                                    )}
                                    {task.permissions?.canUnarchive && task.status === 'archived' && (
                                        <DropdownMenuItem onClick={() => { setActionsMenuOpen(false); onUnarchive(task.id); }} className="text-green-600 hover:bg-green-50">
                                            <Archive className="mr-2 h-4 w-4" aria-hidden="true" />
                                            Désarchiver
                                        </DropdownMenuItem>
                                    )}
                                </DropdownMenuContent>
                            </DropdownMenu>
                        </div>
                    )}
                </div>

                <div className="pt-4 border-t border-gray-200">
                    <div className="flex items-center space-x-2 mb-3">
                        <div className={`flex-shrink-0 p-1 rounded-full ${getStatusColor(task.status)} bg-opacity-20`}>
                            {getStatusIcon(task.status)}
                        </div>
                        <span className="text-sm font-medium">
                            Statut: {getStatusLabel(task.status)}
                        </span>
                        {canUpdateStatus && task.status !== 'archived' && (
                            <div className="ml-auto">
                                <DropdownMenu open={statusMenuOpen} onOpenChange={setStatusMenuOpen}>
                                    <DropdownMenuTrigger asChild>
                                        <Button variant="outline" size="sm" className="text-xs">
                                            Changer le statut
                                        </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end" className="shadow-lg rounded-lg border border-gray-200">
                                        <DropdownMenuItem
                                            onClick={() => { setStatusMenuOpen(false); onUpdateStatus(task.id, 'a_faire'); }}
                                            className="hover:bg-yellow-50"
                                            disabled={task.status === 'a_faire'}
                                        >
                                            <Clock className="mr-2 h-4 w-4 text-yellow-500" aria-hidden="true" />
                                            À faire
                                        </DropdownMenuItem>
                                        <DropdownMenuItem
                                            onClick={() => { setStatusMenuOpen(false); onUpdateStatus(task.id, 'en_cours'); }}
                                            className="hover:bg-blue-50"
                                            disabled={task.status === 'en_cours'}
                                        >
                                            <Circle className="mr-2 h-4 w-4 text-blue-500" aria-hidden="true" />
                                            En cours
                                        </DropdownMenuItem>
                                        {/* Option "en révision" temporairement désactivée
                                        <DropdownMenuItem
                                            onClick={() => onUpdateStatus(task.id, 'en_revision')}
                                            className="hover:bg-purple-50"
                                            disabled={task.status === 'en_revision'}
                                        >
                                            <FileCheck className="mr-2 h-4 w-4 text-purple-500" aria-hidden="true" />
                                            En révision
                                        </DropdownMenuItem>
                                        */}
                                        <DropdownMenuItem
                                            onClick={() => { setStatusMenuOpen(false); onUpdateStatus(task.id, 'achevee'); }}
                                            className="hover:bg-green-50"
                                            disabled={task.status === 'achevee'}
                                        >
                                            <CheckCircle className="mr-2 h-4 w-4 text-green-500" aria-hidden="true" />
                                            Achevée
                                        </DropdownMenuItem>
                                    </DropdownMenuContent>
                                </DropdownMenu>
                            </div>
                        )}
                    </div>

                    <div className="space-y-3">
                        <div className={`flex items-center text-sm ${task.status === 'archived' ? 'text-gray-400' : 'text-gray-600'}`}>
                            <Calendar className={`h-4 w-4 mr-2 ${task.status === 'archived' ? 'text-gray-400' : 'text-indigo-500'}`} aria-hidden="true" />
                            <span className="font-medium">Date:</span>
                            <span className={`ml-1 ${task.status === 'archived' ? 'line-through' : ''}`}>
                                {formatDate(task.start_date)} {task.start_date !== task.end_date && ` - ${formatDate(task.end_date)}`}
                            </span>
                        </div>
                        {task.team_name && (
                            <div className={`flex items-center text-sm ${task.status === 'archived' ? 'text-gray-400' : 'text-gray-600'}`}>
                                <Users className={`h-4 w-4 mr-2 ${task.status === 'archived' ? 'text-gray-400' : 'text-indigo-500'}`} aria-hidden="true" />
                                <span className="font-medium">Équipe:</span>
                                <span className="ml-1">{task.team_name}</span>
                            </div>
                        )}
                        <div className={`flex items-center text-sm ${task.status === 'archived' ? 'text-gray-400' : 'text-gray-600'}`}>
                            <User className={`h-4 w-4 mr-2 ${task.status === 'archived' ? 'text-gray-400' : 'text-indigo-500'}`} aria-hidden="true" />
                            <span className="font-medium">Assigné à:</span>
                            <span className={`ml-1 ${task.status === 'archived' ? 'line-through' : ''}`}>
                                {formatMemberAssignment(task.member_id, task.member_name)}
                            </span>
                        </div>
                    </div>
                </div>

                <div className={`pt-4 border-t border-gray-200 ${task.status === 'archived' ? 'bg-gray-100' : 'bg-gray-50'} -mx-6 -mb-6 p-6 rounded-b-lg`}>
                    <div className={`flex items-center text-sm ${task.status === 'archived' ? 'text-gray-400' : 'text-gray-600'} mb-2`}>
                        <User className={`h-4 w-4 mr-2 ${task.status === 'archived' ? 'text-gray-400' : 'text-indigo-500'}`} aria-hidden="true" />
                        <span className="font-medium">Responsable:</span>
                        <span className="ml-1">{task.responsable_name || 'Administrateur'}</span>
                    </div>
                    <div className={`flex items-center text-sm ${task.status === 'archived' ? 'text-gray-400' : 'text-gray-600'}`}>
                        <Circle className={`h-4 w-4 mr-2 ${task.status === 'archived' ? 'text-gray-400' : 'text-green-500'}`} aria-hidden="true" />
                        <span className="font-medium">Créé le</span>
                        <span className="ml-1">{formatDate(task.created_at)}</span>
                    </div>
                    {task.status === 'archived' && (
                        <div className="mt-2 text-xs text-gray-500 italic bg-gray-200 p-1 rounded-md">
                            Cette tâche est archivée. Seules les actions de suppression et de désarchivage sont disponibles.
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default TeamTaskCard;
