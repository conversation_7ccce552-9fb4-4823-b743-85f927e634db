import React, { useState, useEffect } from 'react';
import { Edit, Trash2, CheckCircle, Clock, Calendar, User, Users, MoreVertical, Circle, AlertCircle, Archive, FileCheck } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from '@/components/ui/dropdown-menu';
import { toast } from 'react-toastify';
import { formatMemberAssignment } from '@/utils/memberDisplayUtils';
// Suppression de l'import Badge qui n'existe pas

const TeamTaskCard = ({ task, onEdit, onDelete, onUpdateStatus, onArchive, onUnarchive, modalOpen }) => {
    const [loading, setLoading] = useState(false);
    const [actionsMenuOpen, setActionsMenuOpen] = useState(false);
    const [statusMenuOpen, setStatusMenuOpen] = useState(false);

    // Fermer tous les menus quand une modale s'ouvre
    useEffect(() => {
        if (modalOpen) {
            setActionsMenuOpen(false);
            setStatusMenuOpen(false);
        }
    }, [modalOpen]);

    // Fonction pour formater les dates
    const formatDate = (dateString) => {
        if (!dateString) return 'Date inconnue';
        try {
            return new Date(dateString).toLocaleDateString('fr-FR', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        } catch (error) {
            console.error('Erreur de formatage de date:', error, dateString);
            return 'Date inconnue';
        }
    };

    // Fonction pour obtenir la couleur en fonction du statut
    const getStatusColor = (status) => {
        switch (status) {
            case 'achevee':
                return 'bg-green-500';
            case 'en_cours':
                return 'bg-blue-500';
            case 'a_faire':
                return 'bg-yellow-500';
            // Option "en révision" temporairement désactivée
            // case 'en_revision':
            //    return 'bg-purple-500';
            case 'archived':
                return 'bg-gray-500';
            default:
                return 'bg-blue-500';
        }
    };

    // Fonction pour obtenir le libellé du statut
    const getStatusLabel = (status) => {
        switch (status) {
            case 'achevee':
                return 'Achevée';
            case 'en_cours':
                return 'En cours';
            case 'a_faire':
                return 'À faire';
            // Option "en révision" temporairement désactivée
            // case 'en_revision':
            //    return 'En révision';
            case 'archived':
                return 'Archivée';
            default:
                return 'Inconnu';
        }
    };

    // Fonction pour obtenir la couleur de la priorité
    const getPriorityColor = (priority) => {
        switch (priority) {
            case 'haute':
                return 'bg-red-500';
            case 'moyenne':
                return 'bg-yellow-500';
            case 'faible':
                return 'bg-green-500';
            default:
                return 'bg-gray-500';
        }
    };

    // Fonction pour obtenir le libellé de la priorité
    const getPriorityLabel = (priority) => {
        switch (priority) {
            case 'haute':
                return 'Haute';
            case 'moyenne':
                return 'Moyenne';
            case 'faible':
                return 'Faible';
            default:
                return 'Normale';
        }
    };

    // Vérifier si l'utilisateur peut gérer cette tâche
    const canManage = task.permissions?.canManage;

    // Vérifier si l'utilisateur peut mettre à jour le statut de cette tâche
    const canUpdateStatus = task.permissions?.canUpdateStatus;



    return (
        <div className={`rounded-lg shadow-sm hover:shadow-md transition-all duration-200 border ${task.status === 'archived' ? 'bg-gray-50 border-gray-300' : 'bg-white border-gray-200'}`}>
            <div className="p-6">
                <div className="flex justify-between items-start mb-4">
                    <div className="flex-1">
                        <h3 className={`text-lg font-semibold ${task.status === 'archived' ? 'line-through text-gray-500' : 'text-gray-900'}`}>
                            {task.title}
                        </h3>
                        <p className={`text-sm mt-1 ${task.status === 'archived' ? 'line-through text-gray-400' : 'text-gray-600'}`}>
                            {task.description || 'Description détaillée de la tâche'}
                        </p>
                    </div>

                    {/* Menu trois points */}
                    <DropdownMenu open={actionsMenuOpen} onOpenChange={setActionsMenuOpen}>
                        <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full">
                                <MoreVertical className="h-4 w-4" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-48 shadow-lg rounded-lg border border-gray-200">
                            {/* Actions de modification du statut pour les employés assignés */}
                            {task.permissions?.canUpdateStatus && task.status !== 'archived' && (
                                <>
                                    <DropdownMenuItem
                                        onClick={() => {
                                            setActionsMenuOpen(false);
                                            onUpdateStatus(task.id, 'a_faire');
                                        }}
                                        className="hover:bg-yellow-50 text-yellow-700"
                                        disabled={task.status === 'a_faire'}
                                    >
                                        <Clock className="mr-2 h-4 w-4" />
                                        Marquer comme à faire
                                    </DropdownMenuItem>
                                    <DropdownMenuItem
                                        onClick={() => {
                                            setActionsMenuOpen(false);
                                            onUpdateStatus(task.id, 'en_cours');
                                        }}
                                        className="hover:bg-blue-50 text-blue-700"
                                        disabled={task.status === 'en_cours'}
                                    >
                                        <Circle className="mr-2 h-4 w-4" />
                                        Marquer comme en cours
                                    </DropdownMenuItem>
                                    <DropdownMenuItem
                                        onClick={() => {
                                            setActionsMenuOpen(false);
                                            onUpdateStatus(task.id, 'achevee');
                                        }}
                                        className="hover:bg-green-50 text-green-700"
                                        disabled={task.status === 'achevee'}
                                    >
                                        <CheckCircle className="mr-2 h-4 w-4" />
                                        Marquer comme achevée
                                    </DropdownMenuItem>
                                </>
                            )}

                            {/* Séparateur si il y a des actions de statut ET d'autres actions */}
                            {task.permissions?.canUpdateStatus && task.status !== 'archived' &&
                                (task.permissions?.canEdit || task.permissions?.canDelete || task.permissions?.canArchive || task.permissions?.canUnarchive) && (
                                    <div className="border-t border-gray-200 my-1" />
                                )}

                            {/* Action Modifier */}
                            {task.permissions?.canEdit && task.status !== 'archived' && (
                                <DropdownMenuItem
                                    onClick={() => {
                                        setActionsMenuOpen(false);
                                        onEdit(task);
                                    }}
                                    className="hover:bg-blue-50 text-blue-700"
                                >
                                    <Edit className="mr-2 h-4 w-4" />
                                    Modifier
                                </DropdownMenuItem>
                            )}

                            {/* Action Archiver/Désarchiver */}
                            {task.permissions?.canArchive && task.status !== 'archived' && (
                                <DropdownMenuItem
                                    onClick={() => {
                                        setActionsMenuOpen(false);
                                        onArchive(task.id);
                                    }}
                                    className="hover:bg-gray-50 text-gray-700"
                                >
                                    <Archive className="mr-2 h-4 w-4" />
                                    Archiver
                                </DropdownMenuItem>
                            )}
                            {task.permissions?.canUnarchive && task.status === 'archived' && (
                                <DropdownMenuItem
                                    onClick={() => {
                                        setActionsMenuOpen(false);
                                        onUnarchive(task.id);
                                    }}
                                    className="hover:bg-green-50 text-green-700"
                                >
                                    <Archive className="mr-2 h-4 w-4" />
                                    Désarchiver
                                </DropdownMenuItem>
                            )}

                            {/* Action Supprimer */}
                            {task.permissions?.canDelete && (
                                <DropdownMenuItem
                                    onClick={() => {
                                        setActionsMenuOpen(false);
                                        onDelete(task.id);
                                    }}
                                    className="hover:bg-red-50 text-red-700"
                                >
                                    <Trash2 className="mr-2 h-4 w-4" />
                                    Supprimer
                                </DropdownMenuItem>
                            )}
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>

                {/* Badges de statut et priorité */}
                <div className="flex items-center gap-3 mb-4">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(task.status)} text-white`}>
                        {getStatusLabel(task.status)}
                    </span>
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getPriorityColor(task.priority || 'moyenne')} text-white`}>
                        {getPriorityLabel(task.priority || 'moyenne')}
                    </span>
                </div>

                {/* Informations de date */}
                <div className="flex items-center text-sm text-gray-600 mb-2">
                    <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                    <span className={task.status === 'archived' ? 'line-through text-gray-500' : ''}>
                        {formatDate(task.start_date)} - {formatDate(task.end_date)}
                    </span>
                </div>

                {/* Date de création */}
                <div className="text-xs text-gray-500">
                    Créée le {formatDate(task.created_at)}
                </div>
            </div>
        </div>
    );
};

export default TeamTaskCard;
