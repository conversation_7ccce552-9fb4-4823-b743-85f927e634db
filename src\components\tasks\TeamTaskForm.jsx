import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar as CalendarIcon, Loader2 } from 'lucide-react';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
// Suppression des imports date-fns qui ne sont pas installés
import { useTeam } from '@/contexts/TeamContext';
import { useAuth } from '@/contexts/AuthContext';
import { showSuccessToast, showErrorToast, showInfoToast } from '@/utils/toastUtils';

const TeamTaskForm = ({ task, onSubmit, onCancel, isSubmitting, teamTasks = [] }) => {
  // Fonction pour créer une date valide
  const createValidDate = (dateString) => {
    if (!dateString) return new Date();
    try {
      const date = new Date(dateString);
      return isNaN(date.getTime()) ? new Date() : date;
    } catch (error) {
      console.error('Erreur lors de la création de la date:', error);
      return new Date();
    }
  };

  const { teams, loading: teamsLoading } = useTeam();
  const { user } = useAuth();

  // État du formulaire (sans react-hook-form pour éviter les soumissions automatiques)
  const [formData, setFormData] = useState({
    title: task?.title || '',
    description: task?.description || '',
    start_date: createValidDate(task?.start_date),
    end_date: createValidDate(task?.end_date),
    team_id: task?.team_id || '',
    member_id: task?.member_id || '',
    responsable: task?.responsable || '',
    priority: task?.priority || 'moyenne'
  });

  const [errors, setErrors] = useState({});
  const [teamMembers, setTeamMembers] = useState([]);
  const [startDateOpen, setStartDateOpen] = useState(false);
  const [endDateOpen, setEndDateOpen] = useState(false);

  // État pour suivre si la soumission est intentionnelle (comme dans PersonalTaskForm)
  const [isIntentionalSubmit, setIsIntentionalSubmit] = useState(false);

  // Filtrer les équipes dont l'utilisateur est responsable
  const adminTeams = teams.filter(team => team.is_responsable);

  // Mettre à jour les membres de l'équipe lorsque l'équipe sélectionnée change
  useEffect(() => {
    if (formData.team_id) {
      const team = teams.find(t => t.id === formData.team_id);
      if (team && team.members) {
        setTeamMembers(team.members);

        // Vérifier si le membre actuellement sélectionné appartient à cette équipe
        if (formData.member_id) {
          const memberExists = team.members.some(m => String(m.id) === String(formData.member_id));
          if (!memberExists) {
            // Si le membre n'appartient pas à cette équipe, réinitialiser la sélection
            console.log('Le membre sélectionné n\'appartient pas à cette équipe, réinitialisation...');
            setFormData(prev => ({ ...prev, member_id: '' }));
          }
        }
      } else {
        setTeamMembers([]);
        setFormData(prev => ({ ...prev, member_id: '' }));
      }
    } else {
      setTeamMembers([]);
      setFormData(prev => ({ ...prev, member_id: '' }));
    }
  }, [formData.team_id, teams]);

  // Gérer les changements de champs
  const handleInputChange = (name, value) => {
    setFormData(prev => ({ ...prev, [name]: value }));

    // Effacer l'erreur pour ce champ
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: null }));
    }

    // Si la tâche existe déjà et qu'on change d'équipe, on affiche un avertissement
    if (name === 'team_id' && task && task.id && task.team_id !== value) {
      showInfoToast('Attention: Changer d\'équipe réinitialisera le membre assigné.');
    }
  };

  // Gérer les changements de date
  const handleDateChange = (date, field) => {
    if (!date) return;

    setFormData(prev => ({ ...prev, [field]: date }));

    // Effacer l'erreur pour ce champ
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };

  // Formater la date pour l'affichage
  const formatDate = (date) => {
    if (!date) return '';
    try {
      return date.toLocaleDateString('fr-FR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (error) {
      console.error('Erreur de formatage de date:', error);
      // Fallback en cas d'erreur
      return date instanceof Date ?
        `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear()}` :
        'Date invalide';
    }
  };

  // Vérifier si une tâche d'équipe avec le même titre existe déjà dans la même équipe
  const checkTeamTaskTitleExists = (title, teamId) => {
    if (!teamTasks || !Array.isArray(teamTasks)) return false;

    // Si nous sommes en mode édition, exclure la tâche actuelle de la vérification
    const existingTasks = task ? teamTasks.filter(t => t.id !== task.id) : teamTasks;

    // Vérifier si une tâche avec le même titre existe déjà dans la même équipe
    return existingTasks.some(t =>
      t.title && t.title.toLowerCase().trim() === title.toLowerCase().trim() &&
      t.team_id === teamId
    );
  };

  // Valider le formulaire
  const validateForm = () => {
    const newErrors = {};

    if (!formData.title || formData.title.trim().length === 0) {
      newErrors.title = "Le titre est requis";
    } else if (formData.title.trim().length < 3) {
      newErrors.title = "Le titre doit contenir au moins 3 caractères";
    }

    if (!formData.team_id || formData.team_id === '') {
      newErrors.team_id = "Veuillez sélectionner une équipe";
    }

    // Vérifier les doublons de titre dans la même équipe
    if (formData.title && formData.team_id && checkTeamTaskTitleExists(formData.title.trim(), formData.team_id)) {
      newErrors.title = "Cette équipe a déjà une tâche avec ce titre";
    }

    if (!formData.start_date) {
      newErrors.start_date = "La date de début est requise";
    }

    if (!formData.end_date) {
      newErrors.end_date = "La date de fin est requise";
    }

    if (formData.start_date && formData.end_date && formData.end_date < formData.start_date) {
      newErrors.end_date = "La date de fin doit être après la date de début";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Soumettre le formulaire - PROTECTION COMME PersonalTaskForm
  const handleFormSubmit = (e) => {
    console.log('🚨 TeamTask handleFormSubmit appelé!', {
      event: e,
      eventType: e?.type,
      target: e?.target?.tagName,
      submitter: e?.submitter?.tagName,
      submitterType: e?.submitter?.type,
      isIntentionalSubmit: isIntentionalSubmit,
      stack: new Error().stack
    });

    e.preventDefault();
    e.stopPropagation();

    // Bloquer les soumissions automatiques (pas intentionnelles)
    if (!isIntentionalSubmit) {
      console.log('🚫 TeamTask Soumission bloquée - soumission automatique détectée');
      setIsIntentionalSubmit(false); // Reset pour la prochaine fois
      return;
    }

    // Reset le flag
    setIsIntentionalSubmit(false);

    if (!validateForm()) {
      showErrorToast("Veuillez corriger les erreurs dans le formulaire");
      return;
    }

    // Convertir les dates en chaînes de caractères ISO
    const formattedData = {
      ...formData,
      start_date: formData.start_date.toISOString().split('T')[0],
      end_date: formData.end_date.toISOString().split('T')[0],
    };

    // Ajouter le nom de l'équipe et du responsable
    if (formattedData.team_id) {
      const team = teams.find(t => t.id === formattedData.team_id);
      if (team) {
        formattedData.team_name = team.name;
      }
    }

    // Ajouter le nom du membre assigné
    if (formattedData.member_id) {
      const member = teamMembers.find(m => String(m.id) === String(formattedData.member_id));
      if (member) {
        formattedData.member_name = member.name;
        console.log(`Membre assigné: ${member.name} (ID: ${member.id})`);
      } else {
        console.warn(`Membre avec ID ${formattedData.member_id} non trouvé dans l'équipe`);
        // Vérifier si le membre existe dans une autre équipe
        let memberFound = false;
        teams.forEach(team => {
          if (team.members && Array.isArray(team.members)) {
            const foundMember = team.members.find(m => String(m.id) === String(formattedData.member_id));
            if (foundMember && !memberFound) {
              formattedData.member_name = foundMember.name;
              memberFound = true;
              console.log(`Membre trouvé dans une autre équipe: ${foundMember.name} (ID: ${foundMember.id})`);
            }
          }
        });

        if (!memberFound) {
          // Si le membre n'est pas trouvé, on le supprime pour éviter des erreurs
          console.warn('Membre non trouvé, suppression de l\'assignation');
          formattedData.member_id = '';
          delete formattedData.member_name;
        }
      }
    } else {
      // S'assurer que member_name est supprimé si member_id est vide
      delete formattedData.member_name;
    }

    // Ajouter le nom du responsable
    if (formattedData.responsable) {
      formattedData.responsable_name = user.name;
    } else {
      formattedData.responsable = user.id;
      formattedData.responsable_name = user.name;
    }

    onSubmit(formattedData);
  };

  // Empêcher la soumission du formulaire par les touches Enter dans les calendriers (comme PersonalTaskForm)
  const handleFormKeyDown = (e) => {
    // Si Enter est pressé et que nous sommes dans un popover de calendrier, empêcher la soumission
    if (e.key === 'Enter') {
      const target = e.target;
      const isInCalendar = target.closest('[role="dialog"]') || target.closest('.calendar') || target.closest('[data-radix-popper-content-wrapper]');

      if (isInCalendar) {
        console.log('🚫 TeamTask Touche Enter bloquée dans le calendrier');
        e.preventDefault();
        e.stopPropagation();
        return false;
      }

      // Empêcher Enter sur tous les champs sauf les boutons submit
      if (e.target.type !== 'submit') {
        console.log('🚫 TeamTask Touche Enter bloquée sur champ:', e.target.type);
        e.preventDefault();
        e.stopPropagation();
      }
    }
  };

  return (
    <form onSubmit={handleFormSubmit} onKeyDown={handleFormKeyDown} className="space-y-4">
      <div className="space-y-3">
        <div className="bg-white p-2 rounded-md shadow-sm border border-gray-200">
          <Label htmlFor="title" className="text-sm font-medium text-gray-700 flex items-center">
            <span className="bg-indigo-100 text-indigo-800 p-1 rounded-md mr-2 text-xs">Titre</span>
            <span className="text-red-500 ml-1">*</span>
          </Label>
          <Input
            id="title"
            value={formData.title}
            onChange={(e) => handleInputChange('title', e.target.value)}
            className={`mt-1 ${errors.title ? 'border-red-500' : 'border-indigo-200'} focus:border-indigo-500 focus:ring-indigo-500`}
            placeholder="Titre de la tâche"
          />
          {errors.title && (
            <p className="mt-1 text-xs text-red-500 bg-red-50 p-1 rounded-md">{errors.title}</p>
          )}
        </div>

        <div className="bg-white p-2 rounded-md shadow-sm border border-gray-200">
          <Label htmlFor="description" className="text-sm font-medium text-gray-700 flex items-center">
            <span className="bg-indigo-100 text-indigo-800 p-1 rounded-md mr-2 text-xs">Description</span>
          </Label>
          <Textarea
            id="description"
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            className="mt-1 border-indigo-200 focus:border-indigo-500 focus:ring-indigo-500"
            placeholder="Description détaillée de la tâche"
            rows={2}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <div className="bg-white p-2 rounded-md shadow-sm border border-gray-200">
            <Label htmlFor="start_date" className="text-sm font-medium text-gray-700 flex items-center">
              <span className="bg-indigo-100 text-indigo-800 p-1 rounded-md mr-2 text-xs">Date de début</span>
              <span className="text-red-500 ml-1">*</span>
            </Label>
            <Popover open={startDateOpen} onOpenChange={setStartDateOpen}>
              <PopoverTrigger asChild>
                <Button
                  type="button"
                  id="start_date"
                  variant="outline"
                  className={`w-full mt-1 justify-start text-left font-normal text-sm ${errors.start_date ? 'border-red-500' : 'border-indigo-200'} hover:bg-indigo-50`}
                  onClick={() => setStartDateOpen(true)}
                >
                  <CalendarIcon className="mr-2 h-4 w-4 text-indigo-600" />
                  {formData.start_date ? formatDate(formData.start_date) : <span>Sélectionner une date</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent
                className="w-auto p-0"
                align="start"
                onOpenAutoFocus={(e) => e.preventDefault()}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    console.log('🚫 TeamTask Enter bloqué dans PopoverContent start_date');
                    e.preventDefault();
                    e.stopPropagation();
                  }
                }}
              >
                <div className="p-2">
                  <Calendar
                    mode="single"
                    selected={formData.start_date}
                    onSelect={(date) => {
                      console.log('🗓️ TeamTask Calendar onSelect appelé pour start_date:', date);
                      if (date) {
                        handleDateChange(date, 'start_date');
                        setStartDateOpen(false);
                      }
                    }}
                    initialFocus
                  /* locale supprimé */
                  />
                  <div className="mt-2 flex justify-end">
                    <Button
                      type="button"
                      size="sm"
                      className="bg-indigo-600 hover:bg-indigo-700 text-white"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        setStartDateOpen(false);
                      }}
                    >
                      Confirmer
                    </Button>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
            {errors.start_date && (
              <p className="mt-1 text-xs text-red-500 bg-red-50 p-1 rounded-md">{errors.start_date}</p>
            )}
          </div>

          <div className="bg-white p-2 rounded-md shadow-sm border border-gray-200">
            <Label htmlFor="end_date" className="text-sm font-medium text-gray-700 flex items-center">
              <span className="bg-indigo-100 text-indigo-800 p-1 rounded-md mr-2 text-xs">Date de fin</span>
              <span className="text-red-500 ml-1">*</span>
            </Label>
            <Popover open={endDateOpen} onOpenChange={setEndDateOpen}>
              <PopoverTrigger asChild>
                <Button
                  type="button"
                  id="end_date"
                  variant="outline"
                  className={`w-full mt-1 justify-start text-left font-normal text-sm ${errors.end_date ? 'border-red-500' : 'border-indigo-200'} hover:bg-indigo-50`}
                  onClick={() => setEndDateOpen(true)}
                >
                  <CalendarIcon className="mr-2 h-4 w-4 text-indigo-600" />
                  {formData.end_date ? formatDate(formData.end_date) : <span>Sélectionner une date</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent
                className="w-auto p-0"
                align="start"
                onOpenAutoFocus={(e) => e.preventDefault()}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    console.log('🚫 TeamTask Enter bloqué dans PopoverContent end_date');
                    e.preventDefault();
                    e.stopPropagation();
                  }
                }}
              >
                <div className="p-2">
                  <Calendar
                    mode="single"
                    selected={formData.end_date}
                    onSelect={(date) => {
                      console.log('🗓️ TeamTask Calendar onSelect appelé pour end_date:', date);
                      if (date) {
                        handleDateChange(date, 'end_date');
                        setEndDateOpen(false);
                      }
                    }}
                    initialFocus
                    /* locale supprimé */
                    disabled={(date) => date < formData.start_date}
                  />
                  <div className="mt-2 flex justify-end">
                    <Button
                      type="button"
                      size="sm"
                      className="bg-indigo-600 hover:bg-indigo-700 text-white"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        setEndDateOpen(false);
                      }}
                    >
                      Confirmer
                    </Button>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
            {errors.end_date && (
              <p className="mt-1 text-xs text-red-500 bg-red-50 p-1 rounded-md">{errors.end_date}</p>
            )}
          </div>
        </div>

        <div className="bg-white p-2 rounded-md shadow-sm border border-gray-200">
          <Label htmlFor="team_id" className="text-sm font-medium text-gray-700 flex items-center">
            <span className="bg-indigo-100 text-indigo-800 p-1 rounded-md mr-2 text-xs">Équipe</span>
            <span className="text-red-500 ml-1">*</span>
          </Label>
          <Select
            value={formData.team_id}
            onValueChange={(value) => handleInputChange('team_id', value)}
            disabled={teamsLoading}
          >
            <SelectTrigger
              id="team_id"
              className={`mt-2 ${errors.team_id ? 'border-red-600 border-4' : 'border-gray-800 border-4'} bg-white hover:border-gray-900 focus:border-blue-600 focus:ring-4 focus:ring-blue-500 h-14 shadow-xl`}
              style={{
                color: '#000000',
                fontWeight: '900',
                fontSize: '18px',
                backgroundColor: '#ffffff'
              }}
            >
              <SelectValue
                placeholder="Sélectionner une équipe"
                style={{
                  color: '#000000',
                  fontWeight: '900',
                  fontSize: '18px'
                }}
              />
            </SelectTrigger>
            <SelectContent>
              {adminTeams.length > 0 ? (
                adminTeams.map((team) => (
                  <SelectItem
                    key={team.id}
                    value={team.id}
                    className="py-4 px-4"
                    style={{
                      color: '#000000',
                      fontWeight: '900',
                      fontSize: '16px',
                      backgroundColor: 'transparent'
                    }}
                  >
                    {team.name}
                  </SelectItem>
                ))
              ) : (
                <div className="p-4 text-base text-black font-bold italic">Aucune équipe disponible</div>
              )}
            </SelectContent>
          </Select>
          {errors.team_id && (
            <p className="mt-1 text-xs text-red-500 bg-red-50 p-1 rounded-md">{errors.team_id}</p>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <div className="bg-white p-2 rounded-md shadow-sm border border-gray-200">
            <Label htmlFor="member_id" className="text-sm font-medium text-gray-700 flex items-center">
              <span className="bg-indigo-100 text-indigo-800 p-1 rounded-md mr-2 text-xs">Membre assigné</span>
            </Label>
            <Select
              value={formData.member_id}
              onValueChange={(value) => handleInputChange('member_id', value)}
              disabled={!formData.team_id || teamMembers.length === 0}
            >
              <SelectTrigger
                id="member_id"
                className="mt-2 border-gray-800 border-4 bg-white hover:border-gray-900 focus:border-blue-600 focus:ring-4 focus:ring-blue-500 h-14 shadow-xl"
                style={{
                  color: '#000000',
                  fontWeight: '900',
                  fontSize: '18px',
                  backgroundColor: '#ffffff'
                }}
              >
                <SelectValue
                  placeholder="Sélectionner un membre (optionnel)"
                  style={{
                    color: '#000000',
                    fontWeight: '900',
                    fontSize: '18px'
                  }}
                />
              </SelectTrigger>
              <SelectContent>
                <SelectItem
                  value=""
                  className="py-4 px-4"
                  style={{
                    color: '#666666',
                    fontWeight: '900',
                    fontSize: '16px',
                    fontStyle: 'italic'
                  }}
                >
                  Aucun membre assigné
                </SelectItem>
                {teamMembers.map((member) => (
                  <SelectItem
                    key={member.id}
                    value={member.id}
                    className="py-4 px-4"
                    style={{
                      color: '#000000',
                      fontWeight: '900',
                      fontSize: '16px'
                    }}
                  >
                    {member.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="bg-white p-2 rounded-md shadow-sm border border-gray-200">
            <Label htmlFor="priority" className="text-sm font-medium text-gray-700 flex items-center">
              <span className="bg-indigo-100 text-indigo-800 p-1 rounded-md mr-2 text-xs">Priorité</span>
            </Label>
            <Select
              value={formData.priority}
              onValueChange={(value) => handleInputChange('priority', value)}
            >
              <SelectTrigger
                id="priority"
                className="mt-2 border-gray-800 border-4 bg-white hover:border-gray-900 focus:border-blue-600 focus:ring-4 focus:ring-blue-500 h-14 shadow-xl"
                style={{
                  color: '#000000',
                  fontWeight: '900',
                  fontSize: '18px',
                  backgroundColor: '#ffffff'
                }}
              >
                <SelectValue
                  placeholder="Sélectionner une priorité"
                  style={{
                    color: '#000000',
                    fontWeight: '900',
                    fontSize: '18px'
                  }}
                />
              </SelectTrigger>
              <SelectContent>
                <SelectItem
                  value="faible"
                  className="py-4 px-4"
                  style={{
                    color: '#15803d',
                    fontWeight: '900',
                    fontSize: '16px'
                  }}
                >
                  Faible
                </SelectItem>
                <SelectItem
                  value="moyenne"
                  className="py-4 px-4"
                  style={{
                    color: '#d97706',
                    fontWeight: '900',
                    fontSize: '16px'
                  }}
                >
                  Moyenne
                </SelectItem>
                <SelectItem
                  value="haute"
                  className="py-4 px-4"
                  style={{
                    color: '#dc2626',
                    fontWeight: '900',
                    fontSize: '16px'
                  }}
                >
                  Haute
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      <div className="flex justify-end space-x-2 pt-3 border-t border-gray-200">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isSubmitting}
          className="text-sm h-9 px-3"
        >
          Annuler
        </Button>
        <Button
          type="submit"
          disabled={isSubmitting}
          className="bg-indigo-600 hover:bg-indigo-700 text-white text-sm h-9 px-3"
          onClick={() => {
            console.log('🎯 TeamTask Bouton submit cliqué - soumission intentionnelle');
            setIsIntentionalSubmit(true);
          }}
        >
          {isSubmitting ? (
            <>
              <Loader2 className="mr-1 h-3 w-3 animate-spin" />
              Enregistrement...
            </>
          ) : (
            <>Enregistrer</>
          )}
        </Button>
      </div>
    </form>
  );
};

export default TeamTaskForm;
