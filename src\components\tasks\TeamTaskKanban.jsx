import React, { useState } from 'react';
import { Edit, Trash2, CheckCircle, Clock, Circle, AlertCircle, Archive, MoreVertical, Calendar, User, Users, FileCheck } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from '@/components/ui/dropdown-menu';
import { DragDropContext, Draggable } from 'react-beautiful-dnd';
import StrictModeDroppable from './StrictModeDroppable';
import { toast } from 'react-toastify';

const TeamTaskKanban = ({ tasks, onEdit, onDelete, onUpdateStatus, onArchive, onUnarchive }) => {
    const [isDragging, setIsDragging] = useState(false);

    // Fonction pour formater les dates
    const formatDate = (dateString) => {
        if (!dateString) return 'Date inconnue';
        try {
            return new Date(dateString).toLocaleDateString('fr-FR', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            });
        } catch (error) {
            console.error('Erreur de formatage de date:', error, dateString);
            return 'Date inconnue';
        }
    };

    // Fonction pour obtenir la couleur en fonction de la priorité
    const getPriorityColor = (priority) => {
        switch (priority) {
            case 'haute':
                return 'bg-red-500 text-white';
            case 'moyenne':
                return 'bg-yellow-500 text-white';
            case 'faible':
                return 'bg-green-500 text-white';
            default:
                return 'bg-gray-500 text-white';
        }
    };

    // Fonction pour obtenir le libellé de la priorité
    const getPriorityLabel = (priority) => {
        switch (priority) {
            case 'haute':
                return 'Haute';
            case 'moyenne':
                return 'Moyenne';
            case 'faible':
                return 'Faible';
            default:
                return 'Normale';
        }
    };

    // Définir les colonnes du Kanban
    const columns = [
        { id: 'a_faire', title: 'À faire', color: 'bg-yellow-500', icon: <Clock className="h-4 w-4" aria-hidden="false" /> },
        { id: 'en_cours', title: 'En cours', color: 'bg-blue-500', icon: <Circle className="h-4 w-4" aria-hidden="false" /> },
        // Option "en révision" temporairement désactivée
        // { id: 'en_revision', title: 'En révision', color: 'bg-purple-500', icon: <FileCheck className="h-4 w-4" aria-hidden="false" /> },
        { id: 'achevee', title: 'Terminé', color: 'bg-green-500', icon: <CheckCircle className="h-4 w-4" aria-hidden="false" /> },
        { id: 'archived', title: 'Archivées', color: 'bg-gray-500', icon: <Archive className="h-4 w-4" aria-hidden="false" /> }
    ];

    // Grouper les tâches par statut
    const tasksByStatus = {
        a_faire: tasks.filter(task => task.status === 'a_faire'),
        en_cours: tasks.filter(task => task.status === 'en_cours'),
        // Temporairement désactivé, mais on garde le code pour les tâches existantes
        en_revision: tasks.filter(task => task.status === 'en_revision'),
        achevee: tasks.filter(task => task.status === 'achevee'),
        archived: tasks.filter(task => task.status === 'archived'),
    };

    // Gérer la fin du drag and drop
    const handleDragEnd = (result) => {
        setIsDragging(false);

        // Si pas de destination ou même colonne, ne rien faire
        if (!result.destination || result.source.droppableId === result.destination.droppableId) {
            return;
        }

        const taskId = result.draggableId;
        const newStatus = result.destination.droppableId;

        // Vérifier si l'utilisateur peut mettre à jour le statut
        const task = tasks.find(t => t.id.toString() === taskId);

        if (!task) {
            toast.error("Tâche introuvable");
            return;
        }

        if (!task.permissions?.canUpdateStatus) {
            toast.error("Vous n'avez pas la permission de modifier le statut de cette tâche");
            return;
        }

        // Mettre à jour le statut
        onUpdateStatus(taskId, newStatus);

        // Le message de succès est déjà affiché par le contexte
        // Ajouter un effet de vibration pour le feedback tactile si disponible
        if (window.navigator && window.navigator.vibrate) {
            window.navigator.vibrate(100); // vibration de 100ms
        }
    };

    // Gérer le début du drag
    const handleDragStart = () => {
        setIsDragging(true);
    };

    // Fonction pour rendre une carte de tâche
    const renderTaskCard = (task, index) => (
        <Draggable
            key={task.id.toString()}
            draggableId={task.id.toString()}
            index={index}
            isDragDisabled={!task.permissions?.canUpdateStatus || task.status === 'archived'}
        >
            {(provided, snapshot) => (
                <div
                    ref={provided.innerRef}
                    {...provided.draggableProps}
                    {...provided.dragHandleProps}
                    className={`rounded-md shadow-sm border p-4 mb-3
                        ${task.status === 'archived' ? 'bg-gray-100 border-gray-300' : 'bg-white border-gray-200'}
                        ${snapshot.isDragging ? 'shadow-lg ring-2 ring-indigo-500 ring-opacity-50' : 'hover:shadow-md'}
                        transition-all`}
                    style={{
                        ...provided.draggableProps.style,
                        opacity: task.status === 'archived' ? 0.85 : (task.permissions?.canUpdateStatus ? 1 : 0.7)
                    }}
                    aria-roledescription="Tâche déplaçable"
                    aria-label={`Tâche: ${task.title}. Statut actuel: ${task.status}. ${task.permissions?.canUpdateStatus ? 'Vous pouvez déplacer cette tâche.' : 'Vous ne pouvez pas déplacer cette tâche.'}`}
                >
                    <div className="flex justify-between items-start mb-2">
                        <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(task.priority || 'moyenne')}`}>
                            {getPriorityLabel(task.priority || 'moyenne')}
                        </span>
                        <div className="flex space-x-1">
                            {/* Bouton Modifier - Uniquement pour les tâches non archivées */}
                            {task.status !== 'archived' && (
                                <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => onEdit(task)}
                                    className="h-6 w-6 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-full"
                                    title="Modifier"
                                >
                                    <Edit className="h-3 w-3" aria-hidden="false" />
                                </Button>
                            )}

                            {/* Bouton Supprimer - Disponible pour toutes les tâches */}
                            <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => onDelete(task.id)}
                                className={`h-6 w-6 ${task.status === 'archived' ? 'text-red-700' : 'text-red-600'} hover:text-red-800 hover:bg-red-50 rounded-full`}
                                title="Supprimer"
                            >
                                <Trash2 className="h-3 w-3" aria-hidden="false" />
                            </Button>

                            {/* Bouton Archiver/Désarchiver selon le statut - Uniquement pour les admins */}
                            {task.permissions?.canArchive && task.status !== 'archived' && (
                                <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => onArchive(task.id)}
                                    className="h-6 w-6 text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-full"
                                    title="Archiver"
                                >
                                    <Archive className="h-3 w-3" />
                                </Button>
                            )}
                            {task.permissions?.canUnarchive && task.status === 'archived' && (
                                <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => onUnarchive(task.id)}
                                    className="h-6 w-6 text-green-600 hover:text-green-800 hover:bg-green-50 rounded-full"
                                    title="Désarchiver"
                                >
                                    <Archive className="h-3 w-3" />
                                </Button>
                            )}
                        </div>
                    </div>

                    <h3 className={`font-medium mb-1 ${task.status === 'archived' ? 'line-through text-gray-500' : 'text-gray-900'}`}>
                        {task.title}
                    </h3>
                    <p className={`text-sm mb-3 line-clamp-2 ${task.status === 'archived' ? 'line-through text-gray-400' : 'text-gray-500'}`}>
                        {task.description || 'Aucune description'}
                    </p>

                    <div className={`flex items-center text-xs ${task.status === 'archived' ? 'text-gray-400' : 'text-gray-600'} mb-2`}>
                        <Calendar className="h-3 w-3 mr-1 text-gray-400" aria-hidden="false" />
                        <span className={task.status === 'archived' ? 'line-through' : ''}>
                            {formatDate(task.end_date)}
                        </span>
                    </div>

                    <div className={`flex items-center text-xs ${task.status === 'archived' ? 'text-gray-400' : 'text-gray-600'}`}>
                        <User className="h-3 w-3 mr-1 text-gray-400" />
                        <span className={task.status === 'archived' ? 'line-through' : ''}>
                            {task.member_id ?
                                (task.member_name || "Membre spécifique") :
                                "Toute l'équipe"
                            }
                        </span>
                    </div>

                    {/* Boutons de statut rapides pour les employés assignés */}
                    {task.permissions?.canUpdateStatus && task.status !== 'archived' && (
                        <div className="mt-3 pt-3 border-t border-gray-200">
                            <div className="flex flex-wrap gap-1">
                                {task.status !== 'a_faire' && (
                                    <Button
                                        size="sm"
                                        variant="outline"
                                        onClick={() => onUpdateStatus(task.id, 'a_faire')}
                                        className="text-xs px-2 py-1 h-6 text-yellow-600 border-yellow-200 hover:bg-yellow-50"
                                        title="Marquer comme À faire"
                                    >
                                        <Clock className="h-3 w-3 mr-1" />
                                        À faire
                                    </Button>
                                )}
                                {task.status !== 'en_cours' && (
                                    <Button
                                        size="sm"
                                        variant="outline"
                                        onClick={() => onUpdateStatus(task.id, 'en_cours')}
                                        className="text-xs px-2 py-1 h-6 text-blue-600 border-blue-200 hover:bg-blue-50"
                                        title="Marquer comme En cours"
                                    >
                                        <Circle className="h-3 w-3 mr-1" />
                                        En cours
                                    </Button>
                                )}
                                {task.status !== 'achevee' && (
                                    <Button
                                        size="sm"
                                        variant="outline"
                                        onClick={() => onUpdateStatus(task.id, 'achevee')}
                                        className="text-xs px-2 py-1 h-6 text-green-600 border-green-200 hover:bg-green-50"
                                        title="Marquer comme Achevée"
                                    >
                                        <CheckCircle className="h-3 w-3 mr-1" />
                                        Achevée
                                    </Button>
                                )}
                            </div>
                        </div>
                    )}

                    {task.status === 'archived' && (
                        <div className="mt-2 text-xs text-gray-500 italic bg-gray-100 p-1 rounded-md">
                            Tâche archivée
                        </div>
                    )}

                    {!task.permissions?.canUpdateStatus && task.status !== 'archived' && (
                        <div className="mt-2 text-xs text-gray-500 italic">
                            Vous ne pouvez pas déplacer cette tâche
                        </div>
                    )}
                </div>
            )}
        </Draggable>
    );

    return (
        <DragDropContext onDragEnd={handleDragEnd} onDragStart={handleDragStart}>
            <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md text-blue-700 text-sm">
                <p className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                    Glissez-déposez les tâches entre les colonnes pour changer leur statut. Seuls les employés assignés peuvent déplacer les tâches.
                </p>
            </div>
            <div className={`grid grid-cols-1 md:grid-cols-4 gap-6 ${isDragging ? 'opacity-95 bg-gray-50 p-2 rounded-lg transition-all duration-300' : ''}`}>
                {columns.map(column => (
                    <div key={column.id} className="bg-gray-50 rounded-lg p-4">
                        <h3 className="font-medium text-gray-700 mb-3 flex items-center">
                            <span className={`w-3 h-3 rounded-full ${column.color} mr-2`}></span>
                            {column.title}
                            <span className="ml-2 text-xs bg-gray-200 text-gray-700 px-2 py-0.5 rounded-full">
                                {tasksByStatus[column.id].length}
                            </span>
                        </h3>
                        <StrictModeDroppable droppableId={column.id}>
                            {(provided, snapshot) => (
                                <div
                                    ref={provided.innerRef}
                                    {...provided.droppableProps}
                                    className={`space-y-3 min-h-[200px] transition-colors ${snapshot.isDraggingOver
                                        ? `bg-gray-100 rounded-md p-2 ring-2 ring-${column.color.replace('bg-', '')} ring-opacity-50`
                                        : ''
                                        }`}
                                    aria-label={`Colonne ${column.title} contenant ${tasksByStatus[column.id].length} tâches`}
                                    aria-roledescription="Zone de dépôt pour les tâches"
                                >
                                    {tasksByStatus[column.id].map((task, index) => renderTaskCard(task, index))}
                                    {provided.placeholder}
                                </div>
                            )}
                        </StrictModeDroppable>
                    </div>
                ))}
            </div>
        </DragDropContext>
    );
};

export default TeamTaskKanban;
