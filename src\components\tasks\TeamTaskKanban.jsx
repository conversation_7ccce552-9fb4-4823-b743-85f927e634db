import React, { useState } from 'react';
import { Edit, Trash2, CheckCircle, Clock, Circle, AlertCircle, Archive, MoreVertical, Calendar, User, Users, FileCheck } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from '@/components/ui/dropdown-menu';
import { DragDropContext, Draggable } from 'react-beautiful-dnd';
import StrictModeDroppable from './StrictModeDroppable';
import { toast } from 'react-toastify';

const TeamTaskKanban = ({ tasks, onEdit, onDelete, onUpdateStatus, onArchive, onUnarchive }) => {
    const [isDragging, setIsDragging] = useState(false);

    // Fonction pour formater les dates
    const formatDate = (dateString) => {
        if (!dateString) return 'Date inconnue';
        try {
            return new Date(dateString).toLocaleDateString('fr-FR', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            });
        } catch (error) {
            console.error('Erreur de formatage de date:', error, dateString);
            return 'Date inconnue';
        }
    };

    // Fonction pour obtenir la couleur en fonction de la priorité
    const getPriorityColor = (priority) => {
        switch (priority) {
            case 'haute':
                return 'bg-red-500 text-white';
            case 'moyenne':
                return 'bg-yellow-500 text-white';
            case 'faible':
                return 'bg-green-500 text-white';
            default:
                return 'bg-gray-500 text-white';
        }
    };

    // Fonction pour obtenir le libellé de la priorité
    const getPriorityLabel = (priority) => {
        switch (priority) {
            case 'haute':
                return 'Haute';
            case 'moyenne':
                return 'Moyenne';
            case 'faible':
                return 'Faible';
            default:
                return 'Normale';
        }
    };

    // Définir les colonnes du Kanban
    const columns = [
        { id: 'a_faire', title: 'À faire', color: 'bg-blue-500', icon: <Clock className="h-4 w-4" aria-hidden="false" /> },
        { id: 'en_cours', title: 'En cours', color: 'bg-orange-500', icon: <Circle className="h-4 w-4" aria-hidden="false" /> },
        { id: 'achevee', title: 'Achevée', color: 'bg-green-500', icon: <CheckCircle className="h-4 w-4" aria-hidden="false" /> },
        { id: 'archived', title: 'Archivée', color: 'bg-gray-500', icon: <Archive className="h-4 w-4" aria-hidden="false" /> }
    ];

    // Grouper les tâches par statut
    const tasksByStatus = {
        a_faire: tasks.filter(task => task.status === 'a_faire'),
        en_cours: tasks.filter(task => task.status === 'en_cours'),
        // Temporairement désactivé, mais on garde le code pour les tâches existantes
        en_revision: tasks.filter(task => task.status === 'en_revision'),
        achevee: tasks.filter(task => task.status === 'achevee'),
        archived: tasks.filter(task => task.status === 'archived'),
    };

    // Gérer la fin du drag and drop
    const handleDragEnd = (result) => {
        setIsDragging(false);

        // Si pas de destination ou même colonne, ne rien faire
        if (!result.destination || result.source.droppableId === result.destination.droppableId) {
            return;
        }

        const taskId = result.draggableId;
        const newStatus = result.destination.droppableId;

        // Vérifier si l'utilisateur peut mettre à jour le statut
        const task = tasks.find(t => t.id.toString() === taskId);

        if (!task) {
            toast.error("Tâche introuvable");
            return;
        }

        if (!task.permissions?.canUpdateStatus) {
            toast.error("Vous n'avez pas la permission de modifier le statut de cette tâche");
            return;
        }

        // Mettre à jour le statut
        onUpdateStatus(taskId, newStatus);

        // Le message de succès est déjà affiché par le contexte
        // Ajouter un effet de vibration pour le feedback tactile si disponible
        if (window.navigator && window.navigator.vibrate) {
            window.navigator.vibrate(100); // vibration de 100ms
        }
    };

    // Gérer le début du drag
    const handleDragStart = () => {
        setIsDragging(true);
    };

    // Fonction pour rendre une carte de tâche simplifiée
    const renderTaskCard = (task, index) => (
        <Draggable
            key={task.id.toString()}
            draggableId={task.id.toString()}
            index={index}
            isDragDisabled={!task.permissions?.canUpdateStatus || task.status === 'archived'}
        >
            {(provided, snapshot) => (
                <div
                    ref={provided.innerRef}
                    {...provided.draggableProps}
                    {...provided.dragHandleProps}
                    className={`rounded-lg shadow-sm border p-4 mb-3 cursor-pointer
                        ${task.status === 'archived' ? 'bg-gray-50 border-gray-300' : 'bg-white border-gray-200'}
                        ${snapshot.isDragging ? 'shadow-lg ring-2 ring-blue-500 ring-opacity-50' : 'hover:shadow-md'}
                        transition-all duration-200`}
                    style={{
                        ...provided.draggableProps.style,
                        opacity: task.status === 'archived' ? 0.85 : (task.permissions?.canUpdateStatus ? 1 : 0.7)
                    }}
                >
                    {/* En-tête avec titre et menu trois points */}
                    <div className="flex justify-between items-start mb-3">
                        <h3 className={`font-semibold text-sm ${task.status === 'archived' ? 'line-through text-gray-500' : 'text-gray-900'}`}>
                            {task.title}
                        </h3>

                        {/* Menu trois points */}
                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm" className="h-6 w-6 p-0 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full">
                                    <MoreVertical className="h-3 w-3" />
                                </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end" className="w-40 shadow-lg rounded-lg border border-gray-200">
                                {/* Actions de modification du statut pour les employés assignés */}
                                {task.permissions?.canUpdateStatus && task.status !== 'archived' && (
                                    <>
                                        <DropdownMenuItem
                                            onClick={() => onUpdateStatus(task.id, 'a_faire')}
                                            className="hover:bg-yellow-50 text-yellow-700 text-xs"
                                            disabled={task.status === 'a_faire'}
                                        >
                                            <Clock className="mr-2 h-3 w-3" />
                                            À faire
                                        </DropdownMenuItem>
                                        <DropdownMenuItem
                                            onClick={() => onUpdateStatus(task.id, 'en_cours')}
                                            className="hover:bg-blue-50 text-blue-700 text-xs"
                                            disabled={task.status === 'en_cours'}
                                        >
                                            <Circle className="mr-2 h-3 w-3" />
                                            En cours
                                        </DropdownMenuItem>
                                        <DropdownMenuItem
                                            onClick={() => onUpdateStatus(task.id, 'achevee')}
                                            className="hover:bg-green-50 text-green-700 text-xs"
                                            disabled={task.status === 'achevee'}
                                        >
                                            <CheckCircle className="mr-2 h-3 w-3" />
                                            Achevée
                                        </DropdownMenuItem>
                                        <div className="border-t border-gray-200 my-1" />
                                    </>
                                )}

                                {/* Action Modifier */}
                                {task.permissions?.canEdit && task.status !== 'archived' && (
                                    <DropdownMenuItem
                                        onClick={() => onEdit(task)}
                                        className="hover:bg-blue-50 text-blue-700 text-xs"
                                    >
                                        <Edit className="mr-2 h-3 w-3" />
                                        Modifier
                                    </DropdownMenuItem>
                                )}

                                {/* Action Archiver/Désarchiver */}
                                {task.permissions?.canArchive && task.status !== 'archived' && (
                                    <DropdownMenuItem
                                        onClick={() => onArchive(task.id)}
                                        className="hover:bg-gray-50 text-gray-700 text-xs"
                                    >
                                        <Archive className="mr-2 h-3 w-3" />
                                        Archiver
                                    </DropdownMenuItem>
                                )}
                                {task.permissions?.canUnarchive && task.status === 'archived' && (
                                    <DropdownMenuItem
                                        onClick={() => onUnarchive(task.id)}
                                        className="hover:bg-green-50 text-green-700 text-xs"
                                    >
                                        <Archive className="mr-2 h-3 w-3" />
                                        Désarchiver
                                    </DropdownMenuItem>
                                )}

                                {/* Action Supprimer */}
                                {task.permissions?.canDelete && (
                                    <DropdownMenuItem
                                        onClick={() => onDelete(task.id)}
                                        className="hover:bg-red-50 text-red-700 text-xs"
                                    >
                                        <Trash2 className="mr-2 h-3 w-3" />
                                        Supprimer
                                    </DropdownMenuItem>
                                )}
                            </DropdownMenuContent>
                        </DropdownMenu>
                    </div>

                    {/* Description */}
                    <p className={`text-xs mb-3 ${task.status === 'archived' ? 'line-through text-gray-400' : 'text-gray-600'}`}>
                        {task.description || 'Description détaillée de la tâche'}
                    </p>

                    {/* Badge de priorité */}
                    <div className="mb-3">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(task.priority || 'moyenne')} text-white`}>
                            {getPriorityLabel(task.priority || 'moyenne')}
                        </span>
                    </div>

                    {/* Date */}
                    <div className="flex items-center text-xs text-gray-500">
                        <Calendar className="h-3 w-3 mr-1" />
                        <span className={task.status === 'archived' ? 'line-through' : ''}>
                            {formatDate(task.end_date)}
                        </span>
                    </div>
                </div>
            )}
        </Draggable>
    );

    return (
        <DragDropContext onDragEnd={handleDragEnd} onDragStart={handleDragStart}>
            <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg text-blue-700 text-sm">
                <p className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                    Glissez-déposez les tâches entre les colonnes pour changer leur statut. Seuls les employés assignés peuvent déplacer les tâches.
                </p>
            </div>
            <div className={`grid grid-cols-1 md:grid-cols-4 gap-6 ${isDragging ? 'opacity-95 bg-gray-50 p-2 rounded-lg transition-all duration-300' : ''}`}>
                {columns.map(column => (
                    <div key={column.id} className="bg-white rounded-lg border border-gray-200 p-4">
                        <h3 className="font-semibold text-gray-900 mb-4 flex items-center">
                            {column.icon}
                            <span className="ml-2">{column.title}</span>
                            <span className="ml-auto text-sm bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                                {tasksByStatus[column.id].length}
                            </span>
                        </h3>
                        <StrictModeDroppable droppableId={column.id}>
                            {(provided, snapshot) => (
                                <div
                                    ref={provided.innerRef}
                                    {...provided.droppableProps}
                                    className={`space-y-3 min-h-[300px] transition-colors ${snapshot.isDraggingOver
                                        ? `bg-gray-50 rounded-md p-2 ring-2 ring-blue-300 ring-opacity-50`
                                        : ''
                                        }`}
                                    aria-label={`Colonne ${column.title} contenant ${tasksByStatus[column.id].length} tâches`}
                                    aria-roledescription="Zone de dépôt pour les tâches"
                                >
                                    {tasksByStatus[column.id].map((task, index) => renderTaskCard(task, index))}
                                    {provided.placeholder}
                                </div>
                            )}
                        </StrictModeDroppable>
                    </div>
                ))}
            </div>
        </DragDropContext>
    );
};

export default TeamTaskKanban;
