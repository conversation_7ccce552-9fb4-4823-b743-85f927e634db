import React from 'react';
import { Edit, Trash2, CheckCircle, Clock, Circle, AlertCircle, Archive, MoreVertical, Calendar, User, Users, FileCheck } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from '@/components/ui/dropdown-menu';
import { formatMemberAssignment } from '@/utils/memberDisplayUtils';

const TeamTaskList = ({ tasks, onEdit, onDelete, onUpdateStatus, onArchive, onUnarchive }) => {
    // Fonction pour formater les dates
    const formatDate = (dateString) => {
        if (!dateString) return 'Date inconnue';
        try {
            return new Date(dateString).toLocaleDateString('fr-FR', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            });
        } catch (error) {
            console.error('Erreur de formatage de date:', error, dateString);
            return 'Date inconnue';
        }
    };

    // Fonction pour obtenir la couleur en fonction du statut
    const getStatusColor = (status) => {
        switch (status) {
            case 'achevee':
                return 'bg-green-500 text-white';
            case 'en_cours':
                return 'bg-blue-500 text-white';
            case 'a_faire':
                return 'bg-yellow-500 text-white';
            // Option "en révision" temporairement désactivée dans l'interface mais gérée pour les tâches existantes
            case 'en_revision':
                return 'bg-purple-500 text-white';
            case 'archived':
                return 'bg-gray-500 text-white';
            default:
                return 'bg-blue-500 text-white';
        }
    };

    // Fonction pour obtenir le libellé du statut
    const getStatusLabel = (status) => {
        switch (status) {
            case 'achevee':
                return 'Terminé';
            case 'en_cours':
                return 'En cours';
            case 'a_faire':
                return 'À faire';
            // Option "en révision" temporairement désactivée dans l'interface mais gérée pour les tâches existantes
            case 'en_revision':
                return 'En révision';
            case 'archived':
                return 'Archivée';
            default:
                return 'Inconnu';
        }
    };

    // Fonction pour obtenir la couleur de la priorité
    const getPriorityColor = (priority) => {
        switch (priority) {
            case 'haute':
                return 'bg-red-500 text-white';
            case 'moyenne':
                return 'bg-yellow-500 text-white';
            case 'faible':
                return 'bg-green-500 text-white';
            default:
                return 'bg-gray-500 text-white';
        }
    };

    // Fonction pour obtenir le libellé de la priorité
    const getPriorityLabel = (priority) => {
        switch (priority) {
            case 'haute':
                return 'Haute';
            case 'moyenne':
                return 'Moyenne';
            case 'faible':
                return 'Faible';
            default:
                return 'Normale';
        }
    };

    return (
        <div className="overflow-x-auto">
            <table className="w-full border-collapse">
                <thead>
                    <tr className="bg-gray-50 border-b border-gray-200">
                        <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">Titre</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">Statut</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">Priorité</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">Assigné à</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">Équipe</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">Échéance</th>
                        <th className="px-4 py-3 text-center text-sm font-medium text-gray-700">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {tasks.map((task) => (
                        <tr key={task.id} className={`border-b hover:bg-gray-50 ${task.status === 'archived' ? 'bg-gray-100 border-gray-300' : 'border-gray-200'}`}>
                            <td className="px-4 py-4">
                                <div>
                                    <h3 className={`font-medium ${task.status === 'archived' ? 'line-through text-gray-500' : 'text-gray-900'}`}>
                                        {task.title}
                                    </h3>
                                    <p className={`text-sm truncate max-w-xs ${task.status === 'archived' ? 'line-through text-gray-400' : 'text-gray-500'}`}>
                                        {task.description || 'Aucune description'}
                                    </p>
                                    {task.status === 'archived' && (
                                        <span className="text-xs italic text-gray-500 block mt-1">
                                            Tâche archivée
                                        </span>
                                    )}
                                </div>
                            </td>
                            <td className="px-4 py-4">
                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(task.status)}`}>
                                    {getStatusLabel(task.status)}
                                </span>
                            </td>
                            <td className="px-4 py-4">
                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(task.priority || 'moyenne')}`}>
                                    {getPriorityLabel(task.priority || 'moyenne')}
                                </span>
                            </td>
                            <td className="px-4 py-4 text-sm text-gray-700">
                                <span className={task.status === 'archived' ? 'line-through text-gray-500' : ''}>
                                    {formatMemberAssignment(task.member_id, task.member_name)}
                                </span>
                            </td>
                            <td className="px-4 py-4 text-sm text-gray-700">
                                {task.team_name || '-'}
                            </td>
                            <td className="px-4 py-4 text-sm text-gray-700">
                                <span className={task.status === 'archived' ? 'line-through text-gray-500' : ''}>
                                    {formatDate(task.end_date)}
                                </span>
                            </td>
                            <td className="px-4 py-4 text-right">
                                <div className="flex justify-center space-x-2">
                                    {/* Bouton Modifier - Uniquement pour les tâches non archivées */}
                                    {task.status !== 'archived' && (
                                        <Button
                                            variant="ghost"
                                            size="icon"
                                            onClick={() => onEdit(task)}
                                            className="h-8 w-8 text-blue-600 hover:text-blue-800 hover:bg-blue-50"
                                            title="Modifier"
                                        >
                                            <Edit className="h-4 w-4" aria-hidden="false" />
                                        </Button>
                                    )}

                                    {/* Bouton Supprimer - Disponible pour toutes les tâches */}
                                    <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={() => onDelete(task.id)}
                                        className={`h-8 w-8 ${task.status === 'archived' ? 'text-red-700' : 'text-red-600'} hover:text-red-800 hover:bg-red-50`}
                                        title="Supprimer"
                                    >
                                        <Trash2 className="h-4 w-4" />
                                    </Button>

                                    {/* Bouton Archiver/Désarchiver selon le statut - Uniquement pour les admins */}
                                    {task.permissions?.canArchive && task.status !== 'archived' && (
                                        <Button
                                            variant="ghost"
                                            size="icon"
                                            onClick={() => onArchive(task.id)}
                                            className="h-8 w-8 text-gray-600 hover:text-gray-800 hover:bg-gray-50"
                                            title="Archiver"
                                        >
                                            <Archive className="h-4 w-4" />
                                        </Button>
                                    )}
                                    {task.permissions?.canUnarchive && task.status === 'archived' && (
                                        <Button
                                            variant="ghost"
                                            size="icon"
                                            onClick={() => onUnarchive(task.id)}
                                            className="h-8 w-8 text-green-600 hover:text-green-800 hover:bg-green-50"
                                            title="Désarchiver"
                                        >
                                            <Archive className="h-4 w-4" aria-hidden="false" />
                                        </Button>
                                    )}
                                </div>
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
};

export default TeamTaskList;
