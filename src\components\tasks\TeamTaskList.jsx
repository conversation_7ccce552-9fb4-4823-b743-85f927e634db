import React, { useState } from 'react';
import { Edit, Trash2, CheckCircle, Clock, Circle, AlertCircle, Archive, MoreVertical, Calendar, User, Users, FileCheck } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from '@/components/ui/dropdown-menu';
import { formatMemberAssignment } from '@/utils/memberDisplayUtils';

const TeamTaskList = ({ tasks, onEdit, onDelete, onUpdateStatus, onArchive, onUnarchive }) => {
    const [openStatusMenus, setOpenStatusMenus] = useState({});
    // Fonction pour formater les dates
    const formatDate = (dateString) => {
        if (!dateString) return 'Date inconnue';
        try {
            return new Date(dateString).toLocaleDateString('fr-FR', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            });
        } catch (error) {
            console.error('Erreur de formatage de date:', error, dateString);
            return 'Date inconnue';
        }
    };

    // Fonction pour gérer l'ouverture/fermeture des menus de statut
    const toggleStatusMenu = (taskId) => {
        setOpenStatusMenus(prev => ({
            ...prev,
            [taskId]: !prev[taskId]
        }));
    };

    // Fonction pour obtenir la couleur en fonction du statut
    const getStatusColor = (status) => {
        switch (status) {
            case 'achevee':
                return 'bg-green-500 text-white';
            case 'en_cours':
                return 'bg-blue-500 text-white';
            case 'a_faire':
                return 'bg-yellow-500 text-white';
            // Option "en révision" temporairement désactivée dans l'interface mais gérée pour les tâches existantes
            case 'en_revision':
                return 'bg-purple-500 text-white';
            case 'archived':
                return 'bg-gray-500 text-white';
            default:
                return 'bg-blue-500 text-white';
        }
    };

    // Fonction pour obtenir le libellé du statut
    const getStatusLabel = (status) => {
        switch (status) {
            case 'achevee':
                return 'Terminé';
            case 'en_cours':
                return 'En cours';
            case 'a_faire':
                return 'À faire';
            // Option "en révision" temporairement désactivée dans l'interface mais gérée pour les tâches existantes
            case 'en_revision':
                return 'En révision';
            case 'archived':
                return 'Archivée';
            default:
                return 'Inconnu';
        }
    };

    // Fonction pour obtenir la couleur de la priorité
    const getPriorityColor = (priority) => {
        switch (priority) {
            case 'haute':
                return 'bg-red-500 text-white';
            case 'moyenne':
                return 'bg-yellow-500 text-white';
            case 'faible':
                return 'bg-green-500 text-white';
            default:
                return 'bg-gray-500 text-white';
        }
    };

    // Fonction pour obtenir le libellé de la priorité
    const getPriorityLabel = (priority) => {
        switch (priority) {
            case 'haute':
                return 'Haute';
            case 'moyenne':
                return 'Moyenne';
            case 'faible':
                return 'Faible';
            default:
                return 'Normale';
        }
    };

    return (
        <div className="overflow-x-auto">
            <table className="w-full border-collapse bg-white rounded-lg shadow-sm">
                <thead>
                    <tr className="bg-gray-50 border-b border-gray-200">
                        <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Tâche</th>
                        <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Dates</th>
                        <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Statut</th>
                        <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Priorité</th>
                        <th className="px-6 py-4 text-center text-sm font-medium text-gray-700">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {tasks.map((task) => (
                        <tr key={task.id} className={`border-b hover:bg-gray-50 transition-colors ${task.status === 'archived' ? 'bg-gray-100 border-gray-300' : 'border-gray-200'}`}>
                            <td className="px-6 py-4">
                                <div>
                                    <h3 className={`font-semibold text-base ${task.status === 'archived' ? 'line-through text-gray-500' : 'text-gray-900'}`}>
                                        {task.title}
                                    </h3>
                                    <p className={`text-sm mt-1 ${task.status === 'archived' ? 'line-through text-gray-400' : 'text-gray-600'}`}>
                                        {task.description || 'Description détaillée de la tâche'}
                                    </p>
                                    {task.status === 'archived' && (
                                        <span className="text-xs italic text-gray-500 block mt-1">
                                            Tâche archivée
                                        </span>
                                    )}
                                </div>
                            </td>
                            <td className="px-6 py-4">
                                <div className="flex items-center text-sm text-gray-600">
                                    <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                                    <span className={task.status === 'archived' ? 'line-through text-gray-500' : ''}>
                                        {formatDate(task.start_date)} - {formatDate(task.end_date)}
                                    </span>
                                </div>
                            </td>
                            <td className="px-6 py-4">
                                <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(task.status)}`}>
                                    {getStatusLabel(task.status)}
                                </span>
                            </td>
                            <td className="px-6 py-4">
                                <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getPriorityColor(task.priority || 'moyenne')}`}>
                                    {getPriorityLabel(task.priority || 'moyenne')}
                                </span>
                            </td>
                            <td className="px-6 py-4 text-center">
                                <DropdownMenu
                                    open={openStatusMenus[task.id] || false}
                                    onOpenChange={() => toggleStatusMenu(task.id)}
                                >
                                    <DropdownMenuTrigger asChild>
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            className="h-8 w-8 p-0 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full"
                                        >
                                            <MoreVertical className="h-4 w-4" />
                                        </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end" className="w-48 shadow-lg rounded-lg border border-gray-200">
                                        {/* Actions de modification du statut pour les employés assignés */}
                                        {task.permissions?.canUpdateStatus && task.status !== 'archived' && (
                                            <>
                                                <DropdownMenuItem
                                                    onClick={() => {
                                                        toggleStatusMenu(task.id);
                                                        onUpdateStatus(task.id, 'a_faire');
                                                    }}
                                                    className="hover:bg-yellow-50 text-yellow-700"
                                                    disabled={task.status === 'a_faire'}
                                                >
                                                    <Clock className="mr-2 h-4 w-4" />
                                                    Marquer comme à faire
                                                </DropdownMenuItem>
                                                <DropdownMenuItem
                                                    onClick={() => {
                                                        toggleStatusMenu(task.id);
                                                        onUpdateStatus(task.id, 'en_cours');
                                                    }}
                                                    className="hover:bg-blue-50 text-blue-700"
                                                    disabled={task.status === 'en_cours'}
                                                >
                                                    <Circle className="mr-2 h-4 w-4" />
                                                    Marquer comme en cours
                                                </DropdownMenuItem>
                                                <DropdownMenuItem
                                                    onClick={() => {
                                                        toggleStatusMenu(task.id);
                                                        onUpdateStatus(task.id, 'achevee');
                                                    }}
                                                    className="hover:bg-green-50 text-green-700"
                                                    disabled={task.status === 'achevee'}
                                                >
                                                    <CheckCircle className="mr-2 h-4 w-4" />
                                                    Marquer comme achevée
                                                </DropdownMenuItem>
                                            </>
                                        )}

                                        {/* Séparateur si il y a des actions de statut ET d'autres actions */}
                                        {task.permissions?.canUpdateStatus && task.status !== 'archived' &&
                                            (task.permissions?.canEdit || task.permissions?.canDelete || task.permissions?.canArchive || task.permissions?.canUnarchive) && (
                                                <div className="border-t border-gray-200 my-1" />
                                            )}

                                        {/* Action Modifier */}
                                        {task.permissions?.canEdit && task.status !== 'archived' && (
                                            <DropdownMenuItem
                                                onClick={() => {
                                                    toggleStatusMenu(task.id);
                                                    onEdit(task);
                                                }}
                                                className="hover:bg-blue-50 text-blue-700"
                                            >
                                                <Edit className="mr-2 h-4 w-4" />
                                                Modifier
                                            </DropdownMenuItem>
                                        )}

                                        {/* Action Archiver/Désarchiver */}
                                        {task.permissions?.canArchive && task.status !== 'archived' && (
                                            <DropdownMenuItem
                                                onClick={() => {
                                                    toggleStatusMenu(task.id);
                                                    onArchive(task.id);
                                                }}
                                                className="hover:bg-gray-50 text-gray-700"
                                            >
                                                <Archive className="mr-2 h-4 w-4" />
                                                Archiver
                                            </DropdownMenuItem>
                                        )}
                                        {task.permissions?.canUnarchive && task.status === 'archived' && (
                                            <DropdownMenuItem
                                                onClick={() => {
                                                    toggleStatusMenu(task.id);
                                                    onUnarchive(task.id);
                                                }}
                                                className="hover:bg-green-50 text-green-700"
                                            >
                                                <Archive className="mr-2 h-4 w-4" />
                                                Désarchiver
                                            </DropdownMenuItem>
                                        )}

                                        {/* Action Supprimer */}
                                        {task.permissions?.canDelete && (
                                            <DropdownMenuItem
                                                onClick={() => {
                                                    toggleStatusMenu(task.id);
                                                    onDelete(task.id);
                                                }}
                                                className="hover:bg-red-50 text-red-700"
                                            >
                                                <Trash2 className="mr-2 h-4 w-4" />
                                                Supprimer
                                            </DropdownMenuItem>
                                        )}
                                    </DropdownMenuContent>
                                </DropdownMenu>
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
};

export default TeamTaskList;
