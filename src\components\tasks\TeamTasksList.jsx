import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { showSuccessToast, showErrorToast } from '@/utils/toastUtils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useAuth } from '@/contexts/AuthContext';
import { useTeam } from '@/contexts/TeamContext';
import teamTaskService from '@/services/teamTaskService';
import { PlusIcon, Search, Loader2 } from 'lucide-react';
import TeamTaskCard from '@/components/tasks/TeamTaskCard';
import TeamTaskList from '@/components/tasks/TeamTaskList';
import TeamTask<PERSON>anban from '@/components/tasks/TeamTaskKanban';
import ViewSelector from '@/components/tasks/ViewSelector';

const TeamTasksList = ({ teamId, showActions = true }) => {
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [displayView, setDisplayView] = useState('list');
  const { user } = useAuth();
  const { teams } = useTeam();
  const navigate = useNavigate();

  // Filtrer les tâches selon la recherche
  const filteredTasks = tasks.filter(task => {
    if (!searchQuery) return true;
    return task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (task.description && task.description.toLowerCase().includes(searchQuery.toLowerCase()));
  });

  // Récupérer les tâches
  const fetchTasks = async () => {
    setLoading(true);
    setError(null);
    try {
      let tasksData;
      if (teamId) {
        // Si un ID d'équipe est fourni, récupérer les tâches de cette équipe
        tasksData = await teamTaskService.getTeamTasks({ team_id: teamId });
      } else {
        // Sinon, récupérer toutes les tâches accessibles à l'utilisateur
        tasksData = await teamTaskService.getTeamTasks();
      }
      console.log('TeamTasksList - Tasks data:', tasksData);
      setTasks(tasksData);
    } catch (err) {
      console.error('TeamTasksList - Error fetching tasks:', err);
      setError(err.message || 'Erreur lors de la récupération des tâches');
      showErrorToast(err.message || 'Erreur lors de la récupération des tâches');
    } finally {
      setLoading(false);
    }
  };

  // Charger les tâches au montage du composant
  useEffect(() => {
    fetchTasks();
  }, [teamId]);

  // Gérer la création d'une nouvelle tâche
  const handleCreateTask = () => {
    navigate('/team-tasks/create');
  };

  // Gérer la modification d'une tâche
  const handleEditTask = (taskId) => {
    navigate(`/team-tasks/edit/${taskId}`);
  };

  // Gérer l'archivage d'une tâche
  const handleArchiveTask = async (taskId) => {
    try {
      await teamTaskService.archiveTeamTask(taskId);
      showSuccessToast('Tâche archivée avec succès');
      fetchTasks();
    } catch (err) {
      console.error('TeamTasksList - Error archiving task:', err);
      showErrorToast(err.message || 'Erreur lors de l\'archivage de la tâche');
    }
  };

  // Gérer le désarchivage d'une tâche
  const handleUnarchiveTask = async (taskId) => {
    try {
      await teamTaskService.unarchiveTeamTask(taskId);
      showSuccessToast('Tâche désarchivée avec succès');
      fetchTasks();
    } catch (err) {
      console.error('TeamTasksList - Error unarchiving task:', err);
      showErrorToast(err.message || 'Erreur lors du désarchivage de la tâche');
    }
  };

  // Gérer la suppression d'une tâche
  const handleDeleteTask = async (taskId) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette tâche ? Cette action est irréversible.')) {
      try {
        await teamTaskService.deleteTeamTask(taskId);
        showSuccessToast('Tâche supprimée avec succès');
        fetchTasks();
      } catch (err) {
        console.error('TeamTasksList - Error deleting task:', err);
        showErrorToast(err.message || 'Erreur lors de la suppression de la tâche');
      }
    }
  };

  // Gérer la mise à jour du statut d'une tâche
  const handleUpdateStatus = async (taskId, status) => {
    try {
      await teamTaskService.updateTeamTaskStatus(taskId, status);
      showSuccessToast('Statut mis à jour avec succès');
      fetchTasks();
    } catch (err) {
      console.error('TeamTasksList - Error updating status:', err);
      showErrorToast(err.message || 'Erreur lors de la mise à jour du statut');
    }
  };

  // Fonction pour rendre le contenu selon la vue sélectionnée
  const renderContent = () => {
    if (loading) {
      return (
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
        </div>
      );
    }

    if (error) {
      return (
        <div className="bg-red-50 p-4 rounded-lg">
          <p className="text-red-600">{error}</p>
        </div>
      );
    }

    if (filteredTasks.length === 0) {
      return (
        <div className="text-center py-12">
          <p className="text-gray-500 mb-4">
            {searchQuery ? 'Aucune tâche ne correspond à votre recherche.' : 'Aucune tâche trouvée.'}
          </p>
          {user.role === 'admin' && showActions && !searchQuery && (
            <Button onClick={handleCreateTask} className="bg-indigo-600 hover:bg-indigo-700 text-white">
              <PlusIcon className="mr-2 h-4 w-4" />
              Créer une tâche
            </Button>
          )}
        </div>
      );
    }

    // Rendu selon la vue sélectionnée
    if (displayView === 'kanban') {
      return (
        <TeamTaskKanban
          tasks={filteredTasks}
          onEdit={handleEditTask}
          onDelete={handleDeleteTask}
          onUpdateStatus={handleUpdateStatus}
          onArchive={handleArchiveTask}
          onUnarchive={handleUnarchiveTask}
        />
      );
    }

    if (displayView === 'card') {
      return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTasks.map((task) => (
            <TeamTaskCard
              key={task.id}
              task={task}
              onEdit={handleEditTask}
              onDelete={handleDeleteTask}
              onUpdateStatus={handleUpdateStatus}
              onArchive={handleArchiveTask}
              onUnarchive={handleUnarchiveTask}
              modalOpen={false}
            />
          ))}
        </div>
      );
    }

    // Vue liste par défaut
    return (
      <TeamTaskList
        tasks={filteredTasks}
        onEdit={handleEditTask}
        onDelete={handleDeleteTask}
        onUpdateStatus={handleUpdateStatus}
        onArchive={handleArchiveTask}
        onUnarchive={handleUnarchiveTask}
      />
    );
  };

  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden">
      {/* En-tête avec titre et bouton de création */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <h2 className="text-xl font-bold text-gray-900">
            {teamId ? 'Tâches de l\'équipe' : 'Tâches d\'équipe'}
          </h2>
          {user.role === 'admin' && showActions && (
            <Button onClick={handleCreateTask} className="bg-indigo-600 hover:bg-indigo-700 text-white">
              <PlusIcon className="mr-2 h-4 w-4" />
              Créer une tâche
            </Button>
          )}
        </div>
      </div>

      {/* Barre de recherche et sélecteur de vue */}
      <div className="p-6 border-b border-gray-200 bg-gray-50">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div className="relative w-full md:w-64">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              type="text"
              placeholder="Rechercher une tâche..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          <ViewSelector
            currentView={displayView}
            onViewChange={setDisplayView}
            showFilters={showFilters}
            onToggleFilters={() => setShowFilters(!showFilters)}
          />
        </div>
      </div>

      {/* Contenu principal */}
      <div className="p-6">
        {renderContent()}
      </div>
    </div>
  );
};

export default TeamTasksList;
