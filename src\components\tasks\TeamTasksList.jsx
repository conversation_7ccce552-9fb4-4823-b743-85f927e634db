import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { showSuccessToast, showErrorToast } from '@/utils/toastUtils';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { useTeam } from '@/contexts/TeamContext';
import teamTaskService from '@/services/teamTaskService';
import { PlusIcon, ArchiveIcon, TrashIcon, PencilIcon } from 'lucide-react';

const TeamTasksList = ({ teamId, showActions = true }) => {
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { user } = useAuth();
  const { teams } = useTeam();
  const navigate = useNavigate();

  // Statuts possibles pour les tâches
  const statusLabels = {
    'a_faire': 'À faire',
    'en_cours': 'En cours',
    'achevee': 'Achevée',
    'archived': 'Archivée'
  };

  // Couleurs pour les statuts
  const statusColors = {
    'a_faire': 'bg-yellow-100 text-yellow-800',
    'en_cours': 'bg-blue-100 text-blue-800',
    'achevee': 'bg-green-100 text-green-800',
    'archived': 'bg-gray-100 text-gray-800'
  };

  // Récupérer les tâches
  const fetchTasks = async () => {
    setLoading(true);
    setError(null);
    try {
      let tasksData;
      if (teamId) {
        // Si un ID d'équipe est fourni, récupérer les tâches de cette équipe
        tasksData = await teamTaskService.getTeamTasks({ team_id: teamId });
      } else {
        // Sinon, récupérer toutes les tâches accessibles à l'utilisateur
        tasksData = await teamTaskService.getTeamTasks();
      }
      console.log('TeamTasksList - Tasks data:', tasksData);
      setTasks(tasksData);
    } catch (err) {
      console.error('TeamTasksList - Error fetching tasks:', err);
      setError(err.message || 'Erreur lors de la récupération des tâches');
      showErrorToast(err.message || 'Erreur lors de la récupération des tâches');
    } finally {
      setLoading(false);
    }
  };

  // Charger les tâches au montage du composant
  useEffect(() => {
    fetchTasks();
  }, [teamId]);

  // Gérer la création d'une nouvelle tâche
  const handleCreateTask = () => {
    navigate('/team-tasks/create');
  };

  // Gérer la modification d'une tâche
  const handleEditTask = (taskId) => {
    navigate(`/team-tasks/edit/${taskId}`);
  };

  // Gérer l'archivage d'une tâche
  const handleArchiveTask = async (taskId) => {
    try {
      await teamTaskService.archiveTeamTask(taskId);
      showSuccessToast('Tâche archivée avec succès');
      fetchTasks();
    } catch (err) {
      console.error('TeamTasksList - Error archiving task:', err);
      showErrorToast(err.message || 'Erreur lors de l\'archivage de la tâche');
    }
  };

  // Gérer la suppression d'une tâche
  const handleDeleteTask = async (taskId) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette tâche ? Cette action est irréversible.')) {
      try {
        await teamTaskService.deleteTeamTask(taskId);
        showSuccessToast('Tâche supprimée avec succès');
        fetchTasks();
      } catch (err) {
        console.error('TeamTasksList - Error deleting task:', err);
        showErrorToast(err.message || 'Erreur lors de la suppression de la tâche');
      }
    }
  };

  // Vérifier les permissions pour une tâche
  const checkPermissions = (task) => {
    return teamTaskService.checkTeamTaskPermissions(user, task);
  };

  // Trouver le nom de l'équipe à partir de son ID
  const getTeamName = (teamId) => {
    const team = teams.find(t => t.id === teamId);
    return team ? team.name : 'Équipe inconnue';
  };

  if (loading) {
    return <div className="text-center py-8">Chargement des tâches...</div>;
  }

  if (error) {
    return <div className="text-center py-8 text-red-500">{error}</div>;
  }

  if (tasks.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="mb-4">Aucune tâche trouvée.</p>
        {user.role === 'admin' && showActions && (
          <Button onClick={handleCreateTask} className="bg-indigo-600 hover:bg-indigo-700 text-white">
            <PlusIcon className="mr-2 h-4 w-4" />
            Créer une tâche
          </Button>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {user.role === 'admin' && showActions && (
        <div className="flex justify-end">
          <Button onClick={handleCreateTask} className="bg-indigo-600 hover:bg-indigo-700 text-white">
            <PlusIcon className="mr-2 h-4 w-4" />
            Créer une tâche
          </Button>
        </div>
      )}

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Titre
              </th>
              {!teamId && (
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Équipe
                </th>
              )}
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Statut
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Date de début
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Date de fin
              </th>
              {showActions && (
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              )}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {tasks.map((task) => {
              const permissions = checkPermissions(task);
              return (
                <tr key={task.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{task.title}</div>
                    {task.description && (
                      <div className="text-sm text-gray-500 truncate max-w-xs">{task.description}</div>
                    )}
                  </td>
                  {!teamId && (
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{getTeamName(task.team_id)}</div>
                    </td>
                  )}
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusColors[task.status]}`}>
                      {statusLabels[task.status] || task.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(task.start_date).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(task.end_date).toLocaleDateString()}
                  </td>
                  {showActions && (
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        {permissions.canManage && (
                          <>
                            <Button
                              onClick={() => handleEditTask(task.id)}
                              variant="ghost"
                              size="sm"
                              className="text-indigo-600 hover:text-indigo-900"
                            >
                              <PencilIcon className="h-4 w-4" />
                            </Button>
                            <Button
                              onClick={() => handleArchiveTask(task.id)}
                              variant="ghost"
                              size="sm"
                              className="text-yellow-600 hover:text-yellow-900"
                            >
                              <ArchiveIcon className="h-4 w-4" />
                            </Button>
                            <Button
                              onClick={() => handleDeleteTask(task.id)}
                              variant="ghost"
                              size="sm"
                              className="text-red-600 hover:text-red-900"
                            >
                              <TrashIcon className="h-4 w-4" />
                            </Button>
                          </>
                        )}
                      </div>
                    </td>
                  )}
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default TeamTasksList;
