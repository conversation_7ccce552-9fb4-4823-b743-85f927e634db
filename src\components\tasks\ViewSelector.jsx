import React, { useState } from 'react';
import { LayoutGrid, List, Columns, Lock, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { toast } from 'react-toastify';

const ViewSelector = ({ currentView, onViewChange, isLocked, onLock, isAdmin = false, teamId = null }) => {
    const [showConfirmDialog, setShowConfirmDialog] = useState(false);
    const [viewToConfirm, setViewToConfirm] = useState(null);

    // Fonction pour gérer le clic sur un bouton de vue
    const handleViewClick = (view) => {
        if (isLocked) {
            // Si déjà verrouillé, afficher un message
            toast.info("Vous avez verrouillé ce mode d'affichage pour votre compte. Pour le modifier, vous devez réinitialiser vos préférences.");
            return;
        }

        if (view === currentView && isAdmin) {
            // Si on clique sur la vue actuelle et qu'on est admin, demander confirmation pour verrouiller
            setViewToConfirm(view);
            setShowConfirmDialog(true);
        } else {
            // Sinon, changer simplement la vue
            onViewChange(view);
        }
    };

    // Fonction pour confirmer et verrouiller le choix
    const confirmLock = () => {
        if (viewToConfirm) {
            // Passer l'ID de l'équipe si disponible pour un verrouillage spécifique à l'équipe
            onLock(viewToConfirm, teamId);
            const viewName = viewToConfirm === 'list' ? 'Liste' : viewToConfirm === 'kanban' ? 'Kanban' : 'Cartes';
            toast.success(`Le mode d'affichage a été verrouillé en "${viewName}" ${teamId ? 'pour cette équipe' : ''}.`);
            setShowConfirmDialog(false);
        }
    };

    return (
        <>
            <div className="flex items-center space-x-1 bg-gray-100 p-1 rounded-lg relative">
                {isLocked && (
                    <div className="absolute -top-8 left-0 right-0 text-xs text-center text-gray-600 bg-gray-100 p-1 rounded-md">
                        <span className="flex items-center justify-center">
                            <Lock className="h-3 w-3 mr-1" />
                            Mode d'affichage verrouillé pour votre compte
                        </span>
                    </div>
                )}

                <Button
                    variant={currentView === 'list' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => handleViewClick('list')}
                    className={`flex items-center gap-1 ${currentView === 'list' ? 'bg-white shadow-sm' : 'hover:bg-gray-200'}`}
                    disabled={isLocked && currentView !== 'list'}
                    title={isLocked && currentView !== 'list' ? "Le mode d'affichage a été verrouillé" : ""}
                >
                    <List className="h-4 w-4" />
                    <span className="text-sm">Liste</span>
                    {isLocked && currentView === 'list' && <Lock className="h-3 w-3 ml-1" />}
                </Button>
                <Button
                    variant={currentView === 'kanban' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => handleViewClick('kanban')}
                    className={`flex items-center gap-1 ${currentView === 'kanban' ? 'bg-white shadow-sm' : 'hover:bg-gray-200'}`}
                    disabled={isLocked && currentView !== 'kanban'}
                    title={isLocked && currentView !== 'kanban' ? "Le mode d'affichage a été verrouillé" : ""}
                >
                    <Columns className="h-4 w-4" />
                    <span className="text-sm">Kanban</span>
                    {isLocked && currentView === 'kanban' && <Lock className="h-3 w-3 ml-1" />}
                </Button>
                <Button
                    variant={currentView === 'cards' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => handleViewClick('cards')}
                    className={`flex items-center gap-1 ${currentView === 'cards' ? 'bg-white shadow-sm' : 'hover:bg-gray-200'}`}
                    disabled={isLocked && currentView !== 'cards'}
                    title={isLocked && currentView !== 'cards' ? "Le mode d'affichage a été verrouillé" : ""}
                >
                    <LayoutGrid className="h-4 w-4" />
                    <span className="text-sm">Cartes</span>
                    {isLocked && currentView === 'cards' && <Lock className="h-3 w-3 ml-1" />}
                </Button>

                {!isLocked && isAdmin && (
                    <div className="absolute -bottom-8 left-0 right-0 text-xs text-center text-blue-600 bg-blue-50 p-1 rounded-md">
                        <span className="flex items-center justify-center">
                            <span className="mr-1">👆</span>
                            Cliquez à nouveau sur le mode actif pour le verrouiller définitivement
                        </span>
                    </div>
                )}
            </div>

            {/* Dialogue de confirmation */}
            <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
                <DialogContent className="sm:max-w-[425px]">
                    <DialogHeader>
                        <DialogTitle className="flex items-center gap-2">
                            <AlertTriangle className="h-5 w-5 text-amber-500" />
                            Confirmer le verrouillage du mode d'affichage
                        </DialogTitle>
                        <DialogDescription>
                            Vous êtes sur le point de verrouiller le mode d'affichage en "
                            {viewToConfirm === 'list' ? 'Liste' : viewToConfirm === 'kanban' ? 'Kanban' : 'Cartes'}"
                            {teamId ? " pour cette équipe" : ""}.
                            <br /><br />
                            <strong>Cette action ne s'applique qu'à votre compte.</strong> Une fois verrouillé,
                            vous ne pourrez plus changer le mode d'affichage, mais les autres administrateurs
                            pourront toujours choisir leur propre mode d'affichage.
                        </DialogDescription>
                    </DialogHeader>
                    <DialogFooter className="flex space-x-2 justify-end">
                        <Button variant="outline" onClick={() => setShowConfirmDialog(false)}>
                            Annuler
                        </Button>
                        <Button
                            onClick={confirmLock}
                            className="bg-amber-600 hover:bg-amber-700 text-white"
                        >
                            Verrouiller définitivement
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </>
    );
};

export default ViewSelector;
