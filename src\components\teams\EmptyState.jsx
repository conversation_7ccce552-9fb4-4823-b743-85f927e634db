import React from 'react';
import { Users } from 'lucide-react';

const EmptyState = ({ message, showCreateButton, onCreateTeam }) => {
    return (
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-8">
            <div className="flex flex-col items-center justify-center gap-4 text-center">
                <div className="w-16 h-16 bg-[#6B4EFF] bg-opacity-10 dark:bg-opacity-20 rounded-full flex items-center justify-center">
                    <Users className="w-8 h-8 text-[#6B4EFF] dark:text-[#8B6FFF]" />
                </div>
                
                <div className="space-y-2">
                    <p className="text-gray-600 dark:text-gray-400">
                        {message || "Aucune équipe trouvée"}
                    </p>
                    
                    {showCreateButton && (
                        <button
                            onClick={onCreateTeam}
                            className="mt-4 px-4 py-2 bg-[#6B4EFF] text-white rounded-lg 
                                     hover:bg-[#5b3ff0] transition-all duration-200 
                                     flex items-center gap-2 mx-auto"
                        >
                            C<PERSON>er une équipe
                        </button>
                    )}
                </div>
            </div>
        </div>
    );
};

export default EmptyState;