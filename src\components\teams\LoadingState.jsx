import React from 'react';

const LoadingState = () => {
    return (
        <div className="space-y-6">
            {[1, 2].map((index) => (
                <div key={index} className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 animate-pulse">
                    <div className="flex items-start justify-between mb-4">
                        <div className="space-y-3 flex-1">
                            <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
                            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
                        </div>
                        <div className="flex space-x-2">
                            <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
                            <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
                        </div>
                    </div>

                    <div className="border-t border-gray-100 dark:border-gray-700 pt-4">
                        <div className="flex items-center justify-between mb-3">
                            <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded w-1/6"></div>
                            <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-24"></div>
                        </div>

                        <div className="space-y-3">
                            {[1, 2, 3].map((memberIndex) => (
                                <div key={memberIndex} className="flex items-center justify-between py-2">
                                    <div className="flex items-center space-x-3 flex-1">
                                        <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
                                        <div className="space-y-2 flex-1">
                                            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
                                            <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/5"></div>
                                        </div>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
                                        <div className="w-6 h-6 bg-gray-200 dark:bg-gray-700 rounded"></div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            ))}
        </div>
    );
};

export default LoadingState;