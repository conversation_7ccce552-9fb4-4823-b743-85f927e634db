import React from 'react';
import { Edit, Trash2, UserPlus, UserMinus, User, Users, MoreVertical, Circle, Calendar } from 'lucide-react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from '@/components/ui/dropdown-menu';

const TeamCard = ({ team, permissions, onEdit, onDelete, onAddMember, onRemoveMember }) => {
    // Log des données de l'équipe pour le débogage
    console.log('TeamCard - Team data:', team);
    
    const formatDate = (dateString) => {
        if (!dateString) return 'Date inconnue';
        try {
            return new Date(dateString).toLocaleDateString('fr-FR', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        } catch (error) {
            console.error('Erreur de formatage de date:', error, dateString);
            return 'Date inconnue';
        }
    };

    // Convertir l'objet members en tableau
    const membersList = Array.isArray(team.members) 
        ? team.members.map(member => ({
            id: member.id || member.user_id, // Utiliser user_id comme fallback
            name: member.name,
            role: member.role || 'employee',
            added_at: member.added_at
        }))
        : Object.entries(team.members || {}).map(([id, info]) => ({
            id,
            name: typeof info === 'object' ? info.name : info,
            role: typeof info === 'object' ? info.role : 'employee',
            added_at: typeof info === 'object' ? info.added_at : null
        }));

    return (
        <div className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100">
            {/* En-tête de la carte avec un bandeau de couleur */}
            <div className="bg-gradient-to-r from-blue-500 to-indigo-600 h-2 w-full"></div>
            
            <div className="p-6 space-y-4">
                <div className="flex justify-between items-start">
                    <div>
                        <h3 className="text-xl font-semibold text-gray-800 hover:text-indigo-600 transition-colors">{team.name}</h3>
                        <p className="text-sm text-gray-600 mt-1">{team.description || 'Aucune description'}</p>
                    </div>
                    {permissions.canManage && (
                        <div className="relative">
                            <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" size="icon" className="hover:bg-gray-100 rounded-full">
                                        <MoreVertical className="h-5 w-5 text-gray-500" />
                                    </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end" className="shadow-lg rounded-lg border border-gray-200">
                                    <DropdownMenuItem onClick={() => onEdit(team)} className="hover:bg-gray-50">
                                        <Edit className="mr-2 h-4 w-4 text-blue-500" />
                                        Modifier
                                    </DropdownMenuItem>
                                    <DropdownMenuItem onClick={() => onDelete(team.id)} className="text-red-600 hover:bg-red-50">
                                        <Trash2 className="mr-2 h-4 w-4" />
                                        Supprimer
                                    </DropdownMenuItem>
                                    <DropdownMenuItem asChild>
                                        <Link to={`/admin/calendar?team_id=${team.id}`} className="hover:bg-indigo-50">
                                            <Calendar className="mr-2 h-4 w-4 text-indigo-500" />
                                            Calendrier
                                        </Link>
                                    </DropdownMenuItem>
                                </DropdownMenuContent>
                            </DropdownMenu>
                        </div>
                    )}
                </div>

                <div className="pt-4 border-t border-gray-200">
                    <div className="flex items-center justify-between mb-4">
                        <h4 className="text-sm font-semibold text-gray-700 flex items-center">
                            <Users className="h-4 w-4 mr-2 text-indigo-500" />
                            Membres ({membersList.length})
                        </h4>
                        {permissions.canAddMembers && (
                            <Button
                                onClick={() => onAddMember(team)}
                                variant="outline"
                                size="sm"
                                className="flex items-center border-indigo-300 text-indigo-600 hover:bg-indigo-50"
                            >
                                <UserPlus className="h-4 w-4 mr-2" />
                                Ajouter
                            </Button>
                        )}
                    </div>

                    <div className="space-y-3 max-h-48 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                        {membersList.length > 0 ? (
                            membersList.map((member) => (
                                <div key={member.id} className="flex items-center justify-between bg-gray-50 hover:bg-gray-100 p-2 rounded-lg transition-colors">
                                    <div className="flex items-center space-x-3">
                                        <div className="flex-shrink-0 bg-indigo-100 p-2 rounded-full">
                                            <User className="h-5 w-5 text-indigo-600" />
                                        </div>
                                        <div>
                                            <p className="text-sm font-medium text-gray-900">{member.name}</p>
                                            <p className="text-xs text-gray-500">
                                                {member.role === 'admin' ? 'Administrateur' : 'Employé'} ·
                                                {member.added_at ? `Ajouté le ${formatDate(member.added_at)}` : 'Date d\'ajout inconnue'}
                                            </p>
                                        </div>
                                    </div>
                                    {permissions.canRemoveMembers && (
                                        <Button
                                            onClick={() => onRemoveMember(team.id, member.id)}
                                            variant="ghost"
                                            size="sm"
                                            className="text-red-600 hover:text-red-700 hover:bg-red-50 rounded-full"
                                        >
                                            <UserMinus className="h-4 w-4" />
                                        </Button>
                                    )}
                                </div>
                            ))
                        ) : (
                            <p className="text-sm text-gray-500 text-center py-4 bg-gray-50 rounded-lg italic">
                                Aucun membre dans cette équipe
                            </p>
                        )}
                    </div>
                </div>

                <div className="pt-4 border-t border-gray-200 bg-gray-50 -mx-6 -mb-6 p-6 rounded-b-lg">
                    <div className="flex items-center text-sm text-gray-600 mb-2">
                        <User className="h-4 w-4 mr-2 text-indigo-500" />
                        <span className="font-medium">Responsable:</span> <span className="ml-1">{team.responsable_name || 'Non spécifié'}</span>
                    </div>
                    <div className="flex items-center text-sm text-gray-600 mb-2">
                        <Circle className="h-4 w-4 mr-2 text-green-500" />
                        <span className="font-medium">Créée le</span> <span className="ml-1">{formatDate(team.created_at)}</span>
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                        <Circle className="h-4 w-4 mr-2 text-orange-500" />
                        <span className="font-medium">Mise à jour le</span> <span className="ml-1">{formatDate(team.updated_at)}</span>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default TeamCard;