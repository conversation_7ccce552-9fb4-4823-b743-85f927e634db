import React from 'react';
import { Filter } from 'lucide-react';

const TeamFilters = ({ statusFilter, setStatusFilter, roleFilter, setRoleFilter }) => {
    return (
        <div className="flex items-center gap-3 bg-white px-4 py-2 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-all duration-200">
            <div className="flex items-center">
                <Filter className="h-4 w-4 text-gray-500 mr-2" />
            </div>
            
            <div className="flex items-center gap-3">
                <select
                    id="statusFilter"
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200"
                >
                    <option value="all">Tous les statuts</option>
                    <option value="active">Actives</option>
                    <option value="inactive">Inactives</option>
                </select>
                
                <select
                    id="roleFilter"
                    value={roleFilter}
                    onChange={(e) => setRoleFilter(e.target.value)}
                    className="p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200"
                >
                    <option value="all">Tous les rôles</option>
                    <option value="member">Membre</option>
                    <option value="admin">Responsable</option>
                </select>
            </div>
        </div>
    );
};

export default TeamFilters;