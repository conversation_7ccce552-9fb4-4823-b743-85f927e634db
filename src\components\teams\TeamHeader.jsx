import React from 'react';
import { Plus, Users, CheckCircle, User } from 'lucide-react';

const TeamHeader = ({ onCreateClick, canCreateTeam, teams = [] }) => {
    // Calculer les statistiques
    const totalTeams = teams.length;
    const activeTeams = teams.filter(team => team.is_active !== false).length;
    const totalMembers = teams.reduce((total, team) => {
        const membersCount = team.members ? 
            (Array.isArray(team.members) ? team.members.length : Object.keys(team.members).length) : 0;
        return total + membersCount;
    }, 0);

    return (
        <div className="space-y-6">
            <div className="flex justify-between items-center">
                <div>
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Gestion des Équipes</h1>
                    <p className="text-gray-600 dark:text-gray-400 text-sm mt-1">
                        Consultez et gérez toutes vos équipes
                    </p>
                </div>

                {canCreateTeam && (
                    <button
                        onClick={onCreateClick}
                        className="px-4 py-2 bg-[#6B4EFF] text-white rounded-lg 
                                hover:bg-[#5b3ff0] transition-all duration-200 
                                flex items-center gap-2 shadow-sm
                                hover:shadow-md active:transform active:scale-95"
                    >
                        <Plus className="w-5 h-5" />
                        Nouvelle Équipe
                    </button>
                )}
            </div>

            {/* Barre de statistiques */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-white p-4 rounded-lg shadow-md border-l-4 border-blue-500 flex items-center">
                    <div className="bg-blue-100 p-3 rounded-full mr-4">
                        <Users className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                        <p className="text-sm text-gray-500">Total des Équipes</p>
                        <p className="text-2xl font-bold text-gray-800">{totalTeams}</p>
                    </div>
                </div>
                
                <div className="bg-white p-4 rounded-lg shadow-md border-l-4 border-green-500 flex items-center">
                    <div className="bg-green-100 p-3 rounded-full mr-4">
                        <CheckCircle className="h-6 w-6 text-green-600" />
                    </div>
                    <div>
                        <p className="text-sm text-gray-500">Équipes Actives</p>
                        <p className="text-2xl font-bold text-gray-800">{activeTeams}</p>
                    </div>
                </div>
                
                <div className="bg-white p-4 rounded-lg shadow-md border-l-4 border-purple-500 flex items-center">
                    <div className="bg-purple-100 p-3 rounded-full mr-4">
                        <User className="h-6 w-6 text-purple-600" />
                    </div>
                    <div>
                        <p className="text-sm text-gray-500">Membres</p>
                        <p className="text-2xl font-bold text-gray-800">{totalMembers}</p>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default TeamHeader;