import React, { useEffect } from 'react';
import { useTeam } from '@/contexts/TeamContext';
import TeamCard from './TeamCard';

const TeamList = () => {
  const { teams, loading, error, fetchTeams } = useTeam();

  useEffect(() => {
    fetchTeams();
  }, [fetchTeams]);

  if (loading) return <div className="p-4 text-center">Chargement des équipes...</div>;
  if (error) return <div className="p-4 text-center text-red-600">{error}</div>;
  if (teams.length === 0) return <div className="p-4 text-center">Aucune équipe disponible</div>;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {teams.map(team => (
        <TeamCard
          key={team.id}
          team={team}
        />
      ))}
    </div>
  );
};

export default TeamList;