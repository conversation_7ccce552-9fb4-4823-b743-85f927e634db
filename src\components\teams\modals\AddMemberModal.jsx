import React, { useState, useEffect, useCallback } from 'react';
import { X, Plus, Search } from 'lucide-react';
import { toast } from 'react-toastify';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { authService } from '@/services/authService';
import teamService from '@/services/teamService';

const AddMemberModal = ({ show, onClose, onSubmit, teamId, teamName }) => {
    const { user, getAuthHeader } = useAuth();
    const navigate = useNavigate();
    const [activeTab, setActiveTab] = useState('search');
    const [employees, setEmployees] = useState([]);
    const [loading, setLoading] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [error, setError] = useState(null);
    const [notification, setNotification] = useState({ type: '', message: '' });
    const [newMember, setNewMember] = useState({
        name: '',
        email: '',
        role: 'employee' // Valeur par défaut
    });

    const fetchEmployees = useCallback(async () => {
        try {
            setLoading(true);
            setError(null);

            console.log('Début de la récupération des employés disponibles pour l\'équipe:', teamId);

            // Utiliser l'endpoint testé et approuvé via teamService
            const employeesData = await teamService.getAvailableEmployeesForTeam(teamId);
            console.log('Employés disponibles récupérés:', employeesData);

            // Les données sont déjà filtrées pour ne contenir que les employés disponibles
            const filteredEmployees = Array.isArray(employeesData) ? employeesData : [];

            console.log(`${filteredEmployees.length} employés disponibles pour ajout à l'équipe:`, filteredEmployees);
            setEmployees(filteredEmployees);

            if (filteredEmployees.length === 0) {
                setError('Aucun employé disponible pour cette équipe. Tous les employés sont déjà membres ou aucun employé n\'existe dans le système.');
            }
        } catch (error) {
            console.error('Erreur détaillée lors de la récupération des employés:', error);

            // Gestion spécifique des erreurs
            if (error.message?.includes('Session expirée') || error.message?.includes('401')) {
                setError('Session expirée, veuillez vous reconnecter');
                navigate('/login');
                return;
            }

            if (error.message?.includes('403') || error.message?.includes('Unauthorized')) {
                setError('Vous n\'avez pas les permissions pour voir les employés disponibles.');
            } else {
                setError('Erreur lors du chargement des employés: ' + (error.message || 'Erreur inconnue'));
            }

            setEmployees([]);
        } finally {
            setLoading(false);
        }
    }, [navigate, teamId]);

    // Fonction pour rechercher des employés avec l'endpoint testé
    const searchEmployees = useCallback(async (query) => {
        try {
            setLoading(true);
            console.log('Recherche d\'employés avec la requête:', query);

            // Utiliser l'endpoint testé avec la requête de recherche
            const results = await teamService.searchAvailableEmployeesForTeam(teamId, query);
            console.log('Résultats de recherche:', results);

            setEmployees(results);
        } catch (error) {
            console.error('Erreur lors de la recherche d\'employés:', error);
            setError('Erreur lors de la recherche: ' + (error.message || 'Erreur inconnue'));
        } finally {
            setLoading(false);
        }
    }, [teamId]);

    useEffect(() => {
        if (!show) {
            setSearchTerm('');
            setError(null);
            setEmployees([]);
            setNewMember({ name: '', email: '', role: 'employee' });
            setActiveTab('search');
            setNotification({ type: '', message: '' });
            setIsSubmitting(false);
            return;
        }

        if (!teamId) {
            setError("Identifiant de l'équipe manquant");
            return;
        }

        setError(null);
        fetchEmployees();
    }, [show, teamId, fetchEmployees]);

    // Effet pour la recherche en temps réel
    useEffect(() => {
        if (show && activeTab === 'search') {
            const timeoutId = setTimeout(() => {
                if (searchTerm.trim()) {
                    searchEmployees(searchTerm);
                } else {
                    fetchEmployees(); // Récupérer tous les employés si pas de recherche
                }
            }, 300); // Délai de 300ms pour éviter trop de requêtes

            return () => clearTimeout(timeoutId);
        }
    }, [searchTerm, show, activeTab, searchEmployees, fetchEmployees]);

    const handleEmployeeSelect = async (employee) => {
        try {
            setIsSubmitting(true);
            setError(null);

            // Vérifier que l'employé a un ID valide
            if (!employee || !employee.id) {
                throw new Error('Données de l\'employé invalides');
            }

            // Appeler onSubmit avec l'ID de l'utilisateur
            await onSubmit({
                user_id: employee.id
            });

            // Réinitialiser le formulaire et fermer le modal
            setSearchTerm('');
            onClose();
        } catch (err) {
            console.error('Error adding member:', err);
            setError(err.message);
            toast.error(err.message);
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleCreateMember = async (e) => {
        e.preventDefault();

        if (!newMember.name?.trim()) {
            setError("Le nom est requis");
            return;
        }

        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(newMember.email)) {
            setError("Format d'email invalide");
            return;
        }

        try {
            setIsSubmitting(true);
            setError(null);

            const response = await axios.post(
                `http://localhost:8000/api/teams/${teamId}/members/create/`,
                {
                    name: newMember.name.trim(),
                    email: newMember.email.trim().toLowerCase(),
                    role: newMember.role,
                    generate_temp_password: true,
                    send_email: true
                },
                {
                    headers: {
                        ...authService.getAuthHeader(),
                        'Content-Type': 'application/json'
                    }
                }
            );

            if (response.data?.member) {
                setNotification({
                    type: 'success',
                    message: 'Nouveau membre créé et ajouté avec succès. Un email avec les identifiants temporaires a été envoyé.'
                });
                // Extraire l'ID du membre créé et le passer au format attendu par onSubmit
                await onSubmit({
                    user_id: response.data.member.id || response.data.member.user_id
                });
                setNewMember({ name: '', email: '', role: 'employee' });
                onClose();
            }
        } catch (err) {
            console.error('Erreur détaillée:', err);
            if (err.response?.data?.error) {
                setError(err.response.data.error);
            } else if (err.response?.status === 409) {
                setError("Un utilisateur avec cet email existe déjà");
            } else {
                setError("Une erreur est survenue lors de la création du membre");
            }
        } finally {
            setIsSubmitting(false);
        }
    };

    if (!show) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-md p-6 relative">
                <button
                    onClick={onClose}
                    className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200"
                >
                    <X className="w-5 h-5" />
                </button>

                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                    Ajouter un membre
                    {teamName && <span className="text-sm text-gray-500 block mt-1">à l'équipe {teamName}</span>}
                </h2>

                {error && (
                    <div className="mb-4 p-4 bg-red-50 text-red-600 rounded-lg text-sm">
                        {error}
                    </div>
                )}

                {notification.message && (
                    <div className={`mb-4 p-4 rounded-lg text-sm ${notification.type === 'success'
                        ? 'bg-green-50 text-green-600'
                        : 'bg-red-50 text-red-600'
                        }`}>
                        {notification.message}
                    </div>
                )}

                <div className="mb-6">
                    <div className="flex border-b border-gray-200">
                        <button
                            className={`px-4 py-2 ${activeTab === 'search' ? 'text-[#6B4EFF] border-b-2 border-[#6B4EFF]' : 'text-gray-500'}`}
                            onClick={() => setActiveTab('search')}
                        >
                            Rechercher
                        </button>
                        <button
                            className={`px-4 py-2 ${activeTab === 'create' ? 'text-[#6B4EFF] border-b-2 border-[#6B4EFF]' : 'text-gray-500'}`}
                            onClick={() => setActiveTab('create')}
                        >
                            Nouveau membre
                        </button>
                    </div>
                </div>

                {activeTab === 'search' ? (
                    <div className="space-y-4">
                        <div className="relative">
                            <input
                                type="text"
                                placeholder="Rechercher un employé..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="w-full px-4 py-2 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#6B4EFF] focus:border-transparent"
                                disabled={isSubmitting}
                            />
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                        </div>

                        {loading ? (
                            <div className="text-center py-4">
                                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#6B4EFF] mx-auto"></div>
                            </div>
                        ) : (
                            <div className="max-h-60 overflow-y-auto">
                                {employees.map(employee => (
                                    <div
                                        key={employee.id}
                                        className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg"
                                    >
                                        <div>
                                            <div className="font-medium text-gray-900">{employee.name}</div>
                                            <div className="text-sm text-gray-500">{employee.email}</div>
                                        </div>
                                        <button
                                            onClick={() => handleEmployeeSelect(employee)}
                                            disabled={isSubmitting}
                                            className="px-3 py-1 bg-[#6B4EFF] text-white rounded hover:bg-[#5b3ff0] transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                                        >
                                            {isSubmitting ? 'Ajout...' : 'Ajouter'}
                                        </button>
                                    </div>
                                ))}
                                {employees.length === 0 && !loading && (
                                    <p className="text-center text-gray-500 py-4">
                                        {searchTerm ? 'Aucun employé trouvé pour cette recherche' : 'Aucun employé disponible'}
                                    </p>
                                )}
                            </div>
                        )}
                    </div>
                ) : (
                    <form onSubmit={handleCreateMember} className="space-y-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Nom complet
                            </label>
                            <input
                                type="text"
                                value={newMember.name}
                                onChange={(e) => setNewMember({ ...newMember, name: e.target.value })}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#6B4EFF] focus:border-transparent"
                                placeholder="Nom du nouveau membre"
                                disabled={isSubmitting}
                                required
                            />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Email
                            </label>
                            <input
                                type="email"
                                value={newMember.email}
                                onChange={(e) => setNewMember({ ...newMember, email: e.target.value })}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#6B4EFF] focus:border-transparent"
                                placeholder="<EMAIL>"
                                disabled={isSubmitting}
                                required
                            />
                            <p className="mt-1 text-sm text-gray-500">
                                Un email avec les identifiants temporaires sera envoyé à cette adresse
                            </p>
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Rôle
                            </label>
                            <select
                                value={newMember.role}
                                onChange={(e) => setNewMember({ ...newMember, role: e.target.value })}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#6B4EFF] focus:border-transparent"
                                disabled={isSubmitting}
                                required
                            >
                                <option value="employee">Employé</option>
                                <option value="admin">Administrateur</option>
                            </select>
                        </div>

                        <div className="flex justify-end space-x-3 pt-4">
                            <button
                                type="button"
                                onClick={() => setActiveTab('search')}
                                className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors duration-200"
                                disabled={isSubmitting}
                            >
                                Retour
                            </button>
                            <button
                                type="submit"
                                className="px-4 py-2 bg-[#6B4EFF] text-white rounded-lg hover:bg-[#5b3ff0] transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                                disabled={isSubmitting}
                            >
                                {isSubmitting ? 'Création...' : 'Créer et ajouter'}
                            </button>
                        </div>
                    </form>
                )}
            </div>
        </div>
    );
};

export default AddMemberModal;