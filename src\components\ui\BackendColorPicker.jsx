import React, { useState, useEffect } from 'react';
import colorService from '@/services/colorService';
import { toast } from 'react-toastify';

const BackendColorPicker = ({
  selectedColor,
  onColorChange,
  eventTitle = '',
  eventType = 'general',
  label = "Couleur de l'événement",
  className = "",
  showSuggestions = true
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [palette, setPalette] = useState([]);
  const [categories, setCategories] = useState({});
  const [loading, setLoading] = useState(true);
  const [suggestedColor, setSuggestedColor] = useState(null);
  const [selectedColorInfo, setSelectedColorInfo] = useState(null);

  // Charger les données du backend
  useEffect(() => {
    const loadColorData = async () => {
      try {
        setLoading(true);
        const [paletteData, categoriesData] = await Promise.all([
          colorService.getCachedPalette(),
          colorService.getCachedCategories()
        ]);

        setPalette(paletteData);
        setCategories(categoriesData);
      } catch (error) {
        console.error('Erreur lors du chargement des couleurs:', error);
        toast.error('Erreur lors du chargement des couleurs');
      } finally {
        setLoading(false);
      }
    };

    loadColorData();
  }, []);

  // Mettre à jour les informations de la couleur sélectionnée
  useEffect(() => {
    const updateSelectedColorInfo = async () => {
      if (selectedColor) {
        const colorInfo = await colorService.getColorInfo(selectedColor);
        setSelectedColorInfo(colorInfo);
      }
    };

    updateSelectedColorInfo();
  }, [selectedColor]);

  // Suggérer une couleur basée sur le titre
  useEffect(() => {
    const suggestColorFromTitle = async () => {
      if (showSuggestions && eventTitle && eventTitle.length > 3) {
        try {
          const suggestion = await colorService.suggestColor(eventTitle);
          if (suggestion && suggestion.suggested_color) {
            setSuggestedColor(suggestion);
          }
        } catch (error) {
          console.error('Erreur lors de la suggestion de couleur:', error);
        }
      }
    };

    const debounceTimer = setTimeout(suggestColorFromTitle, 500);
    return () => clearTimeout(debounceTimer);
  }, [eventTitle, showSuggestions]);

  const handleColorSelect = async (color) => {
    try {
      // Valider la couleur avec le backend
      await colorService.validateColor(color.id || color.hex_code);
      onColorChange(color.id || color.hex_code);
      setIsOpen(false);
      toast.success(`Couleur "${color.description || color.name}" sélectionnée`);
    } catch (error) {
      console.error('Erreur lors de la sélection de couleur:', error);
      toast.error('Couleur invalide');
    }
  };

  const handleCustomColorChange = async (hexColor) => {
    try {
      await colorService.validateColor(hexColor);
      onColorChange(hexColor);
      toast.success('Couleur personnalisée appliquée');
    } catch (error) {
      console.error('Erreur couleur personnalisée:', error);
      toast.error('Code couleur invalide');
    }
  };

  const applySuggestedColor = () => {
    if (suggestedColor && suggestedColor.suggested_color) {
      handleColorSelect({ id: suggestedColor.suggested_color });
      setSuggestedColor(null);
    }
  };

  if (loading) {
    return (
      <div className={`${className}`}>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {label}
        </label>
        <div className="w-full h-10 bg-gray-100 rounded-lg animate-pulse"></div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {label}
      </label>

      {/* Suggestion automatique */}
      {suggestedColor && (
        <div className="mb-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div
                className="w-4 h-4 rounded-full border"
                style={{ backgroundColor: suggestedColor.hex_code }}
              />
              <span className="text-sm text-blue-700">
                Couleur suggérée : {suggestedColor.description}
              </span>
            </div>
            <button
              type="button"
              onClick={applySuggestedColor}
              className="text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700"
            >
              Appliquer
            </button>
          </div>
        </div>
      )}

      {/* Bouton de sélection de couleur */}
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex items-center justify-between px-3 py-2 border border-gray-300 rounded-lg shadow-sm bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors"
      >
        <div className="flex items-center gap-3">
          <div
            className="w-6 h-6 rounded-full border-2 shadow-sm"
            style={{
              backgroundColor: selectedColorInfo?.hex_code || selectedColor || '#3788d8'
            }}
          />
          <span className="text-sm font-medium text-gray-700">
            {selectedColorInfo?.description || selectedColorInfo?.name || 'Sélectionner une couleur'}
          </span>
        </div>
        <svg
          className={`w-4 h-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {/* Dropdown avec palette de couleurs */}
      {isOpen && (
        <div className="absolute z-50 mt-2 w-full bg-white border border-gray-200 rounded-lg shadow-lg max-h-96 overflow-y-auto">
          {/* Couleurs par catégories */}
          {Object.entries(categories).map(([categoryName, colors]) => (
            <div key={categoryName} className="p-4 border-b border-gray-100">
              <h4 className="text-sm font-semibold text-gray-700 mb-3 capitalize">
                {categoryName}
              </h4>
              <div className="grid grid-cols-3 gap-2">
                {colors.map((color) => (
                  <button
                    key={color.id}
                    type="button"
                    onClick={() => handleColorSelect(color)}
                    className={`group relative flex flex-col items-center p-2 rounded-lg border-2 transition-all hover:scale-105 ${(selectedColorInfo?.id === color.id || selectedColor === color.id)
                        ? 'border-indigo-500 bg-indigo-50'
                        : 'border-gray-200 hover:border-gray-300'
                      }`}
                    title={color.description}
                  >
                    <div
                      className="w-6 h-6 rounded-full shadow-sm mb-1 border"
                      style={{ backgroundColor: color.hex_code }}
                    />
                    <span className="text-xs text-gray-600 text-center leading-tight">
                      {color.name}
                    </span>

                    {(selectedColorInfo?.id === color.id || selectedColor === color.id) && (
                      <div className="absolute -top-1 -right-1 w-4 h-4 bg-indigo-500 rounded-full flex items-center justify-center">
                        <svg className="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                    )}
                  </button>
                ))}
              </div>
            </div>
          ))}

          {/* Section couleur personnalisée */}
          <div className="p-4">
            <label className="block text-xs font-medium text-gray-600 mb-2">
              Couleur personnalisée
            </label>
            <div className="flex items-center gap-2">
              <input
                type="color"
                value={selectedColor?.startsWith('#') ? selectedColor : '#3788d8'}
                onChange={(e) => handleCustomColorChange(e.target.value)}
                className="w-10 h-8 rounded border border-gray-300 cursor-pointer"
              />
              <input
                type="text"
                value={selectedColor?.startsWith('#') ? selectedColor : ''}
                onChange={(e) => {
                  const value = e.target.value;
                  if (value.match(/^#[0-9A-F]{6}$/i) || value === '') {
                    handleCustomColorChange(value);
                  }
                }}
                placeholder="#3788d8"
                className="flex-1 px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500"
              />
            </div>
          </div>
        </div>
      )}

      {/* Overlay pour fermer le dropdown */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
};

export default BackendColorPicker;
