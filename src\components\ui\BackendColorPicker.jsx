import React, { useState } from 'react';
import { PERSONAL_COLORS, TEAM_COLORS, getColorById, getColorHex } from '@/utils/backendColors';

const BackendColorPicker = ({ 
  selectedColor, 
  onColorChange, 
  label = "Couleur de l'événement",
  className = "",
  type = "personal", // "personal" ou "team"
  showCustomColor = true
}) => {
  const [isOpen, setIsOpen] = useState(false);

  // Choisir les couleurs selon le type
  const availableColors = type === "team" ? TEAM_COLORS : PERSONAL_COLORS;
  
  // Déterminer la couleur sélectionnée
  const getSelectedColorInfo = () => {
    // Si selectedColor est un ID de couleur
    const colorById = getColorById(selectedColor);
    if (colorById) {
      return colorById;
    }
    
    // Si selectedColor est une couleur hex, essayer de trouver l'ID correspondant
    if (typeof selectedColor === 'string' && selectedColor.startsWith('#')) {
      const matchingColor = availableColors.find(color => 
        color.hex.toLowerCase() === selectedColor.toLowerCase()
      );
      if (matchingColor) {
        return matchingColor;
      }
      // Si pas de correspondance, créer un objet temporaire pour la couleur custom
      return {
        id: 'custom',
        name: 'Couleur personnalisée',
        hex: selectedColor,
        category: 'custom'
      };
    }
    
    // Couleur par défaut
    return availableColors[0];
  };

  const selectedColorInfo = getSelectedColorInfo();

  const handleColorSelect = (color) => {
    // Envoyer l'ID de la couleur au parent
    onColorChange(color.id);
    setIsOpen(false);
  };

  const handleCustomColorChange = (hexColor) => {
    // Pour les couleurs personnalisées, envoyer directement la valeur hex
    onColorChange(hexColor);
  };

  return (
    <div className={`relative ${className}`}>
      {label && (
        <label 
          htmlFor="color-picker-button"
          className="block text-sm font-medium text-gray-700 mb-2"
        >
          {label}
        </label>
      )}
      
      {/* Bouton de sélection de couleur */}
      <button
        id="color-picker-button"
        name="colorPicker"
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex items-center gap-3 px-3 py-2 border border-gray-300 rounded-lg shadow-sm bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors"
      >
        <div 
          className="w-6 h-6 rounded-full border-2 border-gray-300 shadow-sm"
          style={{ backgroundColor: selectedColorInfo.hex }}
        />
        <span className="text-sm text-gray-700 flex-1 text-left">
          {selectedColorInfo.name}
        </span>
        <svg 
          className={`w-4 h-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`}
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {/* Dropdown avec palette de couleurs */}
      {isOpen && (
        <>
          <div className="absolute z-50 mt-2 w-full bg-white border border-gray-200 rounded-lg shadow-lg p-4">
            {/* Couleurs prédéfinies */}
            <div className="mb-4">
              <h4 className="text-xs font-medium text-gray-600 mb-3">
                {type === "team" ? "Couleurs d'équipe" : "Couleurs personnelles"}
              </h4>
              <div className="grid grid-cols-3 gap-2">
                {availableColors.map((color) => (
                  <button
                    key={color.id}
                    type="button"
                    onClick={() => handleColorSelect(color)}
                    className={`flex items-center gap-2 p-2 rounded-lg border transition-all hover:bg-gray-50 ${
                      selectedColorInfo.id === color.id
                        ? 'border-indigo-500 bg-indigo-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    title={color.name}
                  >
                    <div 
                      className="w-5 h-5 rounded-full border border-gray-300 shadow-sm"
                      style={{ backgroundColor: color.hex }}
                    />
                    <span className="text-xs text-gray-700 truncate">
                      {color.name.replace(type === "team" ? " Équipe" : " Personnel", "")}
                    </span>
                  </button>
                ))}
              </div>
            </div>

            {/* Section couleur personnalisée */}
            {showCustomColor && (
              <div className="pt-3 border-t border-gray-200">
                <h4 className="text-xs font-medium text-gray-600 mb-2">
                  Couleur personnalisée
                </h4>
                <div className="flex items-center gap-2">
                  <input
                    id="custom-color-input"
                    name="customColor"
                    type="color"
                    value={selectedColorInfo.category === 'custom' ? selectedColorInfo.hex : '#3788d8'}
                    onChange={(e) => handleCustomColorChange(e.target.value)}
                    className="w-10 h-8 rounded border border-gray-300 cursor-pointer"
                  />
                  <input
                    id="custom-color-text"
                    name="customColorText"
                    type="text"
                    value={selectedColorInfo.category === 'custom' ? selectedColorInfo.hex : ''}
                    onChange={(e) => {
                      const value = e.target.value;
                      if (value.match(/^#[0-9A-F]{6}$/i) || value === '') {
                        handleCustomColorChange(value);
                      }
                    }}
                    placeholder="#3788d8"
                    className="flex-1 px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500"
                  />
                </div>
              </div>
            )}
          </div>

          {/* Overlay pour fermer le dropdown */}
          <div 
            className="fixed inset-0 z-40" 
            onClick={() => setIsOpen(false)}
          />
        </>
      )}
    </div>
  );
};

export default BackendColorPicker;
