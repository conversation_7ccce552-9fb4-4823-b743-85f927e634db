import React, { useState } from 'react';
import { PERSONAL_COLORS, TEAM_COLORS, getColorById, getColorHex } from '@/utils/backendColors';
import { ChevronDown, Palette, Check } from 'lucide-react';

const BackendColorPicker = ({
  selectedColor,
  onColorChange,
  label = "Couleur de l'événement",
  className = "",
  type = "personal", // "personal" ou "team"
  showCustomColor = true,
  size = "default" // "sm", "default", "lg"
}) => {
  const [isOpen, setIsOpen] = useState(false);

  // Choisir les couleurs selon le type
  const availableColors = type === "team" ? TEAM_COLORS : PERSONAL_COLORS;

  // Déterminer la couleur sélectionnée
  const getSelectedColorInfo = () => {
    // Si selectedColor est un ID de couleur
    const colorById = getColorById(selectedColor);
    if (colorById) {
      return colorById;
    }

    // Si selectedColor est une couleur hex, essayer de trouver l'ID correspondant
    if (typeof selectedColor === 'string' && selectedColor.startsWith('#')) {
      const matchingColor = availableColors.find(color =>
        color.hex.toLowerCase() === selectedColor.toLowerCase()
      );
      if (matchingColor) {
        return matchingColor;
      }
      // Si pas de correspondance, créer un objet temporaire pour la couleur custom
      return {
        id: 'custom',
        name: 'Couleur personnalisée',
        hex: selectedColor,
        category: 'custom'
      };
    }

    // Couleur par défaut
    return availableColors[0];
  };

  const selectedColorInfo = getSelectedColorInfo();

  const handleColorSelect = (color) => {
    // Envoyer l'ID de la couleur au parent
    onColorChange(color.id);
    setIsOpen(false);
  };

  const handleCustomColorChange = (hexColor) => {
    // Pour les couleurs personnalisées, envoyer directement la valeur hex
    onColorChange(hexColor);
  };

  // Tailles selon le prop size
  const sizeClasses = {
    sm: {
      button: "px-2 py-1.5 text-xs",
      colorPreview: "w-4 h-4",
      dropdown: "p-3",
      colorButton: "p-1.5",
      colorCircle: "w-6 h-6"
    },
    default: {
      button: "px-3 py-2 text-sm",
      colorPreview: "w-6 h-6",
      dropdown: "p-4",
      colorButton: "p-2",
      colorCircle: "w-8 h-8"
    },
    lg: {
      button: "px-4 py-3 text-base",
      colorPreview: "w-8 h-8",
      dropdown: "p-5",
      colorButton: "p-3",
      colorCircle: "w-10 h-10"
    }
  };

  const currentSize = sizeClasses[size];

  return (
    <div className={`relative ${className}`}>
      {label && (
        <label
          htmlFor="color-picker-button"
          className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-2"
        >
          <Palette className="w-4 h-4" />
          {label}
        </label>
      )}

      {/* Bouton de sélection de couleur amélioré */}
      <button
        id="color-picker-button"
        name="colorPicker"
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className={`w-full flex items-center gap-3 border border-gray-300 rounded-xl shadow-sm bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200 hover:shadow-md ${currentSize.button}`}
      >
        <div className="flex items-center gap-2">
          <div
            className={`${currentSize.colorPreview} rounded-full border-2 border-white shadow-lg ring-1 ring-gray-200`}
            style={{ backgroundColor: selectedColorInfo.hex }}
          />
          <span className="text-gray-700 font-medium">
            {selectedColorInfo.name}
          </span>
        </div>
        <ChevronDown
          className={`w-4 h-4 text-gray-400 ml-auto transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
        />
      </button>

      {/* Dropdown avec palette de couleurs */}
      {isOpen && (
        <>
          <div className={`absolute z-50 mt-2 w-full bg-white border border-gray-200 rounded-xl shadow-xl ${currentSize.dropdown} backdrop-blur-sm`}>
            {/* En-tête */}
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-sm font-semibold text-gray-800 flex items-center gap-2">
                <Palette className="w-4 h-4" />
                {type === "team" ? "Couleurs d'équipe" : "Couleurs personnelles"}
              </h4>
              <button
                type="button"
                onClick={() => setIsOpen(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                ×
              </button>
            </div>

            {/* Couleurs prédéfinies avec design amélioré */}
            <div className="mb-4">
              <div className="grid grid-cols-2 gap-2">
                {availableColors.map((color) => (
                  <button
                    key={color.id}
                    type="button"
                    onClick={() => handleColorSelect(color)}
                    className={`flex items-center gap-3 ${currentSize.colorButton} rounded-lg border-2 transition-all duration-200 hover:scale-105 hover:shadow-md ${selectedColorInfo.id === color.id
                      ? 'border-indigo-500 bg-indigo-50 shadow-md'
                      : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                      }`}
                    title={color.name}
                  >
                    <div className="relative">
                      <div
                        className={`${currentSize.colorCircle} rounded-full border-2 border-white shadow-lg ring-1 ring-gray-200`}
                        style={{ backgroundColor: color.hex }}
                      />
                      {selectedColorInfo.id === color.id && (
                        <div className="absolute inset-0 flex items-center justify-center">
                          <Check className="w-4 h-4 text-white drop-shadow-lg" />
                        </div>
                      )}
                    </div>
                    <div className="flex-1 text-left">
                      <span className="text-sm font-medium text-gray-700 block">
                        {color.name.replace(type === "team" ? " Équipe" : " Personnel", "")}
                      </span>
                      <span className="text-xs text-gray-500">
                        {color.hex.toUpperCase()}
                      </span>
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Section couleur personnalisée améliorée */}
            {showCustomColor && (
              <div className="pt-4 border-t border-gray-200">
                <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center gap-2">
                  <div className="w-3 h-3 bg-gradient-to-r from-red-500 via-yellow-500 to-blue-500 rounded-full"></div>
                  Couleur personnalisée
                </h4>
                <div className="flex items-center gap-3">
                  <div className="relative">
                    <input
                      id="custom-color-input"
                      name="customColor"
                      type="color"
                      value={selectedColorInfo.category === 'custom' ? selectedColorInfo.hex : '#3788d8'}
                      onChange={(e) => handleCustomColorChange(e.target.value)}
                      className="w-12 h-10 rounded-lg border-2 border-gray-300 cursor-pointer shadow-sm hover:shadow-md transition-shadow"
                    />
                  </div>
                  <div className="flex-1">
                    <input
                      id="custom-color-text"
                      name="customColorText"
                      type="text"
                      value={selectedColorInfo.category === 'custom' ? selectedColorInfo.hex : ''}
                      onChange={(e) => {
                        const value = e.target.value;
                        if (value.match(/^#[0-9A-F]{6}$/i) || value === '') {
                          handleCustomColorChange(value);
                        }
                      }}
                      placeholder="#3788d8"
                      className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Format: #RRGGBB (ex: #3788d8)
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Overlay pour fermer le dropdown */}
          <div
            className="fixed inset-0 z-40"
            onClick={() => setIsOpen(false)}
          />
        </>
      )}
    </div>
  );
};

export default BackendColorPicker;
