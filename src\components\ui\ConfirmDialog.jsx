import React from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';

const ConfirmDialog = ({ 
  open, 
  onOpenChange, 
  title = "Confirmation", 
  description, 
  confirmText = "OK", 
  cancelText = "Annuler", 
  onConfirm, 
  onCancel,
  variant = "destructive" // "destructive" pour les suppressions, "default" pour autres
}) => {
  const handleConfirm = () => {
    onConfirm?.();
    onOpenChange(false);
  };

  const handleCancel = () => {
    onCancel?.();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="text-lg font-semibold">
            {title}
          </DialogTitle>
          {description && (
            <DialogDescription className="text-sm text-gray-600 mt-2">
              {description}
            </DialogDescription>
          )}
        </DialogHeader>
        
        <div className="flex justify-end space-x-3 pt-6">
          <Button
            variant="outline"
            onClick={handleCancel}
            className="px-6"
          >
            {cancelText}
          </Button>
          <Button
            variant={variant}
            onClick={handleConfirm}
            className={`px-6 ${
              variant === "destructive" 
                ? "bg-blue-600 hover:bg-blue-700 text-white" 
                : ""
            }`}
          >
            {confirmText}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ConfirmDialog;
