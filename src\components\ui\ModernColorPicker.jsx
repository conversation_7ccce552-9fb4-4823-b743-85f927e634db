import React, { useState } from 'react';
import { getAvailableColors, normalizeEventColor } from '@/utils/modernCalendarHelper';

const ModernColorPicker = ({ 
  selectedColor, 
  onColorChange, 
  label = "Couleur de l'événement",
  className = "" 
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const availableColors = getAvailableColors();

  // Normaliser la couleur sélectionnée
  const normalizedColor = normalizeEventColor(selectedColor);
  
  // Trouver la couleur sélectionnée dans la palette
  const selectedColorInfo = availableColors.find(color => 
    color.border === normalizedColor || color.key === selectedColor
  ) || availableColors[0]; // Fallback vers la première couleur

  const handleColorSelect = (color) => {
    onColorChange(color.border);
    setIsOpen(false);
  };

  return (
    <div className={`relative ${className}`}>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {label}
      </label>
      
      {/* Bouton de sélection de couleur */}
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex items-center justify-between px-3 py-2 border border-gray-300 rounded-lg shadow-sm bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors"
      >
        <div className="flex items-center gap-3">
          <div 
            className="w-6 h-6 rounded-full border-2 shadow-sm"
            style={{ 
              backgroundColor: selectedColorInfo.background,
              borderColor: selectedColorInfo.border 
            }}
          />
          <span className="text-sm font-medium text-gray-700">
            {selectedColorInfo.name}
          </span>
        </div>
        <svg 
          className={`w-4 h-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`}
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {/* Dropdown avec palette de couleurs */}
      {isOpen && (
        <div className="absolute z-50 mt-2 w-full bg-white border border-gray-200 rounded-lg shadow-lg p-4">
          <div className="grid grid-cols-4 gap-3">
            {availableColors.map((color) => (
              <button
                key={color.key}
                type="button"
                onClick={() => handleColorSelect(color)}
                className={`group relative flex flex-col items-center p-3 rounded-lg border-2 transition-all hover:scale-105 ${
                  selectedColorInfo.key === color.key
                    ? 'border-indigo-500 bg-indigo-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                title={color.name}
              >
                {/* Cercle de couleur */}
                <div 
                  className="w-8 h-8 rounded-full shadow-sm mb-2 border-2"
                  style={{ 
                    backgroundColor: color.background,
                    borderColor: color.border 
                  }}
                />
                
                {/* Nom de la couleur */}
                <span className="text-xs font-medium text-gray-600 text-center leading-tight">
                  {color.name}
                </span>

                {/* Indicateur de sélection */}
                {selectedColorInfo.key === color.key && (
                  <div className="absolute -top-1 -right-1 w-5 h-5 bg-indigo-500 rounded-full flex items-center justify-center">
                    <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                )}
              </button>
            ))}
          </div>

          {/* Section couleur personnalisée */}
          <div className="mt-4 pt-4 border-t border-gray-200">
            <label className="block text-xs font-medium text-gray-600 mb-2">
              Couleur personnalisée
            </label>
            <div className="flex items-center gap-2">
              <input
                type="color"
                value={normalizedColor || '#3B82F6'}
                onChange={(e) => onColorChange(e.target.value)}
                className="w-10 h-8 rounded border border-gray-300 cursor-pointer"
              />
              <input
                type="text"
                value={normalizedColor || ''}
                onChange={(e) => {
                  const value = e.target.value;
                  if (value.match(/^#[0-9A-F]{6}$/i) || value === '') {
                    onColorChange(value);
                  }
                }}
                placeholder="#3B82F6"
                className="flex-1 px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500"
              />
            </div>
          </div>
        </div>
      )}

      {/* Overlay pour fermer le dropdown */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
};

export default ModernColorPicker;
