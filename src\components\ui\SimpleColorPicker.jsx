import React, { useState } from 'react';

const SimpleColorPicker = ({ 
  selectedColor, 
  onColorChange, 
  label = "Couleur de l'événement",
  className = "" 
}) => {
  const [isOpen, setIsOpen] = useState(false);

  // Palette de couleurs simple comme dans l'image
  const colors = [
    { name: 'G<PERSON>', hex: '#6B7280', bg: '#F3F4F6' },
    { name: 'Rouge', hex: '#EF4444', bg: '#FEE2E2' },
    { name: 'Orange', hex: '#F97316', bg: '#FED7AA' },
    { name: '<PERSON><PERSON><PERSON>', hex: '#EAB308', bg: '#FEF3C7' },
    { name: 'Vert', hex: '#22C55E', bg: '#DCFCE7' },
    { name: 'Bleu', hex: '#3B82F6', bg: '#DBEAFE' },
    { name: 'Violet', hex: '#8B5CF6', bg: '#EDE9FE' },
    { name: 'Noir', hex: '#1F2937', bg: '#F9FAFB' }
  ];

  // Trouver la couleur sélectionnée
  const selectedColorInfo = colors.find(color => 
    color.hex === selectedColor || 
    color.hex.toLowerCase() === selectedColor?.toLowerCase()
  ) || colors[5]; // Bleu par défaut

  const handleColorSelect = (color) => {
    onColorChange(color.hex);
    setIsOpen(false);
  };

  return (
    <div className={`relative ${className}`}>
      {label && (
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {label}
        </label>
      )}
      
      {/* Bouton de sélection de couleur */}
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 px-3 py-2 border border-gray-300 rounded-lg shadow-sm bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors"
      >
        <div 
          className="w-5 h-5 rounded-full border border-gray-300"
          style={{ backgroundColor: selectedColorInfo.hex }}
        />
        <span className="text-sm text-gray-700">
          {selectedColorInfo.name}
        </span>
        <svg 
          className={`w-4 h-4 text-gray-400 ml-auto transition-transform ${isOpen ? 'rotate-180' : ''}`}
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {/* Dropdown avec palette de couleurs */}
      {isOpen && (
        <>
          <div className="absolute z-50 mt-2 p-3 bg-gray-800 rounded-lg shadow-lg">
            <div className="flex gap-2">
              {colors.map((color) => (
                <button
                  key={color.hex}
                  type="button"
                  onClick={() => handleColorSelect(color)}
                  className={`w-8 h-8 rounded-full border-2 transition-all hover:scale-110 ${
                    selectedColorInfo.hex === color.hex
                      ? 'border-white ring-2 ring-blue-400'
                      : 'border-gray-600 hover:border-gray-400'
                  }`}
                  style={{ backgroundColor: color.hex }}
                  title={color.name}
                />
              ))}
            </div>
          </div>

          {/* Overlay pour fermer le dropdown */}
          <div 
            className="fixed inset-0 z-40" 
            onClick={() => setIsOpen(false)}
          />
        </>
      )}
    </div>
  );
};

export default SimpleColorPicker;
