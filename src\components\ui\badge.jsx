import * as React from "react";
import { cn } from "@/lib/utils";

function Badge({ className, variant = "default", ...props }) {
  const getVariantClass = (variant) => {
    switch (variant) {
      case "default":
        return "border-transparent bg-primary text-primary-foreground hover:bg-primary/80";
      case "secondary":
        return "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80";
      case "destructive":
        return "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80";
      case "outline":
        return "text-foreground";
      case "success":
        return "border-transparent bg-green-500 text-white hover:bg-green-600";
      case "warning":
        return "border-transparent bg-yellow-500 text-white hover:bg-yellow-600";
      case "info":
        return "border-transparent bg-blue-500 text-white hover:bg-blue-600";
      default:
        return "border-transparent bg-primary text-primary-foreground hover:bg-primary/80";
    }
  };

  const baseClass = "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2";
  const variantClass = getVariantClass(variant);

  return (
    <div className={cn(baseClass, variantClass, className)} {...props} />
  );
}

export { Badge };
