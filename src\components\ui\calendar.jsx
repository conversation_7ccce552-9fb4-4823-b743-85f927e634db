import React, { useState } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';

const Calendar = React.forwardRef(({
  className,
  mode = "single",
  selected,
  onSelect,
  disabled,
  initialFocus,
  ...props
}, ref) => {
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [focusedDay, setFocusedDay] = useState(null);

  // Fonction pour obtenir le nombre de jours dans un mois
  const getDaysInMonth = (year, month) => {
    return new Date(year, month + 1, 0).getDate();
  };

  // Fonction pour obtenir le premier jour du mois (0 = dimanche, 1 = lundi, etc.)
  const getFirstDayOfMonth = (year, month) => {
    return new Date(year, month, 1).getDay();
  };

  // Fonction pour formater la date (année-mois-jour)
  const formatDate = (date) => {
    return date.toISOString().split('T')[0];
  };

  // Fonction pour vérifier si deux dates sont le même jour
  const isSameDay = (date1, date2) => {
    if (!date1 || !date2) return false;
    return formatDate(date1) === formatDate(date2);
  };

  // Fonction pour vérifier si une date est sélectionnée
  const isSelected = (date) => {
    if (!selected) return false;
    if (Array.isArray(selected)) {
      return selected.some(selectedDate => isSameDay(selectedDate, date));
    }
    return isSameDay(selected, date);
  };

  // Fonction pour vérifier si une date est désactivée
  const isDisabledDate = (date) => {
    if (!disabled) return false;
    if (typeof disabled === 'function') {
      return disabled(date);
    }
    return false;
  };

  // Fonction pour gérer la sélection d'une date
  const handleSelectDate = (date) => {
    if (isDisabledDate(date)) return;
    if (onSelect) {
      onSelect(date);
    }
  };

  // Fonction pour passer au mois précédent
  const handlePrevMonth = () => {
    setCurrentMonth(prevMonth => {
      const newMonth = new Date(prevMonth);
      newMonth.setMonth(newMonth.getMonth() - 1);
      return newMonth;
    });
  };

  // Fonction pour passer au mois suivant
  const handleNextMonth = () => {
    setCurrentMonth(prevMonth => {
      const newMonth = new Date(prevMonth);
      newMonth.setMonth(newMonth.getMonth() + 1);
      return newMonth;
    });
  };

  // Obtenir l'année et le mois actuels
  const currentYear = currentMonth.getFullYear();
  const currentMonthIndex = currentMonth.getMonth();

  // Obtenir le nombre de jours dans le mois actuel
  const daysInMonth = getDaysInMonth(currentYear, currentMonthIndex);

  // Obtenir le premier jour du mois (0 = dimanche, 1 = lundi, etc.)
  const firstDayOfMonth = getFirstDayOfMonth(currentYear, currentMonthIndex);

  // Noms des mois
  const monthNames = [
    'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
    'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
  ];

  // Noms des jours de la semaine
  const dayNames = ['Dim', 'Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam'];

  // Générer les jours du mois
  const days = [];
  for (let i = 0; i < firstDayOfMonth; i++) {
    days.push(null); // Jours vides avant le premier jour du mois
  }
  for (let i = 1; i <= daysInMonth; i++) {
    days.push(new Date(currentYear, currentMonthIndex, i));
  }

  return (
    <div className={cn("p-3", className)} ref={ref} {...props}>
      <div className="flex justify-between items-center mb-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={handlePrevMonth}
          className="h-7 w-7 p-0 rounded-full"
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
        <div className="font-medium">
          {monthNames[currentMonthIndex]} {currentYear}
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleNextMonth}
          className="h-7 w-7 p-0 rounded-full"
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>
      <div className="grid grid-cols-7 gap-1 mb-2">
        {dayNames.map((day, index) => (
          <div key={index} className="text-center text-xs font-medium text-gray-500">
            {day}
          </div>
        ))}
      </div>
      <div className="grid grid-cols-7 gap-1">
        {days.map((day, index) => {
          if (!day) {
            return <div key={`empty-${index}`} className="h-9" />;
          }
          
          const isToday = isSameDay(day, new Date());
          const isSelectedDay = isSelected(day);
          const isDisabled = isDisabledDate(day);
          
          return (
            <Button
              key={day.toString()}
              variant="ghost"
              size="sm"
              disabled={isDisabled}
              className={cn(
                "h-9 w-9 p-0 font-normal rounded-full",
                isToday && "bg-gray-100",
                isSelectedDay && "bg-indigo-100 text-indigo-900",
                isDisabled && "text-gray-300 cursor-not-allowed"
              )}
              onClick={() => handleSelectDate(day)}
            >
              {day.getDate()}
            </Button>
          );
        })}
      </div>
    </div>
  );
});

Calendar.displayName = "Calendar";

export { Calendar };
