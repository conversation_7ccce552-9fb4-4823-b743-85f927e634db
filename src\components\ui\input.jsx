import { forwardRef } from 'react';
import { cn } from '@/lib/utils';

const Input = forwardRef(({ className, type = "text", onKeyDown, ...props }, ref) => {
    const handleKeyDown = (e) => {
        // Empêcher la soumission automatique du formulaire avec Enter
        if (e.key === 'Enter' && type !== 'submit') {
            e.preventDefault();
            e.stopPropagation();
        }

        // Appeler le gestionnaire onKeyDown personnalisé s'il existe
        if (onKeyDown) {
            onKeyDown(e);
        }
    };

    return (
        <input
            type={type}
            className={cn(
                "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
                className
            )}
            ref={ref}
            onKeyDown={handleKeyDown}
            {...props}
        />
    );
});

Input.displayName = "Input";

export { Input };
