import React from 'react';
import { cn } from '@/lib/utils';

const Popover = ({ children, open, onOpenChange }) => {
  const [isOpen, setIsOpen] = React.useState(open || false);

  React.useEffect(() => {
    if (open !== undefined) {
      setIsOpen(open);
    }
  }, [open]);

  const handleOpenChange = (newOpen) => {
    setIsOpen(newOpen);
    if (onOpenChange) {
      onOpenChange(newOpen);
    }
  };

  return (
    <PopoverContext.Provider value={{ open: isOpen, onOpenChange: handleOpenChange }}>
      {children}
    </PopoverContext.Provider>
  );
};

const PopoverContext = React.createContext({
  open: false,
  onOpenChange: () => {},
});

const usePopoverContext = () => {
  const context = React.useContext(PopoverContext);
  if (!context) {
    throw new Error('Popover compound components must be used within a Popover component');
  }
  return context;
};

const PopoverTrigger = React.forwardRef(({ children, asChild, ...props }, ref) => {
  const { open, onOpenChange } = usePopoverContext();
  
  const handleClick = (e) => {
    e.preventDefault();
    onOpenChange(!open);
    
    if (props.onClick) {
      props.onClick(e);
    }
  };

  if (asChild && React.isValidElement(children)) {
    return React.cloneElement(children, {
      ref,
      onClick: handleClick,
      'aria-expanded': open,
      'aria-haspopup': true,
      ...props,
    });
  }

  return (
    <button
      ref={ref}
      onClick={handleClick}
      aria-expanded={open}
      aria-haspopup={true}
      {...props}
    >
      {children}
    </button>
  );
});

PopoverTrigger.displayName = 'PopoverTrigger';

const PopoverContent = React.forwardRef(({ className, children, ...props }, ref) => {
  const { open } = usePopoverContext();

  if (!open) {
    return null;
  }

  return (
    <div
      ref={ref}
      className={cn(
        "absolute z-50 min-w-[8rem] overflow-hidden rounded-md border border-gray-200 bg-white p-1 shadow-md animate-in fade-in-80",
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
});

PopoverContent.displayName = 'PopoverContent';

export { Popover, PopoverTrigger, PopoverContent };
