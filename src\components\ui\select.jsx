import React, { createContext, useContext, useState, forwardRef } from 'react';
import { cn } from '@/lib/utils';
import { ChevronDown } from 'lucide-react';

const SelectContext = createContext({
  value: undefined,
  onValueChange: () => { },
  open: false,
  onOpenChange: () => { },
  disabled: false
});

const useSelectContext = () => {
  const context = useContext(SelectContext);
  if (!context) {
    throw new Error('Select compound components must be used within a Select component');
  }
  return context;
};

const Select = ({ children, value, onValueChange, disabled = false }) => {
  const [open, setOpen] = useState(false);

  return (
    <SelectContext.Provider value={{ value, onValueChange, open, onOpenChange: setOpen, disabled }}>
      <div className="relative w-full">
        {children}
      </div>
    </SelectContext.Provider>
  );
};

const SelectTrigger = forwardRef(({ className, children, id, ...props }, ref) => {
  const { value, open, onOpenChange, disabled } = useSelectContext();

  return (
    <button
      type="button"
      id={id}
      ref={ref}
      onClick={(e) => {
        e.preventDefault();
        e.stopPropagation();
        if (!disabled) onOpenChange(!open);
      }}
      onKeyDown={(e) => {
        // Empêcher Enter de soumettre le formulaire
        if (e.key === 'Enter') {
          e.preventDefault();
          e.stopPropagation();
        }
      }}
      className={cn(
        "flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
        className
      )}
      disabled={disabled}
      aria-expanded={open}
      {...props}
    >
      {children}
      <ChevronDown className="h-4 w-4 opacity-50" />
    </button>
  );
});

SelectTrigger.displayName = "SelectTrigger";

const SelectValue = ({ placeholder, className, ...props }) => {
  const { value } = useSelectContext();

  return (
    <span className={cn("block truncate", className)} {...props}>
      {value ? props.children : placeholder}
    </span>
  );
};

SelectValue.displayName = "SelectValue";

const SelectContent = React.forwardRef(({ className, children, ...props }, ref) => {
  const { open } = useSelectContext();

  if (!open) return null;

  return (
    <div
      ref={ref}
      className={cn(
        "absolute z-50 min-w-[8rem] w-full overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md animate-in fade-in-80 mt-1",
        className
      )}
      {...props}
    >
      <div className="p-1">{children}</div>
    </div>
  );
});

SelectContent.displayName = "SelectContent";

const SelectItem = React.forwardRef(({ className, children, value: itemValue, ...props }, ref) => {
  const { value, onValueChange, onOpenChange } = useSelectContext();
  const isSelected = value === itemValue;

  return (
    <div
      ref={ref}
      className={cn(
        "relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 px-2 text-sm outline-none hover:bg-accent hover:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
        isSelected && "bg-accent text-accent-foreground",
        className
      )}
      onClick={(e) => {
        e.preventDefault();
        e.stopPropagation();
        onValueChange(itemValue);
        onOpenChange(false);
      }}
      onKeyDown={(e) => {
        // Empêcher Enter de soumettre le formulaire
        if (e.key === 'Enter') {
          e.preventDefault();
          e.stopPropagation();
          onValueChange(itemValue);
          onOpenChange(false);
        }
      }}
      {...props}
    >
      <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
        {isSelected && (
          <span className="h-2 w-2 rounded-full bg-current" />
        )}
      </span>
      <span className="pl-6">{children}</span>
    </div>
  );
});

SelectItem.displayName = "SelectItem";

export { Select, SelectTrigger, SelectValue, SelectContent, SelectItem };