import React from 'react';
import { Switch } from '@headlessui/react';

const PERMISSIONS_CONFIG = [
    {
        category: 'Gestion équipes',
        permissions: [
            {
                key: 'manage_teams',
                label: 'Gérer les équipes',
                description: 'C<PERSON>er/modifier/supprimer des équipes'
            },
            {
                key: 'manage_team_tasks',
                label: 'Gérer tâches d\'équipe',
                description: 'Gérer les tâches des équipes'
            },
            {
                key: 'manage_team_calendars',
                label: 'Gérer calendriers d\'équipe',
                description: 'Gérer les calendriers d\'équipe'
            },
            {
                key: 'modify_team_event_status',
                label: 'Modifier statut événements équipe',
                description: 'Modifier le statut des événements d\'équipe assignés'
            },
            {
                key: 'modify_team_task_status',
                label: 'Modifier statut tâches équipe',
                description: 'Modifier le statut des tâches d\'équipe assignées'
            }
        ]
    },
    {
        category: 'Gestion personnelle',
        permissions: [
            {
                key: 'manage_personal_tasks',
                label: 'Gérer tâches personnelles',
                description: '<PERSON><PERSON><PERSON>, modifier et supprimer ses propres tâches'
            },
            {
                key: 'manage_personal_calendar',
                label: 'Gérer calendrier personnel',
                description: 'Gérer son calendrier personnel et ses événements'
            },
            {
                key: 'manage_journal_notes',
                label: 'Gérer journal & notes personnelles',
                description: 'Gérer journal et notes personnelles'
            },
            {
                key: 'activate_focus_mode',
                label: 'Activer mode focus',
                description: 'Activer le mode focus pour les sessions de travail'
            }
        ]
    },
    {
        category: 'Tableaux de bord',
        permissions: [
            {
                key: 'view_personal_dashboard',
                label: 'Dashboard personnel',
                description: 'Consulter son dashboard personnel'
            },
            {
                key: 'view_team_dashboards',
                label: 'Dashboards équipes',
                description: 'Accès aux dashboards d\'équipe'
            },
            {
                key: 'view_main_dashboard',
                label: 'Dashboard principal',
                description: 'Accès au dashboard principal'
            }
        ]
    }
];

const UserPermissionsDisplay = ({ permissions, onToggle, disabled, userRole }) => {
    // Filtrer les permissions selon le rôle de l'utilisateur
    const getRelevantCategories = () => {
        if (!userRole) return PERMISSIONS_CONFIG;

        switch (userRole) {
            case 'admin':
                return PERMISSIONS_CONFIG.filter(category =>
                    category.category === 'Gestion équipes' ||
                    category.category === 'Tableaux de bord'
                ).map(category => {
                    if (category.category === 'Gestion équipes') {
                        return {
                            ...category,
                            permissions: category.permissions.filter(p =>
                                p.key === 'manage_teams' ||
                                p.key === 'manage_team_tasks' ||
                                p.key === 'manage_team_calendars'
                            )
                        };
                    }
                    if (category.category === 'Tableaux de bord') {
                        return {
                            ...category,
                            permissions: category.permissions.filter(p =>
                                p.key === 'view_team_dashboards'
                            )
                        };
                    }
                    return category;
                });

            case 'employee':
                return PERMISSIONS_CONFIG.filter(category =>
                    category.category === 'Gestion personnelle' ||
                    category.category === 'Gestion équipes'
                ).map(category => {
                    if (category.category === 'Gestion personnelle') {
                        return {
                            ...category,
                            permissions: category.permissions.filter(p =>
                                p.key === 'manage_personal_tasks' ||
                                p.key === 'manage_personal_calendar'
                            )
                        };
                    }
                    if (category.category === 'Gestion équipes') {
                        return {
                            ...category,
                            permissions: category.permissions.filter(p =>
                                p.key === 'modify_team_event_status' ||
                                p.key === 'modify_team_task_status'
                            )
                        };
                    }
                    return category;
                });

            case 'client':
                return PERMISSIONS_CONFIG.filter(category =>
                    category.category === 'Gestion personnelle' ||
                    category.category === 'Tableaux de bord'
                ).map(category => {
                    if (category.category === 'Gestion personnelle') {
                        return {
                            ...category,
                            permissions: category.permissions.filter(p =>
                                p.key === 'manage_personal_tasks' ||
                                p.key === 'manage_personal_calendar' ||
                                p.key === 'manage_journal_notes' ||
                                p.key === 'activate_focus_mode'
                            )
                        };
                    }
                    if (category.category === 'Tableaux de bord') {
                        return {
                            ...category,
                            permissions: category.permissions.filter(p =>
                                p.key === 'view_personal_dashboard'
                            )
                        };
                    }
                    return category;
                });

            default:
                return PERMISSIONS_CONFIG;
        }
    };

    const relevantCategories = getRelevantCategories();

    return (
        <div className="space-y-8">
            {relevantCategories.map((category) => (
                <div key={category.category} className="bg-white rounded-lg shadow-sm p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">
                        {category.category}
                    </h3>
                    <div className="space-y-4">
                        {category.permissions.map((permission) => (
                            <div key={permission.key} className="flex items-center justify-between py-2">
                                <div>
                                    <h4 className="text-sm font-medium text-gray-900">
                                        {permission.label}
                                    </h4>
                                    <p className="text-sm text-gray-500">
                                        {permission.description}
                                    </p>
                                </div>
                                <Switch
                                    checked={permissions[permission.key] || false}
                                    onChange={() => onToggle(permission.key)}
                                    disabled={disabled}
                                    className={`${permissions[permission.key] ? 'bg-[#6B4EFF]' : 'bg-gray-200'}
                                        relative inline-flex h-6 w-11 items-center rounded-full transition-colors
                                        focus:outline-none focus:ring-2 focus:ring-[#6B4EFF] focus:ring-offset-2
                                        ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                                >
                                    <span
                                        className={`${permissions[permission.key] ? 'translate-x-6' : 'translate-x-1'}
                                            inline-block h-4 w-4 transform rounded-full bg-white transition-transform`}
                                    />
                                </Switch>
                            </div>
                        ))}
                    </div>
                </div>
            ))}
        </div>
    );
};

export default UserPermissionsDisplay;