// Configuration pour le service d'email

export const emailConfig = {
    // URL de base de l'application pour les liens dans les emails
    appUrl: 'http://localhost:5173',

    // Format du lien de réinitialisation de mot de passe
    // Le backend remplacera {token} par le token réel
    resetPasswordUrl: 'http://localhost:5173/reset-password/{token}',

    // Configuration des emails
    templates: {
        // Sujet de l'email pour la réinitialisation de mot de passe
        resetPasswordSubject: 'Réinitialisation de votre mot de passe',

        // Expéditeur des emails
        fromEmail: '<EMAIL>',

        // Nom de l'expéditeur
        fromName: 'Notora'
    },

    // Délai d'expiration des liens de réinitialisation (en heures)
    resetPasswordExpiration: 1
};