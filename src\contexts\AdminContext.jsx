import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useAuth } from './AuthContext';
import { useTeam } from './TeamContext';
import axios from 'axios';
import { API_URL } from '@/config/constants';
import { toast } from 'react-toastify';

// Créer le contexte
const AdminContext = createContext();

// Hook personnalisé pour utiliser le contexte
export const useAdmin = () => {
  const context = useContext(AdminContext);
  if (!context) {
    console.warn('useAdmin must be used within an AdminProvider');
    return {
      metrics: null,
      teams: [],
      loading: false,
      error: null,
      fetchAdminMetrics: () => Promise.resolve(null),
      fetchTeams: () => Promise.resolve(),
      isAdmin: false
    };
  }
  return context;
};

// Fournisseur du contexte
export const AdminProvider = ({ children }) => {
  const { user, getAuthHeader } = useAuth();

  // Utiliser le hook avec des vérifications de sécurité
  const teamContext = useTeam();
  const teams = teamContext?.teams || [];
  const fetchTeams = teamContext?.fetchTeams || (() => Promise.resolve());

  // États
  const [metrics, setMetrics] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Vérifier si l'utilisateur est un admin
  const isAdmin = user && user.role === 'admin';

  // Récupérer les métriques BI pour l'admin
  const fetchAdminMetrics = useCallback(async () => {
    if (!isAdmin) return;

    setLoading(true);
    setError(null);

    try {
      const header = await getAuthHeader();
      if (!header) {
        throw new Error('Session expirée, veuillez vous reconnecter');
      }

      // ❌ ENDPOINT SUPPRIMÉ - /bi/metrics/ n'existe plus
      // Pour les admins, utiliser des données mockées ou un endpoint spécifique
      throw new Error('Endpoint BI non disponible pour les admins');

      console.log('AdminContext - Métriques récupérées:', response.data);
      setMetrics(response.data);
      return response.data;
    } catch (err) {
      console.error('Erreur lors de la récupération des métriques admin:', err);
      setError(err.message || 'Erreur lors de la récupération des métriques admin');
      toast.error('Impossible de charger les métriques du tableau de bord');
      return null;
    } finally {
      setLoading(false);
    }
  }, [isAdmin, getAuthHeader]);

  // Charger les équipes si elles ne sont pas déjà chargées
  useEffect(() => {
    if (isAdmin && (!teams || teams.length === 0)) {
      fetchTeams();
    }
  }, [isAdmin, teams, fetchTeams]);

  // Valeur du contexte
  const value = {
    metrics,
    teams,
    loading,
    error,
    fetchAdminMetrics,
    fetchTeams,
    isAdmin
  };

  return (
    <AdminContext.Provider value={value}>
      {children}
    </AdminContext.Provider>
  );
};

export default AdminContext;
