import { createContext, useState, useContext, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { authService } from '@/services/authService';
import { toast } from 'react-toastify';

const AuthContext = createContext(null);

export const AuthProvider = ({ children }) => {
    const [user, setUser] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const navigate = useNavigate();

    const checkAuth = async () => {
        try {
            console.log('Vérification de l\'authentification...');

            // Vérifier si un token est présent
            const token = authService.getAccessToken();
            console.log('Token d\'authentification présent:', !!token);

            if (!token) {
                console.log('Aucun token trouvé, utilisateur non authentifié');
                setUser(null);
                setLoading(false);
                return;
            }

            // Récupérer les données utilisateur
            const currentUser = authService.getCurrentUser();
            console.log('Données utilisateur récupérées:', currentUser);

            if (currentUser) {
                // Mettre à jour l'état de l'utilisateur
                setUser(currentUser);
                console.log('Utilisateur authentifié:', currentUser.role);
                console.log('Équipes de l\'utilisateur:', currentUser.teams);
            } else {
                // Si pas d'utilisateur mais un token, essayer de rafraîchir le token
                console.log('Token présent mais pas de données utilisateur, tentative de rafraîchissement');
                const refreshed = await authService.refreshToken();

                if (refreshed) {
                    // Récupérer à nouveau les données utilisateur après rafraîchissement
                    const refreshedUser = authService.getCurrentUser();
                    if (refreshedUser) {
                        setUser(refreshedUser);
                        console.log('Authentification restaurée après rafraîchissement');
                    } else {
                        console.error('Échec de récupération des données utilisateur après rafraîchissement');
                        authService.logout();
                        setUser(null);
                    }
                } else {
                    console.error('Échec du rafraîchissement du token');
                    authService.logout();
                    setUser(null);
                }
            }
        } catch (error) {
            console.error('Erreur lors de la vérification de l\'authentification:', error);
            setError(error.message);
            authService.logout();
            setUser(null);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        checkAuth();
    }, []);

    const login = async (credentials) => {
        try {
            setError(null);
            console.log('Tentative de connexion avec les identifiants:', { email: credentials.email });

            const response = await authService.login(credentials);
            console.log('Login response:', response);

            if (!response.user) {
                console.error('Réponse de connexion sans données utilisateur');
                throw new Error('Données utilisateur manquantes dans la réponse');
            }

            // Mettre à jour l'état de l'utilisateur
            setUser(response.user);
            console.log('Utilisateur connecté:', response.user);

            return response;
        } catch (error) {
            console.error('Login error:', error);
            setError(error.message || 'Échec de la connexion');

            // Nettoyer les données d'authentification en cas d'erreur
            authService.logout();
            setUser(null);

            throw error;
        }
    };

    const logout = () => {
        console.log('Logging out...');
        authService.logout();
        setUser(null);
        navigate('/login');
    };

    const register = async (userData) => {
        try {
            setError(null);
            console.log('Tentative d\'inscription avec les données:', { email: userData.email, role: userData.role });

            const response = await authService.register(userData);
            console.log('Register response:', response);

            toast.success('Inscription réussie ! Vous pouvez maintenant vous connecter.');
            return response;
        } catch (error) {
            console.error('Registration error:', error);
            const errorMessage = error.message || error.error || 'Échec de l\'inscription';
            setError(errorMessage);
            toast.error(errorMessage);
            throw error;
        }
    };

    const changePassword = async (passwordData) => {
        try {
            console.log('Tentative de changement de mot de passe depuis AuthContext');
            console.log('Utilisateur actuel:', {
                id: user?.id,
                role: user?.role,
                temp_password_required: user?.temp_password_required
            });

            // Vérifier si l'utilisateur est super_admin
            if (user?.role === 'super_admin') {
                console.log('Changement de mot de passe pour un super admin');
            }

            const response = await authService.changePassword(passwordData);

            console.log('Réponse du changement de mot de passe:', response);

            // Si le changement nécessite une reconnexion
            if (response.requiresRelogin) {
                toast.success('Mot de passe changé avec succès. Veuillez vous reconnecter.');
                logout();
                navigate('/login');
            } else {
                // Mettre à jour l'utilisateur si nécessaire
                if (user?.temp_password_required) {
                    const updatedUser = {
                        ...user,
                        temp_password_required: false,
                        temp_password_used: true
                    };
                    setUser(updatedUser);
                }
                toast.success('Mot de passe changé avec succès');
            }

            return response;
        } catch (error) {
            console.error('Erreur lors du changement de mot de passe:', error);

            // Message d'erreur spécifique pour le super admin
            if (user?.role === 'super_admin' && error.error && error.error.includes('super administrateur')) {
                toast.error(error.error);
                return null;
            }

            const errorMessage = error.error || error.message || 'Échec du changement de mot de passe';
            toast.error(errorMessage);
            throw error;
        }
    };

    const updateUser = (userData) => {
        if (!userData) return;

        const updatedUser = { ...user, ...userData };
        setUser(updatedUser);

        // Mettre à jour les données dans le localStorage
        const storedUser = authService.getCurrentUser();
        if (storedUser) {
            localStorage.setItem('user', JSON.stringify({ ...storedUser, ...userData }));
        }
    };

    const completePasswordChange = () => {
        if (user) {
            const updatedUser = {
                ...user,
                temp_password_required: false,
                temp_password_used: true
            };
            setUser(updatedUser);
            localStorage.setItem('user', JSON.stringify(updatedUser));
        }
    };

    const value = useMemo(() => ({
        user,
        loading,
        error,
        login,
        logout,
        register,
        changePassword,
        updateUser,
        completePasswordChange,
        isAuthenticated: () => {
            const token = authService.getAccessToken();
            const currentUser = authService.getCurrentUser();
            console.log('Token présent:', !!token);
            console.log('Utilisateur présent dans le contexte:', !!user);
            console.log('Utilisateur présent dans localStorage:', !!currentUser);

            // Vérifier à la fois le token et l'utilisateur (soit dans le contexte, soit dans localStorage)
            return !!token && (!!user || !!currentUser);
        },
        getAuthHeader: () => authService.getAuthHeader()
    }), [user, loading, error]);

    if (loading) {
        return <div>Loading...</div>;
    }

    return (
        <AuthContext.Provider value={value}>
            {children}
        </AuthContext.Provider>
    );
};

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};

export default AuthContext;