import { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useAuth } from './AuthContext';
import clientService from '@/services/clientService';
import { toast } from 'react-toastify';

// Créer le contexte
const ClientContext = createContext();

// Hook personnalisé pour utiliser le contexte
export const useClient = () => {
  const context = useContext(ClientContext);
  if (!context) {
    console.warn('useClient must be used within a ClientProvider');
    return {
      metrics: null,
      dashboard: null,
      pomodoroSettings: null,
      pomodoroState: {
        isActive: false,
        isPaused: false,
        timeRemaining: 25 * 60,
        currentSession: 'work',
        status: 'inactive',
        elapsedTime: 0
      },
      loading: false,
      error: null,
      fetchPersonalMetrics: () => Promise.resolve(null),
      fetchPersonalDashboard: () => Promise.resolve(null),
      updatePersonalDashboard: () => Promise.resolve(null),
      fetchPomodoroSettings: () => Promise.resolve(null),
      fetchPomodoroState: () => Promise.resolve(null),
      updatePomodoroSettings: () => Promise.resolve(null),
      controlPomodoro: () => Promise.resolve(null),
      updatePomodoroTimer: () => { },
      isClient: false
    };
  }
  return context;
};

// Fournisseur du contexte
export const ClientProvider = ({ children }) => {
  const { user } = useAuth();

  // États
  const [metrics, setMetrics] = useState(null);
  const [dashboard, setDashboard] = useState(null);
  const [pomodoroSettings, setPomodoroSettings] = useState(null);
  const [pomodoroState, setPomodoroState] = useState({
    isActive: false,
    isPaused: false,
    timeRemaining: 25 * 60, // 25 minutes par défaut
    currentSession: 'work',
    status: 'inactive',
    elapsedTime: 0
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Vérifier si l'utilisateur est un client
  const isClient = user && user.role === 'client';

  // Récupérer les métriques BI personnelles
  const fetchPersonalMetrics = useCallback(async () => {
    if (!isClient) return;

    setLoading(true);
    setError(null);

    try {
      const metricsData = await clientService.getPersonalMetrics();
      setMetrics(metricsData);
      return metricsData;
    } catch (err) {
      console.error('Erreur lors de la récupération des métriques personnelles:', err);
      setError(err.message || 'Erreur lors de la récupération des métriques personnelles');
      return null;
    } finally {
      setLoading(false);
    }
  }, [isClient]);

  // Récupérer le tableau de bord BI personnel
  const fetchPersonalDashboard = useCallback(async () => {
    if (!isClient) return;

    setLoading(true);
    setError(null);

    try {
      const dashboardData = await clientService.getPersonalDashboard();
      setDashboard(dashboardData);
      return dashboardData;
    } catch (err) {
      console.error('Erreur lors de la récupération du tableau de bord personnel:', err);
      setError(err.message || 'Erreur lors de la récupération du tableau de bord personnel');
      return null;
    } finally {
      setLoading(false);
    }
  }, [isClient]);

  // Mettre à jour le tableau de bord BI personnel
  const updatePersonalDashboard = useCallback(async (dashboardData) => {
    if (!isClient) return;

    setLoading(true);
    setError(null);

    try {
      const updatedDashboard = await clientService.updatePersonalDashboard(dashboardData);
      setDashboard(updatedDashboard);
      toast.success('Tableau de bord mis à jour avec succès');
      return updatedDashboard;
    } catch (err) {
      console.error('Erreur lors de la mise à jour du tableau de bord personnel:', err);
      setError(err.message || 'Erreur lors de la mise à jour du tableau de bord personnel');
      toast.error(err.message || 'Erreur lors de la mise à jour du tableau de bord personnel');
      return null;
    } finally {
      setLoading(false);
    }
  }, [isClient]);

  // Récupérer les paramètres du mode Pomodoro
  const fetchPomodoroSettings = useCallback(async () => {
    if (!isClient) return;

    setLoading(true);
    setError(null);

    try {
      const settingsData = await clientService.getPomodoroSettings();
      setPomodoroSettings(settingsData);

      // Initialiser l'état du Pomodoro avec les données des paramètres
      if (settingsData) {
        const workDuration = (settingsData.work_duration || 25) * 60; // Convertir en secondes
        setPomodoroState({
          isActive: settingsData.is_active || false,
          isPaused: false,
          timeRemaining: workDuration, // Utiliser la durée de travail des paramètres
          currentSession: 'work',
          status: settingsData.is_active ? 'active' : 'inactive',
          elapsedTime: 0
        });
      }

      return settingsData;
    } catch (err) {
      console.error('Erreur lors de la récupération des paramètres du mode Pomodoro:', err);
      setError(err.message || 'Erreur lors de la récupération des paramètres du mode Pomodoro');
      return null;
    } finally {
      setLoading(false);
    }
  }, [isClient]);

  // Mettre à jour les paramètres du mode Pomodoro
  const updatePomodoroSettings = useCallback(async (settingsData) => {
    if (!isClient) return;

    setLoading(true);
    setError(null);

    try {
      const updatedSettings = await clientService.updatePomodoroSettings(settingsData);
      setPomodoroSettings(updatedSettings);
      // Pas de toast ici - sera géré par le composant
      return updatedSettings;
    } catch (err) {
      console.error('Erreur lors de la mise à jour des paramètres du mode Pomodoro:', err);
      setError(err.message || 'Erreur lors de la mise à jour des paramètres du mode Pomodoro');
      // Pas de toast ici - sera géré par le composant
      throw err; // Relancer l'erreur pour que le composant puisse la gérer
    } finally {
      setLoading(false);
    }
  }, [isClient]);



  // Récupérer l'état actuel du mode Pomodoro
  const fetchPomodoroState = useCallback(async () => {
    if (!isClient) return;

    try {
      const pomodoroData = await clientService.getPomodoroSettings();
      console.log('Réponse fetchPomodoroState:', pomodoroData);

      // Mettre à jour l'état du Pomodoro avec la réponse du serveur
      setPomodoroState(prevState => {
        // Déterminer l'état de pause basé sur la réponse du backend
        const isCurrentlyPaused = pomodoroData.status === 'paused';
        const isCurrentlyActive = pomodoroData.is_active || pomodoroData.active || false;

        const newState = {
          isActive: isCurrentlyActive,
          isPaused: isCurrentlyPaused,
          timeRemaining: pomodoroData.remaining_time || pomodoroData.time_remaining || prevState.timeRemaining,
          currentSession: pomodoroData.session_type || 'work',
          status: pomodoroData.status || 'inactive',
          elapsedTime: pomodoroData.elapsed_time || 0
        };

        console.log('fetchPomodoroState - Nouvel état:', newState);
        return newState;
      });

      return pomodoroData;
    } catch (err) {
      console.error('Erreur lors de la récupération de l\'état Pomodoro:', err);
      // Ne pas lever d'erreur pour ne pas interrompre le flux
    }
  }, [isClient]);

  // Contrôler le mode Pomodoro
  const controlPomodoro = useCallback(async (action) => {
    if (!isClient) return;

    setLoading(true);
    setError(null);

    try {
      const pomodoroData = await clientService.controlPomodoro(action);
      console.log('Réponse controlPomodoro:', pomodoroData);

      // Mettre à jour l'état du Pomodoro avec la réponse du serveur
      setPomodoroState(prevState => {
        // Déterminer l'état de pause basé sur la réponse du backend
        const isCurrentlyPaused = pomodoroData.status === 'paused';
        const isCurrentlyActive = pomodoroData.is_active || pomodoroData.active || false;

        const newState = {
          isActive: isCurrentlyActive,
          isPaused: isCurrentlyPaused,
          timeRemaining: pomodoroData.remaining_time || pomodoroData.time_remaining || prevState.timeRemaining,
          currentSession: pomodoroData.session_type || 'work',
          status: pomodoroData.status || 'inactive',
          elapsedTime: pomodoroData.elapsed_time || 0
        };

        console.log('Réponse backend:', pomodoroData);
        console.log('Nouvel état Pomodoro:', newState);
        return newState;
      });

      return pomodoroData;
    } catch (err) {
      console.error(`Erreur lors du contrôle du mode Pomodoro (${action}):`, err);
      setError(err.message || `Erreur lors du contrôle du mode Pomodoro (${action})`);
      // Pas de toast ici - sera géré par le composant
      throw err; // Relancer l'erreur pour que le composant puisse la gérer
    } finally {
      setLoading(false);
    }
  }, [isClient]);

  // Fonction pour mettre à jour le timer localement
  const updatePomodoroTimer = useCallback(() => {
    setPomodoroState(prevState => {
      // Ne pas mettre à jour si pas actif, en pause, ou temps écoulé
      if (!prevState.isActive || prevState.isPaused || prevState.timeRemaining <= 0) {
        console.log('Timer arrêté - État:', {
          isActive: prevState.isActive,
          isPaused: prevState.isPaused,
          timeRemaining: prevState.timeRemaining
        });
        return prevState;
      }

      if (prevState.timeRemaining <= 1) {
        // Session terminée automatiquement
        console.log('Session terminée automatiquement');
        return {
          ...prevState,
          timeRemaining: 0,
          isActive: false,
          status: 'completed'
        };
      }

      // Décrémenter le temps
      const newState = {
        ...prevState,
        timeRemaining: prevState.timeRemaining - 1,
        elapsedTime: prevState.elapsedTime + 1
      };

      console.log('Timer mis à jour:', { timeRemaining: newState.timeRemaining });
      return newState;
    });
  }, []);

  // Charger les données au montage du composant
  useEffect(() => {
    if (isClient) {
      fetchPersonalMetrics();
      fetchPersonalDashboard();
      fetchPomodoroSettings();
    }
  }, [isClient, fetchPersonalMetrics, fetchPersonalDashboard, fetchPomodoroSettings]);

  // Valeur du contexte
  const value = {
    metrics,
    dashboard,
    pomodoroSettings,
    pomodoroState,
    loading,
    error,
    fetchPersonalMetrics,
    fetchPersonalDashboard,
    updatePersonalDashboard,
    fetchPomodoroSettings,
    fetchPomodoroState,
    updatePomodoroSettings,
    controlPomodoro,
    updatePomodoroTimer,
    isClient
  };

  return (
    <ClientContext.Provider value={value}>
      {children}
    </ClientContext.Provider>
  );
};

export default ClientContext;
