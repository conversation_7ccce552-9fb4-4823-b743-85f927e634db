import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useAuth } from './AuthContext';
import { useTeam } from './TeamContext';
import axios from 'axios';
import { API_URL } from '@/config/constants';
import { showErrorToast } from '@/utils/toastUtils';

// Créer le contexte
const EmployeeContext = createContext();

// Hook personnalisé pour utiliser le contexte
export const useEmployee = () => useContext(EmployeeContext);

// Fournisseur du contexte
export const EmployeeProvider = ({ children }) => {
  const { user, getAuthHeader } = useAuth();
  const { teams, fetchTeams } = useTeam();

  // États
  const [metrics, setMetrics] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Vérifier si l'utilisateur est un employé
  const isEmployee = user && user.role === 'employee';

  // Récupérer les métriques BI pour l'employé
  const fetchEmployeeMetrics = useCallback(async () => {
    if (!isEmployee) return;

    setLoading(true);
    setError(null);

    try {
      const header = await getAuthHeader();
      if (!header) {
        throw new Error('Session expirée, veuillez vous reconnecter');
      }

      // ❌ ENDPOINT SUPPRIMÉ - /bi/metrics/ n'existe plus
      // Pour les employés, utiliser des données mockées ou un endpoint spécifique
      throw new Error('Endpoint BI non disponible pour les employés');

      console.log('EmployeeContext - Métriques récupérées:', response.data);
      setMetrics(response.data);
      return response.data;
    } catch (err) {
      console.error('Erreur lors de la récupération des métriques employé:', err);
      setError(err.message || 'Erreur lors de la récupération des métriques employé');
      showErrorToast('Impossible de charger les métriques du tableau de bord');
      return null;
    } finally {
      setLoading(false);
    }
  }, [isEmployee, getAuthHeader]);

  // Charger les équipes si elles ne sont pas déjà chargées
  useEffect(() => {
    if (isEmployee && (!teams || teams.length === 0)) {
      fetchTeams();
    }
  }, [isEmployee, teams, fetchTeams]);

  // Valeur du contexte
  const value = {
    metrics,
    teams,
    loading,
    error,
    fetchEmployeeMetrics,
    fetchTeams,
    isEmployee
  };

  return (
    <EmployeeContext.Provider value={value}>
      {children}
    </EmployeeContext.Provider>
  );
};

export default EmployeeContext;
