import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useAuth } from './AuthContext';
import { useTeam } from './TeamContext';
import { useTeamTask } from './TeamTaskContext';
import { useEvent } from './EventContext';
import { usePersonalTask } from './PersonalTaskContext';
import { usePersonalEvent } from './PersonalEventContext';
import axios from 'axios';
import { API_URL } from '@/config/constants';
import { showErrorToast } from '@/utils/toastUtils';

// Créer le contexte
const EmployeeContext = createContext();

// Hook personnalisé pour utiliser le contexte
export const useEmployee = () => useContext(EmployeeContext);

// Fournisseur du contexte
export const EmployeeProvider = ({ children }) => {
  const { user, getAuthHeader } = useAuth();
  const { teams, fetchTeams } = useTeam();
  const { tasks: teamTasks, fetchTasks: fetchTeamTasks } = useTeamTask();
  const { events: teamEvents, fetchEvents: fetchTeamEvents } = useEvent();
  const { personalTasks, fetchPersonalTasks } = usePersonalTask();
  const { personalEvents, fetchPersonalEvents } = usePersonalEvent();

  // États
  const [metrics, setMetrics] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Vérifier si l'utilisateur est un employé
  const isEmployee = user && user.role === 'employee';

  // Calculer les métriques côté client à partir des données disponibles
  const calculateMetrics = useCallback(() => {
    if (!isEmployee) return null;

    try {
      // Métriques personnelles
      const personalMetrics = {
        task_count: personalTasks?.length || 0,
        event_count: personalEvents?.length || 0,
        pomodoro_session_count: 0, // À implémenter si nécessaire
        task_completion_rate: personalTasks?.length > 0
          ? (personalTasks.filter(t => t.status === 'achevee').length / personalTasks.length) * 100
          : 0,
        event_completion_rate: personalEvents?.length > 0
          ? (personalEvents.filter(e => e.status === 'completed').length / personalEvents.length) * 100
          : 0,
        pomodoro_focus_rate: 0, // À implémenter si nécessaire
        task_status_distribution: {
          a_faire: personalTasks?.filter(t => t.status === 'a_faire').length || 0,
          en_cours: personalTasks?.filter(t => t.status === 'en_cours').length || 0,
          achevee: personalTasks?.filter(t => t.status === 'achevee').length || 0,
          archived: personalTasks?.filter(t => t.status === 'archived').length || 0
        },
        event_status_distribution: {
          pending: personalEvents?.filter(e => e.status === 'pending').length || 0,
          completed: personalEvents?.filter(e => e.status === 'completed').length || 0,
          archived: personalEvents?.filter(e => e.status === 'archived').length || 0
        }
      };

      // Métriques par équipe
      const teamMetrics = teams?.map(team => {
        const teamTasksForTeam = teamTasks?.filter(t => t.team_id === team.id) || [];
        const teamEventsForTeam = teamEvents?.filter(e => e.team_id === team.id) || [];

        return {
          team_id: team.id,
          team_name: team.name,
          member_count: team.members?.length || 0,
          task_count: teamTasksForTeam.length,
          event_count: teamEventsForTeam.length,
          task_completion_rate: teamTasksForTeam.length > 0
            ? (teamTasksForTeam.filter(t => t.status === 'achevee').length / teamTasksForTeam.length) * 100
            : 0,
          event_completion_rate: teamEventsForTeam.length > 0
            ? (teamEventsForTeam.filter(e => e.status === 'completed').length / teamEventsForTeam.length) * 100
            : 0,
          task_status_distribution: {
            a_faire: teamTasksForTeam.filter(t => t.status === 'a_faire').length,
            en_cours: teamTasksForTeam.filter(t => t.status === 'en_cours').length,
            achevee: teamTasksForTeam.filter(t => t.status === 'achevee').length,
            archived: teamTasksForTeam.filter(t => t.status === 'archived').length
          },
          event_status_distribution: {
            pending: teamEventsForTeam.filter(e => e.status === 'pending').length,
            completed: teamEventsForTeam.filter(e => e.status === 'completed').length,
            archived: teamEventsForTeam.filter(e => e.status === 'archived').length
          }
        };
      }) || [];

      return {
        personal_metrics: personalMetrics,
        team_metrics: teamMetrics
      };
    } catch (err) {
      console.error('Erreur lors du calcul des métriques:', err);
      return null;
    }
  }, [isEmployee, personalTasks, personalEvents, teamTasks, teamEvents, teams]);

  // Récupérer les métriques pour l'employé
  const fetchEmployeeMetrics = useCallback(async () => {
    if (!isEmployee) return;

    setLoading(true);
    setError(null);

    try {
      // Récupérer toutes les données nécessaires
      await Promise.all([
        fetchTeams(),
        fetchTeamTasks(),
        fetchTeamEvents(),
        fetchPersonalTasks(),
        fetchPersonalEvents()
      ]);

      // Calculer les métriques à partir des données récupérées
      const calculatedMetrics = calculateMetrics();
      setMetrics(calculatedMetrics);

      console.log('EmployeeContext - Métriques calculées:', calculatedMetrics);
      return calculatedMetrics;
    } catch (err) {
      console.error('Erreur lors de la récupération des métriques employé:', err);
      setError(err.message || 'Erreur lors de la récupération des métriques employé');
      showErrorToast('Impossible de charger les métriques du tableau de bord');
      return null;
    } finally {
      setLoading(false);
    }
  }, [isEmployee, fetchTeams, fetchTeamTasks, fetchTeamEvents, fetchPersonalTasks, fetchPersonalEvents, calculateMetrics]);

  // Charger les équipes si elles ne sont pas déjà chargées
  useEffect(() => {
    if (isEmployee && (!teams || teams.length === 0)) {
      fetchTeams();
    }
  }, [isEmployee, teams, fetchTeams]);

  // Recalculer les métriques quand les données changent
  useEffect(() => {
    if (isEmployee && (personalTasks || personalEvents || teamTasks || teamEvents || teams)) {
      const calculatedMetrics = calculateMetrics();
      if (calculatedMetrics) {
        setMetrics(calculatedMetrics);
        console.log('EmployeeContext - Métriques recalculées automatiquement:', calculatedMetrics);
      }
    }
  }, [isEmployee, personalTasks, personalEvents, teamTasks, teamEvents, teams, calculateMetrics]);

  // Valeur du contexte
  const value = {
    metrics,
    teams,
    loading,
    error,
    fetchEmployeeMetrics,
    fetchTeams,
    isEmployee,
    // Exposer les données brutes pour un accès direct si nécessaire
    teamTasks,
    teamEvents,
    personalTasks,
    personalEvents
  };

  return (
    <EmployeeContext.Provider value={value}>
      {children}
    </EmployeeContext.Provider>
  );
};

export default EmployeeContext;
