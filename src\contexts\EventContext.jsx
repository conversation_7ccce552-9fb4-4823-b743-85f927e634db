import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useAuth } from './AuthContext';
import { useTeam } from './TeamContext';
import eventService from '@/services/eventService';
import { toast } from 'react-toastify';

// Créer le contexte
const EventContext = createContext();

// Hook personnalisé pour utiliser le contexte
export const useEvent = () => {
  const context = useContext(EventContext);
  if (!context) {
    console.warn('useEvent must be used within an EventProvider');
    return {
      events: [],
      loading: false,
      error: null,
      filters: {},
      fetchEvents: () => Promise.resolve([]),
      getEventById: () => Promise.resolve(null),
      createEvent: () => Promise.resolve(null),
      updateEvent: () => Promise.resolve(null),
      updateEventStatus: () => Promise.resolve(null),
      deleteEvent: () => Promise.resolve(),
      archiveEvent: () => Promise.resolve(null),
      unarchiveEvent: () => Promise.resolve(null),
      updateFilters: () => { },
      resetFilters: () => { }
    };
  }
  return context;
};

// Fournisseur du contexte
export const EventProvider = ({ children }) => {
  const { user } = useAuth();
  const teamContext = useTeam();
  const teams = teamContext?.teams || [];

  // États
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({});

  // Récupérer tous les événements
  const fetchEvents = useCallback(async (customFilters = {}) => {
    if (!user) {
      console.log('EventContext - Aucun utilisateur connecté, impossible de récupérer les événements');
      setEvents([]);
      setLoading(false);
      return [];
    }

    // Si l'utilisateur est super_admin, ne pas essayer de récupérer les événements
    if (user.role === 'super_admin') {
      console.log('EventContext - Super admin user detected, no access to events');
      setEvents([]);
      setLoading(false);
      return [];
    }

    setLoading(true);
    setError(null);

    try {
      console.log('EventContext - Récupération des événements avec les filtres:', { ...filters, ...customFilters });
      console.log('EventContext - User role:', user?.role);

      const eventsData = await eventService.getEvents({ ...filters, ...customFilters });
      console.log('EventContext - Événements récupérés:', eventsData?.length || 0, 'événements');
      console.log('EventContext - Détails des événements récupérés:', eventsData);

      if (!eventsData || !Array.isArray(eventsData)) {
        console.warn('EventContext - Données d\'événements invalides:', eventsData);
        setEvents([]);
        setLoading(false);
        return [];
      }

      // Ajouter les propriétés de permission à chaque événement
      const eventsWithPermissions = eventsData.map(event => {
        if (!event || !event.id) {
          console.warn('EventContext - Événement invalide:', event);
          return null;
        }

        try {
          console.log('EventContext - Checking permissions for event:', event.id, 'title:', event.title);
          const permissions = eventService.checkEventPermissions(user, event);

          // S'assurer que l'événement a une couleur valide
          let eventColor = event.color;
          if (!eventColor || typeof eventColor !== 'string' || !eventColor.startsWith('#')) {
            // Assigner une couleur par défaut basée sur le statut
            if (event.status === 'completed') {
              eventColor = '#BDEBC8'; // Vert pastel
            } else if (event.status === 'archived') {
              eventColor = '#F3F4F6'; // Gris très clair
            } else {
              // Utiliser une des couleurs de la palette
              const defaultColors = [
                '#CDB4DB', // Violet pastel
                '#FFC8DD', // Rose clair pastel
                '#FFAFCC', // Rose vif pastel
                '#BDE0FE', // Bleu clair pastel
                '#A2D2FF'  // Bleu pastel
              ];
              // Utiliser l'ID de l'événement pour choisir une couleur de manière déterministe
              const colorIndex = event.id ? Math.abs(event.id.toString().split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)) % defaultColors.length : 0;
              eventColor = defaultColors[colorIndex];
            }
          }

          return {
            ...event,
            color: eventColor,
            can_manage: permissions.canManage,
            can_update_status: permissions.canUpdateStatus
          };
        } catch (err) {
          console.error('Erreur lors de la vérification des permissions pour l\'événement:', err);
          return {
            ...event,
            color: event.color || '#BDE0FE', // Bleu clair pastel par défaut
            can_manage: false,
            can_update_status: false
          };
        }
      }).filter(event => event !== null); // Filtrer les événements invalides

      setEvents(eventsWithPermissions);
      return eventsWithPermissions;
    } catch (err) {
      console.error('Erreur lors de la récupération des événements:', err);

      // Gérer les erreurs de permission
      if (err.status === 403 || (err.response && err.response.status === 403)) {
        console.log('EventContext - Erreur de permission, retour d\'un tableau vide');
        setEvents([]);

        // Ne pas définir d'erreur pour les super_admin, c'est un comportement attendu
        if (user.role === 'super_admin') {
          setLoading(false);
          return [];
        }
      }

      // Pour les autres erreurs, définir le message d'erreur
      setError(err.message || 'Erreur lors de la récupération des événements');

      // Retourner un tableau vide au lieu de propager l'erreur
      setEvents([]);
      return [];
    } finally {
      setLoading(false);
    }
  }, [user, filters]);

  // Récupérer un événement par son ID
  const getEventById = useCallback(async (eventId) => {
    if (!user || !eventId) return null;

    // Si l'utilisateur est super_admin, ne pas essayer de récupérer l'événement
    if (user.role === 'super_admin') {
      console.log('EventContext - Super admin user detected, no access to events');
      return null;
    }

    setLoading(true);
    setError(null);

    try {
      const eventData = await eventService.getEventById(eventId);
      return eventData;
    } catch (err) {
      console.error(`Erreur lors de la récupération de l'événement ${eventId}:`, err);
      // Ne pas définir d'erreur pour les super_admin, c'est un comportement attendu
      if (!(err.status === 403 && user.role === 'super_admin')) {
        setError(err.message || `Erreur lors de la récupération de l'événement ${eventId}`);
      }
      throw err;
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Créer un nouvel événement
  const createEvent = useCallback(async (eventData) => {
    if (!user) return;

    // Si l'utilisateur est super_admin, ne pas essayer de créer l'événement
    if (user.role === 'super_admin') {
      console.log('EventContext - Super admin user detected, no access to events');
      toast.error('Les super administrateurs ne peuvent pas créer d\'événements');
      return null;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await eventService.createEvent(eventData);
      console.log('Réponse traitée dans createEvent du contexte:', response);

      // Extraire l'événement de la réponse (qui peut être dans event ou directement l'objet)
      const newEvent = response.event || response;

      // Ajouter les propriétés de permission si elles ne sont pas présentes
      if (newEvent && !newEvent.hasOwnProperty('can_manage')) {
        const permissions = eventService.checkEventPermissions(user, newEvent);
        newEvent.can_manage = permissions.canManage;
        newEvent.can_update_status = permissions.canUpdateStatus;
      }

      setEvents(prevEvents => [...prevEvents, newEvent]);
      return newEvent;
    } catch (err) {
      console.error('Erreur lors de la création de l\'événement:', err);
      // Ne pas définir d'erreur pour les super_admin, c'est un comportement attendu
      if (!(err.status === 403 && user.role === 'super_admin')) {
        setError(err.message || 'Erreur lors de la création de l\'événement');
      }
      throw err;
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Mettre à jour un événement
  const updateEvent = useCallback(async (eventId, eventData) => {
    if (!user || !eventId) {
      console.log('EventContext - Utilisateur non connecté ou ID d\'événement manquant');
      return null;
    }

    // Si l'utilisateur est super_admin, ne pas essayer de mettre à jour l'événement
    if (user.role === 'super_admin') {
      console.log('EventContext - Super admin user detected, no access to events');
      toast.error('Les super administrateurs ne peuvent pas modifier d\'événements');
      return null;
    }

    setLoading(true);
    setError(null);

    try {
      console.log('EventContext - Updating event with ID:', eventId);
      console.log('EventContext - Update data being sent:', eventData);

      // Vérifier et valider la couleur
      if (eventData.color) {
        if (typeof eventData.color !== 'string' || !eventData.color.startsWith('#')) {
          console.warn('EventContext - Format de couleur invalide:', eventData.color);
          // Utiliser une couleur par défaut
          eventData.color = '#BDE0FE'; // Bleu clair pastel par défaut
        }

        // Mettre à jour la variable globale pour la cohérence
        if (typeof window.eventColors === 'undefined') {
          window.eventColors = {};
        }
        window.eventColors[eventId] = eventData.color;
        console.log(`EventContext - Couleur mise à jour dans la variable globale: ${eventData.color}`);
      }

      const updatedEvent = await eventService.updateEvent(eventId, eventData);
      console.log('EventContext - Received updated event:', updatedEvent);

      // Mettre à jour l'événement dans le state en conservant les propriétés importantes
      setEvents(prevEvents =>
        prevEvents.map(event => {
          if (event.id === eventId) {
            // Fusionner correctement les données
            const mergedEvent = {
              ...event,                // Garder toutes les propriétés existantes
              ...updatedEvent,         // Ajouter les propriétés mises à jour
              // S'assurer que ces propriétés sont correctement préservées
              start: updatedEvent.start || event.start,
              end: updatedEvent.end || event.end,
              // Recalculer les propriétés de date/heure pour le calendrier si nécessaire
              start_date: updatedEvent.start_date || event.start_date,
              end_date: updatedEvent.end_date || event.end_date,
              start_time: updatedEvent.start_time || event.start_time,
              end_time: updatedEvent.end_time || event.end_time,
              // Conserver la couleur mise à jour - PRIORITÉ À LA COULEUR ENVOYÉE
              color: eventData.color || updatedEvent.color || event.color
            };

            // S'assurer que la couleur est valide
            if (!mergedEvent.color || typeof mergedEvent.color !== 'string' || !mergedEvent.color.startsWith('#')) {
              // Utiliser une des couleurs de la palette
              const defaultColors = [
                '#CDB4DB', // Violet pastel
                '#FFC8DD', // Rose clair pastel
                '#FFAFCC', // Rose vif pastel
                '#BDE0FE', // Bleu clair pastel
                '#A2D2FF'  // Bleu pastel
              ];
              // Utiliser l'ID de l'événement pour choisir une couleur de manière déterministe
              const colorIndex = eventId ? Math.abs(eventId.toString().split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)) % defaultColors.length : 0;
              mergedEvent.color = defaultColors[colorIndex];
            }

            console.log('EventContext - Merged event:', mergedEvent);
            console.log('EventContext - Color being set:', mergedEvent.color);

            // Mettre à jour la variable globale pour la cohérence
            if (typeof window.eventColors === 'undefined') {
              window.eventColors = {};
            }
            window.eventColors[eventId] = mergedEvent.color;
            console.log(`EventContext - Updated global color for event ${eventId}:`, window.eventColors[eventId]);

            return mergedEvent;
          }
          return event;
        })
      );

      // Émettre un événement pour informer les autres composants de la mise à jour
      // Toujours émettre l'événement, même si la couleur n'a pas changé
      if (window.dispatchEvent) {
        // Utiliser la couleur finale de l'événement fusionné
        const finalColor = window.eventColors[eventId];
        window.dispatchEvent(new CustomEvent('eventColorUpdated', {
          detail: {
            eventId: eventId,
            color: finalColor,
            timestamp: Date.now(),
            source: 'EventContext'
          }
        }));
        console.log('EventContext - Événement eventColorUpdated émis avec la couleur:', finalColor);
      }

      // Rafraîchir les événements pour s'assurer que tout est à jour
      setTimeout(() => fetchEvents(), 500);

      return updatedEvent;
    } catch (err) {
      console.error(`Erreur lors de la mise à jour de l'événement ${eventId}:`, err);

      // Gérer les erreurs de permission
      if (err.status === 403 || (err.response && err.response.status === 403)) {
        if (user.role === 'super_admin') {
          console.log('EventContext - Erreur de permission pour super_admin');
          setLoading(false);
          return null;
        }
      }

      // Pour les autres erreurs, définir le message d'erreur
      setError(err.message || `Erreur lors de la mise à jour de l'événement ${eventId}`);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [user, fetchEvents]);

  // Mettre à jour le statut d'un événement
  const updateEventStatus = useCallback(async (eventId, status) => {
    if (!user || !eventId) return;

    // Si l'utilisateur est super_admin, ne pas essayer de mettre à jour le statut de l'événement
    if (user.role === 'super_admin') {
      console.log('EventContext - Super admin user detected, no access to events');
      toast.error('Les super administrateurs ne peuvent pas modifier le statut des événements');
      return null;
    }

    setLoading(true);
    setError(null);

    try {
      const updatedEvent = await eventService.updateEventStatus(eventId, status);
      setEvents(prevEvents =>
        prevEvents.map(event =>
          event.id === eventId ? { ...event, status } : event
        )
      );
      return updatedEvent;
    } catch (err) {
      console.error(`Erreur lors de la mise à jour du statut de l'événement ${eventId}:`, err);
      // Ne pas définir d'erreur pour les super_admin, c'est un comportement attendu
      if (!(err.status === 403 && user.role === 'super_admin')) {
        setError(err.message || `Erreur lors de la mise à jour du statut de l'événement ${eventId}`);
      }
      throw err;
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Supprimer un événement
  const deleteEvent = useCallback(async (eventId) => {
    if (!user || !eventId) return;

    // Si l'utilisateur est super_admin, ne pas essayer de supprimer l'événement
    if (user.role === 'super_admin') {
      console.log('EventContext - Super admin user detected, no access to events');
      toast.error('Les super administrateurs ne peuvent pas supprimer d\'événements');
      return null;
    }

    setLoading(true);
    setError(null);

    try {
      await eventService.deleteEvent(eventId);
      setEvents(prevEvents => prevEvents.filter(event => event.id !== eventId));
    } catch (err) {
      console.error(`Erreur lors de la suppression de l'événement ${eventId}:`, err);
      // Ne pas définir d'erreur pour les super_admin, c'est un comportement attendu
      if (!(err.status === 403 && user.role === 'super_admin')) {
        setError(err.message || `Erreur lors de la suppression de l'événement ${eventId}`);
      }
      throw err;
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Archiver un événement
  const archiveEvent = useCallback(async (eventId) => {
    if (!user || !eventId) return;

    // Si l'utilisateur est super_admin, ne pas essayer d'archiver l'événement
    if (user.role === 'super_admin') {
      console.log('EventContext - Super admin user detected, no access to events');
      toast.error('Les super administrateurs ne peuvent pas archiver d\'événements');
      return null;
    }

    setLoading(true);
    setError(null);

    try {
      const archivedEvent = await eventService.archiveEvent(eventId);
      setEvents(prevEvents =>
        prevEvents.map(event =>
          event.id === eventId ? { ...event, status: 'archived' } : event
        )
      );
      return archivedEvent;
    } catch (err) {
      console.error(`Erreur lors de l'archivage de l'événement ${eventId}:`, err);
      // Ne pas définir d'erreur pour les super_admin, c'est un comportement attendu
      if (!(err.status === 403 && user.role === 'super_admin')) {
        setError(err.message || `Erreur lors de l'archivage de l'événement ${eventId}`);
      }
      throw err;
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Désarchiver un événement
  const unarchiveEvent = useCallback(async (eventId) => {
    if (!user || !eventId) return;

    // Si l'utilisateur est super_admin, ne pas essayer de désarchiver l'événement
    if (user.role === 'super_admin') {
      console.log('EventContext - Super admin user detected, no access to events');
      toast.error('Les super administrateurs ne peuvent pas désarchiver d\'événements');
      return null;
    }

    setLoading(true);
    setError(null);

    try {
      const unarchivedEvent = await eventService.unarchiveEvent(eventId);
      setEvents(prevEvents =>
        prevEvents.map(event =>
          event.id === eventId ? { ...event, status: 'pending' } : event
        )
      );
      return unarchivedEvent;
    } catch (err) {
      console.error(`Erreur lors du désarchivage de l'événement ${eventId}:`, err);
      // Ne pas définir d'erreur pour les super_admin, c'est un comportement attendu
      if (!(err.status === 403 && user.role === 'super_admin')) {
        setError(err.message || `Erreur lors du désarchivage de l'événement ${eventId}`);
      }
      throw err;
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Mettre à jour les filtres
  const updateFilters = useCallback((newFilters) => {
    console.log('Mise à jour des filtres:', newFilters);
    setFilters(prevFilters => ({
      ...prevFilters,
      ...newFilters
    }));
  }, []);

  // Réinitialiser les filtres
  const resetFilters = useCallback(() => {
    console.log('Réinitialisation des filtres');
    setFilters({});
  }, []);

  // Désactivation du chargement automatique des événements au montage du composant
  useEffect(() => {
    console.log('EventContext - Component mounted, automatic event fetching disabled');
    // Réinitialiser les événements si l'utilisateur se déconnecte
    if (!user) {
      setEvents([]);
      setLoading(false);
    } else {
      // Ne pas charger automatiquement les événements
      // Ils seront chargés explicitement par les composants qui en ont besoin
      setLoading(false);
      console.log('EventContext - Chargement automatique des événements désactivé');
    }
  }, [user]);

  // Valeur du contexte
  const value = {
    events,
    loading,
    error,
    filters,
    fetchEvents,
    getEventById,
    createEvent,
    updateEvent,
    updateEventStatus,
    deleteEvent,
    archiveEvent,
    unarchiveEvent,
    updateFilters,
    resetFilters
  };

  return (
    <EventContext.Provider value={value}>
      {children}
    </EventContext.Provider>
  );
};

export default EventContext;