import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useAuth } from './AuthContext';
import personalEventService from '@/services/personalEventService';
import { toast } from 'react-toastify';

// Créer le contexte
const PersonalEventContext = createContext();

// Hook personnalisé pour utiliser le contexte
export const usePersonalEvent = () => useContext(PersonalEventContext);

// Fournisseur du contexte
export const PersonalEventProvider = ({ children }) => {
  const { user } = useAuth();

  // États
  const [personalEvents, setPersonalEvents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({});

  // Récupérer tous les événements personnels
  const fetchPersonalEvents = useCallback(async (customFilters = {}) => {
    if (!user) return [];

    setLoading(true);
    setError(null);

    try {
      // IMPORTANT: Inclure les événements archivés dans la requête
      const filtersWithArchived = {
        ...filters,
        ...customFilters,
        include_archived: true  // Forcer l'inclusion des événements archivés
      };

      console.log('Récupération des événements personnels avec les filtres (incluant archivés):', filtersWithArchived);
      const eventsData = await personalEventService.getPersonalEvents(filtersWithArchived);
      console.log('Événements personnels récupérés:', eventsData);

      setPersonalEvents(eventsData);
      return eventsData;
    } catch (err) {
      console.error('Erreur lors de la récupération des événements personnels:', err);
      setError(err.message || 'Erreur lors de la récupération des événements personnels');
      toast.error('Impossible de charger vos événements personnels');
      return [];
    } finally {
      setLoading(false);
    }
  }, [user, filters]);

  // Récupérer un événement personnel par son ID
  const getPersonalEventById = useCallback(async (eventId) => {
    if (!user || !eventId) return null;

    setLoading(true);
    setError(null);

    try {
      const eventData = await personalEventService.getPersonalEvent(eventId);
      return eventData;
    } catch (err) {
      console.error(`Erreur lors de la récupération de l'événement personnel ${eventId}:`, err);
      setError(err.message || `Erreur lors de la récupération de l'événement personnel ${eventId}`);
      toast.error(`Impossible de charger les détails de l'événement`);
      return null;
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Créer un nouvel événement personnel
  const createPersonalEvent = useCallback(async (eventData) => {
    if (!user) {
      toast.error('Vous devez être connecté pour créer un événement personnel');
      return null;
    }

    setLoading(true);
    setError(null);

    try {
      const newEvent = await personalEventService.createPersonalEvent(eventData);
      console.log('Nouvel événement personnel créé:', newEvent);

      // Mettre à jour la liste des événements
      setPersonalEvents(prevEvents => [newEvent, ...prevEvents]);

      toast.success('Événement personnel créé avec succès');
      return newEvent;
    } catch (err) {
      console.error('Erreur lors de la création de l\'événement personnel:', err);

      // Gérer les erreurs de validation
      if (err.errors) {
        const errorMessages = Object.values(err.errors).join(', ');
        setError(errorMessages);
        toast.error(errorMessages);
      } else {
        setError(err.message || 'Erreur lors de la création de l\'événement personnel');
        toast.error('Impossible de créer l\'événement personnel');
      }

      return null;
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Mettre à jour un événement personnel
  const updatePersonalEvent = useCallback(async (eventId, eventData) => {
    if (!user || !eventId) {
      toast.error('Informations manquantes pour la mise à jour');
      return null;
    }

    setLoading(true);
    setError(null);

    try {
      const updatedEvent = await personalEventService.updatePersonalEvent(eventId, eventData);
      console.log('Événement personnel mis à jour:', updatedEvent);

      // Mettre à jour la liste des événements
      setPersonalEvents(prevEvents =>
        prevEvents.map(event => event.id === eventId ? updatedEvent : event)
      );

      // Déclencher un événement pour informer les autres composants de la mise à jour de couleur
      if (updatedEvent.color && window.dispatchEvent) {
        try {
          setTimeout(() => {
            window.dispatchEvent(new CustomEvent('eventColorUpdated', {
              detail: {
                eventId: eventId,
                color: updatedEvent.color,
                timestamp: Date.now(),
                source: 'PersonalEventContext'
              }
            }));
            console.log(`PersonalEventContext - Événement eventColorUpdated émis pour ${eventId} avec la couleur ${updatedEvent.color}`);
          }, 50);
        } catch (error) {
          console.error('PersonalEventContext - Erreur lors du déclenchement de l\'événement:', error);
        }
      }

      toast.success('Événement personnel mis à jour avec succès');
      return updatedEvent;
    } catch (err) {
      console.error(`Erreur lors de la mise à jour de l'événement personnel ${eventId}:`, err);

      // Gérer les erreurs de validation
      if (err.errors) {
        const errorMessages = Object.values(err.errors).join(', ');
        setError(errorMessages);
        toast.error(errorMessages);
      } else {
        setError(err.message || `Erreur lors de la mise à jour de l'événement personnel`);
        toast.error('Impossible de mettre à jour l\'événement personnel');
      }

      return null;
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Mettre à jour le statut d'un événement personnel
  const updatePersonalEventStatus = useCallback(async (eventId, status) => {
    if (!user || !eventId || !status) {
      toast.error('Informations manquantes pour la mise à jour du statut');
      return null;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await personalEventService.updatePersonalEventStatus(eventId, status);
      console.log('✅ PersonalEventContext - Réponse de mise à jour du statut:', response);

      // L'API retourne seulement {message: "...", status: "..."}
      // Nous devons mettre à jour manuellement l'événement local
      setPersonalEvents(prevEvents => {
        const newEvents = prevEvents.map(event => {
          if (event.id === eventId) {
            const updatedEvent = { ...event, status: status };
            console.log('🔄 PersonalEventContext - Mise à jour de l\'événement:', {
              avant: { id: event.id, status: event.status },
              après: { id: updatedEvent.id, status: updatedEvent.status }
            });
            return updatedEvent;
          }
          return event;
        });
        console.log('📋 PersonalEventContext - Nouvelle liste d\'événements:', newEvents.map(e => ({
          id: e.id,
          title: e.title,
          status: e.status
        })));
        return newEvents;
      });

      toast.success(`Statut de l'événement mis à jour: ${status}`);
      return { success: true, status };
    } catch (err) {
      console.error(`Erreur lors de la mise à jour du statut de l'événement personnel ${eventId}:`, err);
      setError(err.message || `Erreur lors de la mise à jour du statut de l'événement personnel`);
      toast.error('Impossible de mettre à jour le statut de l\'événement');
      return null;
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Supprimer un événement personnel
  const deletePersonalEvent = useCallback(async (eventId) => {
    if (!user || !eventId) {
      toast.error('Informations manquantes pour la suppression');
      return false;
    }

    setLoading(true);
    setError(null);

    try {
      await personalEventService.deletePersonalEvent(eventId);
      console.log('Événement personnel supprimé:', eventId);

      // Mettre à jour la liste des événements
      setPersonalEvents(prevEvents => prevEvents.filter(event => event.id !== eventId));

      // Ne pas afficher de toast ici car c'est géré par la page
      return true;
    } catch (err) {
      console.error(`Erreur lors de la suppression de l'événement personnel ${eventId}:`, err);
      setError(err.message || `Erreur lors de la suppression de l'événement personnel`);
      toast.error('Impossible de supprimer l\'événement personnel');
      return false;
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Mettre à jour les filtres
  const updateFilters = useCallback((newFilters) => {
    console.log('Mise à jour des filtres:', newFilters);
    setFilters(prevFilters => ({
      ...prevFilters,
      ...newFilters
    }));
  }, []);

  // Réinitialiser les filtres
  const resetFilters = useCallback(() => {
    console.log('Réinitialisation des filtres');
    setFilters({});
  }, []);

  // Désactivation du chargement automatique des événements au montage du composant
  // Les événements seront chargés uniquement lorsque l'utilisateur accède à une page spécifique
  useEffect(() => {
    // Réinitialiser les événements si l'utilisateur se déconnecte
    if (!user) {
      setPersonalEvents([]);
      setLoading(false);
    } else {
      // Ne pas charger automatiquement les événements
      // Ils seront chargés explicitement par les composants qui en ont besoin
      setLoading(false);
      console.log('Chargement automatique des événements personnels désactivé');
    }
  }, [user]);

  // Archiver un événement personnel
  const archivePersonalEvent = useCallback(async (eventId) => {
    if (!user || !eventId) {
      toast.error('Informations manquantes pour l\'archivage');
      return null;
    }

    setLoading(true);
    setError(null);

    try {
      const archivedEvent = await personalEventService.archivePersonalEvent(eventId);
      console.log('Événement personnel archivé:', archivedEvent);

      // Forcer un rafraîchissement complet des données depuis le backend
      console.log('Rafraîchissement des événements après archivage...');
      await fetchPersonalEvents();

      // Ne pas afficher de toast ici car c'est géré par la page
      return archivedEvent;
    } catch (err) {
      console.error(`Erreur lors de l'archivage de l'événement personnel ${eventId}:`, err);
      setError(err.message || `Erreur lors de l'archivage de l'événement personnel`);
      toast.error('Impossible d\'archiver l\'événement personnel');
      return null;
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Désarchiver un événement personnel
  const unarchivePersonalEvent = useCallback(async (eventId) => {
    if (!user || !eventId) {
      toast.error('Informations manquantes pour le désarchivage');
      return null;
    }

    setLoading(true);
    setError(null);

    try {
      const unarchivedEvent = await personalEventService.unarchivePersonalEvent(eventId);
      console.log('Événement personnel désarchivé:', unarchivedEvent);

      // Forcer un rafraîchissement complet des données depuis le backend
      console.log('Rafraîchissement des événements après désarchivage...');
      await fetchPersonalEvents();

      // Ne pas afficher de toast ici car c'est géré par la page
      return unarchivedEvent;
    } catch (err) {
      console.error(`Erreur lors du désarchivage de l'événement personnel ${eventId}:`, err);
      setError(err.message || `Erreur lors du désarchivage de l'événement personnel`);
      toast.error('Impossible de désarchiver l\'événement personnel');
      return null;
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Récupérer les événements archivés spécifiquement
  const fetchArchivedPersonalEvents = useCallback(async () => {
    if (!user) {
      console.log('PersonalEventContext - No user, skipping archived events fetch');
      return [];
    }

    setLoading(true);
    setError(null);

    try {
      console.log('PersonalEventContext - Fetching archived personal events...');
      const archivedEvents = await personalEventService.getArchivedPersonalEvents();
      console.log('PersonalEventContext - Archived personal events fetched:', archivedEvents);
      return archivedEvents || [];
    } catch (err) {
      console.error('PersonalEventContext - Error fetching archived personal events:', err);
      setError(err.message || 'Erreur lors de la récupération des événements personnels archivés');
      toast.error('Impossible de charger vos événements personnels archivés');
      return [];
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Valeur du contexte
  const value = {
    personalEvents,
    loading,
    error,
    filters,
    fetchPersonalEvents,
    fetchArchivedPersonalEvents,
    getPersonalEventById,
    createPersonalEvent,
    updatePersonalEvent,
    updatePersonalEventStatus,
    deletePersonalEvent,
    archivePersonalEvent,
    unarchivePersonalEvent,
    updateFilters,
    resetFilters
  };

  return (
    <PersonalEventContext.Provider value={value}>
      {children}
    </PersonalEventContext.Provider>
  );
};

export default PersonalEventContext;