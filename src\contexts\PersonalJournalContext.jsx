import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useAuth } from './AuthContext';
import personalJournalService from '@/services/personalJournalService';
import { toast } from 'react-toastify';

// Créer le contexte
const PersonalJournalContext = createContext();

// Hook personnalisé pour utiliser le contexte
export const usePersonalJournal = () => useContext(PersonalJournalContext);

// Fournisseur du contexte
export const PersonalJournalProvider = ({ children }) => {
  const { user } = useAuth();

  // États
  const [personalJournals, setPersonalJournals] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({});

  // Récupérer tous les journaux personnels
  const fetchPersonalJournals = useCallback(async (customFilters = {}) => {
    if (!user) return [];

    setLoading(true);
    setError(null);

    try {
      console.log('PersonalJournalContext - Récupération des journaux personnels avec les filtres:', { ...filters, ...customFilters });
      const journalsData = await personalJournalService.getPersonalJournals({ ...filters, ...customFilters });
      console.log('PersonalJournalContext - Journaux personnels récupérés:', journalsData);

      setPersonalJournals(journalsData);
      return journalsData;
    } catch (err) {
      console.error('Erreur lors de la récupération des journaux personnels:', err);
      setError(err.message || 'Erreur lors de la récupération des journaux personnels');
      toast.error('Impossible de charger vos journaux personnels');
      return [];
    } finally {
      setLoading(false);
    }
  }, [user, filters]);

  // Récupérer un journal personnel par son ID
  const getPersonalJournalById = useCallback(async (journalId) => {
    if (!user || !journalId) return null;

    setLoading(true);
    setError(null);

    try {
      const journalData = await personalJournalService.getPersonalJournal(journalId);
      return journalData;
    } catch (err) {
      console.error(`Erreur lors de la récupération du journal personnel ${journalId}:`, err);
      setError(err.message || `Erreur lors de la récupération du journal personnel ${journalId}`);
      toast.error(`Impossible de charger les détails du journal`);
      return null;
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Créer un nouveau journal personnel
  const createPersonalJournal = useCallback(async (journalData) => {
    if (!user) {
      const errorMsg = 'Vous devez être connecté pour créer un journal personnel';
      toast.error(errorMsg);
      return Promise.reject({ message: errorMsg });
    }

    // Vérifier que l'utilisateur a le rôle approprié
    if (!['client', 'employee', 'admin', 'super_admin'].includes(user.role)) {
      const errorMsg = `Votre rôle (${user.role}) ne vous permet pas de créer un journal personnel`;
      toast.error(errorMsg);
      return Promise.reject({ message: errorMsg });
    }

    setLoading(true);
    setError(null);

    try {
      console.log('PersonalJournalContext - Tentative de création de journal personnel avec les données:', journalData);
      console.log('PersonalJournalContext - Utilisateur actuel:', user);

      const newJournal = await personalJournalService.createPersonalJournal(journalData);
      console.log('Nouveau journal personnel créé:', newJournal);

      // Mettre à jour la liste des journaux
      setPersonalJournals(prevJournals => [newJournal, ...prevJournals]);

      toast.success('Journal personnel créé avec succès');
      return newJournal;
    } catch (err) {
      console.error('Erreur lors de la création du journal personnel:', err);

      // Gérer les erreurs de validation
      if (err.errors) {
        const errorMessages = Object.values(err.errors).join(', ');
        setError(errorMessages);
        toast.error(errorMessages);
      } else if (err.message) {
        setError(err.message);
        toast.error(err.message);
      } else if (err.response?.data?.detail) {
        // Erreur spécifique de l'API Django
        const errorMsg = err.response.data.detail;
        setError(errorMsg);
        toast.error(errorMsg);
      } else {
        const errorMsg = 'Erreur lors de la création du journal personnel';
        setError(errorMsg);
        toast.error(errorMsg);
      }

      return Promise.reject(err);
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Mettre à jour un journal personnel
  const updatePersonalJournal = useCallback(async (journalId, journalData) => {
    if (!user || !journalId) {
      toast.error('Informations manquantes pour la mise à jour');
      return null;
    }

    setLoading(true);
    setError(null);

    try {
      const updatedJournal = await personalJournalService.updatePersonalJournal(journalId, journalData);
      console.log('Journal personnel mis à jour:', updatedJournal);

      // Mettre à jour la liste des journaux
      setPersonalJournals(prevJournals =>
        prevJournals.map(journal => journal.id === journalId ? updatedJournal : journal)
      );

      toast.success('Journal personnel mis à jour avec succès');
      return updatedJournal;
    } catch (err) {
      console.error(`Erreur lors de la mise à jour du journal personnel ${journalId}:`, err);

      // Gérer les erreurs de validation
      if (err.errors) {
        const errorMessages = Object.values(err.errors).join(', ');
        setError(errorMessages);
        toast.error(errorMessages);
      } else {
        setError(err.message || `Erreur lors de la mise à jour du journal personnel`);
        toast.error('Impossible de mettre à jour le journal personnel');
      }

      return null;
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Supprimer un journal personnel
  const deletePersonalJournal = useCallback(async (journalId) => {
    if (!user || !journalId) {
      toast.error('Informations manquantes pour la suppression');
      return false;
    }

    setLoading(true);
    setError(null);

    try {
      await personalJournalService.deletePersonalJournal(journalId);
      console.log('Journal personnel supprimé:', journalId);

      // Mettre à jour la liste des journaux
      setPersonalJournals(prevJournals => prevJournals.filter(journal => journal.id !== journalId));

      toast.success('Journal personnel supprimé avec succès');
      return true;
    } catch (err) {
      console.error(`Erreur lors de la suppression du journal personnel ${journalId}:`, err);
      setError(err.message || `Erreur lors de la suppression du journal personnel`);
      toast.error('Impossible de supprimer le journal personnel');
      return false;
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Mettre à jour les filtres
  const updateFilters = useCallback((newFilters) => {
    console.log('Mise à jour des filtres:', newFilters);
    setFilters(prevFilters => ({
      ...prevFilters,
      ...newFilters
    }));
  }, []);

  // Réinitialiser les filtres
  const resetFilters = useCallback(() => {
    console.log('Réinitialisation des filtres');
    setFilters({});
  }, []);

  // Archiver un journal personnel
  const archivePersonalJournal = useCallback(async (journalId) => {
    if (!user || !journalId) {
      toast.error('Informations manquantes pour l\'archivage');
      return null;
    }

    setLoading(true);
    setError(null);

    try {
      const archivedJournal = await personalJournalService.archivePersonalJournal(journalId);
      console.log('Journal personnel archivé:', archivedJournal);

      // Mettre à jour la liste des journaux
      setPersonalJournals(prevJournals =>
        prevJournals.map(journal => journal.id === journalId ? { ...journal, is_archived: true } : journal)
      );

      toast.success('Journal personnel archivé avec succès');
      return archivedJournal;
    } catch (err) {
      console.error(`Erreur lors de l'archivage du journal personnel ${journalId}:`, err);
      setError(err.message || `Erreur lors de l'archivage du journal personnel`);
      toast.error('Impossible d\'archiver le journal personnel');
      return null;
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Désarchiver un journal personnel
  const unarchivePersonalJournal = useCallback(async (journalId) => {
    if (!user || !journalId) {
      toast.error('Informations manquantes pour le désarchivage');
      return null;
    }

    setLoading(true);
    setError(null);

    try {
      const unarchivedJournal = await personalJournalService.unarchivePersonalJournal(journalId);
      console.log('Journal personnel désarchivé:', unarchivedJournal);

      // Mettre à jour la liste des journaux
      setPersonalJournals(prevJournals =>
        prevJournals.map(journal => journal.id === journalId ? { ...journal, is_archived: false } : journal)
      );

      toast.success('Journal personnel désarchivé avec succès');
      return unarchivedJournal;
    } catch (err) {
      console.error(`Erreur lors du désarchivage du journal personnel ${journalId}:`, err);
      setError(err.message || `Erreur lors du désarchivage du journal personnel`);
      toast.error('Impossible de désarchiver le journal personnel');
      return null;
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Désactivation du chargement automatique des journaux au montage du composant
  // Les journaux seront chargés uniquement lorsque l'utilisateur accède à une page spécifique
  useEffect(() => {
    // Réinitialiser les journaux si l'utilisateur se déconnecte
    if (!user) {
      setPersonalJournals([]);
      setLoading(false);
    } else {
      // Ne pas charger automatiquement les journaux
      // Ils seront chargés explicitement par les composants qui en ont besoin
      setLoading(false);
      console.log('Chargement automatique des journaux personnels désactivé');
    }
  }, [user]);

  // Valeur du contexte
  const value = {
    personalJournals,
    loading,
    error,
    filters,
    fetchPersonalJournals,
    getPersonalJournalById,
    createPersonalJournal,
    updatePersonalJournal,
    deletePersonalJournal,
    archivePersonalJournal,
    unarchivePersonalJournal,
    updateFilters,
    resetFilters
  };

  return (
    <PersonalJournalContext.Provider value={value}>
      {children}
    </PersonalJournalContext.Provider>
  );
};

export default PersonalJournalContext;
