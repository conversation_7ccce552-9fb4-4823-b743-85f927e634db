import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useAuth } from './AuthContext';
import personalNoteService from '@/services/personalNoteService';
import { toast } from 'react-toastify';

// Créer le contexte
const PersonalNoteContext = createContext();

// Hook personnalisé pour utiliser le contexte
export const usePersonalNote = () => useContext(PersonalNoteContext);

// Fournisseur du contexte
export const PersonalNoteProvider = ({ children }) => {
  const { user } = useAuth();

  // États
  const [personalNotes, setPersonalNotes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({});

  // Récupérer toutes les notes personnelles
  const fetchPersonalNotes = useCallback(async (customFilters = {}) => {
    if (!user) return [];

    setLoading(true);
    setError(null);

    try {
      console.log('PersonalNoteContext - Récupération des notes personnelles avec les filtres:', { ...filters, ...customFilters });
      const notesData = await personalNoteService.getPersonalNotes({ ...filters, ...customFilters });
      console.log('PersonalNoteContext - Notes personnelles récupérées:', notesData);

      setPersonalNotes(notesData);
      return notesData;
    } catch (err) {
      console.error('Erreur lors de la récupération des notes personnelles:', err);
      setError(err.message || 'Erreur lors de la récupération des notes personnelles');
      toast.error('Impossible de charger vos notes personnelles');
      return [];
    } finally {
      setLoading(false);
    }
  }, [user, filters]);

  // Récupérer une note personnelle par son ID
  const getPersonalNoteById = useCallback(async (noteId) => {
    if (!user || !noteId) return null;

    setLoading(true);
    setError(null);

    try {
      const noteData = await personalNoteService.getPersonalNote(noteId);
      return noteData;
    } catch (err) {
      console.error(`Erreur lors de la récupération de la note personnelle ${noteId}:`, err);
      setError(err.message || `Erreur lors de la récupération de la note personnelle ${noteId}`);
      toast.error(`Impossible de charger les détails de la note`);
      return null;
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Créer une nouvelle note personnelle
  const createPersonalNote = useCallback(async (noteData) => {
    if (!user) {
      toast.error('Vous devez être connecté pour créer une note personnelle');
      return null;
    }

    setLoading(true);
    setError(null);

    try {
      const newNote = await personalNoteService.createPersonalNote(noteData);
      console.log('Nouvelle note personnelle créée:', newNote);

      // Mettre à jour la liste des notes
      setPersonalNotes(prevNotes => [newNote, ...prevNotes]);

      toast.success('Note personnelle créée avec succès');
      return newNote;
    } catch (err) {
      console.error('Erreur lors de la création de la note personnelle:', err);

      // Gérer les erreurs de validation
      if (err.errors) {
        const errorMessages = Object.values(err.errors).join(', ');
        setError(errorMessages);
        toast.error(errorMessages);
      } else {
        setError(err.message || 'Erreur lors de la création de la note personnelle');
        toast.error('Impossible de créer la note personnelle');
      }

      return null;
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Mettre à jour une note personnelle
  const updatePersonalNote = useCallback(async (noteId, noteData) => {
    if (!user || !noteId) {
      toast.error('Informations manquantes pour la mise à jour');
      return null;
    }

    setLoading(true);
    setError(null);

    try {
      const updatedNote = await personalNoteService.updatePersonalNote(noteId, noteData);
      console.log('Note personnelle mise à jour:', updatedNote);

      // Mettre à jour la liste des notes
      setPersonalNotes(prevNotes =>
        prevNotes.map(note => note.id === noteId ? updatedNote : note)
      );

      toast.success('Note personnelle mise à jour avec succès');
      return updatedNote;
    } catch (err) {
      console.error(`Erreur lors de la mise à jour de la note personnelle ${noteId}:`, err);

      // Gérer les erreurs de validation
      if (err.errors) {
        const errorMessages = Object.values(err.errors).join(', ');
        setError(errorMessages);
        toast.error(errorMessages);
      } else {
        setError(err.message || `Erreur lors de la mise à jour de la note personnelle`);
        toast.error('Impossible de mettre à jour la note personnelle');
      }

      return null;
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Supprimer une note personnelle
  const deletePersonalNote = useCallback(async (noteId) => {
    if (!user || !noteId) {
      toast.error('Informations manquantes pour la suppression');
      return false;
    }

    setLoading(true);
    setError(null);

    try {
      await personalNoteService.deletePersonalNote(noteId);
      console.log('Note personnelle supprimée:', noteId);

      // Mettre à jour la liste des notes
      setPersonalNotes(prevNotes => prevNotes.filter(note => note.id !== noteId));

      toast.success('Note personnelle supprimée avec succès');
      return true;
    } catch (err) {
      console.error(`Erreur lors de la suppression de la note personnelle ${noteId}:`, err);
      setError(err.message || `Erreur lors de la suppression de la note personnelle`);
      toast.error('Impossible de supprimer la note personnelle');
      return false;
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Mettre à jour les filtres
  const updateFilters = useCallback((newFilters) => {
    console.log('Mise à jour des filtres:', newFilters);
    setFilters(prevFilters => ({
      ...prevFilters,
      ...newFilters
    }));
  }, []);

  // Réinitialiser les filtres
  const resetFilters = useCallback(() => {
    console.log('Réinitialisation des filtres');
    setFilters({});
  }, []);

  // Archiver une note personnelle
  const archivePersonalNote = useCallback(async (noteId) => {
    if (!user || !noteId) {
      toast.error('Informations manquantes pour l\'archivage');
      return null;
    }

    setLoading(true);
    setError(null);

    try {
      const archivedNote = await personalNoteService.archivePersonalNote(noteId);
      console.log('Note personnelle archivée:', archivedNote);

      // Mettre à jour la liste des notes
      setPersonalNotes(prevNotes =>
        prevNotes.map(note => note.id === noteId ? { ...note, is_archived: true } : note)
      );

      toast.success('Note personnelle archivée avec succès');
      return archivedNote;
    } catch (err) {
      console.error(`Erreur lors de l'archivage de la note personnelle ${noteId}:`, err);
      setError(err.message || `Erreur lors de l'archivage de la note personnelle`);
      toast.error('Impossible d\'archiver la note personnelle');
      return null;
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Désarchiver une note personnelle
  const unarchivePersonalNote = useCallback(async (noteId) => {
    if (!user || !noteId) {
      toast.error('Informations manquantes pour le désarchivage');
      return null;
    }

    setLoading(true);
    setError(null);

    try {
      const unarchivedNote = await personalNoteService.unarchivePersonalNote(noteId);
      console.log('Note personnelle désarchivée:', unarchivedNote);

      // Mettre à jour la liste des notes
      setPersonalNotes(prevNotes =>
        prevNotes.map(note => note.id === noteId ? { ...note, is_archived: false } : note)
      );

      toast.success('Note personnelle désarchivée avec succès');
      return unarchivedNote;
    } catch (err) {
      console.error(`Erreur lors du désarchivage de la note personnelle ${noteId}:`, err);
      setError(err.message || `Erreur lors du désarchivage de la note personnelle`);
      toast.error('Impossible de désarchiver la note personnelle');
      return null;
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Désactivation du chargement automatique des notes au montage du composant
  // Les notes seront chargées uniquement lorsque l'utilisateur accède à une page spécifique
  useEffect(() => {
    // Réinitialiser les notes si l'utilisateur se déconnecte
    if (!user) {
      setPersonalNotes([]);
      setLoading(false);
    } else {
      // Ne pas charger automatiquement les notes
      // Elles seront chargées explicitement par les composants qui en ont besoin
      setLoading(false);
      console.log('Chargement automatique des notes personnelles désactivé');
    }
  }, [user]);

  // Valeur du contexte
  const value = {
    personalNotes,
    loading,
    error,
    filters,
    fetchPersonalNotes,
    getPersonalNoteById,
    createPersonalNote,
    updatePersonalNote,
    deletePersonalNote,
    archivePersonalNote,
    unarchivePersonalNote,
    updateFilters,
    resetFilters
  };

  return (
    <PersonalNoteContext.Provider value={value}>
      {children}
    </PersonalNoteContext.Provider>
  );
};

export default PersonalNoteContext;
