import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useAuth } from './AuthContext';
import personalTaskService from '@/services/personalTaskService';
import { showSuccessToast, showErrorToast } from '@/utils/toastUtils';

// Créer le contexte
const PersonalTaskContext = createContext();

// Hook personnalisé pour utiliser le contexte
export const usePersonalTask = () => {
  const context = useContext(PersonalTaskContext);
  if (!context) {
    console.warn('usePersonalTask must be used within a PersonalTaskProvider');
    return {
      personalTasks: [],
      loading: false,
      error: null,
      filters: {},
      fetchPersonalTasks: () => Promise.resolve([]),
      getPersonalTaskById: () => Promise.resolve(null),
      createPersonalTask: () => Promise.resolve(null),
      updatePersonalTask: () => Promise.resolve(null),
      updatePersonalTaskStatus: () => Promise.resolve(null),
      archivePersonalTask: () => Promise.resolve(null),
      unarchivePersonalTask: () => Promise.resolve(null),
      deletePersonalTask: () => Promise.resolve(false),
      updateFilters: () => { },
      resetFilters: () => { }
    };
  }
  return context;
};

// Fournisseur du contexte
export const PersonalTaskProvider = ({ children }) => {
  const { user } = useAuth();

  // États
  const [personalTasks, setPersonalTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({});

  // Récupérer toutes les tâches personnelles
  const fetchPersonalTasks = useCallback(async (customFilters = {}) => {
    if (!user) return [];

    setLoading(true);
    setError(null);

    try {
      console.log('Récupération des tâches personnelles avec les filtres:', { ...filters, ...customFilters });
      const tasksData = await personalTaskService.getPersonalTasks({ ...filters, ...customFilters });
      console.log('Tâches personnelles récupérées:', tasksData);

      setPersonalTasks(tasksData);
      return tasksData;
    } catch (err) {
      console.error('Erreur lors de la récupération des tâches personnelles:', err);
      setError(err.message || 'Erreur lors de la récupération des tâches personnelles');
      showErrorToast('Impossible de charger vos tâches personnelles');
      return [];
    } finally {
      setLoading(false);
    }
  }, [user, filters]);

  // Récupérer une tâche personnelle par son ID
  const getPersonalTaskById = useCallback(async (taskId) => {
    if (!user || !taskId) return null;

    setLoading(true);
    setError(null);

    try {
      const taskData = await personalTaskService.getPersonalTask(taskId);
      return taskData;
    } catch (err) {
      console.error(`Erreur lors de la récupération de la tâche personnelle ${taskId}:`, err);
      setError(err.message || `Erreur lors de la récupération de la tâche personnelle ${taskId}`);
      showErrorToast(`Impossible de charger les détails de la tâche`);
      return null;
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Créer une nouvelle tâche personnelle
  const createPersonalTask = useCallback(async (taskData) => {
    if (!user) {
      showErrorToast('Vous devez être connecté pour créer une tâche personnelle');
      return null;
    }

    setLoading(true);
    setError(null);

    try {
      const newTask = await personalTaskService.createPersonalTask(taskData);
      console.log('Nouvelle tâche personnelle créée:', newTask);

      // Mettre à jour la liste des tâches
      setPersonalTasks(prevTasks => {
        console.log('Mise à jour de la liste des tâches. Avant:', prevTasks.length, 'Après:', prevTasks.length + 1);
        return [newTask, ...prevTasks];
      });

      showSuccessToast('Tâche personnelle créée avec succès');
      return newTask;
    } catch (err) {
      console.error('Erreur lors de la création de la tâche personnelle:', err);

      // Gérer les erreurs de validation
      if (err.errors) {
        const errorMessages = Object.values(err.errors).join(', ');
        setError(errorMessages);
        showErrorToast(`Erreur de validation: ${errorMessages}`);
      } else if (err.response?.data?.errors) {
        const errorMessages = Object.values(err.response.data.errors).join(', ');
        setError(errorMessages);
        showErrorToast(`Erreur de validation: ${errorMessages}`);
      } else if (err.response?.data?.message) {
        setError(err.response.data.message);
        showErrorToast(err.response.data.message);
      } else {
        const errorMessage = err.message || 'Erreur lors de la création de la tâche personnelle';
        setError(errorMessage);
        showErrorToast(errorMessage);
      }

      return null;
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Mettre à jour une tâche personnelle
  const updatePersonalTask = useCallback(async (taskId, taskData) => {
    if (!user || !taskId) {
      showErrorToast('Informations manquantes pour la mise à jour');
      return null;
    }

    setLoading(true);
    setError(null);

    try {
      console.log('PersonalTaskContext - updatePersonalTask - Appel API avec:', { taskId, taskData });
      const updatedTask = await personalTaskService.updatePersonalTask(taskId, taskData);
      console.log('PersonalTaskContext - updatePersonalTask - Réponse API complète:', updatedTask);
      console.log('PersonalTaskContext - updatePersonalTask - Type de updatedTask:', typeof updatedTask);
      console.log('PersonalTaskContext - updatePersonalTask - Clés de updatedTask:', Object.keys(updatedTask || {}));

      // Mettre à jour la liste des tâches immédiatement
      setPersonalTasks(prevTasks => {
        console.log('PersonalTaskContext - updatePersonalTask - Tâches avant mise à jour:', prevTasks);
        const newTasks = prevTasks.map(task => {
          if (task.id === taskId) {
            console.log(`PersonalTaskContext - updatePersonalTask - Remplacement tâche ${taskId}:`);
            console.log('  Ancienne tâche:', task);
            console.log('  Nouvelle tâche:', updatedTask);
            console.log('  Statut ancien:', task.status, 'Statut nouveau:', updatedTask.status);
            console.log('  Dates anciennes:', task.start_date, task.end_date);
            console.log('  Dates nouvelles:', updatedTask.start_date, updatedTask.end_date);
            return updatedTask;
          }
          return task;
        });
        console.log('PersonalTaskContext - updatePersonalTask - Tâches après mise à jour:', newTasks);
        return newTasks;
      });

      showSuccessToast('Tâche personnelle mise à jour avec succès');
      return updatedTask;
    } catch (err) {
      console.error(`Erreur lors de la mise à jour de la tâche personnelle ${taskId}:`, err);

      // Gérer les erreurs de validation
      if (err.errors) {
        const errorMessages = Object.values(err.errors).join(', ');
        setError(errorMessages);
        showErrorToast(errorMessages);
      } else {
        setError(err.message || `Erreur lors de la mise à jour de la tâche personnelle`);
        showErrorToast('Impossible de mettre à jour la tâche personnelle');
      }

      return null;
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Mettre à jour le statut d'une tâche personnelle
  const updatePersonalTaskStatus = useCallback(async (taskId, status) => {
    if (!user || !taskId || !status) {
      toast.error('Informations manquantes pour la mise à jour du statut');
      return null;
    }

    console.log(`PersonalTaskContext - Début mise à jour statut: ${taskId} -> ${status}`);
    setLoading(true);
    setError(null);

    try {
      const updatedTask = await personalTaskService.updatePersonalTaskStatus(taskId, status);
      console.log('PersonalTaskContext - Statut de la tâche personnelle mis à jour:', updatedTask);

      // Mettre à jour la liste des tâches
      setPersonalTasks(prevTasks => {
        console.log('PersonalTaskContext - Tâches avant mise à jour du statut:', prevTasks);
        const newTasks = prevTasks.map(task => {
          if (task.id === taskId) {
            console.log(`PersonalTaskContext - Mise à jour statut: ${task.status} -> ${updatedTask.status}`);
            return updatedTask;
          }
          return task;
        });
        console.log('PersonalTaskContext - Tâches après mise à jour du statut:', newTasks);
        return newTasks;
      });

      showSuccessToast('Statut mis à jour avec succès !');
      return updatedTask;
    } catch (err) {
      console.error(`Erreur lors de la mise à jour du statut de la tâche personnelle ${taskId}:`, err);
      setError(err.message || `Erreur lors de la mise à jour du statut de la tâche personnelle`);
      showErrorToast('Impossible de mettre à jour le statut de la tâche');
      return null;
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Archiver une tâche personnelle
  const archivePersonalTask = useCallback(async (taskId) => {
    if (!user || !taskId) {
      showErrorToast('Informations manquantes pour l\'archivage');
      return null;
    }

    setLoading(true);
    setError(null);

    try {
      const archivedTask = await personalTaskService.archivePersonalTask(taskId);
      console.log('Tâche personnelle archivée:', archivedTask);

      // Mettre à jour la liste des tâches
      setPersonalTasks(prevTasks =>
        prevTasks.map(task => task.id === taskId ? archivedTask : task)
      );

      showSuccessToast('Tâche personnelle archivée avec succès');
      return archivedTask;
    } catch (err) {
      console.error(`Erreur lors de l'archivage de la tâche personnelle ${taskId}:`, err);
      setError(err.message || `Erreur lors de l'archivage de la tâche personnelle`);
      showErrorToast('Impossible d\'archiver la tâche personnelle');
      return null;
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Désarchiver une tâche personnelle
  const unarchivePersonalTask = useCallback(async (taskId) => {
    if (!user || !taskId) {
      showErrorToast('Informations manquantes pour le désarchivage');
      return null;
    }

    setLoading(true);
    setError(null);

    try {
      const unarchivedTask = await personalTaskService.unarchivePersonalTask(taskId);
      console.log('Tâche personnelle désarchivée:', unarchivedTask);

      // Mettre à jour la liste des tâches
      setPersonalTasks(prevTasks =>
        prevTasks.map(task => task.id === taskId ? unarchivedTask : task)
      );

      showSuccessToast('Tâche personnelle désarchivée avec succès');
      return unarchivedTask;
    } catch (err) {
      console.error(`Erreur lors du désarchivage de la tâche personnelle ${taskId}:`, err);
      setError(err.message || `Erreur lors du désarchivage de la tâche personnelle`);
      showErrorToast('Impossible de désarchiver la tâche personnelle');
      return null;
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Supprimer une tâche personnelle
  const deletePersonalTask = useCallback(async (taskId) => {
    if (!user || !taskId) {
      showErrorToast('Informations manquantes pour la suppression');
      return false;
    }

    setLoading(true);
    setError(null);

    try {
      await personalTaskService.deletePersonalTask(taskId);
      console.log('Tâche personnelle supprimée:', taskId);

      // Mettre à jour la liste des tâches
      setPersonalTasks(prevTasks => prevTasks.filter(task => task.id !== taskId));

      showSuccessToast('Tâche personnelle supprimée avec succès');
      return true;
    } catch (err) {
      console.error(`Erreur lors de la suppression de la tâche personnelle ${taskId}:`, err);
      setError(err.message || `Erreur lors de la suppression de la tâche personnelle`);
      showErrorToast('Impossible de supprimer la tâche personnelle');
      return false;
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Mettre à jour les filtres
  const updateFilters = useCallback((newFilters) => {
    console.log('Mise à jour des filtres:', newFilters);
    setFilters(prevFilters => ({
      ...prevFilters,
      ...newFilters
    }));
  }, []);

  // Réinitialiser les filtres
  const resetFilters = useCallback(() => {
    console.log('Réinitialisation des filtres');
    setFilters({});
  }, []);

  // Désactivation du chargement automatique des tâches au montage du composant
  // Les tâches seront chargées uniquement lorsque l'utilisateur accède à une page spécifique
  useEffect(() => {
    // Réinitialiser les tâches si l'utilisateur se déconnecte
    if (!user) {
      setPersonalTasks([]);
      setLoading(false);
    } else {
      // Ne pas charger automatiquement les tâches
      // Elles seront chargées explicitement par les composants qui en ont besoin
      setLoading(false);
      console.log('Chargement automatique des tâches personnelles désactivé');
    }
  }, [user]);

  // Valeur du contexte
  const value = {
    personalTasks,
    loading,
    error,
    filters,
    fetchPersonalTasks,
    getPersonalTaskById,
    createPersonalTask,
    updatePersonalTask,
    updatePersonalTaskStatus,
    archivePersonalTask,
    unarchivePersonalTask,
    deletePersonalTask,
    updateFilters,
    resetFilters
  };

  return (
    <PersonalTaskContext.Provider value={value}>
      {children}
    </PersonalTaskContext.Provider>
  );
};

export default PersonalTaskContext;
