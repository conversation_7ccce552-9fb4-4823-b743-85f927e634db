import React, { createContext, useContext, useState, useCallback } from 'react';
import { useAuth } from './AuthContext';
import { toast } from 'react-toastify';
import biService from '@/services/biService';

// Créer le contexte
const SuperAdminContext = createContext();

// Hook personnalisé pour utiliser le contexte
export const useSuperAdmin = () => useContext(SuperAdminContext);

// Fournisseur du contexte
export const SuperAdminProvider = ({ children }) => {
  const { user, getAuthHeader } = useAuth();

  // États
  const [metrics, setMetrics] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Vérifier si l'utilisateur est un super admin
  const isSuperAdmin = user && user.role === 'super_admin';

  // Récupérer les métriques BI pour le super admin
  const fetchSuperAdminMetrics = useCallback(async () => {
    if (!isSuperAdmin) return;

    setLoading(true);
    setError(null);

    try {
      console.log('SuperAdminContext - Récupération des métriques...');
      // ✅ CORRECT - Utiliser le bon endpoint
      const response = await biService.getSuperAdminDashboard();

      if (response.success) {
        console.log('SuperAdminContext - Métriques récupérées avec succès:', response.data);
        setMetrics(response.data);
        return response.data;
      } else {
        throw new Error(response.error || 'Erreur lors de la récupération des métriques');
      }
    } catch (err) {
      console.error('Erreur lors de la récupération des métriques super admin:', err);
      setError(err.message || 'Erreur lors de la récupération des métriques super admin');

      // Utiliser les données mockées en cas d'erreur
      const mockResponse = await biService.getSuperAdminDashboard();
      if (mockResponse.data) {
        console.log('SuperAdminContext - Utilisation des données mockées');
        setMetrics(mockResponse.data);
        return mockResponse.data;
      } else {
        toast.error('Impossible de charger les métriques du tableau de bord');
        return null;
      }
    } finally {
      setLoading(false);
    }
  }, [isSuperAdmin]);

  // Valeur du contexte
  const value = {
    metrics,
    loading,
    error,
    fetchSuperAdminMetrics,
    isSuperAdmin
  };

  return (
    <SuperAdminContext.Provider value={value}>
      {children}
    </SuperAdminContext.Provider>
  );
};

export default SuperAdminContext;
