import { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { showSuccessToast, showErrorToast } from '@/utils/toastUtils';
import teamService from '@/services/teamService';

const TeamContext = createContext();

export const useTeam = () => {
    const context = useContext(TeamContext);
    if (!context) {
        throw new Error('useTeam must be used within a TeamProvider');
    }
    return context;
};

export const TeamProvider = ({ children }) => {
    const [teams, setTeams] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const fetchTeams = useCallback(async () => {
        console.log('TeamContext - Fetching teams...');
        setLoading(true);
        setError(null);
        try {
            // Récupérer l'utilisateur courant depuis le localStorage
            const userStr = localStorage.getItem('user');
            let currentUser = null;

            if (!userStr) {
                console.log('TeamContext - No user found in localStorage');
                console.log('TeamContext - Checking if token exists...');

                // Vérifier si un token existe mais que les données utilisateur sont manquantes
                const token = localStorage.getItem('authToken');
                if (token) {
                    console.log('TeamContext - Token exists but user data is missing, this might be a synchronization issue');
                    // Attendre un court instant pour permettre à d'autres composants de charger l'utilisateur
                    await new Promise(resolve => setTimeout(resolve, 500));

                    // Réessayer de récupérer l'utilisateur
                    const retryUserStr = localStorage.getItem('user');
                    if (retryUserStr) {
                        try {
                            currentUser = JSON.parse(retryUserStr);
                            console.log('TeamContext - User data retrieved on retry:', currentUser.role);
                        } catch (e) {
                            console.error('TeamContext - Error parsing user data on retry:', e);
                        }
                    } else {
                        console.log('TeamContext - User data still missing after retry');
                    }
                } else {
                    console.log('TeamContext - No authentication token found');
                }

                if (!currentUser) {
                    setTeams([]);
                    setLoading(false);
                    return;
                }
            } else {
                try {
                    currentUser = JSON.parse(userStr);
                    console.log('TeamContext - Current user role:', currentUser.role);
                } catch (e) {
                    console.error('TeamContext - Error parsing user data:', e);
                    setTeams([]);
                    setLoading(false);
                    return;
                }
            }

            // Vérifier si l'utilisateur est super_admin - ils n'ont pas accès aux équipes
            if (!currentUser || currentUser.role === 'super_admin') {
                console.log(`TeamContext - User role ${currentUser?.role} has no access to teams`);
                setTeams([]);
                setLoading(false);
                return;
            }

            const response = await teamService.getTeams();
            console.log('TeamContext - Raw API response:', response);

            // Si la réponse est vide ou null, retourner un tableau vide
            if (!response) {
                console.log('TeamContext - Empty response from API');
                setTeams([]);
                setLoading(false);
                return;
            }

            const teamsData = Array.isArray(response) ? response :
                Array.isArray(response?.teams) ? response.teams :
                    response ? [response] : [];

            console.log('TeamContext - Normalized teams data:', teamsData);

            setTeams(teamsData.map(team => {
                // Normaliser le format du responsable selon la structure du backend
                const responsableData = team.responsable && typeof team.responsable === 'object'
                    ? team.responsable
                    : { id: team.responsable, name: team.responsable_name };

                // Vérifier si l'utilisateur courant est le responsable de l'équipe
                const isResponsable = currentUser && String(currentUser.id) === String(responsableData.id || team.responsable);

                // Vérifier si l'utilisateur courant est membre de l'équipe
                console.log('TeamContext - Checking membership for user:', currentUser?.id);
                console.log('TeamContext - Team members structure:', team.members);

                let isMember = false;
                if (currentUser && team.members) {
                    if (Array.isArray(team.members)) {
                        console.log('TeamContext - Members is an array');
                        isMember = team.members.some(m => {
                            const memberId = m.id || m.user_id;
                            console.log(`TeamContext - Comparing member ID ${memberId} with user ID ${currentUser.id}`);
                            return String(memberId) === String(currentUser.id);
                        });
                    } else if (typeof team.members === 'object') {
                        console.log('TeamContext - Members is an object');
                        isMember = Object.keys(team.members).some(key => {
                            console.log(`TeamContext - Comparing member key ${key} with user ID ${currentUser.id}`);
                            return String(key) === String(currentUser.id);
                        });
                    }
                }

                // Si l'utilisateur est le responsable, il est aussi membre
                if (isResponsable) {
                    isMember = true;
                    console.log(`TeamContext - User ${currentUser?.id} is responsable, setting as member`);
                }

                console.log(`TeamContext - User ${currentUser?.id} is member of team ${team.id}: ${isMember}`);

                // Déterminer si l'utilisateur peut gérer l'équipe
                // Seuls les admins (pas super_admin) peuvent gérer les équipes dont ils sont responsables
                const canManage = currentUser &&
                    currentUser.role === 'admin' &&
                    (isResponsable || team.can_manage);

                return {
                    ...team,
                    // Assurer la cohérence avec le format du backend
                    id: team.id,
                    name: team.name,
                    description: team.description || '',
                    responsable: {
                        id: responsableData.id || team.responsable,
                        name: responsableData.name || team.responsable_name || 'Non spécifié'
                    },
                    // Ajouter le nom du responsable directement pour faciliter l'affichage
                    responsable_name: responsableData.name || team.responsable_name || 'Non spécifié',
                    created_at: team.created_at || null,
                    updated_at: team.updated_at || null,
                    members: Array.isArray(team.members) ? team.members.map(member => ({
                        id: member.id || member.user_id,
                        name: member.name,
                        email: member.email,
                        role: member.role || 'employee',
                        added_at: member.added_at || null
                    })) : Object.entries(team.members || {}).map(([id, info]) => ({
                        id: id,
                        name: info.name || info,
                        email: info.email,
                        role: info.role || 'employee',
                        added_at: info.added_at || null
                    })),
                    // Ajouter les propriétés de permission
                    can_manage: canManage || team.can_manage || false,
                    is_member: isMember || team.is_member || false,
                    is_responsable: isResponsable || team.is_responsable || false
                };
            }));
        } catch (err) {
            console.error('TeamContext - Error fetching teams:', err);

            // Si l'erreur est liée aux permissions, ne pas définir d'erreur
            if (err.status === 403 || (err.response && err.response.status === 403)) {
                console.log('TeamContext - Permission error, setting empty teams array');
                setTeams([]);
            } else {
                // Pour les autres erreurs, définir le message d'erreur
                setError(err.message || 'Une erreur est survenue');
                setTeams([]);
            }
        } finally {
            setLoading(false);
        }
    }, []);

    // Désactivation du chargement automatique des équipes au montage du composant
    useEffect(() => {
        console.log('TeamContext - Component mounted, automatic team fetching disabled');
        // Ne pas charger automatiquement les équipes
        // Elles seront chargées explicitement par les composants qui en ont besoin
        setLoading(false);
    }, []);

    const createTeam = useCallback(async (teamData) => {
        console.log('TeamContext - Creating team:', teamData);
        try {
            // Vérifier si l'utilisateur est super_admin - ils n'ont pas accès aux équipes
            const userStr = localStorage.getItem('user');
            let currentUser = null;
            if (userStr) {
                try {
                    currentUser = JSON.parse(userStr);
                    if (currentUser.role === 'super_admin') {
                        const errorMessage = 'Les super administrateurs ne peuvent pas gérer les équipes';
                        console.error('TeamContext - Super admin cannot create teams');
                        toast.error(errorMessage);
                        throw new Error(errorMessage);
                    }
                } catch (e) {
                    console.error('TeamContext - Error parsing user data:', e);
                }
            }

            const response = await teamService.createTeam(teamData);
            console.log('TeamContext - Team created:', response);
            await fetchTeams();
            showSuccessToast('Équipe créée avec succès');
            return response;
        } catch (err) {
            console.error('TeamContext - Error creating team:', err);
            const errorMessage = err.response?.data?.message ||
                err.response?.data ||
                err.message ||
                'Erreur lors de la création de l\'équipe';
            console.error('TeamContext - Error details:', {
                message: errorMessage,
                status: err.response?.status,
                data: err.response?.data
            });
            showErrorToast(errorMessage);
            throw err;
        }
    }, [fetchTeams]);

    const updateTeam = useCallback(async (teamId, teamData) => {
        console.log('TeamContext - Updating team:', { teamId, teamData });
        try {
            // Vérifier si l'utilisateur est super_admin - ils n'ont pas accès aux équipes
            const userStr = localStorage.getItem('user');
            let currentUser = null;
            if (userStr) {
                try {
                    currentUser = JSON.parse(userStr);
                    if (currentUser.role === 'super_admin') {
                        const errorMessage = 'Les super administrateurs ne peuvent pas gérer les équipes';
                        console.error('TeamContext - Super admin cannot update teams');
                        toast.error(errorMessage);
                        throw new Error(errorMessage);
                    }
                } catch (e) {
                    console.error('TeamContext - Error parsing user data:', e);
                }
            }

            const response = await teamService.updateTeam(teamId, teamData);
            console.log('TeamContext - Team updated:', response);
            await fetchTeams();
            // Ne pas afficher de toast ici pour éviter les doublons
            return response;
        } catch (err) {
            console.error('TeamContext - Error updating team:', err);
            toast.error(err.message || 'Erreur lors de la mise à jour de l\'équipe');
            throw err;
        }
    }, [fetchTeams]);

    const deleteTeam = useCallback(async (teamId) => {
        console.log('TeamContext - Deleting team:', teamId);
        try {
            // Vérifier si l'utilisateur est super_admin - ils n'ont pas accès aux équipes
            const userStr = localStorage.getItem('user');
            let currentUser = null;
            if (userStr) {
                try {
                    currentUser = JSON.parse(userStr);
                    if (currentUser.role === 'super_admin') {
                        const errorMessage = 'Les super administrateurs ne peuvent pas gérer les équipes';
                        console.error('TeamContext - Super admin cannot delete teams');
                        toast.error(errorMessage);
                        throw new Error(errorMessage);
                    }
                } catch (e) {
                    console.error('TeamContext - Error parsing user data:', e);
                }
            }

            const response = await teamService.deleteTeam(teamId);
            console.log('TeamContext - Team deleted:', response);
            await fetchTeams();
            // Ne pas afficher de toast ici pour éviter les doublons
            return response;
        } catch (err) {
            console.error('TeamContext - Error deleting team:', err);
            // Ne pas afficher de toast ici pour éviter les doublons
            throw err;
        }
    }, [fetchTeams]);

    const addMember = useCallback(async (teamId, memberData) => {
        console.log('TeamContext - Adding member:', { teamId, memberData });
        try {
            // Vérifier si l'utilisateur est super_admin - ils n'ont pas accès aux équipes
            const userStr = localStorage.getItem('user');
            let currentUser = null;
            if (userStr) {
                try {
                    currentUser = JSON.parse(userStr);
                    if (currentUser.role === 'super_admin') {
                        const errorMessage = 'Les super administrateurs ne peuvent pas gérer les équipes';
                        console.error('TeamContext - Super admin cannot add team members');
                        showErrorToast(errorMessage);
                        throw new Error(errorMessage);
                    }
                } catch (e) {
                    console.error('TeamContext - Error parsing user data:', e);
                }
            }

            const response = await teamService.addMember(teamId, memberData);
            console.log('TeamContext - Member added response:', response);

            // Récupérer les données fraîches du serveur au lieu de mettre à jour localement
            await fetchTeams();

            // Ne pas afficher de toast ici pour éviter les doublons
            return response;
        } catch (err) {
            console.error('TeamContext - Error adding member:', err);
            // Laisser le composant gérer l'affichage des erreurs
            throw err;
        }
    }, [fetchTeams]);

    const removeMember = useCallback(async (teamId, memberId) => {
        console.log('TeamContext - Removing member:', { teamId, memberId });
        try {
            // Vérifier si l'utilisateur est super_admin - ils n'ont pas accès aux équipes
            const userStr = localStorage.getItem('user');
            let currentUser = null;
            if (userStr) {
                try {
                    currentUser = JSON.parse(userStr);
                    if (currentUser.role === 'super_admin') {
                        const errorMessage = 'Les super administrateurs ne peuvent pas gérer les équipes';
                        console.error('TeamContext - Super admin cannot remove team members');
                        showErrorToast(errorMessage);
                        throw new Error(errorMessage);
                    }
                } catch (e) {
                    console.error('TeamContext - Error parsing user data:', e);
                }
            }

            const response = await teamService.removeMember(teamId, memberId);
            console.log('TeamContext - Member removed:', response);
            await fetchTeams();
            // Ne pas afficher de toast ici pour éviter les doublons
            return response;
        } catch (err) {
            console.error('TeamContext - Error removing member:', err);
            // Laisser le composant gérer l'affichage des erreurs
            throw err;
        }
    }, [fetchTeams]);

    const value = {
        teams,
        loading,
        error,
        fetchTeams,
        createTeam,
        updateTeam,
        deleteTeam,
        addMember,
        removeMember
    };

    return (
        <TeamContext.Provider value={value}>
            {children}
        </TeamContext.Provider>
    );
};

export default TeamContext;