import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { showSuccessToast, showErrorToast } from '@/utils/toastUtils';
import teamTaskService from '@/services/teamTaskService';
import { useAuth } from './AuthContext';

const TeamTaskContext = createContext();

export const useTeamTask = () => {
  const context = useContext(TeamTaskContext);
  if (!context) {
    console.warn('useTeamTask must be used within a TeamTaskProvider');
    return {
      tasks: [],
      loading: false,
      error: null,
      fetchTasks: () => Promise.resolve(),
      createTask: () => Promise.resolve(),
      updateTask: () => Promise.resolve(),
      deleteTask: () => Promise.resolve(),
      archiveTask: () => Promise.resolve(),
      unarchiveTask: () => Promise.resolve()
    };
  }
  return context;
};

export const TeamTaskProvider = ({ children }) => {
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    team_id: '',
    status: '',
    search: ''
  });
  const { user } = useAuth();

  const fetchTasks = useCallback(async (customFilters = null) => {
    console.log('TeamTaskContext - Fetching tasks...');
    setLoading(true);
    setError(null);
    try {
      // Vérifier si l'utilisateur est disponible
      if (!user) {
        console.log('TeamTaskContext - No user available, checking localStorage...');
        const userStr = localStorage.getItem('user');
        if (!userStr) {
          console.log('TeamTaskContext - No user found in localStorage');

          // Vérifier si un token existe mais que les données utilisateur sont manquantes
          const token = localStorage.getItem('authToken');
          if (token) {
            console.log('TeamTaskContext - Token exists but user data is missing, waiting for user data...');
            // Attendre un court instant pour permettre à d'autres composants de charger l'utilisateur
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Si toujours pas d'utilisateur, abandonner
            if (!localStorage.getItem('user')) {
              console.log('TeamTaskContext - Still no user after waiting, aborting fetch');
              setTasks([]);
              setLoading(false);
              return;
            }
          } else {
            console.log('TeamTaskContext - No authentication token found');
            setTasks([]);
            setLoading(false);
            return;
          }
        }
      }

      // Vérifier si l'utilisateur est un super_admin ou un client
      try {
        const currentUser = user || JSON.parse(localStorage.getItem('user'));
        if (currentUser && (currentUser.role === 'super_admin' || currentUser.role === 'client')) {
          console.log(`TeamTaskContext - ${currentUser.role} user detected, setting empty tasks array`);
          setTasks([]);
          setLoading(false);
          return;
        }
      } catch (e) {
        console.error('TeamTaskContext - Error checking user role:', e);
      }

      // Utiliser les filtres personnalisés s'ils sont fournis, sinon utiliser les filtres d'état
      const filtersToUse = customFilters || filters;
      console.log('TeamTaskContext - Using filters:', filtersToUse);

      // Supprimer le filtre de recherche des filtres API car il est géré côté client
      const { search, ...apiFilters } = filtersToUse;

      // Vérifier le token d'authentification
      const token = localStorage.getItem('authToken');
      console.log('TeamTaskContext - Using auth token for task fetch:', token ? 'Token present' : 'No token');

      try {
        const response = await teamTaskService.getTeamTasks(apiFilters);
        console.log('TeamTaskContext - Raw API response:', response);

        // Si la réponse est vide ou null, retourner un tableau vide
        if (!response) {
          console.log('TeamTaskContext - Empty response from API');
          setTasks([]);
          setLoading(false);
          return;
        }

        // Normaliser les données des tâches
        const tasksData = Array.isArray(response) ? response :
          Array.isArray(response?.tasks) ? response.tasks :
            response ? [response] : [];

        console.log('TeamTaskContext - Normalized tasks data:', tasksData);

        // Appliquer le filtre de recherche côté client si nécessaire
        let filteredTasks = tasksData;
        if (search && search.trim() !== '') {
          const searchLower = search.toLowerCase();
          filteredTasks = tasksData.filter(task =>
            task.title.toLowerCase().includes(searchLower) ||
            (task.description && task.description.toLowerCase().includes(searchLower))
          );
        }

        // Récupérer l'utilisateur actuel (peut être différent de user si user était null)
        const currentUser = user || (() => {
          try {
            const userStr = localStorage.getItem('user');
            return userStr ? JSON.parse(userStr) : null;
          } catch (e) {
            console.error('TeamTaskContext - Error parsing user data:', e);
            return null;
          }
        })();

        // Ajouter les permissions pour chaque tâche (seulement si nécessaire)
        const tasksWithPermissions = filteredTasks.map(task => {
          // Éviter de recalculer les permissions si elles existent déjà
          if (task.permissions) {
            return task;
          }
          const permissions = teamTaskService.checkTeamTaskPermissions(currentUser, task);
          return {
            ...task,
            permissions
          };
        });

        setTasks(tasksWithPermissions);
      } catch (apiError) {
        console.error('TeamTaskContext - API error fetching tasks:', apiError);

        // Améliorer le message d'erreur pour le débogage
        if (apiError.response) {
          console.error('TeamTaskContext - Error response status:', apiError.response.status);
          console.error('TeamTaskContext - Error response data:', apiError.response.data);
        }

        // Si l'erreur est liée aux permissions, ne pas définir d'erreur
        if (apiError.status === 403 || (apiError.response && apiError.response.status === 403)) {
          console.log('TeamTaskContext - Permission error, setting empty tasks array');
          setTasks([]);
        } else {
          // Pour les autres erreurs, définir le message d'erreur
          setError(apiError.message || 'Une erreur est survenue lors du chargement des tâches');
          setTasks([]);
        }
      }
    } catch (err) {
      console.error('TeamTaskContext - Unexpected error in fetchTasks:', err);
      setError('Une erreur inattendue est survenue');
      setTasks([]);
    } finally {
      setLoading(false);
    }
  }, [filters, user]);

  // Appeler fetchTasks au chargement du composant et lorsque les filtres changent
  useEffect(() => {
    console.log('TeamTaskContext - Component mounted or filters changed, fetching tasks...');
    fetchTasks();
  }, [fetchTasks]);

  const updateFilters = useCallback((newFilters) => {
    console.log('TeamTaskContext - Updating filters:', newFilters);
    setFilters(prevFilters => ({
      ...prevFilters,
      ...newFilters
    }));
  }, []);

  const resetFilters = useCallback(() => {
    console.log('TeamTaskContext - Resetting filters');
    setFilters({
      team_id: '',
      status: '',
      search: ''
    });
  }, []);

  const createTask = useCallback(async (taskData) => {
    console.log('TeamTaskContext - Creating task:', taskData);
    try {
      const response = await teamTaskService.createTeamTask(taskData);
      console.log('TeamTaskContext - Task created:', response);
      await fetchTasks();
      showSuccessToast('Tâche créée avec succès');
      return response;
    } catch (err) {
      console.error('TeamTaskContext - Error creating task:', err);
      const errorMessage = err.response?.data?.message ||
        err.response?.data ||
        err.message ||
        'Erreur lors de la création de la tâche';
      console.error('TeamTaskContext - Error details:', {
        message: errorMessage,
        status: err.response?.status,
        data: err.response?.data
      });
      showErrorToast(errorMessage);
      throw err;
    }
  }, [fetchTasks]);

  const updateTask = useCallback(async (taskId, taskData) => {
    console.log('TeamTaskContext - Updating task:', { taskId, taskData });
    try {
      const response = await teamTaskService.updateTeamTask(taskId, taskData);
      console.log('TeamTaskContext - Task updated:', response);
      await fetchTasks();
      showSuccessToast('Tâche mise à jour avec succès');
      return response;
    } catch (err) {
      console.error('TeamTaskContext - Error updating task:', err);
      showErrorToast(err.message || 'Erreur lors de la mise à jour de la tâche');
      throw err;
    }
  }, [fetchTasks]);

  const updateTaskStatus = useCallback(async (taskId, status) => {
    console.log('TeamTaskContext - Updating task status:', { taskId, status });
    try {
      const response = await teamTaskService.updateTeamTaskStatus(taskId, status);
      console.log('TeamTaskContext - Task status updated:', response);
      await fetchTasks();
      showSuccessToast('Statut mis à jour avec succès !');
      return response;
    } catch (err) {
      console.error('TeamTaskContext - Error updating task status:', err);
      showErrorToast(err.message || 'Erreur lors de la mise à jour du statut de la tâche');
      throw err;
    }
  }, [fetchTasks]);

  const archiveTask = useCallback(async (taskId) => {
    console.log('TeamTaskContext - Archiving task:', taskId);
    try {
      const response = await teamTaskService.archiveTeamTask(taskId);
      console.log('TeamTaskContext - Task archived:', response);
      await fetchTasks();
      toast.success('Tâche archivée avec succès');
      return response;
    } catch (err) {
      console.error('TeamTaskContext - Error archiving task:', err);
      toast.error(err.message || 'Erreur lors de l\'archivage de la tâche');
      throw err;
    }
  }, [fetchTasks]);

  const unarchiveTask = useCallback(async (taskId) => {
    console.log('TeamTaskContext - Unarchiving task:', taskId);
    try {
      const response = await teamTaskService.unarchiveTeamTask(taskId);
      console.log('TeamTaskContext - Task unarchived:', response);
      await fetchTasks();
      toast.success('Tâche désarchivée avec succès');
      return response;
    } catch (err) {
      console.error('TeamTaskContext - Error unarchiving task:', err);
      toast.error(err.message || 'Erreur lors du désarchivage de la tâche');
      throw err;
    }
  }, [fetchTasks]);

  const deleteTask = useCallback(async (taskId) => {
    console.log('TeamTaskContext - Deleting task:', taskId);
    try {
      const response = await teamTaskService.deleteTeamTask(taskId);
      console.log('TeamTaskContext - Task deleted:', response);
      await fetchTasks();
      showSuccessToast('Tâche supprimée avec succès');
      return response;
    } catch (err) {
      console.error('TeamTaskContext - Error deleting task:', err);
      showErrorToast(err.message || 'Erreur lors de la suppression de la tâche');
      throw err;
    }
  }, [fetchTasks]);

  const value = {
    tasks,
    loading,
    error,
    filters,
    fetchTasks,
    updateFilters,
    resetFilters,
    createTask,
    updateTask,
    updateTaskStatus,
    archiveTask,
    unarchiveTask,
    deleteTask
  };

  return (
    <TeamTaskContext.Provider value={value}>
      {children}
    </TeamTaskContext.Provider>
  );
};

export default TeamTaskContext;
