import { useState, useEffect, useCallback } from 'react';
import { toast } from 'react-toastify';
import adminDashboardService from '@/services/adminDashboardService';

/**
 * Hook personnalisé pour gérer le tableau de bord admin
 * @param {string} initialPeriod - Période initiale
 * @returns {Object} - État et fonctions du dashboard
 */
export const useAdminDashboard = (initialPeriod = 'today') => {
  // États principaux
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [currentPeriod, setCurrentPeriod] = useState(initialPeriod);
  const [lastUpdated, setLastUpdated] = useState(null);

  // Cache pour éviter les requêtes répétées
  const [cache, setCache] = useState(new Map());
  const CACHE_TIMEOUT = 30000; // 30 secondes

  // Périodes disponibles selon votre guide
  const availablePeriods = [
    { value: 'today', label: 'Aujourd\'hui', color: '#10B981' },
    { value: '1h', label: '1h', color: '#3B82F6' },
    { value: '24h', label: '24h', color: '#8B5CF6' },
    { value: '7d', label: '7j', color: '#F59E0B' },
    { value: '30d', label: '30j', color: '#EF4444' }
  ];

  // Fonctions de toast
  const showSuccessToast = (message) => {
    toast.success(message, {
      position: "top-right",
      autoClose: 3000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true
    });
  };

  const showErrorToast = (message) => {
    toast.error(message, {
      position: "top-right",
      autoClose: 5000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true
    });
  };

  const showWarningToast = (message) => {
    toast.warning(message, {
      position: "top-right",
      autoClose: 4000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true
    });
  };

  // Vérifier si les données en cache sont encore valides
  const isCacheValid = (cacheKey) => {
    const cachedItem = cache.get(cacheKey);
    if (!cachedItem) return false;

    const now = Date.now();
    return (now - cachedItem.timestamp) < CACHE_TIMEOUT;
  };

  // Récupérer les données du cache
  const getCachedData = (cacheKey) => {
    const cachedItem = cache.get(cacheKey);
    return cachedItem ? cachedItem.data : null;
  };

  // Mettre en cache les données
  const setCachedData = (cacheKey, data) => {
    setCache(prev => new Map(prev.set(cacheKey, {
      data: data,
      timestamp: Date.now()
    })));
  };

  // Fonction principale pour récupérer les données du dashboard
  const fetchDashboardData = useCallback(async (period = 'today', manualRefresh = true) => {
    try {
      setLoading(true);
      setError(null);

      // Vérifier le cache d'abord (sauf pour les refresh manuels)
      const cacheKey = `admin-dashboard-${period}`;
      if (!manualRefresh && isCacheValid(cacheKey)) {
        console.log(`useAdminDashboard - Utilisation des données en cache pour ${period}`);
        const cachedData = getCachedData(cacheKey);
        setDashboardData(cachedData);
        setLastUpdated(new Date());
        setLoading(false);
        return cachedData;
      }

      console.log(`useAdminDashboard - Récupération des données dashboard admin (période: ${period}, manuel: ${manualRefresh})...`);
      const response = await adminDashboardService.getAdminDashboard(period, manualRefresh);

      if (response.success) {
        console.log('useAdminDashboard - Données dashboard admin récupérées avec succès:', response.data);
        setDashboardData(response.data);
        setLastUpdated(new Date());

        // Mettre en cache les données
        setCachedData(cacheKey, response.data);

        // Afficher le message de succès seulement pour les refresh manuels
        if (manualRefresh) {
          if (response.data.metadata?.features?.real_time_data) {
            showSuccessToast('Données en temps réel chargées avec succès');
          } else {
            showSuccessToast('Données mises à jour avec succès');
          }
        }

        return response.data;
      } else {
        console.log('useAdminDashboard - Utilisation des données mockées:', response.data);
        setDashboardData(response.data);
        setLastUpdated(new Date());

        // Mettre en cache même les données mockées
        setCachedData(cacheKey, response.data);

        if (response.error && manualRefresh) {
          setError('Erreur lors du chargement des données: ' + response.error);
          showWarningToast('Utilisation des données de démonstration en raison d\'une erreur de connexion');
        }

        return response.data;
      }
    } catch (err) {
      console.error('useAdminDashboard - Erreur lors du chargement des données:', err);
      setError('Erreur lors du chargement des données: ' + err.message);

      // Essayer d'utiliser les données en cache en cas d'erreur
      const cacheKey = `admin-dashboard-${period}`;
      const cachedData = getCachedData(cacheKey);
      if (cachedData) {
        console.log('useAdminDashboard - Utilisation des données en cache après erreur');
        setDashboardData(cachedData);
        setLastUpdated(new Date());

        if (manualRefresh) {
          showWarningToast('Utilisation des données en cache en raison d\'une erreur de connexion');
        }

        return cachedData;
      }

      if (manualRefresh) {
        showErrorToast('Erreur lors du chargement des données du dashboard');
      }

      return null;
    } finally {
      setLoading(false);
    }
  }, [cache]);

  // Fonction pour changer de période
  const changePeriod = useCallback((newPeriod) => {
    if (newPeriod !== currentPeriod) {
      console.log(`useAdminDashboard - Changement de période: ${currentPeriod} -> ${newPeriod}`);
      setCurrentPeriod(newPeriod);
      // Récupérer les données pour la nouvelle période
      fetchDashboardData(newPeriod, true);
    }
  }, [currentPeriod, fetchDashboardData]);

  // Fonction pour rafraîchir manuellement
  const refreshDashboard = useCallback(() => {
    console.log('useAdminDashboard - Rafraîchissement manuel des données');
    fetchDashboardData(currentPeriod, true);
  }, [currentPeriod, fetchDashboardData]);

  // Fonction pour récupérer les données de débogage
  const fetchDebugData = useCallback(async () => {
    try {
      console.log('useAdminDashboard - Récupération des données de débogage...');
      const response = await adminDashboardService.getAdminDebugData();

      if (response.success) {
        console.log('useAdminDashboard - Données de débogage récupérées:', response.data);
        showSuccessToast('Données de débogage récupérées avec succès');
        return response.data;
      } else {
        console.error('useAdminDashboard - Erreur lors de la récupération des données de débogage:', response.error);
        showErrorToast('Erreur lors de la récupération des données de débogage');
        return null;
      }
    } catch (err) {
      console.error('useAdminDashboard - Erreur lors de la récupération des données de débogage:', err);
      showErrorToast('Erreur lors de la récupération des données de débogage');
      return null;
    }
  }, []);

  // Chargement initial
  useEffect(() => {
    console.log(`useAdminDashboard - Chargement initial avec la période: ${currentPeriod}`);
    fetchDashboardData(currentPeriod, true);
  }, []); // Dépendance vide pour ne s'exécuter qu'une fois

  // Auto-refresh pour la période "today" seulement
  useEffect(() => {
    let interval = null;

    if (currentPeriod === 'today') {
      console.log('useAdminDashboard - Activation de l\'auto-refresh pour la période "today"');
      interval = setInterval(() => {
        console.log('useAdminDashboard - Actualisation automatique des données (today)...');
        fetchDashboardData('today', false); // false = pas manuel pour l'auto-refresh
      }, 60000); // 60 secondes
    } else {
      console.log(`useAdminDashboard - Pas d'auto-refresh pour la période "${currentPeriod}"`);
    }

    return () => {
      if (interval) {
        console.log('useAdminDashboard - Nettoyage de l\'auto-refresh');
        clearInterval(interval);
      }
    };
  }, [currentPeriod, fetchDashboardData]);

  // Extraction des données pour faciliter l'utilisation
  const getMetricData = () => {
    if (!dashboardData?.metric_cards) return null;

    return {
      teamsManaged: dashboardData.metric_cards.find(card => card.title.includes('Équipes'))?.value || 0,
      teamMembers: dashboardData.metric_cards.find(card => card.title.includes('Membres'))?.value || 0,
      averageProgress: dashboardData.metric_cards.find(card => card.title.includes('Progression'))?.value || "0%"
    };
  };

  const getChartsData = () => {
    if (!dashboardData?.charts) return null;

    return {
      eventsDistribution: dashboardData.charts.events_distribution,
      tasksDistribution: dashboardData.charts.tasks_distribution
    };
  };

  const getDetailedStats = () => {
    return dashboardData?.detailed_stats || null;
  };

  const getMetadata = () => {
    return dashboardData?.metadata || null;
  };

  // Retourner l'état et les fonctions
  return {
    // États
    dashboardData,
    loading,
    error,
    currentPeriod,
    lastUpdated,
    availablePeriods,

    // Fonctions
    changePeriod,
    refreshDashboard,
    fetchDebugData,

    // Données extraites
    metricData: getMetricData(),
    chartsData: getChartsData(),
    detailedStats: getDetailedStats(),
    metadata: getMetadata(),

    // Informations utiles
    isTeamLeader: dashboardData?.is_team_leader || false,
    adminName: dashboardData?.admin_name || '',
    isRealTimeData: dashboardData?.metadata?.features?.real_time_data || false
  };
};
