import { useState, useEffect, useCallback } from 'react';
import { toast } from 'react-toastify';
import biService from '@/services/biService';

/**
 * Hook personnalisé pour gérer le tableau de bord BI avec filtres de période
 * Implémente la logique complète selon la documentation backend
 */
const useBiDashboard = () => {
  const [dashboardData, setDashboardData] = useState(null);
  const [currentPeriod, setCurrentPeriod] = useState('today');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);
  const [debugData, setDebugData] = useState(null);

  // Périodes disponibles selon la documentation backend
  const availablePeriods = [
    { 
      value: 'today', 
      label: 'Aujourd\'hui', 
      color: '#10B981',
      description: 'Données en temps réel via DailyLoginTracker'
    },
    { 
      value: '1h', 
      label: 'Dernière heure', 
      color: '#3B82F6',
      description: 'Approximation via User.last_login'
    },
    { 
      value: '24h', 
      label: 'Dernières 24h', 
      color: '#8B5CF6',
      description: 'Approximation via User.last_login'
    },
    { 
      value: '7d', 
      label: 'Derniers 7 jours', 
      color: '#F59E0B',
      description: 'Approximation via User.last_login'
    },
    { 
      value: '30d', 
      label: 'Derniers 30 jours', 
      color: '#EF4444',
      description: 'Approximation via User.last_login'
    }
  ];

  /**
   * Récupère les données du tableau de bord pour une période donnée
   * @param {string} period - Période à récupérer
   * @param {boolean} manualRefresh - Indique si c'est un refresh manuel
   */
  const fetchDashboardData = useCallback(async (period = currentPeriod, manualRefresh = true) => {
    setIsLoading(true);
    setError(null);

    try {
      console.log(`useBiDashboard - Récupération des données pour la période: ${period}`);
      
      const response = await biService.getSuperAdminDashboard(period, manualRefresh);
      
      if (response.success && response.data) {
        setDashboardData(response.data);
        setCurrentPeriod(period);
        setLastUpdated(new Date());
        
        console.log('useBiDashboard - Données récupérées avec succès:', {
          period: period,
          timestamp: response.data.timestamp,
          isRealtime: response.data.is_realtime,
          metricCards: response.data.metric_cards?.length || 0,
          charts: Object.keys(response.data.charts || {}).length
        });
        
        return response.data;
      } else {
        throw new Error(response.error || 'Erreur lors de la récupération des données');
      }
    } catch (err) {
      console.error('useBiDashboard - Erreur lors du chargement des données:', err);
      setError(err.message || 'Une erreur est survenue lors du chargement des données');
      
      if (manualRefresh) {
        toast.error(err.message || 'Erreur lors du chargement des données du tableau de bord');
      }
      
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [currentPeriod]);

  /**
   * Rafraîchit manuellement les données
   */
  const refreshData = useCallback(() => {
    console.log('useBiDashboard - Rafraîchissement manuel des données');
    fetchDashboardData(currentPeriod, true);
    toast.info('Actualisation des données en cours...');
  }, [fetchDashboardData, currentPeriod]);

  /**
   * Change la période active et récupère les nouvelles données
   * @param {string} newPeriod - Nouvelle période
   */
  const changePeriod = useCallback((newPeriod) => {
    if (newPeriod !== currentPeriod) {
      console.log(`useBiDashboard - Changement de période: ${currentPeriod} -> ${newPeriod}`);
      fetchDashboardData(newPeriod, true);
    }
  }, [fetchDashboardData, currentPeriod]);

  /**
   * Récupère les statistiques de connexion en temps réel
   */
  const fetchRealTimeStats = useCallback(async () => {
    try {
      console.log('useBiDashboard - Récupération des statistiques en temps réel');
      const response = await biService.getRealTimeLoginStats();
      
      if (response.success) {
        console.log('useBiDashboard - Statistiques en temps réel récupérées:', response.data);
        return response.data;
      } else {
        throw new Error(response.error || 'Erreur lors de la récupération des statistiques en temps réel');
      }
    } catch (err) {
      console.error('useBiDashboard - Erreur lors de la récupération des statistiques en temps réel:', err);
      return null;
    }
  }, []);

  /**
   * Récupère les données de débogage
   */
  const fetchDebugData = useCallback(async () => {
    try {
      console.log('useBiDashboard - Récupération des données de débogage');
      const response = await biService.getDebugLoginData();
      
      if (response.success) {
        setDebugData(response.data);
        console.log('useBiDashboard - Données de débogage récupérées:', response.data);
        return response.data;
      } else {
        throw new Error(response.error || 'Erreur lors de la récupération des données de débogage');
      }
    } catch (err) {
      console.error('useBiDashboard - Erreur lors de la récupération des données de débogage:', err);
      return null;
    }
  }, []);

  /**
   * Récupère les données historiques
   * @param {string} dataType - Type de données
   * @param {string} period - Période
   */
  const fetchHistoricalData = useCallback(async (dataType, period) => {
    try {
      console.log(`useBiDashboard - Récupération des données historiques: ${dataType}, ${period}`);
      const response = await biService.getHistoricalData(dataType, period);
      
      if (response.success) {
        console.log('useBiDashboard - Données historiques récupérées:', response.data);
        return response.data;
      } else {
        throw new Error(response.error || 'Erreur lors de la récupération des données historiques');
      }
    } catch (err) {
      console.error('useBiDashboard - Erreur lors de la récupération des données historiques:', err);
      return null;
    }
  }, []);

  /**
   * Obtient les informations sur la période actuelle
   */
  const getCurrentPeriodInfo = useCallback(() => {
    return availablePeriods.find(p => p.value === currentPeriod) || availablePeriods[0];
  }, [currentPeriod]);

  /**
   * Vérifie si les données sont en temps réel
   */
  const isRealTimeData = useCallback(() => {
    return currentPeriod === 'today' && dashboardData?.is_realtime === true;
  }, [currentPeriod, dashboardData]);

  /**
   * Obtient la source de données actuelle
   */
  const getDataSource = useCallback(() => {
    if (currentPeriod === 'today') {
      return 'DailyLoginTracker (Temps réel)';
    } else {
      return 'User.last_login (Approximation)';
    }
  }, [currentPeriod]);

  /**
   * Obtient les métriques formatées pour l'affichage
   */
  const getFormattedMetrics = useCallback(() => {
    if (!dashboardData?.metric_cards) return [];

    return dashboardData.metric_cards.map(card => ({
      ...card,
      isRealTime: card.period === 'today' || currentPeriod === 'today',
      dataSource: card.data_source || getDataSource(),
      lastUpdated: card.last_updated || dashboardData.timestamp
    }));
  }, [dashboardData, currentPeriod, getDataSource]);

  /**
   * Obtient les données des graphiques formatées
   */
  const getChartData = useCallback(() => {
    if (!dashboardData?.charts) return {};

    const charts = {};
    
    Object.keys(dashboardData.charts).forEach(chartKey => {
      const chart = dashboardData.charts[chartKey];
      charts[chartKey] = {
        ...chart,
        isRealTime: chart.period === 'today' || currentPeriod === 'today',
        periodName: getCurrentPeriodInfo().label
      };
    });

    return charts;
  }, [dashboardData, currentPeriod, getCurrentPeriodInfo]);

  // Chargement initial
  useEffect(() => {
    console.log('useBiDashboard - Initialisation avec la période "today"');
    fetchDashboardData('today', true);
  }, []);

  // Retourner toutes les fonctions et états
  return {
    // États
    dashboardData,
    currentPeriod,
    isLoading,
    error,
    lastUpdated,
    debugData,

    // Données formatées
    formattedMetrics: getFormattedMetrics(),
    chartData: getChartData(),
    currentPeriodInfo: getCurrentPeriodInfo(),

    // Fonctions principales
    fetchDashboardData,
    refreshData,
    changePeriod,

    // Fonctions auxiliaires
    fetchRealTimeStats,
    fetchDebugData,
    fetchHistoricalData,

    // Informations utiles
    availablePeriods,
    isRealTimeData: isRealTimeData(),
    dataSource: getDataSource(),

    // Métadonnées
    metadata: dashboardData?.metadata || {},
    timestamp: dashboardData?.timestamp,
    isRealtime: dashboardData?.is_realtime || false
  };
};

export default useBiDashboard;
