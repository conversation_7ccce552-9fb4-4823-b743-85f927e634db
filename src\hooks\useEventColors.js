import { useState, useEffect, useCallback } from 'react';
import colorService from '@/services/colorService';

/**
 * Hook personnalisé pour gérer les couleurs d'événements
 */
export const useEventColors = () => {
  const [palette, setPalette] = useState([]);
  const [categories, setCategories] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Charger les données de couleurs
  useEffect(() => {
    const loadColors = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const [paletteData, categoriesData] = await Promise.all([
          colorService.getCachedPalette(),
          colorService.getCachedCategories()
        ]);
        
        setPalette(paletteData);
        setCategories(categoriesData);
      } catch (err) {
        console.error('Erreur lors du chargement des couleurs:', err);
        setError(err);
      } finally {
        setLoading(false);
      }
    };

    loadColors();
  }, []);

  // Obtenir les informations d'une couleur
  const getColorInfo = useCallback(async (colorIdentifier) => {
    try {
      return await colorService.getColorInfo(colorIdentifier);
    } catch (err) {
      console.error('Erreur lors de la récupération des infos couleur:', err);
      return null;
    }
  }, []);

  // Formater une couleur pour l'affichage
  const formatColorForDisplay = useCallback(async (colorIdentifier) => {
    try {
      return await colorService.formatColorForCalendar(colorIdentifier);
    } catch (err) {
      console.error('Erreur lors du formatage de couleur:', err);
      return {
        background: '#3788d820',
        border: '#3788d8',
        text: '#000000',
        name: 'Couleur par défaut'
      };
    }
  }, []);

  // Suggérer une couleur basée sur le titre
  const suggestColor = useCallback(async (title) => {
    try {
      return await colorService.suggestColor(title);
    } catch (err) {
      console.error('Erreur lors de la suggestion de couleur:', err);
      return null;
    }
  }, []);

  // Valider une couleur
  const validateColor = useCallback(async (color) => {
    try {
      return await colorService.validateColor(color);
    } catch (err) {
      console.error('Erreur lors de la validation de couleur:', err);
      return false;
    }
  }, []);

  // Obtenir les couleurs recommandées pour un type d'événement
  const getRecommendedColors = useCallback(async (eventType = 'general') => {
    try {
      return await colorService.getRecommendedColors(eventType);
    } catch (err) {
      console.error('Erreur lors de la récupération des couleurs recommandées:', err);
      return [];
    }
  }, []);

  // Convertir nom en hex
  const getHexFromName = useCallback(async (colorName) => {
    try {
      return await colorService.getHexFromName(colorName);
    } catch (err) {
      console.error('Erreur lors de la conversion nom -> hex:', err);
      return null;
    }
  }, []);

  // Convertir hex en nom
  const getNameFromHex = useCallback(async (hexCode) => {
    try {
      return await colorService.getNameFromHex(hexCode);
    } catch (err) {
      console.error('Erreur lors de la conversion hex -> nom:', err);
      return null;
    }
  }, []);

  // Rafraîchir les données
  const refreshColors = useCallback(async () => {
    colorService.clearCache();
    try {
      setLoading(true);
      setError(null);
      
      const [paletteData, categoriesData] = await Promise.all([
        colorService.getPalette(),
        colorService.getColorsByCategories()
      ]);
      
      setPalette(paletteData);
      setCategories(categoriesData);
    } catch (err) {
      console.error('Erreur lors du rafraîchissement des couleurs:', err);
      setError(err);
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    // État
    palette,
    categories,
    loading,
    error,
    
    // Fonctions
    getColorInfo,
    formatColorForDisplay,
    suggestColor,
    validateColor,
    getRecommendedColors,
    getHexFromName,
    getNameFromHex,
    refreshColors
  };
};

/**
 * Hook pour gérer la couleur d'un événement spécifique
 */
export const useEventColor = (initialColor = null, eventTitle = '') => {
  const [selectedColor, setSelectedColor] = useState(initialColor);
  const [colorInfo, setColorInfo] = useState(null);
  const [suggestion, setSuggestion] = useState(null);
  const { getColorInfo, suggestColor, validateColor, formatColorForDisplay } = useEventColors();

  // Mettre à jour les informations de couleur quand la couleur change
  useEffect(() => {
    const updateColorInfo = async () => {
      if (selectedColor) {
        const info = await getColorInfo(selectedColor);
        setColorInfo(info);
      } else {
        setColorInfo(null);
      }
    };

    updateColorInfo();
  }, [selectedColor, getColorInfo]);

  // Suggérer une couleur basée sur le titre
  useEffect(() => {
    const getSuggestion = async () => {
      if (eventTitle && eventTitle.length > 3) {
        const suggestionData = await suggestColor(eventTitle);
        setSuggestion(suggestionData);
      } else {
        setSuggestion(null);
      }
    };

    const debounceTimer = setTimeout(getSuggestion, 500);
    return () => clearTimeout(debounceTimer);
  }, [eventTitle, suggestColor]);

  // Changer la couleur avec validation
  const changeColor = useCallback(async (newColor) => {
    try {
      const isValid = await validateColor(newColor);
      if (isValid) {
        setSelectedColor(newColor);
        return true;
      }
      return false;
    } catch (err) {
      console.error('Erreur lors du changement de couleur:', err);
      return false;
    }
  }, [validateColor]);

  // Appliquer la suggestion
  const applySuggestion = useCallback(() => {
    if (suggestion && suggestion.suggested_color) {
      setSelectedColor(suggestion.suggested_color);
      setSuggestion(null);
    }
  }, [suggestion]);

  // Obtenir le style formaté pour l'affichage
  const getDisplayStyle = useCallback(async () => {
    return await formatColorForDisplay(selectedColor);
  }, [selectedColor, formatColorForDisplay]);

  return {
    selectedColor,
    colorInfo,
    suggestion,
    changeColor,
    applySuggestion,
    getDisplayStyle,
    setSelectedColor
  };
};

export default useEventColors;
