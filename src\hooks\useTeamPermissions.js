import { useAuth } from '@/contexts/AuthContext';
import { permissionService } from '@/services/permissionService';

/**
 * Hook personnalisé pour vérifier les permissions d'une équipe
 * @param {Object} team - L'équipe pour laquelle vérifier les permissions
 * @returns {Object} Un objet contenant toutes les permissions pour l'équipe
 */
export const useTeamPermissions = (team) => {
  const { user } = useAuth();
  const permissions = permissionService.checkTeamPermissions(user, team);
  
  return permissions;
};