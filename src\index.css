/*src/index.css*/
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 224 76% 48%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 262 83% 58%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 224 76% 48%;
    --radius: 0.5rem;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:outline-2 focus-visible:outline-offset-2 disabled:pointer-events-none disabled:opacity-50;
  }

  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 focus-visible:outline-primary;
  }

  .btn-secondary {
    @apply bg-white text-gray-700 border border-gray-200 hover:bg-gray-50 hover:border-gray-300;
  }

  .container {
    @apply mx-auto max-w-7xl px-4 sm:px-6 lg:px-8;
  }

  .heading {
    @apply scroll-m-20 text-4xl font-extrabold tracking-tight lg:text-5xl;
  }

  .subheading {
    @apply text-xl text-gray-600;
  }

  .feature-card {
    @apply rounded-2xl border bg-white/80 backdrop-blur-sm p-6 shadow-lg transition-all duration-300;
  }

  .feature-icon {
    @apply mb-4 rounded-xl p-3 w-12 h-12 flex items-center justify-center transition-transform group-hover:scale-110 duration-300;
  }

  .nav-link {
    @apply text-sm font-medium text-gray-600 transition-colors hover:text-gray-900;
  }
}

@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }

  33% {
    transform: translate(30px, -50px) scale(1.1);
  }

  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }

  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* Gradient Text Animation */
@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

.bg-gradient-animate {
  background-size: 200% 200%;
  animation: gradient 15s ease infinite;
}

/* Styles pour le calendrier avec couleurs pastel */
.rbc-calendar {
  font-family: 'Inter', sans-serif;
}

.rbc-header {
  padding: 10px 3px;
  font-weight: 600;
  font-size: 0.85rem;
  color: #4b5563;
}

.rbc-date-cell {
  padding-right: 10px;
  text-align: center;
  font-size: 0.9rem;
  color: #6b7280;
}

.rbc-date-cell.rbc-now {
  font-weight: bold;
  color: #4f46e5;
}

.rbc-toolbar button {
  color: #4b5563;
  border: 1px solid #e5e7eb;
}

.rbc-toolbar button:hover {
  background-color: #f3f4f6;
  color: #4f46e5;
}

.rbc-toolbar button.rbc-active {
  background-color: #4f46e5;
  color: white;
}

.rbc-toolbar button.rbc-active:hover {
  background-color: #4338ca;
  color: white;
}

.rbc-month-view {
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.rbc-day-bg.rbc-today {
  background-color: #f5f8ff;
}

.rbc-event {
  border-radius: 8px !important;
  padding: 3px 8px !important;
  background-color: #BDE0FE !important;
  /* Bleu clair pastel par défaut */
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  color: #333 !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
  transition: transform 0.1s ease-in-out, box-shadow 0.1s ease-in-out;
}

.rbc-event:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1) !important;
}

.rbc-event.rbc-selected {
  background-color: #D4EBFF !important;
  border: 2px solid #4f46e5 !important;
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2) !important;
}

.rbc-event-label {
  font-size: 0.7rem;
  font-weight: 500;
}

.rbc-event-content {
  font-size: 0.85rem;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Styles pour les événements selon leur statut */
.event-completed {
  background-color: #BDEBC8 !important;
  /* Vert pastel */
  color: #333 !important;
  border-left: 3px solid #10B981 !important;
}

.event-pending {
  background-color: #BDE0FE !important;
  /* Bleu clair pastel */
  color: #333 !important;
  border-left: 3px solid #3B82F6 !important;
}

.event-archived {
  background-color: #F3F4F6 !important;
  /* Gris très clair */
  color: #6B7280 !important;
  opacity: 0.7 !important;
}

/* Amélioration de l'apparence générale du calendrier */
.rbc-row-content {
  z-index: 0;
  /* Éviter les problèmes de superposition */
}

.rbc-month-row {
  overflow: visible !important;
  /* Permettre aux événements de déborder légèrement */
}

.rbc-row-segment {
  padding: 1px 2px;
}

.rbc-day-bg {
  transition: background-color 0.2s ease;
}

.rbc-day-bg:hover {
  background-color: #f9fafb;
}

/* Fix radical pour les problèmes d'aria-hidden avec les modals */
[data-radix-portal] {
  pointer-events: auto !important;
}

/* S'assurer que les éléments focusables dans les modals restent accessibles */
[role="dialog"] button,
[role="dialog"] input,
[role="dialog"] select,
[role="dialog"] textarea {
  pointer-events: auto !important;
}

/* Empêcher les problèmes d'aria-hidden sur les conteneurs principaux */
.min-h-screen[aria-hidden="true"] {
  pointer-events: auto !important;
  visibility: visible !important;
}

/* Force tous les éléments focusables à rester accessibles */
[aria-hidden="true"] button,
[aria-hidden="true"] input,
[aria-hidden="true"] select,
[aria-hidden="true"] textarea,
[aria-hidden="true"] [tabindex] {
  pointer-events: auto !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* S'assurer que la sidebar reste toujours cliquable */
aside.fixed,
nav,
aside,
.sidebar,
[role="navigation"],
nav a,
nav button,
aside a,
aside button,
aside .flex.items-center,
aside .space-y-1 a,
aside .space-y-1 button {
  pointer-events: auto !important;
  visibility: visible !important;
  opacity: 1 !important;
  z-index: 9999 !important;
}

/* Forcer spécifiquement les éléments de la sidebar UserSidebar */
aside.fixed.inset-y-0.left-0.w-64,
aside.fixed.inset-y-0.left-0.w-64 *,
aside.fixed.inset-y-0.left-0.w-64 a,
aside.fixed.inset-y-0.left-0.w-64 button {
  pointer-events: auto !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Forcer les liens de navigation à rester accessibles */
a[href],
button[type="button"],
[role="button"] {
  pointer-events: auto !important;
}

/* Solution ultra-agressive pour débloquer l'interface */
* {
  pointer-events: auto !important;
}

/* Forcer tous les éléments à être visibles */
[aria-hidden="true"] {
  visibility: visible !important;
  opacity: 1 !important;
  pointer-events: auto !important;
}

/* Empêcher complètement aria-hidden de bloquer */
.min-h-screen[aria-hidden="true"],
div[aria-hidden="true"],
main[aria-hidden="true"],
section[aria-hidden="true"] {
  pointer-events: auto !important;
  visibility: visible !important;
  opacity: 1 !important;
}