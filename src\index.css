/*src/index.css*/
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 224 76% 48%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 262 83% 58%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 224 76% 48%;
    --radius: 0.5rem;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:outline-2 focus-visible:outline-offset-2 disabled:pointer-events-none disabled:opacity-50;
  }

  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 focus-visible:outline-primary;
  }

  .btn-secondary {
    @apply bg-white text-gray-700 border border-gray-200 hover:bg-gray-50 hover:border-gray-300;
  }

  .container {
    @apply mx-auto max-w-7xl px-4 sm:px-6 lg:px-8;
  }

  .heading {
    @apply scroll-m-20 text-4xl font-extrabold tracking-tight lg:text-5xl;
  }

  .subheading {
    @apply text-xl text-gray-600;
  }

  .feature-card {
    @apply rounded-2xl border bg-white/80 backdrop-blur-sm p-6 shadow-lg transition-all duration-300;
  }

  .feature-icon {
    @apply mb-4 rounded-xl p-3 w-12 h-12 flex items-center justify-center transition-transform group-hover:scale-110 duration-300;
  }

  .nav-link {
    @apply text-sm font-medium text-gray-600 transition-colors hover:text-gray-900;
  }
}

@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }

  33% {
    transform: translate(30px, -50px) scale(1.1);
  }

  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }

  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* Gradient Text Animation */
@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

.bg-gradient-animate {
  background-size: 200% 200%;
  animation: gradient 15s ease infinite;
}

/* Styles pour le calendrier avec couleurs pastel - DEPRECATED */
.rbc-calendar {
  font-family: 'Inter', sans-serif;
}

.rbc-header {
  padding: 10px 3px;
  font-weight: 600;
  font-size: 0.85rem;
  color: #4b5563;
}

.rbc-date-cell {
  padding-right: 10px;
  text-align: center;
  font-size: 0.9rem;
  color: #6b7280;
}

.rbc-date-cell.rbc-now {
  font-weight: bold;
  color: #4f46e5;
}

.rbc-toolbar button {
  color: #4b5563;
  border: 1px solid #e5e7eb;
}

.rbc-toolbar button:hover {
  background-color: #f3f4f6;
  color: #4f46e5;
}

.rbc-toolbar button.rbc-active {
  background-color: #4f46e5;
  color: white;
}

.rbc-toolbar button.rbc-active:hover {
  background-color: #4338ca;
  color: white;
}

.rbc-month-view {
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.rbc-day-bg.rbc-today {
  background-color: #f5f8ff;
}

.rbc-event {
  border-radius: 8px !important;
  padding: 3px 8px !important;
  background-color: #BDE0FE !important;
  /* Bleu clair pastel par défaut */
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  color: #333 !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
  transition: transform 0.1s ease-in-out, box-shadow 0.1s ease-in-out;
}

.rbc-event:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1) !important;
}

.rbc-event.rbc-selected {
  background-color: #D4EBFF !important;
  border: 2px solid #4f46e5 !important;
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2) !important;
}

.rbc-event-label {
  font-size: 0.7rem;
  font-weight: 500;
}

.rbc-event-content {
  font-size: 0.85rem;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Styles pour les événements selon leur statut */
.event-completed {
  background-color: #BDEBC8 !important;
  /* Vert pastel */
  color: #333 !important;
  border-left: 3px solid #10B981 !important;
}

.event-pending {
  background-color: #BDE0FE !important;
  /* Bleu clair pastel */
  color: #333 !important;
  border-left: 3px solid #3B82F6 !important;
}

.event-archived {
  background-color: #F3F4F6 !important;
  /* Gris très clair */
  color: #6B7280 !important;
  opacity: 0.7 !important;
}

/* Amélioration de l'apparence générale du calendrier */
.rbc-row-content {
  z-index: 0;
  /* Éviter les problèmes de superposition */
}

.rbc-month-row {
  overflow: visible !important;
  /* Permettre aux événements de déborder légèrement */
}

.rbc-row-segment {
  padding: 1px 2px;
}

.rbc-day-bg {
  transition: background-color 0.2s ease;
}

.rbc-day-bg:hover {
  background-color: #f9fafb;
}

/* Fix radical pour les problèmes d'aria-hidden avec les modals */
[data-radix-portal] {
  pointer-events: auto !important;
}

/* S'assurer que les éléments focusables dans les modals restent accessibles */
[role="dialog"] button,
[role="dialog"] input,
[role="dialog"] select,
[role="dialog"] textarea {
  pointer-events: auto !important;
}

/* Empêcher les problèmes d'aria-hidden sur les conteneurs principaux */
.min-h-screen[aria-hidden="true"] {
  pointer-events: auto !important;
  visibility: visible !important;
}

/* Force tous les éléments focusables à rester accessibles */
[aria-hidden="true"] button,
[aria-hidden="true"] input,
[aria-hidden="true"] select,
[aria-hidden="true"] textarea,
[aria-hidden="true"] [tabindex] {
  pointer-events: auto !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* S'assurer que la sidebar reste toujours cliquable */
aside.fixed,
nav,
aside,
.sidebar,
[role="navigation"],
nav a,
nav button,
aside a,
aside button,
aside .flex.items-center,
aside .space-y-1 a,
aside .space-y-1 button {
  pointer-events: auto !important;
  visibility: visible !important;
  opacity: 1 !important;
  z-index: 9999 !important;
}

/* Forcer spécifiquement les éléments de la sidebar UserSidebar */
aside.fixed.inset-y-0.left-0.w-64,
aside.fixed.inset-y-0.left-0.w-64 *,
aside.fixed.inset-y-0.left-0.w-64 a,
aside.fixed.inset-y-0.left-0.w-64 button {
  pointer-events: auto !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Forcer les liens de navigation à rester accessibles */
a[href],
button[type="button"],
[role="button"] {
  pointer-events: auto !important;
}

/* Solution ultra-agressive pour débloquer l'interface */
* {
  pointer-events: auto !important;
}

/* Forcer tous les éléments à être visibles */
[aria-hidden="true"] {
  visibility: visible !important;
  opacity: 1 !important;
  pointer-events: auto !important;
}

/* Empêcher complètement aria-hidden de bloquer */
.min-h-screen[aria-hidden="true"],
div[aria-hidden="true"],
main[aria-hidden="true"],
section[aria-hidden="true"] {
  pointer-events: auto !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* ===== MODERN CALENDAR DESIGN ===== */

/* Variables CSS pour la cohérence des couleurs */
:root {
  --calendar-bg: #f8fafc;
  --calendar-white: #ffffff;
  --calendar-border: #e2e8f0;
  --calendar-text-primary: #1a202c;
  --calendar-text-secondary: #4a5568;
  --calendar-text-muted: #718096;
  --calendar-today-bg: #ebf4ff;
  --calendar-hover-bg: #f7fafc;
  --calendar-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  --calendar-shadow-lg: 0 4px 6px rgba(0, 0, 0, 0.07);

  /* Couleurs pastel pour les événements */
  --event-pink: #fce7f3;
  --event-pink-border: #f9a8d4;
  --event-purple: #f3e8ff;
  --event-purple-border: #c084fc;
  --event-blue: #dbeafe;
  --event-blue-border: #60a5fa;
  --event-green: #d1fae5;
  --event-green-border: #6ee7b7;
  --event-yellow: #fef3c7;
  --event-yellow-border: #fbbf24;
  --event-orange: #fed7aa;
  --event-orange-border: #fb923c;
  --event-red: #fee2e2;
  --event-red-border: #f87171;
  --event-gray: #f3f4f6;
  --event-gray-border: #9ca3af;
}

/* Container principal du calendrier */
.modern-calendar-container {
  background: var(--calendar-white);
  border-radius: 16px;
  box-shadow: var(--calendar-shadow-lg);
  overflow: hidden;
  border: 1px solid var(--calendar-border);
}

/* Styles généraux pour tous les calendriers */
.modern-calendar-style {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  background: var(--calendar-white);
  border: none;
  border-radius: 16px;
  overflow: hidden;
}

/* En-têtes des jours de la semaine */
.modern-calendar-style .rbc-header {
  background: var(--calendar-bg);
  border-bottom: 1px solid var(--calendar-border);
  padding: 16px 8px;
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: var(--calendar-text-muted);
  text-align: center;
}

/* Vue mensuelle */
.modern-calendar-style .rbc-month-view {
  border: none;
  background: var(--calendar-white);
}

/* Cellules des jours */
.modern-calendar-style .rbc-day-bg {
  background: var(--calendar-white);
  border-right: 1px solid var(--calendar-border);
  border-bottom: 1px solid var(--calendar-border);
  min-height: 120px;
  transition: background-color 0.2s ease;
}

.modern-calendar-style .rbc-day-bg:hover {
  background: var(--calendar-hover-bg);
}

/* Jours hors du mois actuel */
.modern-calendar-style .rbc-off-range-bg {
  background: var(--calendar-bg);
  color: var(--calendar-text-muted);
}

/* Jour actuel */
.modern-calendar-style .rbc-today {
  background: var(--calendar-today-bg);
  position: relative;
}

/* Numéros des jours */
.modern-calendar-style .rbc-date-cell {
  padding: 8px 12px;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--calendar-text-primary);
  text-align: left;
}

.modern-calendar-style .rbc-date-cell.rbc-now {
  color: #3b82f6;
  font-weight: 700;
}

/* Styles des événements */
.modern-calendar-style .rbc-event {
  border: none;
  border-radius: 8px;
  padding: 4px 8px;
  margin: 2px 4px;
  font-size: 0.75rem;
  font-weight: 500;
  line-height: 1.2;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
}

.modern-calendar-style .rbc-event:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Couleurs des événements selon le statut */
.modern-calendar-style .rbc-event.status-pending {
  background: var(--event-blue);
  border-left-color: var(--event-blue-border);
  color: #1e40af;
}

.modern-calendar-style .rbc-event.status-completed {
  background: var(--event-green);
  border-left-color: var(--event-green-border);
  color: #166534;
}

.modern-calendar-style .rbc-event.status-archived {
  background: var(--event-gray);
  border-left-color: var(--event-gray-border);
  color: var(--calendar-text-muted);
  opacity: 0.7;
  text-decoration: line-through;
}

/* Couleurs personnalisées pour les événements */
.modern-calendar-style .rbc-event.color-pink {
  background: var(--event-pink);
  border-left-color: var(--event-pink-border);
  color: #be185d;
}

.modern-calendar-style .rbc-event.color-purple {
  background: var(--event-purple);
  border-left-color: var(--event-purple-border);
  color: #7c3aed;
}

.modern-calendar-style .rbc-event.color-yellow {
  background: var(--event-yellow);
  border-left-color: var(--event-yellow-border);
  color: #d97706;
}

.modern-calendar-style .rbc-event.color-orange {
  background: var(--event-orange);
  border-left-color: var(--event-orange-border);
  color: #ea580c;
}

.modern-calendar-style .rbc-event.color-red {
  background: var(--event-red);
  border-left-color: var(--event-red-border);
  color: #dc2626;
}

/* Barre d'outils */
.modern-calendar-style .rbc-toolbar {
  padding: 20px 24px;
  background: var(--calendar-white);
  border-bottom: 1px solid var(--calendar-border);
  margin-bottom: 0;
}

.modern-calendar-style .rbc-toolbar button {
  background: var(--calendar-white);
  border: 1px solid var(--calendar-border);
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--calendar-text-secondary);
  transition: all 0.2s ease;
  margin: 0 2px;
}

.modern-calendar-style .rbc-toolbar button:hover {
  background: var(--calendar-hover-bg);
  border-color: #cbd5e0;
  color: var(--calendar-text-primary);
}

.modern-calendar-style .rbc-toolbar button.rbc-active {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
  box-shadow: 0 1px 2px rgba(59, 130, 246, 0.3);
}

.modern-calendar-style .rbc-toolbar-label {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--calendar-text-primary);
  margin: 0 16px;
}

/* Masquer la barre d'outils si nécessaire */
.modern-calendar-style.hide-toolbar .rbc-toolbar {
  display: none;
}

/* Vue agenda */
.modern-calendar-style .rbc-agenda-view {
  background: var(--calendar-white);
}

.modern-calendar-style .rbc-agenda-view table.rbc-agenda-table {
  border: 1px solid var(--calendar-border);
  border-radius: 12px;
  overflow: hidden;
  background: var(--calendar-white);
}

.modern-calendar-style .rbc-agenda-view table.rbc-agenda-table thead>tr>th {
  background: var(--calendar-bg);
  padding: 16px;
  font-weight: 600;
  font-size: 0.875rem;
  color: var(--calendar-text-secondary);
  border-bottom: 1px solid var(--calendar-border);
}

.modern-calendar-style .rbc-agenda-view table.rbc-agenda-table tbody>tr>td {
  padding: 16px;
  border-bottom: 1px solid var(--calendar-border);
  color: var(--calendar-text-primary);
}

.modern-calendar-style .rbc-agenda-view table.rbc-agenda-table tbody>tr:last-child>td {
  border-bottom: none;
}

/* Responsive design */
@media (max-width: 768px) {
  .modern-calendar-style .rbc-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .modern-calendar-style .rbc-toolbar-label {
    text-align: center;
    margin: 0;
  }

  .modern-calendar-style .rbc-btn-group {
    display: flex;
    justify-content: center;
    gap: 4px;
  }

  .modern-calendar-style .rbc-day-bg {
    min-height: 80px;
  }

  .modern-calendar-style .rbc-event {
    font-size: 0.7rem;
    padding: 2px 6px;
    margin: 1px 2px;
  }
}

/* Animation pour les événements */
@keyframes eventAppear {
  from {
    opacity: 0;
    transform: scale(0.95);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

.modern-calendar-style .rbc-event {
  animation: eventAppear 0.2s ease-out;
}

/* Styles pour les événements sélectionnés */
.modern-calendar-style .rbc-event.rbc-selected {
  box-shadow: 0 0 0 2px #3b82f6;
  transform: translateY(-1px);
}

/* Amélioration de l'accessibilité */
.modern-calendar-style .rbc-event:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.modern-calendar-style .rbc-toolbar button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}