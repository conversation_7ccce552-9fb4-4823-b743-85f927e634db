import React from 'react';
import { Link, Outlet, useLocation } from 'react-router-dom';
import { Users, User, Settings, Layers, Calendar } from 'lucide-react';

const menuItems = [
  {
    label: 'Équipes',
    icon: <Layers className="w-5 h-5" />,
    path: '/admin/teams',
  },
  {
    label: 'Calendrier',
    icon: <Calendar className="w-5 h-5" />,
    path: '/admin/calendar',
  },
  {
    label: 'Profil',
    icon: <User className="w-5 h-5" />,
    path: '/admin/profile',
  },
  {
    label: 'Paramètres',
    icon: <Settings className="w-5 h-5" />,
    path: '/admin/settings',
  },
];

import { motion } from 'framer-motion';

const AdminLayout = () => {
  const layoutVariants = {
    initial: { opacity: 0, x: -20 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: 20 }
  };
  const location = useLocation();

  return (
    <div className="flex min-h-screen">
      {/* Sidebar */}
      <aside className="w-64 bg-white border-r border-gray-200 flex flex-col py-6 px-4">
        <div className="mb-8">
          <div className="flex items-center gap-2 mb-2">
            <Users className="w-7 h-7 text-purple-600" />
            <span className="font-bold text-xl text-purple-700">Notora</span>
          </div>
          <span className="text-xs text-gray-400">Gestion des Equipes</span>
        </div>
        <nav className="flex flex-col gap-2 flex-1">
          {menuItems.map((item) => (
            <Link
              key={item.path}
              to={item.path}
              className={`flex items-center gap-3 px-4 py-2 rounded-lg transition-colors font-medium text-gray-700 hover:bg-purple-50 hover:text-purple-700 ${location.pathname.startsWith(item.path) ? 'bg-purple-100 text-purple-700' : ''}`}
            >
              {item.icon}
              {item.label}
            </Link>
          ))}
        </nav>
        <div className="mt-auto pt-6">
          <Link to="/logout" className="flex items-center gap-2 text-red-600 hover:underline text-sm">
            <svg width="18" height="18" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-log-out"><path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/><polyline points="16 17 21 12 16 7"/><line x1="21" x2="9" y1="12" y2="12"/></svg>
            Déconnexion
          </Link>
        </div>
      </aside>
      {/* Main Content */}
      <motion.main 
        className="flex-1 bg-gray-50 p-8"
        variants={layoutVariants}
        initial="initial"
        animate="animate"
        exit="exit"
        transition={{ duration: 0.3, ease: 'easeInOut' }}
      >
        <Outlet />
      </motion.main>
    </div>
  );
};

export default AdminLayout;
