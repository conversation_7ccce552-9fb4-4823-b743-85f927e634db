import React, { useEffect, useState } from 'react';
import { Outlet, useNavigate } from 'react-router-dom';
import SuperAdminSidebar from '@/components/SuperAdminSidebar';
import { useAuth } from '@/contexts/AuthContext';
import { motion } from 'framer-motion';
import { toast } from 'react-toastify';

const SuperAdminLayout = () => {
  const { user, getAuthHeader } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const verifyAuth = async () => {
      try {
        const header = await getAuthHeader();
        if (!header || !user) {
          toast.error('Session expirée, veuillez vous reconnecter');
          navigate('/login');
          return;
        }
        if (user.role !== 'super_admin') {
          toast.error('Accès non autorisé');
          navigate('/unauthorized');
          return;
        }
      } catch (error) {
        console.error('Erreur de vérification:', error);
        navigate('/login');
      } finally {
        setLoading(false);
      }
    };

    verifyAuth();
  }, [user, getAuthHeader, navigate]);

  const getInitials = (name) => {
    if (!name) return 'SA';
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase();
  };

  const layoutVariants = {
    initial: { opacity: 0, x: -20 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: 20 }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#6B4EFF]"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="fixed top-4 right-4 z-50">
        <div className="h-12 w-12 rounded-xl bg-[#6B4EFF] flex items-center justify-center text-white text-lg font-medium shadow-xl hover:shadow-2xl transition-all duration-200 cursor-pointer">
          {getInitials(user?.name)}
        </div>
      </div>
      <SuperAdminSidebar />
      <motion.main 
        className="ml-64 p-8 pt-20"
        variants={layoutVariants}
        initial="initial"
        animate="animate"
        exit="exit"
        transition={{ duration: 0.3, ease: 'easeInOut' }}
      >
        <Outlet />
      </motion.main>
    </div>
  );
};

export default SuperAdminLayout;