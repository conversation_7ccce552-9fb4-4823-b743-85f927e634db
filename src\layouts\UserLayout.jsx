import React from 'react';
import { Navigate, Outlet } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import UserSidebar from '@/components/UserSidebar';
import MemberNavigation from '@/components/MemberNavigation';

import { motion } from 'framer-motion';

const UserLayout = () => {
  const layoutVariants = {
    initial: { opacity: 0, x: -20 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: 20 }
  };
    const { user } = useAuth();

    // Rediriger vers la page de connexion si l'utilisateur n'est pas connecté
    if (!user) {
        return <Navigate to="/login" replace />;
    }

    // L'utilisateur peut accéder à l'interface même avec un mot de passe temporaire
    // L'avertissement sera affiché dans le profil

    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
            <UserSidebar />
            <motion.main 
                className="ml-64 p-8"
                variants={layoutVariants}
                initial="initial"
                animate="animate"
                exit="exit"
                transition={{ duration: 0.3, ease: 'easeInOut' }}
            >
                <Outlet />
            </motion.main>
        </div>
    );
};

export default UserLayout;