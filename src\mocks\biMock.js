// Données simulées pour le tableau de bord BI
export const mockBiData = {
  user_activity: {
    total_users: 12,
    active_users: {
      day: 7,
      week: 9,
      month: 10,
      quarter: 11
    },
    active_percentage: {
      day: 58,
      week: 75,
      month: 83,
      quarter: 92
    },
    users_by_role: {
      super_admin: 1,
      admin: 2,
      employee: 6,
      client: 3
    },
    active_by_role: {
      day: {
        super_admin: 1,
        admin: 2,
        employee: 3,
        client: 1
      }
    },
    user_role_distribution: {
      super_admin: 1,
      admin: 2,
      employee: 6,
      client: 3
    }
  },
  platform_activity: {
    total_events: 15,
    total_team_tasks: 18,
    total_personal_tasks: 30,
    total_personal_events: 22,
    total_teams: 4
  },
  system_health: {
    status: 'optimal',
    percentage: 100,
    components: {
      database: { status: 'healthy', latency: 5 },
      api: { status: 'healthy', latency: 12 },
      storage: { status: 'healthy', usage: 42 }
    }
  },
  // Métriques pour le tableau de bord principal
  user_count: 12,
  team_count: 4,
  active_user_rate: 83,
  new_users_this_month: 3,
  user_growth_rate: 25,
  total_tasks: 48,
  total_events: 37,
  total_pomodoro_sessions: 15,
  active_users: 10,
  completed_tasks_this_month: 22,
  completed_events_this_month: 18
};
