import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useEvent } from '@/contexts/EventContext';
import { useTeam } from '@/contexts/TeamContext';
import { toast } from 'react-toastify';
import { Calendar as CalendarIcon, Plus, Filter, Search, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { EVENT_STATUS } from '@/config/constants';

// Composants
import EventCard from '@/components/events/EventCard';
import EventForm from '@/components/events/EventForm';

// Modals
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';

const AdminCalendarView = () => {
    const { user } = useAuth();
    const { teams } = useTeam();
    const { 
        events, 
        loading, 
        error, 
        fetchEvents, 
        createEvent, 
        updateEvent, 
        updateEventStatus, 
        deleteEvent,
        updateFilters,
        resetFilters
    } = useEvent();

    // États pour la gestion des modales
    const [showCreateModal, setShowCreateModal] = useState(false);
    const [showEditModal, setShowEditModal] = useState(false);
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [selectedEvent, setSelectedEvent] = useState(null);
    
    // États pour les filtres
    const [searchQuery, setSearchQuery] = useState('');
    const [teamFilter, setTeamFilter] = useState('');
    const [statusFilter, setStatusFilter] = useState('');
    const [dateFilter, setDateFilter] = useState('');
    
    // État pour le chargement des actions
    const [actionLoading, setActionLoading] = useState(false);

    // Charger les événements au montage du composant
    useEffect(() => {
        const loadEvents = async () => {
            try {
                await fetchEvents();
            } catch (err) {
                console.error('Erreur lors du chargement des événements:', err);
                toast.error('Erreur lors du chargement des événements');
            }
        };

        loadEvents();
    }, [fetchEvents]);

    // Filtrer les événements
    const filteredEvents = events.filter(event => {
        // Filtre par recherche (titre ou description)
        const matchesSearch = searchQuery === '' || 
            (event.title && event.title.toLowerCase().includes(searchQuery.toLowerCase())) ||
            (event.description && event.description.toLowerCase().includes(searchQuery.toLowerCase()));
        
        // Filtre par équipe
        const matchesTeam = teamFilter === '' || event.team_id === teamFilter;
        
        // Filtre par statut
        const matchesStatus = statusFilter === '' || event.status === statusFilter;
        
        // Filtre par date
        const matchesDate = dateFilter === '' || (
            event.start_date && new Date(event.start_date).toISOString().split('T')[0] === dateFilter
        );
        
        return matchesSearch && matchesTeam && matchesStatus && matchesDate;
    });

    // Appliquer les filtres
    const applyFilters = () => {
        updateFilters({
            team_id: teamFilter,
            status: statusFilter,
            date: dateFilter
        });
    };

    // Réinitialiser les filtres
    const handleResetFilters = () => {
        setSearchQuery('');
        setTeamFilter('');
        setStatusFilter('');
        setDateFilter('');
        resetFilters();
    };

    // Ouvrir la modale de création
    const handleOpenCreateModal = () => {
        setSelectedEvent(null);
        setShowCreateModal(true);
    };

    // Ouvrir la modale d'édition
    const handleOpenEditModal = (event) => {
        setSelectedEvent(event);
        setShowEditModal(true);
    };

    // Ouvrir la modale de suppression
    const handleOpenDeleteModal = (event) => {
        setSelectedEvent(event);
        setShowDeleteModal(true);
    };

    // Créer un nouvel événement
    const handleCreateEvent = async (eventData) => {
        setActionLoading(true);
        try {
            await createEvent(eventData);
            toast.success('Événement créé avec succès');
            setShowCreateModal(false);
            // Rafraîchir les événements
            await fetchEvents();
        } catch (error) {
            console.error('Erreur lors de la création de l\'événement:', error);
            toast.error(error.message || 'Erreur lors de la création de l\'événement');
        } finally {
            setActionLoading(false);
        }
    };

    // Mettre à jour un événement
    const handleUpdateEvent = async (eventData) => {
        if (!selectedEvent) return;
        
        setActionLoading(true);
        try {
            await updateEvent(selectedEvent.id, eventData);
            toast.success('Événement mis à jour avec succès');
            setShowEditModal(false);
            setSelectedEvent(null);
            // Rafraîchir les événements
            await fetchEvents();
        } catch (error) {
            console.error('Erreur lors de la mise à jour de l\'événement:', error);
            toast.error(error.message || 'Erreur lors de la mise à jour de l\'événement');
        } finally {
            setActionLoading(false);
        }
    };

    // Supprimer un événement
    const handleDeleteEvent = async () => {
        if (!selectedEvent) return;
        
        setActionLoading(true);
        try {
            await deleteEvent(selectedEvent.id);
            toast.success('Événement supprimé avec succès');
            setShowDeleteModal(false);
            setSelectedEvent(null);
            // Rafraîchir les événements
            await fetchEvents();
        } catch (error) {
            console.error('Erreur lors de la suppression de l\'événement:', error);
            toast.error('Erreur lors de la suppression de l\'événement');
        } finally {
            setActionLoading(false);
        }
    };

    if (loading) {
        return (
            <div className="flex justify-center items-center min-h-screen">
                <div className="text-xl font-semibold">Chargement des événements...</div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="flex justify-center items-center min-h-screen">
                <div className="text-xl font-semibold text-red-500">{error}</div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50 p-8">
            <div className="max-w-7xl mx-auto space-y-8">
                <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-8">
                    <h1 className="text-2xl font-bold text-gray-900">Gestion du Calendrier</h1>
                    
                    <Button 
                        onClick={handleOpenCreateModal}
                        className="bg-[#6B4EFF] hover:bg-[#5b3dff] text-white flex items-center gap-2"
                    >
                        <Plus className="h-4 w-4" />
                        Nouvel événement
                    </Button>
                </div>
                
                <div className="bg-white p-6 rounded-xl shadow-sm mb-8">
                    <div className="flex flex-col md:flex-row gap-4 items-end">
                        <div className="flex-grow space-y-2">
                            <label className="text-sm font-medium text-gray-700">Recherche</label>
                            <div className="relative">
                                <input
                                    type="text"
                                    placeholder="Rechercher un événement..."
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                    className="w-full pl-10 pr-4 py-2 bg-white border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#6B4EFF] focus:border-transparent"
                                />
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                            </div>
                        </div>
                        
                        <div className="flex-grow space-y-2">
                            <label className="text-sm font-medium text-gray-700">Équipe</label>
                            <select
                                value={teamFilter}
                                onChange={(e) => setTeamFilter(e.target.value)}
                                className="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#6B4EFF] focus:border-transparent"
                            >
                                <option value="">Toutes les équipes</option>
                                {teams.map(team => (
                                    <option key={team.id} value={team.id}>{team.name}</option>
                                ))}
                            </select>
                        </div>
                        
                        <div className="flex-grow space-y-2">
                            <label className="text-sm font-medium text-gray-700">Statut</label>
                            <select
                                value={statusFilter}
                                onChange={(e) => setStatusFilter(e.target.value)}
                                className="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#6B4EFF] focus:border-transparent"
                            >
                                <option value="">Tous les statuts</option>
                                <option value={EVENT_STATUS.PENDING}>En attente</option>
                                <option value={EVENT_STATUS.COMPLETED}>Terminé</option>
                                <option value={EVENT_STATUS.ARCHIVED}>Archivé</option>
                            </select>
                        </div>
                        
                        <div className="flex-grow space-y-2">
                            <label className="text-sm font-medium text-gray-700">Date</label>
                            <Input
                                type="date"
                                value={dateFilter}
                                onChange={(e) => setDateFilter(e.target.value)}
                                className="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#6B4EFF] focus:border-transparent"
                            />
                        </div>
                        
                        <div className="flex gap-2">
                            <Button 
                                variant="outline" 
                                onClick={applyFilters}
                                className="px-4 py-2 border border-gray-200 rounded-lg hover:bg-gray-100"
                            >
                                <Filter className="h-4 w-4 mr-2" />
                                Filtrer
                            </Button>
                            
                            <Button 
                                variant="outline" 
                                onClick={handleResetFilters}
                                className="px-4 py-2 border border-gray-200 rounded-lg hover:bg-gray-100"
                            >
                                <X className="h-4 w-4 mr-2" />
                                Réinitialiser
                            </Button>
                        </div>
                    </div>
                </div>

                {filteredEvents.length === 0 ? (
                    <div className="text-center py-12 bg-white rounded-xl border border-gray-100 shadow-sm">
                        <CalendarIcon className="w-12 h-12 mx-auto text-[#6B4EFF] mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 mb-2">
                            Aucun événement trouvé
                        </h3>
                        <p className="text-gray-500">
                            Aucun événement ne correspond à vos critères de recherche
                        </p>
                    </div>
                ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {filteredEvents.map(event => (
                            <EventCard
                                key={event.id}
                                event={event}
                                onEdit={() => handleOpenEditModal(event)}
                                onDelete={() => handleOpenDeleteModal(event)}
                            />
                        ))}
                    </div>
                )}
            </div>

            {/* Modal de création d'événement */}
            <Dialog open={showCreateModal} onOpenChange={setShowCreateModal}>
                <DialogContent className="sm:max-w-2xl">
                    <DialogHeader>
                        <DialogTitle>Créer un nouvel événement</DialogTitle>
                        <DialogDescription>
                            Remplissez les détails pour créer un nouvel événement
                        </DialogDescription>
                    </DialogHeader>
                    <EventForm 
                        onSubmit={handleCreateEvent} 
                        onCancel={() => setShowCreateModal(false)}
                        isLoading={actionLoading}
                    />
                </DialogContent>
            </Dialog>

            {/* Modal d'édition d'événement */}
            <Dialog open={showEditModal} onOpenChange={setShowEditModal}>
                <DialogContent className="sm:max-w-2xl">
                    <DialogHeader>
                        <DialogTitle>Modifier l'événement</DialogTitle>
                        <DialogDescription>
                            Modifiez les détails de l'événement
                        </DialogDescription>
                    </DialogHeader>
                    <EventForm 
                        event={selectedEvent}
                        onSubmit={handleUpdateEvent} 
                        onCancel={() => setShowEditModal(false)}
                        isLoading={actionLoading}
                    />
                </DialogContent>
            </Dialog>

            {/* Modal de suppression d'événement */}
            <Dialog open={showDeleteModal} onOpenChange={setShowDeleteModal}>
                <DialogContent className="sm:max-w-md">
                    <DialogHeader>
                        <DialogTitle>Supprimer l'événement</DialogTitle>
                        <DialogDescription>
                            Êtes-vous sûr de vouloir supprimer cet événement ? Cette action est irréversible.
                        </DialogDescription>
                    </DialogHeader>
                    <div className="flex justify-end gap-3 pt-4">
                        <Button
                            variant="outline"
                            onClick={() => setShowDeleteModal(false)}
                            disabled={actionLoading}
                        >
                            Annuler
                        </Button>
                        <Button
                            variant="destructive"
                            onClick={handleDeleteEvent}
                            disabled={actionLoading}
                            className="bg-red-600 hover:bg-red-700 text-white"
                        >
                            {actionLoading ? 'Suppression...' : 'Supprimer'}
                        </Button>
                    </div>
                </DialogContent>
            </Dialog>
        </div>
    );
};

export default AdminCalendarView;