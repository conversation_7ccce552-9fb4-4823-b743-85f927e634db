import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useEvent } from '@/contexts/EventContext';
import { useTeam } from '@/contexts/TeamContext';
import { toast } from 'react-toastify';
import { Calendar as CalendarIcon, Plus, Filter, Search, X, Edit, Trash2, CheckCircle, Clock, AlertCircle, RotateCcw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { EVENT_STATUS } from '@/config/constants';

// Composants
import EventForm from '@/components/events/EventForm';
import EventPermissionGate from '@/components/events/EventPermissionGate';
import RolePermissionGate from '@/components/RolePermissionGate';

// Modals
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';

const AdminEventsList = () => {
    const { user } = useAuth();
    const { teams } = useTeam();
    const {
        events,
        loading,
        error,
        fetchEvents,
        createEvent,
        updateEvent,
        updateEventStatus,
        deleteEvent,
        archiveEvent,
        unarchiveEvent,
        updateFilters,
        resetFilters
    } = useEvent();

    // États pour la gestion des modales
    const [showCreateModal, setShowCreateModal] = useState(false);
    const [showEditModal, setShowEditModal] = useState(false);
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [selectedEvent, setSelectedEvent] = useState(null);

    // États pour les filtres
    const [searchQuery, setSearchQuery] = useState('');
    const [teamFilter, setTeamFilter] = useState('');
    const [statusFilter, setStatusFilter] = useState('');
    const [dateFilter, setDateFilter] = useState('');

    // État pour le chargement des actions
    const [actionLoading, setActionLoading] = useState(false);

    // Charger les événements au montage du composant
    useEffect(() => {
        const loadEvents = async () => {
            try {
                await fetchEvents();
            } catch (err) {
                console.error('Erreur lors du chargement des événements:', err);
                toast.error('Erreur lors du chargement des événements');
            }
        };

        loadEvents();
    }, [fetchEvents]);

    // Filtrer les événements
    const filteredEvents = events.filter(event => {
        // Filtre par recherche (titre ou description)
        const matchesSearch = searchQuery === '' ||
            (event.title && event.title.toLowerCase().includes(searchQuery.toLowerCase())) ||
            (event.description && event.description.toLowerCase().includes(searchQuery.toLowerCase()));

        // Filtre par équipe
        const matchesTeam = teamFilter === '' || event.team_id === teamFilter;

        // Filtre par statut
        const matchesStatus = statusFilter === '' || event.status === statusFilter;

        // Filtre par date
        const matchesDate = dateFilter === '' || (
            event.start_date && new Date(event.start_date).toISOString().split('T')[0] === dateFilter
        );

        return matchesSearch && matchesTeam && matchesStatus && matchesDate;
    });

    // Appliquer les filtres
    const applyFilters = () => {
        updateFilters({
            team_id: teamFilter,
            status: statusFilter,
            date: dateFilter
        });
    };

    // Réinitialiser les filtres
    const handleResetFilters = () => {
        setSearchQuery('');
        setTeamFilter('');
        setStatusFilter('');
        setDateFilter('');
        resetFilters();
    };

    // Ouvrir la modale de création
    const handleOpenCreateModal = () => {
        setSelectedEvent(null);
        setShowCreateModal(true);
    };

    // Ouvrir la modale d'édition
    const handleOpenEditModal = (event) => {
        setSelectedEvent(event);
        setShowEditModal(true);
    };

    // Ouvrir la modale de suppression
    const handleOpenDeleteModal = (event) => {
        setSelectedEvent(event);
        setShowDeleteModal(true);
    };

    // Créer un nouvel événement
    const handleCreateEvent = async (eventData) => {
        setActionLoading(true);
        try {
            await createEvent(eventData);
            toast.success('Événement créé avec succès');
            setShowCreateModal(false);
            // Rafraîchir les événements
            await fetchEvents();
        } catch (error) {
            console.error('Erreur lors de la création de l\'événement:', error);
            toast.error(error.message || 'Erreur lors de la création de l\'événement');
        } finally {
            setActionLoading(false);
        }
    };

    // Mettre à jour un événement
    const handleUpdateEvent = async (eventData) => {
        setActionLoading(true);
        try {
            await updateEvent(selectedEvent.id, eventData);
            toast.success('Événement mis à jour avec succès');
            setShowEditModal(false);
            setSelectedEvent(null);
            // Rafraîchir les événements
            await fetchEvents();
        } catch (error) {
            console.error('Erreur lors de la mise à jour de l\'événement:', error);
            toast.error(error.message || 'Erreur lors de la mise à jour de l\'événement');
        } finally {
            setActionLoading(false);
        }
    };

    // Supprimer un événement
    const handleDeleteEvent = async () => {
        setActionLoading(true);
        try {
            await deleteEvent(selectedEvent.id);
            toast.success('Événement supprimé avec succès');
            setShowDeleteModal(false);
            setSelectedEvent(null);
            // Rafraîchir les événements
            await fetchEvents();
        } catch (error) {
            console.error('Erreur lors de la suppression de l\'événement:', error);
            toast.error(error.message || 'Erreur lors de la suppression de l\'événement');
        } finally {
            setActionLoading(false);
        }
    };

    // Désarchiver un événement
    const handleUnarchiveEvent = async (eventId) => {
        if (!window.confirm('Êtes-vous sûr de vouloir désarchiver cet événement ?')) return;

        setActionLoading(true);
        try {
            await unarchiveEvent(eventId);
            toast.success('Événement désarchivé avec succès');
            // Rafraîchir les événements
            await fetchEvents();
        } catch (error) {
            console.error('Erreur lors du désarchivage de l\'événement:', error);
            toast.error(error.message || 'Erreur lors du désarchivage de l\'événement');
        } finally {
            setActionLoading(false);
        }
    };

    // Formater le statut pour l'affichage
    const formatStatus = (status) => {
        switch (status) {
            case 'pending':
                return (
                    <span className="flex items-center gap-1 text-yellow-600 bg-yellow-100 px-2 py-1 rounded-full text-xs">
                        <Clock className="w-3 h-3" />
                        En attente
                    </span>
                );
            case 'completed':
                return (
                    <span className="flex items-center gap-1 text-green-600 bg-green-100 px-2 py-1 rounded-full text-xs">
                        <CheckCircle className="w-3 h-3" />
                        Terminé
                    </span>
                );
            case 'archived':
                return (
                    <span className="flex items-center gap-1 text-gray-600 bg-gray-100 px-2 py-1 rounded-full text-xs">
                        <AlertCircle className="w-3 h-3" />
                        Archivé
                    </span>
                );
            default:
                return status;
        }
    };

    if (loading) {
        return (
            <div className="flex justify-center items-center min-h-screen">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#6B4EFF]"></div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="flex justify-center items-center min-h-screen">
                <div className="text-xl font-semibold text-red-500">{error}</div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50 p-8">
            <div className="max-w-7xl mx-auto space-y-8">
                <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-8">
                    <h1 className="text-2xl font-bold text-gray-900">Liste des événements</h1>

                    <RolePermissionGate allowedRoles="admin" permission="manage_events">
                        <Button
                            onClick={handleOpenCreateModal}
                            className="bg-[#6B4EFF] hover:bg-[#5b3dff] text-white flex items-center gap-2"
                        >
                            <Plus className="h-4 w-4" />
                            Nouvel événement
                        </Button>
                    </RolePermissionGate>
                </div>

                <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
                    <div className="flex flex-col md:flex-row gap-4 mb-6">
                        <div className="flex-grow relative">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                            <Input
                                type="text"
                                placeholder="Rechercher un événement..."
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                                className="pl-10"
                            />
                        </div>
                        <div className="flex gap-2">
                            <select
                                id="team-filter"
                                name="team-filter"
                                value={teamFilter}
                                onChange={(e) => setTeamFilter(e.target.value)}
                                className="border border-gray-300 rounded-md px-3 py-2 text-sm"
                            >
                                <option value="">Toutes les équipes</option>
                                {teams.map(team => (
                                    <option key={team.id} value={team.id}>{team.name}</option>
                                ))}
                            </select>
                            <select
                                id="status-filter"
                                name="status-filter"
                                value={statusFilter}
                                onChange={(e) => setStatusFilter(e.target.value)}
                                className="border border-gray-300 rounded-md px-3 py-2 text-sm"
                            >
                                <option value="">Tous les statuts</option>
                                {Object.entries(EVENT_STATUS).map(([key, value]) => (
                                    <option key={key} value={key}>{value}</option>
                                ))}
                            </select>
                            <Input
                                type="date"
                                value={dateFilter}
                                onChange={(e) => setDateFilter(e.target.value)}
                                className="w-auto"
                            />
                            <Button
                                variant="outline"
                                onClick={handleResetFilters}
                                className="flex items-center gap-1"
                            >
                                <X className="h-4 w-4" />
                                Réinitialiser
                            </Button>
                        </div>
                    </div>

                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">TITRE</th>
                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ÉQUIPE</th>
                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">DATE</th>
                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">STATUT</th>
                                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">ACTIONS</th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {filteredEvents.length === 0 ? (
                                    <tr>
                                        <td colSpan="5" className="px-6 py-4 text-center text-sm text-gray-500">
                                            Aucun événement trouvé
                                        </td>
                                    </tr>
                                ) : (
                                    filteredEvents.map((event) => (
                                        <tr key={event.id} className="hover:bg-gray-50">
                                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{event.title}</td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{event.team_name}</td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {event.start_date && new Date(event.start_date).toLocaleDateString('fr-FR', {
                                                    year: 'numeric',
                                                    month: 'long',
                                                    day: 'numeric'
                                                })}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {formatStatus(event.status)}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                <div className="flex justify-end gap-2">
                                                    {/* Bouton de modification (seulement pour les événements non archivés) */}
                                                    {event.status !== 'archived' && (
                                                        <EventPermissionGate event={event} permissionType="canManage">
                                                            <Button
                                                                variant="ghost"
                                                                size="sm"
                                                                onClick={() => handleOpenEditModal(event)}
                                                                className="text-blue-600 hover:text-blue-800"
                                                                title="Modifier"
                                                            >
                                                                <Edit className="h-4 w-4" />
                                                            </Button>
                                                        </EventPermissionGate>
                                                    )}

                                                    {/* Bouton de désarchivage (seulement pour les événements archivés) */}
                                                    {event.status === 'archived' && (
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => handleUnarchiveEvent(event.id)}
                                                            className="text-green-600 hover:text-green-800"
                                                            disabled={actionLoading}
                                                            title="Désarchiver"
                                                        >
                                                            <RotateCcw className="h-4 w-4" />
                                                        </Button>
                                                    )}

                                                    {/* Bouton de suppression (toujours visible) */}
                                                    <EventPermissionGate event={event} permissionType="canManage">
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => handleOpenDeleteModal(event)}
                                                            className="text-red-600 hover:text-red-800"
                                                            title="Supprimer"
                                                        >
                                                            <Trash2 className="h-4 w-4" />
                                                        </Button>
                                                    </EventPermissionGate>
                                                </div>
                                            </td>
                                        </tr>
                                    ))
                                )}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            {/* Modal de création d'événement */}
            <Dialog open={showCreateModal} onOpenChange={setShowCreateModal}>
                <DialogContent className="sm:max-w-2xl">
                    <DialogHeader>
                        <DialogTitle>Créer un nouvel événement</DialogTitle>
                        <DialogDescription>
                            Remplissez les détails pour créer un nouvel événement
                        </DialogDescription>
                    </DialogHeader>
                    <EventForm
                        onSubmit={handleCreateEvent}
                        onCancel={() => setShowCreateModal(false)}
                        isLoading={actionLoading}
                    />
                </DialogContent>
            </Dialog>

            {/* Modal d'édition d'événement */}
            <Dialog open={showEditModal} onOpenChange={setShowEditModal}>
                <DialogContent className="sm:max-w-2xl">
                    <DialogHeader>
                        <DialogTitle>Modifier l'événement</DialogTitle>
                        <DialogDescription>
                            Modifiez les détails de l'événement
                        </DialogDescription>
                    </DialogHeader>
                    <EventForm
                        event={selectedEvent}
                        onSubmit={handleUpdateEvent}
                        onCancel={() => setShowEditModal(false)}
                        isLoading={actionLoading}
                    />
                </DialogContent>
            </Dialog>

            {/* Modal de suppression d'événement */}
            <Dialog open={showDeleteModal} onOpenChange={setShowDeleteModal}>
                <DialogContent className="sm:max-w-md">
                    <DialogHeader>
                        <DialogTitle>Supprimer l'événement</DialogTitle>
                        <DialogDescription>
                            Êtes-vous sûr de vouloir supprimer cet événement ? Cette action est irréversible.
                        </DialogDescription>
                    </DialogHeader>
                    <div className="flex justify-end gap-3 mt-4">
                        <Button
                            variant="outline"
                            onClick={() => setShowDeleteModal(false)}
                        >
                            Annuler
                        </Button>
                        <Button
                            variant="destructive"
                            onClick={handleDeleteEvent}
                            disabled={actionLoading}
                        >
                            {actionLoading ? 'Suppression...' : 'Supprimer'}
                        </Button>
                    </div>
                </DialogContent>
            </Dialog>
        </div>
    );
};

export default AdminEventsList;
