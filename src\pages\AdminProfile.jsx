import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'react-toastify';
import axios from 'axios';
import { Eye, EyeOff, Lock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

const API_URL = 'http://localhost:8000/api';

const AdminProfile = () => {
  const { user, getAuthHeader, completePasswordChange } = useAuth();
  const [showPasswordForm, setShowPasswordForm] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [passwordData, setPasswordData] = useState({
    current_password: '',
    new_password: '',
    confirm_password: ''
  });
  const [errors, setErrors] = useState({});

  const handlePasswordChange = (e) => {
    const { name, value } = e.target;
    setPasswordData(prev => ({
      ...prev,
      [name]: value
    }));
    // Effacer l'erreur lorsque l'utilisateur commence à taper
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    // Valider le mot de passe actuel
    if (!passwordData.current_password) {
      newErrors.current_password = 'Le mot de passe actuel est requis';
    }
    
    // Valider le nouveau mot de passe
    if (!passwordData.new_password) {
      newErrors.new_password = 'Le nouveau mot de passe est requis';
    } else if (passwordData.new_password.length < 6) {
      newErrors.new_password = 'Le mot de passe doit contenir au moins 6 caractères';
    } else if (!/[A-Z]/.test(passwordData.new_password)) {
      newErrors.new_password = 'Le mot de passe doit contenir au moins une lettre majuscule';
    } else if (!/[a-z]/.test(passwordData.new_password)) {
      newErrors.new_password = 'Le mot de passe doit contenir au moins une lettre minuscule';
    } else if (!/\d/.test(passwordData.new_password)) {
      newErrors.new_password = 'Le mot de passe doit contenir au moins un chiffre';
    }
    
    // Valider la confirmation du mot de passe
    if (!passwordData.confirm_password) {
      newErrors.confirm_password = 'La confirmation du mot de passe est requise';
    } else if (passwordData.new_password !== passwordData.confirm_password) {
      newErrors.confirm_password = 'Les mots de passe ne correspondent pas';
    }
    
    // Vérifier si le nouveau mot de passe est différent de l'actuel
    if (passwordData.current_password && passwordData.new_password && 
        passwordData.current_password === passwordData.new_password) {
      newErrors.new_password = 'Le nouveau mot de passe doit être différent de l\'ancien';
    }
    
    return newErrors;
  };

  const handleUpdatePassword = async (e) => {
    e.preventDefault();
    
    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    setIsLoading(true);
    try {
      const response = await axios.put(
        `${API_URL}/profile/change-password/`,
        {
          current_password: passwordData.current_password,
          new_password: passwordData.new_password,
          confirm_password: passwordData.confirm_password
        },
        {
          headers: {
            ...getAuthHeader(),
            'Content-Type': 'application/json'
          }
        }
      );
      
      toast.success(response.data.message || 'Mot de passe mis à jour avec succès');
      setPasswordData({
        current_password: '',
        new_password: '',
        confirm_password: ''
      });
      setShowPasswordForm(false);
      
      // Mettre à jour le statut du mot de passe temporaire
      if (user?.temp_password_required) {
        completePasswordChange();
      }
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Erreur lors du changement de mot de passe';
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-xl mx-auto bg-white dark:bg-gray-800 rounded-xl shadow-sm p-8">
      <h1 className="text-2xl font-bold mb-4 dark:text-white">Mon Profil</h1>
      
      {user?.temp_password_required && (
        <div className="mb-6 p-4 bg-amber-50 dark:bg-amber-900/30 border-l-4 border-amber-500 text-amber-700 dark:text-amber-200">
          <p className="font-medium">Vous utilisez actuellement un mot de passe temporaire</p>
          <p className="text-sm mt-1">Pour des raisons de sécurité, nous vous recommandons de le changer dès que possible.</p>
        </div>
      )}
      
      <div className="space-y-4 mb-6">
        <div>
          <span className="font-semibold text-gray-700 dark:text-gray-300">Nom :</span>
          <span className="ml-2 text-gray-900 dark:text-gray-100">{user?.name}</span>
        </div>
        <div>
          <span className="font-semibold text-gray-700 dark:text-gray-300">Email :</span>
          <span className="ml-2 text-gray-900 dark:text-gray-100">{user?.email}</span>
        </div>
        <div>
          <span className="font-semibold text-gray-700 dark:text-gray-300">Rôle :</span>
          <span className="ml-2 text-purple-700 dark:text-purple-400 capitalize">{user?.role}</span>
        </div>
      </div>
      
      {!showPasswordForm ? (
        <Button 
          onClick={() => setShowPasswordForm(true)}
          variant="outline"
          className="mt-4"
        >
          Changer mon mot de passe
        </Button>
      ) : (
        <div className="mt-6 border-t pt-6">
          <h2 className="text-xl font-semibold mb-4 dark:text-white">Changer mon mot de passe</h2>
          <form onSubmit={handleUpdatePassword} className="space-y-4">
            <div>
              <Label htmlFor="current_password" className="flex items-center gap-2 text-gray-700 dark:text-gray-200">
                <Lock className="w-4 h-4" />
                Mot de passe actuel
              </Label>
              <div className="mt-1 relative">
                <Input
                  id="current_password"
                  name="current_password"
                  type={showCurrentPassword ? "text" : "password"}
                  autoComplete="current-password"
                  placeholder="Entrez votre mot de passe actuel"
                  value={passwordData.current_password}
                  onChange={handlePasswordChange}
                  className={`block w-full rounded-lg border ${errors.current_password ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-gray-600'} pr-10`}
                />
                <button
                  type="button"
                  onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                  className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600 dark:text-gray-300 dark:hover:text-gray-100"
                >
                  {showCurrentPassword ? (
                    <EyeOff className="h-5 w-5" />
                  ) : (
                    <Eye className="h-5 w-5" />
                  )}
                </button>
              </div>
              {errors.current_password && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.current_password}</p>
              )}
            </div>
            
            <div>
              <Label htmlFor="new_password" className="flex items-center gap-2 text-gray-700 dark:text-gray-200">
                <Lock className="w-4 h-4" />
                Nouveau mot de passe
              </Label>
              <div className="mt-1 relative">
                <Input
                  id="new_password"
                  name="new_password"
                  type={showNewPassword ? "text" : "password"}
                  autoComplete="new-password"
                  placeholder="Entrez votre nouveau mot de passe"
                  value={passwordData.new_password}
                  onChange={handlePasswordChange}
                  className={`block w-full rounded-lg border ${errors.new_password ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-gray-600'} pr-10`}
                />
                <button
                  type="button"
                  onClick={() => setShowNewPassword(!showNewPassword)}
                  className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600 dark:text-gray-300 dark:hover:text-gray-100"
                >
                  {showNewPassword ? (
                    <EyeOff className="h-5 w-5" />
                  ) : (
                    <Eye className="h-5 w-5" />
                  )}
                </button>
              </div>
              {errors.new_password && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.new_password}</p>
              )}
            </div>
            
            <div>
              <Label htmlFor="confirm_password" className="flex items-center gap-2 text-gray-700 dark:text-gray-200">
                <Lock className="w-4 h-4" />
                Confirmer le mot de passe
              </Label>
              <div className="mt-1 relative">
                <Input
                  id="confirm_password"
                  name="confirm_password"
                  type={showConfirmPassword ? "text" : "password"}
                  autoComplete="new-password"
                  placeholder="Confirmez votre nouveau mot de passe"
                  value={passwordData.confirm_password}
                  onChange={handlePasswordChange}
                  className={`block w-full rounded-lg border ${errors.confirm_password ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-gray-600'} pr-10`}
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600 dark:text-gray-300 dark:hover:text-gray-100"
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-5 w-5" />
                  ) : (
                    <Eye className="h-5 w-5" />
                  )}
                </button>
              </div>
              {errors.confirm_password && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.confirm_password}</p>
              )}
            </div>
            
            <div className="flex gap-3">
              <Button 
                type="submit" 
                disabled={isLoading}
              >
                {isLoading ? 'Chargement...' : 'Changer le mot de passe'}
              </Button>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => {
                  setShowPasswordForm(false);
                  setPasswordData({
                    current_password: '',
                    new_password: '',
                    confirm_password: ''
                  });
                  setErrors({});
                }}
              >
                Annuler
              </Button>
            </div>
          </form>
        </div>
      )}
    </div>
  );
};

export default AdminProfile;
