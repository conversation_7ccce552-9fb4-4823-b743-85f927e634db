import React, { useState, useEffect, useMemo } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useTeam } from '@/contexts/TeamContext';
import { showSuccessToast, showErrorToast } from '@/utils/toastUtils';
import teamService from '@/services/teamService';
import {
    Search,
    Plus,
    MoreVertical,
    User,
    Users,
    Circle,
    Edit,
    Trash2,
    UserPlus,
    UserMinus,
    X,
    Mail
} from 'lucide-react';

// Composants
import TeamHeader from '@/components/teams/TeamHeader';
import SearchBar from '@/components/teams/SearchBar';
import TeamFilters from '@/components/teams/TeamFilters';
import TeamCard from '@/components/teams/TeamCard';
import EmptyState from '@/components/teams/EmptyState';
import LoadingState from '@/components/teams/LoadingState';
import CreateTeamModal from '@/components/teams/modals/CreateTeamModal';
import EditTeamModal from '@/components/teams/modals/EditTeamModal';
import AddMemberModal from '@/components/teams/modals/AddMemberModal';

const checkTeamPermissions = (user, team) => {
    const defaultPermissions = {
        canManage: false,
        canAddMembers: false,
        canRemoveMembers: false,
        canViewDetails: false,
        canCreateTeam: false
    };

    if (!user) return defaultPermissions;

    const isAdmin = user.role === 'admin';

    if (!team) {
        return {
            ...defaultPermissions,
            canCreateTeam: isAdmin
        };
    }

    // Pour les admins, ils peuvent gérer seulement les équipes dont ils sont responsables
    // Pour les employés, ils peuvent voir les détails des équipes dont ils sont membres
    const isTeamResponsable = team.is_responsable;

    return {
        canManage: isAdmin && isTeamResponsable,
        canAddMembers: isAdmin && isTeamResponsable,
        canRemoveMembers: isAdmin && isTeamResponsable,
        canViewDetails: (isAdmin && isTeamResponsable) || team.is_member,
        canCreateTeam: isAdmin
    };
};

const AdminTeams = () => {
    const { user } = useAuth();
    const {
        teams,
        loading: contextLoading,
        error,
        fetchTeams,
        createTeam,
        updateTeam,
        deleteTeam,
        addMember,
        removeMember
    } = useTeam();

    const [selectedTeam, setSelectedTeam] = useState(null);
    const [searchQuery, setSearchQuery] = useState('');
    const [showCreateModal, setShowCreateModal] = useState(false);
    const [showEditModal, setShowEditModal] = useState(false);
    const [showAddMemberModal, setShowAddMemberModal] = useState(false);
    const [statusFilter, setStatusFilter] = useState('all');
    const [roleFilter, setRoleFilter] = useState('all');
    const [newTeam, setNewTeam] = useState({
        name: '',
        description: ''
    });
    const [editTeam, setEditTeam] = useState({
        name: '',
        description: ''
    });
    const [newMember, setNewMember] = useState({
        email: '',
        role: 'employee'
    });

    useEffect(() => {
        const loadTeams = async () => {
            if (!user) {
                toast.error('Veuillez vous connecter');
                return;
            }
            if (user.role === 'super_admin') {
                toast.error('Accès non autorisé pour les super administrateurs');
                return;
            }
            try {
                await fetchTeams();
            } catch (err) {
                console.error('Error loading teams:', err);
                toast.error(err.message || 'Erreur lors du chargement des équipes');
            }
        };

        loadTeams();
    }, [fetchTeams, user]);

    const handleCreateTeam = async (e) => {
        e.preventDefault();
        if (!user) {
            showErrorToast('Veuillez vous connecter');
            return;
        }
        if (!checkTeamPermissions(user, {}).canCreateTeam) {
            showErrorToast('Vous n\'avez pas la permission de créer une équipe');
            return;
        }
        try {
            if (!newTeam.name || newTeam.name.trim().length < 3) {
                showErrorToast('Le nom de l\'équipe doit contenir au moins 3 caractères');
                return;
            }
            const teamData = {
                name: newTeam.name.trim(),
                description: newTeam.description.trim()
            };
            await createTeam(teamData);
            setShowCreateModal(false);
            setNewTeam({ name: '', description: '' });
            // Le message de succès est déjà affiché par le TeamContext
        } catch (err) {
            console.error('Error creating team:', err);
            showErrorToast(err.message || 'Erreur lors de la création de l\'équipe');
        }
    };

    const handleEditClick = (team) => {
        setSelectedTeam(team);
        setEditTeam({
            name: team.name,
            description: team.description || ''
        });
        setShowEditModal(true);
    };

    const handleEditTeam = async (e) => {
        e.preventDefault();
        if (!user) {
            showErrorToast('Veuillez vous connecter');
            return;
        }
        if (!selectedTeam) {
            showErrorToast('Aucune équipe sélectionnée');
            return;
        }
        if (!checkTeamPermissions(user, selectedTeam).canManage) {
            showErrorToast('Vous n\'avez pas la permission de modifier cette équipe');
            return;
        }
        try {
            if (!editTeam.name || editTeam.name.trim().length < 3) {
                showErrorToast('Le nom de l\'équipe doit contenir au moins 3 caractères');
                return;
            }
            await updateTeam(selectedTeam.id, {
                name: editTeam.name.trim(),
                description: editTeam.description.trim()
            });
            setShowEditModal(false);
            setSelectedTeam(null);
            setEditTeam({ name: '', description: '' });
            showSuccessToast('Équipe modifiée avec succès');
        } catch (err) {
            console.error('Error updating team:', err);
            showErrorToast(err.message || 'Erreur lors de la modification de l\'équipe');
        }
    };

    const handleDeleteTeam = async (teamId) => {
        if (!user) {
            showErrorToast('Veuillez vous connecter');
            return;
        }
        const teamToDelete = teams.find(t => t.id === teamId);
        if (!teamToDelete) {
            showErrorToast('Équipe non trouvée');
            return;
        }
        if (!checkTeamPermissions(user, teamToDelete).canManage) {
            showErrorToast('Vous n\'avez pas la permission de supprimer cette équipe');
            return;
        }
        if (!window.confirm('Êtes-vous sûr de vouloir supprimer cette équipe ?')) {
            return;
        }

        try {
            await deleteTeam(teamId);
            showSuccessToast('Équipe supprimée avec succès');
        } catch (err) {
            console.error('Error deleting team:', err);
            showErrorToast(err.message || 'Erreur lors de la suppression de l\'équipe');
        }
    };

    const handleAddMember = async (memberData) => {
        if (!user) {
            showErrorToast('Veuillez vous connecter');
            return;
        }
        if (!selectedTeam) {
            showErrorToast('Aucune équipe sélectionnée');
            return;
        }
        if (!checkTeamPermissions(user, selectedTeam).canAddMembers) {
            showErrorToast('Vous n\'avez pas la permission d\'ajouter des membres à cette équipe');
            return;
        }

        try {
            // Vérifier que nous avons un ID utilisateur valide
            if (!memberData.user_id) {
                showErrorToast('ID utilisateur invalide');
                return;
            }

            // Ajouter le membre via le contexte
            const response = await addMember(selectedTeam.id, memberData);

            // Afficher un message de succès une seule fois
            showSuccessToast('Membre ajouté avec succès');

            // Fermer le modal
            setShowAddMemberModal(false);
        } catch (err) {
            console.error('Error adding member:', err);
            showErrorToast(err.message);
        }
    };

    const handleRemoveMember = async (teamId, memberId) => {
        if (!user) {
            showErrorToast('Veuillez vous connecter');
            return;
        }
        const team = teams.find(t => t.id === teamId);
        if (!team) {
            showErrorToast('Équipe non trouvée');
            return;
        }
        if (!checkTeamPermissions(user, team).canRemoveMembers) {
            showErrorToast('Vous n\'avez pas la permission de retirer des membres de cette équipe');
            return;
        }
        if (!window.confirm('Êtes-vous sûr de vouloir retirer ce membre ?')) {
            return;
        }

        try {
            await removeMember(teamId, memberId);
            showSuccessToast('Membre retiré avec succès');
        } catch (err) {
            console.error('Error removing member:', err);
            showErrorToast(err.message || 'Erreur lors du retrait du membre');
        }
    };

    const filteredTeams = useMemo(() => {
        if (!teams) return [];

        return teams.filter(team => {
            const matchesSearch = team.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                team.description?.toLowerCase().includes(searchQuery.toLowerCase());

            const matchesStatus = statusFilter === 'all' ||
                (statusFilter === 'active' && team.is_active) ||
                (statusFilter === 'inactive' && !team.is_active);

            const matchesRole = roleFilter === 'all' ||
                (roleFilter === 'member' && team.is_member) ||
                (roleFilter === 'admin' && team.is_responsable);

            // Pour les admins, ne montrer que les équipes dont ils sont responsables
            const isAdminTeam = user?.role === 'admin' ? team.is_responsable : true;

            return matchesSearch && matchesStatus && matchesRole && isAdminTeam;
        });
    }, [teams, searchQuery, statusFilter, roleFilter, user]);

    if (user?.role === 'super_admin') {
        return (
            <div className="p-8 text-center">
                <h2 className="text-2xl font-semibold text-gray-700 mb-4">
                    Accès non autorisé
                </h2>
                <p className="text-gray-600">
                    Les super administrateurs n'ont pas accès à la gestion des équipes.
                </p>
            </div>
        );
    }

    if (contextLoading) {
        return <LoadingState />;
    }

    if (error) {
        return (
            <div className="p-8 text-center">
                <h2 className="text-2xl font-semibold text-red-600 mb-4">
                    Erreur
                </h2>
                <p className="text-gray-600">{error}</p>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
            <div className="max-w-7xl mx-auto space-y-8">
                <TeamHeader
                    onCreateClick={() => setShowCreateModal(true)}
                    canCreateTeam={checkTeamPermissions(user, null).canCreateTeam}
                    teams={filteredTeams}
                />

                <div className="space-y-6">
                    <div className="flex flex-col md:flex-row gap-4 mb-6">
                        <div className="flex-grow">
                            <SearchBar searchQuery={searchQuery} setSearchQuery={setSearchQuery} />
                        </div>
                        <div className="flex-shrink-0 w-full md:w-auto">
                            <TeamFilters
                                statusFilter={statusFilter}
                                setStatusFilter={setStatusFilter}
                                roleFilter={roleFilter}
                                setRoleFilter={setRoleFilter}
                            />
                        </div>
                    </div>

                    {contextLoading ? (
                        <LoadingState />
                    ) : error ? (
                        <div className="text-red-600 text-center py-8">{error}</div>
                    ) : filteredTeams.length === 0 ? (
                        <EmptyState
                            message="Aucune équipe trouvée"
                            showCreateButton={checkTeamPermissions(user, null).canCreateTeam}
                            onCreateTeam={() => setShowCreateModal(true)}
                        />
                    ) : (
                        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                            {filteredTeams.map(team => (
                                <TeamCard
                                    key={team.id}
                                    team={team}
                                    permissions={checkTeamPermissions(user, team)}
                                    onEdit={handleEditClick}
                                    onDelete={handleDeleteTeam}
                                    onAddMember={(team) => {
                                        setSelectedTeam(team);
                                        setShowAddMemberModal(true);
                                    }}
                                    onRemoveMember={handleRemoveMember}
                                />
                            ))}
                        </div>
                    )}
                </div>
            </div>

            <CreateTeamModal
                show={showCreateModal}
                onClose={() => setShowCreateModal(false)}
                onSubmit={handleCreateTeam}
                formData={newTeam}
                setFormData={setNewTeam}
            />

            <EditTeamModal
                show={showEditModal}
                onClose={() => {
                    setShowEditModal(false);
                    setSelectedTeam(null);
                    setEditTeam({ name: '', description: '' });
                }}
                onSubmit={handleEditTeam}
                formData={editTeam}
                setFormData={setEditTeam}
            />

            {selectedTeam && (
                <AddMemberModal
                    show={showAddMemberModal}
                    onClose={() => {
                        setShowAddMemberModal(false);
                        setSelectedTeam(null);
                    }}
                    onSubmit={handleAddMember}
                    teamId={selectedTeam.id}
                    teamName={selectedTeam.name}
                />
            )}
        </div>
    );
};

export default AdminTeams;