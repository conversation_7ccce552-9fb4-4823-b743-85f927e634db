import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useClient } from '@/contexts/ClientContext';
import { usePersonalTask } from '@/contexts/PersonalTaskContext';
import { usePersonalEvent } from '@/contexts/PersonalEventContext';
import { toast } from 'react-toastify';
import {
  Calendar,
  CheckSquare,
  Clock,
  FileText,
  Book,
  User,
  BarChart,
  Loader2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';


const ClientDashboard = () => {
  const { user } = useAuth();
  const { metrics, dashboard, loading: clientLoading, fetchPersonalMetrics } = useClient();
  const { personalTasks, loading: tasksLoading } = usePersonalTask();
  const { personalEvents, loading: eventsLoading } = usePersonalEvent();

  const [stats, setStats] = useState({
    totalTasks: 0,
    completedTasks: 0,
    upcomingEvents: 0,
    completionRate: 0,
    recentNotes: 0,
    recentJournals: 0
  });

  // Calculer les statistiques à partir des données disponibles
  useEffect(() => {
    if (personalTasks && personalEvents) {
      const totalTasks = personalTasks.length;
      const completedTasks = personalTasks.filter(task => task.status === 'achevee').length;
      const upcomingEvents = personalEvents.filter(event => {
        const eventDate = new Date(event.start_date);
        const today = new Date();
        return eventDate >= today && !event.is_archived;
      }).length;

      setStats({
        totalTasks,
        completedTasks,
        upcomingEvents,
        completionRate: totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0,
        recentNotes: metrics?.recent_notes || 0,
        recentJournals: metrics?.recent_journals || 0
      });
    }
  }, [personalTasks, personalEvents, metrics]);

  // Charger les métriques au chargement du composant
  useEffect(() => {
    fetchPersonalMetrics();
  }, [fetchPersonalMetrics]);

  const isLoading = clientLoading || tasksLoading || eventsLoading;

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Tableau de bord</h1>
          <p className="text-gray-600">Bienvenue, {user?.name || 'Client'}</p>
        </div>
        <Button
          onClick={fetchPersonalMetrics}
          variant="outline"
          className="flex items-center gap-2"
          disabled={isLoading}
        >
          {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <BarChart className="h-4 w-4" />}
          Actualiser
        </Button>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
        </div>
      ) : (
        <>
          {/* Statistiques principales */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg font-medium">Tâches</CardTitle>
                <CardDescription>Progression des tâches</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-indigo-600">{stats.completedTasks}/{stats.totalTasks}</div>
                <div className="h-2 mt-2 bg-gray-200 rounded-full">
                  <div
                    className="h-full bg-indigo-600 rounded-full"
                    style={{ width: `${stats.completionRate}%` }}
                  ></div>
                </div>
              </CardContent>
              <CardFooter className="pt-0">
                <p className="text-sm text-gray-500">{stats.completionRate}% complété</p>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg font-medium">Événements</CardTitle>
                <CardDescription>Événements à venir</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-indigo-600">{stats.upcomingEvents}</div>
              </CardContent>
              <CardFooter className="pt-0">
                <p className="text-sm text-gray-500">Dans votre calendrier</p>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg font-medium">Notes</CardTitle>
                <CardDescription>Notes récentes</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-indigo-600">{stats.recentNotes}</div>
              </CardContent>
              <CardFooter className="pt-0">
                <p className="text-sm text-gray-500">Créées ce mois-ci</p>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg font-medium">Journal</CardTitle>
                <CardDescription>Entrées récentes</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-indigo-600">{stats.recentJournals}</div>
              </CardContent>
              <CardFooter className="pt-0">
                <p className="text-sm text-gray-500">Créées ce mois-ci</p>
              </CardFooter>
            </Card>
          </div>

          {/* Accès rapides */}
          <h2 className="text-xl font-semibold text-gray-800 mb-4">Accès rapides</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <Button
              variant="outline"
              className="h-auto py-6 flex flex-col items-center justify-center gap-2 hover:bg-indigo-50"
              onClick={() => window.location.href = '/personal-tasks'}
            >
              <CheckSquare className="h-8 w-8 text-indigo-600" />
              <span className="text-lg font-medium">Mes Tâches</span>
              <span className="text-sm text-gray-500">Gérer vos tâches personnelles</span>
            </Button>

            <Button
              variant="outline"
              className="h-auto py-6 flex flex-col items-center justify-center gap-2 hover:bg-indigo-50"
              onClick={() => window.location.href = '/personal-events'}
            >
              <Calendar className="h-8 w-8 text-indigo-600" />
              <span className="text-lg font-medium">Mon Calendrier</span>
              <span className="text-sm text-gray-500">Gérer vos événements</span>
            </Button>

            <Button
              variant="outline"
              className="h-auto py-6 flex flex-col items-center justify-center gap-2 hover:bg-indigo-50"
              onClick={() => window.location.href = '/client-pomodoro'}
            >
              <Clock className="h-8 w-8 text-indigo-600" />
              <span className="text-lg font-medium">Mode Pomodoro</span>
              <span className="text-sm text-gray-500">Améliorer votre productivité</span>
            </Button>
          </div>

          {/* Activité récente */}
          <h2 className="text-xl font-semibold text-gray-800 mb-4">Activité récente</h2>
          <Card>
            <CardHeader>
              <CardTitle>Résumé de l'activité</CardTitle>
              <CardDescription>Votre activité des 7 derniers jours</CardDescription>
            </CardHeader>
            <CardContent>
              {metrics?.recent_activity ? (
                <ul className="space-y-4">
                  {metrics.recent_activity.map((activity, index) => (
                    <li key={index} className="flex items-start gap-3 pb-3 border-b border-gray-100">
                      <div className="bg-indigo-100 p-2 rounded-full">
                        {activity.type === 'task' && <CheckSquare className="h-5 w-5 text-indigo-600" />}
                        {activity.type === 'event' && <Calendar className="h-5 w-5 text-indigo-600" />}
                        {activity.type === 'note' && <FileText className="h-5 w-5 text-indigo-600" />}
                        {activity.type === 'journal' && <Book className="h-5 w-5 text-indigo-600" />}
                      </div>
                      <div>
                        <p className="font-medium">{activity.title}</p>
                        <p className="text-sm text-gray-500">{activity.description}</p>
                        <p className="text-xs text-gray-400">{new Date(activity.date).toLocaleString()}</p>
                      </div>
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-center py-8 text-gray-500">Aucune activité récente à afficher</p>
              )}
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
};

export default ClientDashboard;
