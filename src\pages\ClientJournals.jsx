import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { usePersonalJournal } from '@/contexts/PersonalJournalContext';
import { toast } from 'react-toastify';
import {
  Book,
  Plus,
  Search,
  Calendar,
  Trash2,
  Edit,
  Archive,
  MoreVertical,
  Loader2,
  X,
  Check
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';

const ClientJournals = () => {
  const { user } = useAuth();
  const {
    personalJournals,
    loading,
    error,
    fetchPersonalJournals,
    createPersonalJournal,
    updatePersonalJournal,
    deletePersonalJournal,
    archivePersonalJournal,
    unarchivePersonalJournal
  } = usePersonalJournal();

  // États locaux
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const [showJournalForm, setShowJournalForm] = useState(false);
  const [editingJournal, setEditingJournal] = useState(null);
  const [openDropdownId, setOpenDropdownId] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    date: new Date().toISOString().split('T')[0],
    mood: ''
  });

  // Charger les journaux au montage du composant
  useEffect(() => {
    if (user) {
      fetchPersonalJournals();
    }
  }, [user, fetchPersonalJournals]);

  // Filtrer les journaux en fonction de la recherche et de l'onglet actif
  const filteredJournals = (personalJournals || []).filter(journal => {
    // Vérifier que les propriétés existent avant d'appeler toLowerCase()
    const title = journal?.title || '';
    const content = journal?.content || '';

    const matchesSearch =
      title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      content.toLowerCase().includes(searchTerm.toLowerCase());

    if (activeTab === 'all') return matchesSearch && !journal.is_archived;
    if (activeTab === 'archived') return matchesSearch && journal.is_archived;

    return matchesSearch;
  });

  // Gérer les changements dans le formulaire
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Réinitialiser le formulaire
  const resetForm = () => {
    setFormData({
      title: '',
      content: '',
      date: new Date().toISOString().split('T')[0],
      mood: ''
    });
    setEditingJournal(null);
  };

  // Ouvrir le formulaire pour créer un nouveau journal
  const handleNewJournal = () => {
    setEditingJournal(null);
    setFormData({
      title: '',
      content: '',
      date: new Date().toISOString().split('T')[0],
      mood: ''
    });
    setShowJournalForm(true);
  };

  // Ouvrir le formulaire pour éditer un journal existant
  const handleEditJournal = (journal) => {
    // Fermer le dropdown avant d'ouvrir le dialog
    setOpenDropdownId(null);

    // Attendre un court instant pour que le dropdown se ferme complètement
    setTimeout(() => {
      setEditingJournal(journal);
      setFormData({
        title: journal.title || '',
        content: journal.content || '',
        date: journal.date || new Date().toISOString().split('T')[0],
        mood: journal.mood || ''
      });
      setShowJournalForm(true);
    }, 100);
  };

  // Vérifier si un journal avec le même titre existe déjà
  const checkJournalTitleExists = (title) => {
    if (!personalJournals || !Array.isArray(personalJournals)) return false;

    // Si nous sommes en mode édition, exclure le journal actuel de la vérification
    const existingJournals = editingJournal ? personalJournals.filter(j => j.id !== editingJournal.id) : personalJournals;

    // Vérifier si un journal avec le même titre existe déjà pour cet utilisateur
    return existingJournals.some(j =>
      j.title && j.title.toLowerCase().trim() === title.toLowerCase().trim()
    );
  };

  // Soumettre le formulaire
  const handleSubmit = async (e) => {
    e.preventDefault();

    // S'assurer que la date est au format ISO (YYYY-MM-DD)
    let formattedDate;
    try {
      // Vérifier si la date est valide
      const dateObj = new Date(formData.date);
      if (isNaN(dateObj.getTime())) {
        toast.error("La date saisie n'est pas valide");
        return;
      }

      // Formater la date au format YYYY-MM-DD
      formattedDate = dateObj.toISOString().split('T')[0];
      console.log("Date formatée:", formattedDate);
    } catch (error) {
      console.error("Erreur lors du formatage de la date:", error);
      toast.error("Erreur avec le format de date. Veuillez utiliser le format YYYY-MM-DD");
      return;
    }

    // Préparer les données avec validation stricte
    const journalData = {
      title: formData.title.trim(),
      content: formData.content.trim(),
      date: formattedDate
    };

    // Vérifier que tous les champs requis sont présents et non vides
    if (!journalData.title) {
      toast.error("Le titre est requis");
      return;
    }

    if (!journalData.content) {
      toast.error("Le contenu est requis");
      return;
    }

    if (!journalData.date) {
      toast.error("La date est requise");
      return;
    }

    // Vérifier les doublons de titre
    if (checkJournalTitleExists(journalData.title)) {
      toast.error("Vous avez déjà un journal avec ce titre");
      return;
    }

    // Ajouter l'humeur si elle est présente
    if (formData.mood && formData.mood.trim()) {
      journalData.mood = formData.mood.trim();
    }

    console.log("Données du journal à envoyer:", journalData);

    try {
      if (editingJournal) {
        // Mettre à jour un journal existant
        await updatePersonalJournal(editingJournal.id, journalData);
        setShowJournalForm(false);
        resetForm();
        fetchPersonalJournals();
      } else {
        // Les vérifications d'utilisateur et de rôle sont gérées dans le contexte

        // Créer un nouveau journal avec gestion d'erreur améliorée
        try {
          const result = await createPersonalJournal(journalData);
          if (result) {
            // Le toast de succès est géré dans le contexte
            // Fermer le formulaire et rafraîchir les journaux
            setShowJournalForm(false);
            resetForm();
            fetchPersonalJournals();
          }
        } catch (createError) {
          console.error('Erreur spécifique lors de la création:', createError);
          // Les toasts d'erreur sont gérés dans le contexte
        }
      }
    } catch (error) {
      console.error('Erreur lors de la sauvegarde du journal:', error);
      // Les toasts d'erreur sont gérés dans le contexte
    }
  };

  // Supprimer un journal
  const handleDeleteJournal = async (journalId) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce journal ?')) {
      try {
        await deletePersonalJournal(journalId);
        // Le toast de succès est géré dans le contexte
      } catch (error) {
        console.error('Erreur lors de la suppression du journal:', error);
        // Le toast d'erreur est géré dans le contexte
      }
    }
  };

  // Archiver un journal
  const handleArchiveJournal = async (journalId) => {
    try {
      await archivePersonalJournal(journalId);
      // Le toast de succès est géré dans le contexte
    } catch (error) {
      console.error('Erreur lors de l\'archivage du journal:', error);
      // Le toast d'erreur est géré dans le contexte
    }
  };

  // Désarchiver un journal
  const handleUnarchiveJournal = async (journalId) => {
    try {
      await unarchivePersonalJournal(journalId);
      // Le toast de succès est géré dans le contexte
    } catch (error) {
      console.error('Erreur lors du désarchivage du journal:', error);
      // Le toast d'erreur est géré dans le contexte
    }
  };

  // Formater la date
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Obtenir l'emoji d'humeur
  const getMoodEmoji = (mood) => {
    switch (mood?.toLowerCase()) {
      case 'happy': return '😊';
      case 'sad': return '😢';
      case 'angry': return '😠';
      case 'excited': return '😃';
      case 'calm': return '😌';
      case 'anxious': return '😰';
      case 'tired': return '😴';
      default: return '';
    }
  };

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Mon Journal</h1>
          <p className="text-gray-600">Gérez vos entrées de journal personnel</p>
        </div>
        <Button
          onClick={handleNewJournal}
          className="bg-indigo-600 hover:bg-indigo-700 text-white"
        >
          <Plus className="h-4 w-4 mr-2" />
          Nouvelle Entrée
        </Button>
      </div>

      {/* Barre de recherche et filtres */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="relative flex-grow">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <Input
            type="text"
            placeholder="Rechercher dans le journal..."
            className="pl-10"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {/* Onglets */}
      <Tabs defaultValue="all" className="mb-6" onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="all">Toutes les entrées</TabsTrigger>
          <TabsTrigger value="archived">Archives</TabsTrigger>
        </TabsList>
      </Tabs>

      {/* Liste des journaux */}
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
        </div>
      ) : filteredJournals.length === 0 ? (
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <Book className="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Aucune entrée trouvée</h3>
          <p className="text-gray-500 mb-4">
            {searchTerm
              ? "Aucune entrée ne correspond à votre recherche."
              : activeTab === 'archived'
                ? "Vous n'avez pas d'entrées archivées."
                : "Vous n'avez pas encore créé d'entrées de journal."}
          </p>
          {!searchTerm && activeTab !== 'archived' && (
            <Button
              onClick={handleNewJournal}
              variant="outline"
              className="mx-auto"
            >
              <Plus className="h-4 w-4 mr-2" />
              Créer une entrée
            </Button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredJournals.map(journal => (
            <Card key={journal.id} className={`overflow-hidden ${journal.is_archived ? 'bg-gray-50' : ''}`}>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className={`text-xl ${journal.is_archived ? 'text-gray-500 line-through' : ''}`}>
                      {journal.title} {journal.mood && <span>{getMoodEmoji(journal.mood)}</span>}
                    </CardTitle>
                    <CardDescription className="flex items-center text-sm text-gray-500">
                      <Calendar className="h-3 w-3 mr-1" />
                      {formatDate(journal.date)}
                    </CardDescription>
                  </div>
                  <DropdownMenu
                    open={openDropdownId === journal.id}
                    onOpenChange={(open) => setOpenDropdownId(open ? journal.id : null)}
                  >
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      {!journal.is_archived && (
                        <DropdownMenuItem onClick={() => handleEditJournal(journal)}>
                          <Edit className="h-4 w-4 mr-2" />
                          Modifier
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuItem
                        onClick={() => {
                          setOpenDropdownId(null);
                          handleDeleteJournal(journal.id);
                        }}
                        className="text-red-600"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Supprimer
                      </DropdownMenuItem>
                      {journal.is_archived ? (
                        <DropdownMenuItem
                          onClick={() => {
                            setOpenDropdownId(null);
                            handleUnarchiveJournal(journal.id);
                          }}
                        >
                          <Archive className="h-4 w-4 mr-2" />
                          Désarchiver
                        </DropdownMenuItem>
                      ) : (
                        <DropdownMenuItem
                          onClick={() => {
                            setOpenDropdownId(null);
                            handleArchiveJournal(journal.id);
                          }}
                        >
                          <Archive className="h-4 w-4 mr-2" />
                          Archiver
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent>
                <p className={`text-gray-700 whitespace-pre-line line-clamp-4 ${journal.is_archived ? 'text-gray-400' : ''}`}>
                  {journal.content}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Formulaire de création/édition de journal */}
      <Dialog
        open={showJournalForm}
        onOpenChange={(open) => {
          setShowJournalForm(open);
          if (!open) {
            resetForm();
          }
        }}
      >
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{editingJournal ? 'Modifier l\'entrée' : 'Nouvelle entrée de journal'}</DialogTitle>
            <DialogDescription>
              {editingJournal
                ? 'Modifiez les détails de votre entrée de journal ci-dessous.'
                : 'Créez une nouvelle entrée de journal en remplissant le formulaire ci-dessous.'}
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit}>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="title">Titre</Label>
                <Input
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="date">Date</Label>
                <Input
                  id="date"
                  name="date"
                  type="date"
                  value={formData.date}
                  onChange={(e) => {
                    // Vérifier et formater la date immédiatement
                    const inputDate = e.target.value;
                    console.log("Date saisie:", inputDate);

                    // Vérifier si la date est au format YYYY-MM-DD
                    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
                    if (dateRegex.test(inputDate)) {
                      // Vérifier que la date est valide
                      const dateObj = new Date(inputDate);
                      if (!isNaN(dateObj.getTime())) {
                        // Format correct et date valide
                        handleInputChange(e);
                      } else {
                        toast.error("La date saisie n'est pas valide");
                      }
                    } else if (inputDate) {
                      toast.error("La date doit être au format YYYY-MM-DD");
                    } else {
                      // Champ vide, accepter quand même
                      handleInputChange(e);
                    }
                  }}
                  required
                />
                <p className="text-xs text-gray-500">Format: AAAA-MM-JJ</p>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="mood">Humeur</Label>
                <Input
                  id="mood"
                  name="mood"
                  value={formData.mood}
                  onChange={handleInputChange}
                  placeholder="happy, sad, excited, calm..."
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="content">Contenu</Label>
                <Textarea
                  id="content"
                  name="content"
                  rows={10}
                  value={formData.content}
                  onChange={handleInputChange}
                  required
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setShowJournalForm(false);
                  resetForm();
                }}
              >
                Annuler
              </Button>
              <Button type="submit" className="bg-indigo-600 hover:bg-indigo-700 text-white">
                {editingJournal ? 'Mettre à jour' : 'Créer'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ClientJournals;
