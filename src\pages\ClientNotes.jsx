import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { usePersonalNote } from '@/contexts/PersonalNoteContext';
import { toast } from 'react-toastify';
import {
  FileText,
  Plus,
  Search,
  Filter,
  Trash2,
  Edit,
  Archive,
  MoreVertical,
  Loader2,
  X,
  Check
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';

const ClientNotes = () => {
  const { user } = useAuth();
  const {
    personalNotes,
    loading,
    error,
    fetchPersonalNotes,
    createPersonalNote,
    updatePersonalNote,
    deletePersonalNote,
    archivePersonalNote,
    unarchivePersonalNote
  } = usePersonalNote();

  // États locaux
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const [showNoteForm, setShowNoteForm] = useState(false);
  const [editingNote, setEditingNote] = useState(null);
  const [openDropdownId, setOpenDropdownId] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    tags: ''
  });

  // Charger les notes au montage du composant
  useEffect(() => {
    if (user) {
      fetchPersonalNotes();
    }
  }, [user, fetchPersonalNotes]);

  // Filtrer les notes en fonction de la recherche et de l'onglet actif
  const filteredNotes = (personalNotes || []).filter(note => {
    // Vérifier que les propriétés existent avant d'appeler toLowerCase()
    const title = note?.title || '';
    const content = note?.content || '';

    const matchesSearch =
      title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      content.toLowerCase().includes(searchTerm.toLowerCase());

    if (activeTab === 'all') return matchesSearch && !note.is_archived;
    if (activeTab === 'archived') return matchesSearch && note.is_archived;

    return matchesSearch;
  });

  // Gérer les changements dans le formulaire
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Réinitialiser le formulaire
  const resetForm = () => {
    setFormData({
      title: '',
      content: '',
      tags: ''
    });
    setEditingNote(null);
  };

  // Ouvrir le formulaire pour créer une nouvelle note
  const handleNewNote = () => {
    setEditingNote(null);
    setFormData({
      title: '',
      content: '',
      tags: ''
    });
    setShowNoteForm(true);
  };

  // Ouvrir le formulaire pour éditer une note existante
  const handleEditNote = (note) => {
    // Fermer le dropdown avant d'ouvrir le dialog
    setOpenDropdownId(null);

    // Attendre un court instant pour que le dropdown se ferme complètement
    setTimeout(() => {
      setEditingNote(note);
      setFormData({
        title: note.title || '',
        content: note.content || '',
        tags: note.tags?.join(', ') || ''
      });
      setShowNoteForm(true);
    }, 100);
  };

  // Vérifier si une note avec le même titre existe déjà
  const checkNoteTitleExists = (title) => {
    if (!personalNotes || !Array.isArray(personalNotes)) return false;

    // Si nous sommes en mode édition, exclure la note actuelle de la vérification
    const existingNotes = editingNote ? personalNotes.filter(n => n.id !== editingNote.id) : personalNotes;

    // Vérifier si une note avec le même titre existe déjà pour cet utilisateur
    return existingNotes.some(n =>
      n.title && n.title.toLowerCase().trim() === title.toLowerCase().trim()
    );
  };

  // Soumettre le formulaire
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Vérifier les doublons de titre
    if (checkNoteTitleExists(formData.title.trim())) {
      toast.error("Vous avez déjà une note avec ce titre");
      return;
    }

    // Préparer les données
    const noteData = {
      title: formData.title,
      content: formData.content
    };

    // Ajouter les tags s'ils sont présents
    if (formData.tags) {
      noteData.tags = formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag);
    }

    try {
      if (editingNote) {
        // Mettre à jour une note existante
        await updatePersonalNote(editingNote.id, noteData);
      } else {
        // Créer une nouvelle note
        await createPersonalNote(noteData);
      }

      // Fermer le formulaire et rafraîchir les notes
      setShowNoteForm(false);
      resetForm();
      fetchPersonalNotes();
    } catch (error) {
      console.error('Erreur lors de la sauvegarde de la note:', error);
      // Le toast d'erreur est déjà géré dans le contexte
    }
  };

  // Supprimer une note
  const handleDeleteNote = async (noteId) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette note ?')) {
      try {
        await deletePersonalNote(noteId);
        // Le toast de succès est déjà géré dans le contexte
      } catch (error) {
        console.error('Erreur lors de la suppression de la note:', error);
        // Le toast d'erreur est déjà géré dans le contexte
      }
    }
  };

  // Archiver une note
  const handleArchiveNote = async (noteId) => {
    try {
      await archivePersonalNote(noteId);
      // Le toast de succès est déjà géré dans le contexte
    } catch (error) {
      console.error('Erreur lors de l\'archivage de la note:', error);
      // Le toast d'erreur est déjà géré dans le contexte
    }
  };

  // Désarchiver une note
  const handleUnarchiveNote = async (noteId) => {
    try {
      await unarchivePersonalNote(noteId);
      // Le toast de succès est déjà géré dans le contexte
    } catch (error) {
      console.error('Erreur lors du désarchivage de la note:', error);
      // Le toast d'erreur est déjà géré dans le contexte
    }
  };

  // Formater la date
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Mes Notes</h1>
          <p className="text-gray-600">Gérez vos notes personnelles</p>
        </div>
        <Button
          onClick={handleNewNote}
          className="bg-indigo-600 hover:bg-indigo-700 text-white"
        >
          <Plus className="h-4 w-4 mr-2" />
          Nouvelle Note
        </Button>
      </div>

      {/* Barre de recherche et filtres */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="relative flex-grow">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <Input
            type="text"
            placeholder="Rechercher dans les notes..."
            className="pl-10"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {/* Onglets */}
      <Tabs defaultValue="all" className="mb-6" onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="all">Toutes les notes</TabsTrigger>
          <TabsTrigger value="archived">Archives</TabsTrigger>
        </TabsList>
      </Tabs>

      {/* Liste des notes */}
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
        </div>
      ) : filteredNotes.length === 0 ? (
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <FileText className="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Aucune note trouvée</h3>
          <p className="text-gray-500 mb-4">
            {searchTerm
              ? "Aucune note ne correspond à votre recherche."
              : activeTab === 'archived'
                ? "Vous n'avez pas de notes archivées."
                : "Vous n'avez pas encore créé de notes."}
          </p>
          {!searchTerm && activeTab !== 'archived' && (
            <Button
              onClick={handleNewNote}
              variant="outline"
              className="mx-auto"
            >
              <Plus className="h-4 w-4 mr-2" />
              Créer une note
            </Button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredNotes.map(note => (
            <Card key={note.id} className={`overflow-hidden ${note.is_archived ? 'bg-gray-50' : ''}`}>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <CardTitle className={`text-xl ${note.is_archived ? 'text-gray-500 line-through' : ''}`}>
                    {note.title}
                  </CardTitle>
                  <DropdownMenu
                    open={openDropdownId === note.id}
                    onOpenChange={(open) => setOpenDropdownId(open ? note.id : null)}
                  >
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      {!note.is_archived && (
                        <DropdownMenuItem onClick={() => handleEditNote(note)}>
                          <Edit className="h-4 w-4 mr-2" />
                          Modifier
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuItem
                        onClick={() => {
                          setOpenDropdownId(null);
                          handleDeleteNote(note.id);
                        }}
                        className="text-red-600"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Supprimer
                      </DropdownMenuItem>
                      {note.is_archived ? (
                        <DropdownMenuItem
                          onClick={() => {
                            setOpenDropdownId(null);
                            handleUnarchiveNote(note.id);
                          }}
                        >
                          <Archive className="h-4 w-4 mr-2" />
                          Désarchiver
                        </DropdownMenuItem>
                      ) : (
                        <DropdownMenuItem
                          onClick={() => {
                            setOpenDropdownId(null);
                            handleArchiveNote(note.id);
                          }}
                        >
                          <Archive className="h-4 w-4 mr-2" />
                          Archiver
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <CardDescription className="text-sm text-gray-500">
                  {formatDate(note.created_at)}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className={`text-gray-700 whitespace-pre-line line-clamp-3 ${note.is_archived ? 'text-gray-400' : ''}`}>
                  {note.content}
                </p>
              </CardContent>
              <CardFooter className="pt-0 flex flex-wrap gap-2">
                {note.tags?.map((tag, index) => (
                  <Badge key={index} variant="outline" className="bg-indigo-50 text-indigo-700 hover:bg-indigo-100">
                    {tag}
                  </Badge>
                ))}
              </CardFooter>
            </Card>
          ))}
        </div>
      )}

      {/* Formulaire de création/édition de note */}
      <Dialog
        open={showNoteForm}
        onOpenChange={(open) => {
          setShowNoteForm(open);
          if (!open) {
            resetForm();
          }
        }}
      >
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{editingNote ? 'Modifier la note' : 'Nouvelle note'}</DialogTitle>
            <DialogDescription>
              {editingNote
                ? 'Modifiez les détails de votre note ci-dessous.'
                : 'Créez une nouvelle note en remplissant le formulaire ci-dessous.'}
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit}>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="title">Titre</Label>
                <Input
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="content">Contenu</Label>
                <Textarea
                  id="content"
                  name="content"
                  rows={8}
                  value={formData.content}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="tags">Tags (séparés par des virgules)</Label>
                <Input
                  id="tags"
                  name="tags"
                  value={formData.tags}
                  onChange={handleInputChange}
                  placeholder="travail, important, idée..."
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setShowNoteForm(false);
                  resetForm();
                }}
              >
                Annuler
              </Button>
              <Button type="submit" className="bg-indigo-600 hover:bg-indigo-700 text-white">
                {editingNote ? 'Mettre à jour' : 'Créer'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ClientNotes;
