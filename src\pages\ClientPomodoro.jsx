import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'react-toastify';
import clientService from '@/services/clientService';
import {
  Play,
  Pause,
  RotateCcw,
  Settings,
  CheckCircle,
  Volume2,
  VolumeX,
  Loader2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';

const ClientPomodoro = () => {
  const { user } = useAuth();

  // États locaux - Version corrigée
  const [settings, setSettings] = useState({
    focus_duration: 25,
    short_break_duration: 5,
    long_break_duration: 15,
    sessions_before_long_break: 4
  });

  const [currentSession, setCurrentSession] = useState({
    active: false,
    status: 'inactive',
    remaining_time: 0,
    focus_duration: 25,
    break_duration: 5
  });

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState('');
  const [showSettings, setShowSettings] = useState(false);
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [completedSessionsToday, setCompletedSessionsToday] = useState(0);

  // Utiliser useRef pour éviter les re-renders inutiles
  const intervalRef = useRef(null);
  const workCompleteSound = useRef(null);
  const breakCompleteSound = useRef(null);

  // Fonction pour formater le temps
  const formatTime = useCallback((seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  }, []);

  // Utiliser le service client existant pour les appels API

  // Charger les paramètres Pomodoro
  const loadSettings = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const data = await clientService.getPomodoroSettings();
      setSettings(data);
      setCurrentSession(data.current_session);
      console.log('Settings loaded:', data);
    } catch (error) {
      setError(`Erreur lors du chargement des paramètres: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Contrôler la session Pomodoro - VERSION CORRIGÉE
  const controlSession = useCallback(async (action) => {
    try {
      setIsLoading(true);
      setError(null);
      setSuccessMessage('');

      console.log(`🎯 Action demandée: ${action}`);
      console.log('📊 État actuel:', currentSession);

      const data = await clientService.controlPomodoro(action);

      console.log('📥 Réponse serveur:', data);

      // ✅ CORRECTION CRITIQUE : Mettre à jour l'état immédiatement
      if (data.current_session) {
        setCurrentSession(data.current_session);
        console.log('🔄 État mis à jour:', data.current_session);
      }

      // Afficher le message de succès
      if (data.message) {
        setSuccessMessage(data.message);
        console.log('✅ Message:', data.message);
        toast.success(data.message);

        // Effacer le message après 3 secondes
        setTimeout(() => setSuccessMessage(''), 3000);
      }

    } catch (error) {
      console.error('❌ Erreur contrôle session:', error);
      setError(`Erreur lors du contrôle de la session: ${error.message}`);
      toast.error(`Erreur: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  }, [currentSession]);

  // ✅ CORRECTION CRITIQUE : Gestion du timer basée sur le statut
  useEffect(() => {
    console.log('🕐 Timer effect - Status:', currentSession.status, 'Active:', currentSession.active);

    // Nettoyer l'intervalle existant
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    // Démarrer le timer SEULEMENT si la session est active ET pas en pause
    if (currentSession.active && currentSession.status === 'active') {
      console.log('▶️ Démarrage du timer');

      intervalRef.current = setInterval(() => {
        setCurrentSession(prev => {
          const newRemainingTime = prev.remaining_time - 1;

          console.log('⏰ Timer tick:', newRemainingTime);

          if (newRemainingTime <= 0) {
            console.log('⏰ Session terminée automatiquement');
            controlSession('complete');
            return prev;
          }

          return {
            ...prev,
            remaining_time: newRemainingTime
          };
        });
      }, 1000);
    } else {
      console.log('⏸️ Timer arrêté - Status:', currentSession.status);
    }

    // Nettoyage
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [currentSession.active, currentSession.status, controlSession]);

  // Charger les paramètres au montage du composant
  useEffect(() => {
    loadSettings();
  }, [loadSettings]);

  // Gestionnaires d'événements
  const handleStart = () => {
    console.log('🚀 Démarrage demandé');
    controlSession('start');
  };

  const handlePause = () => {
    console.log('⏸️ Pause demandée');
    controlSession('pause');
  };

  const handleResume = () => {
    console.log('▶️ Reprise demandée');
    controlSession('resume');
  };

  const handleReset = () => {
    console.log('🔄 Reset demandé');
    controlSession('reset');
  };

  // Fonction pour déterminer le texte du statut
  const getStatusText = () => {
    switch (currentSession.status) {
      case 'active':
        return 'Session de travail en cours';
      case 'paused':
        return 'Session en pause';
      case 'inactive':
        return 'Prêt à commencer';
      case 'completed':
        return 'Session terminée !';
      default:
        return 'État inconnu';
    }
  };

  // Fonction pour déterminer la couleur du statut
  const getStatusColor = () => {
    switch (currentSession.status) {
      case 'active':
        return 'text-green-600';
      case 'paused':
        return 'text-yellow-600';
      case 'inactive':
        return 'text-gray-600';
      case 'completed':
        return 'text-blue-600';
      default:
        return 'text-gray-600';
    }
  };

  // Fonction pour jouer les sons
  const playNotificationSound = useCallback((isWorkComplete) => {
    if (!soundEnabled) return;

    try {
      const sound = isWorkComplete ? workCompleteSound.current : breakCompleteSound.current;
      if (sound) {
        sound.currentTime = 0;
        sound.play().catch(error => {
          console.log('Impossible de jouer le son:', error);
        });
      }
    } catch (error) {
      console.log('Erreur lors de la lecture du son:', error);
    }
  }, [soundEnabled]);

  // Gérer les changements dans le formulaire de paramètres
  const handleSettingsChange = (e) => {
    const { name, value } = e.target;
    setSettings(prev => ({
      ...prev,
      [name]: parseInt(value, 10)
    }));
  };

  // Sauvegarder les paramètres
  const handleSaveSettings = async () => {
    try {
      setIsLoading(true);
      await clientService.updatePomodoroSettings({
        work_duration: settings.focus_duration,
        short_break_duration: settings.short_break_duration,
        long_break_duration: settings.long_break_duration,
        sessions_before_long_break: settings.sessions_before_long_break
      });

      setShowSettings(false);
      toast.success('Paramètres sauvegardés avec succès');
      loadSettings(); // Recharger les paramètres
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des paramètres:', error);
      toast.error('Erreur lors de la sauvegarde des paramètres');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Mode Pomodoro</h1>
          <p className="text-gray-600">Améliorez votre productivité avec la technique Pomodoro</p>
        </div>
        <Button
          onClick={() => setShowSettings(true)}
          variant="outline"
          className="flex items-center gap-2"
        >
          <Settings className="h-4 w-4" />
          Paramètres
        </Button>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Timer principal */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="text-center text-2xl">
                {getStatusText()}
              </CardTitle>
              <CardDescription className="text-center">
                {currentSession.status === 'active' ? 'Concentrez-vous sur votre tâche' :
                  currentSession.status === 'paused' ? 'Session mise en pause' :
                    currentSession.status === 'completed' ? 'Prenez une pause bien méritée !' :
                      'Cliquez sur Démarrer pour commencer une session Pomodoro'}
              </CardDescription>
            </CardHeader>
            <CardContent className="flex flex-col items-center">
              {/* Messages */}
              {error && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4 w-full">
                  {error}
                </div>
              )}

              {successMessage && (
                <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4 w-full">
                  {successMessage}
                </div>
              )}

              <div className="relative w-64 h-64 mb-8">
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-6xl font-bold text-indigo-600">
                    {formatTime(currentSession.remaining_time || 0)}
                  </div>
                </div>
                <svg className="w-full h-full" viewBox="0 0 100 100">
                  <circle
                    className="text-gray-200"
                    strokeWidth="4"
                    stroke="currentColor"
                    fill="transparent"
                    r="45"
                    cx="50"
                    cy="50"
                  />
                  <circle
                    className={`${currentSession.status === 'active' ? 'text-indigo-600' :
                      currentSession.status === 'paused' ? 'text-yellow-500' :
                        'text-green-500'
                      }`}
                    strokeWidth="4"
                    strokeDasharray={283}
                    strokeDashoffset={283 - (283 * 50) / 100}
                    strokeLinecap="round"
                    stroke="currentColor"
                    fill="transparent"
                    r="45"
                    cx="50"
                    cy="50"
                  />
                </svg>
              </div>

              {/* Debug Info */}
              <div className="text-xs text-gray-400 mb-4">
                Status: {currentSession.status} | Active: {currentSession.active ? 'Oui' : 'Non'}
              </div>

              <div className="flex gap-4">
                {!currentSession.active ? (
                  <Button
                    onClick={handleStart}
                    className="bg-indigo-600 hover:bg-indigo-700 text-white"
                    size="lg"
                    disabled={isLoading}
                  >
                    <Play className="h-5 w-5 mr-2" />
                    {isLoading ? 'Démarrage...' : 'Démarrer'}
                  </Button>
                ) : (
                  <>
                    {currentSession.status === 'active' ? (
                      <Button
                        onClick={handlePause}
                        variant="outline"
                        size="lg"
                        disabled={isLoading}
                      >
                        <Pause className="h-5 w-5 mr-2" />
                        {isLoading ? 'Pause...' : 'Pause'}
                      </Button>
                    ) : currentSession.status === 'paused' ? (
                      <Button
                        onClick={handleResume}
                        className="bg-indigo-600 hover:bg-indigo-700 text-white"
                        size="lg"
                        disabled={isLoading}
                      >
                        <Play className="h-5 w-5 mr-2" />
                        {isLoading ? 'Reprise...' : 'Reprendre'}
                      </Button>
                    ) : null}

                    <Button
                      onClick={handleReset}
                      variant="outline"
                      size="lg"
                      disabled={isLoading}
                    >
                      <RotateCcw className="h-5 w-5 mr-2" />
                      {isLoading ? 'Reset...' : 'Réinitialiser'}
                    </Button>
                  </>
                )}
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <div className="flex items-center gap-2">
                <div className="flex items-center gap-2">
                  {soundEnabled ? <Volume2 className="h-5 w-5" /> : <VolumeX className="h-5 w-5" />}
                  <Switch
                    checked={soundEnabled}
                    onChange={setSoundEnabled}
                  />
                </div>
                <span className="text-sm text-gray-500">
                  {soundEnabled ? 'Son activé' : 'Son désactivé'}
                </span>
              </div>
              <div className="text-sm text-gray-500">
                Sessions complétées aujourd'hui: <span className="font-semibold">{completedSessionsToday}</span>
              </div>
            </CardFooter>
          </Card>

          {/* Statistiques et conseils */}
          <Card>
            <CardHeader>
              <CardTitle>Statistiques</CardTitle>
              <CardDescription>Votre progression aujourd'hui</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <div className="flex justify-between mb-2">
                  <span className="text-sm font-medium">Sessions complétées</span>
                  <span className="text-sm font-semibold">{completedSessionsToday}</span>
                </div>
                <div className="h-2 bg-gray-200 rounded-full">
                  <div
                    className="h-full bg-indigo-600 rounded-full"
                    style={{ width: `${Math.min(completedSessionsToday / 8 * 100, 100)}%` }}
                  ></div>
                </div>
              </div>

              <div>
                <div className="flex justify-between mb-2">
                  <span className="text-sm font-medium">État actuel</span>
                  <span className={`text-sm font-semibold ${getStatusColor()}`}>
                    {currentSession.status === 'active' ? 'En cours' :
                      currentSession.status === 'paused' ? 'En pause' :
                        currentSession.status === 'completed' ? 'Terminée' :
                          'Inactif'}
                  </span>
                </div>
              </div>

              <div className="pt-4 border-t border-gray-100">
                <h4 className="font-medium mb-2">Conseils Pomodoro</h4>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>Choisissez une tâche spécifique pour chaque session</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>Éliminez les distractions pendant les sessions de travail</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>Prenez vraiment des pauses pour reposer votre esprit</span>
                  </li>
                </ul>
              </div>

              {/* Refresh Button pour debug */}
              <button
                onClick={loadSettings}
                disabled={isLoading}
                className="text-gray-600 hover:text-gray-800 text-sm underline w-full text-center"
              >
                🔄 Actualiser l'état
              </button>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Formulaire de paramètres */}
      <Dialog open={showSettings} onOpenChange={setShowSettings}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Paramètres du Pomodoro</DialogTitle>
            <DialogDescription>
              Personnalisez les durées des sessions et des pauses selon vos préférences.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="focus_duration">Durée de travail (minutes)</Label>
              <Input
                id="focus_duration"
                name="focus_duration"
                type="number"
                min="1"
                max="60"
                value={settings.focus_duration}
                onChange={handleSettingsChange}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="short_break_duration">Durée de pause courte (minutes)</Label>
              <Input
                id="short_break_duration"
                name="short_break_duration"
                type="number"
                min="1"
                max="30"
                value={settings.short_break_duration}
                onChange={handleSettingsChange}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="long_break_duration">Durée de pause longue (minutes)</Label>
              <Input
                id="long_break_duration"
                name="long_break_duration"
                type="number"
                min="5"
                max="60"
                value={settings.long_break_duration}
                onChange={handleSettingsChange}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="sessions_before_long_break">Sessions avant une pause longue</Label>
              <Input
                id="sessions_before_long_break"
                name="sessions_before_long_break"
                type="number"
                min="1"
                max="10"
                value={settings.sessions_before_long_break}
                onChange={handleSettingsChange}
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setShowSettings(false)}>
              Annuler
            </Button>
            <Button type="button" onClick={handleSaveSettings} className="bg-indigo-600 hover:bg-indigo-700 text-white">
              Sauvegarder
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Sons */}
      <audio
        ref={workCompleteSound}
        preload="none"
        onError={() => console.log('Fichier audio work-complete.mp3 non trouvé')}
      >
        <source src="/sounds/work-complete.mp3" type="audio/mpeg" />
        <source src="/sounds/work-complete.wav" type="audio/wav" />
      </audio>
      <audio
        ref={breakCompleteSound}
        preload="none"
        onError={() => console.log('Fichier audio break-complete.mp3 non trouvé')}
      >
        <source src="/sounds/break-complete.mp3" type="audio/mpeg" />
        <source src="/sounds/break-complete.wav" type="audio/wav" />
      </audio>
    </div>
  );
};

export default ClientPomodoro;
