import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import colorService from '@/services/colorService';
import BackendColorPicker from '@/components/ui/BackendColorPicker';
import { useEventColors } from '@/hooks/useEventColors';

const ColorSystemTest = () => {
  const [testTitle, setTestTitle] = useState('');
  const [selectedColor, setSelectedColor] = useState('#3788d8');
  const [suggestion, setSuggestion] = useState(null);
  const [validation, setValidation] = useState(null);
  const [colorInfo, setColorInfo] = useState(null);

  const {
    palette,
    categories,
    loading,
    error,
    getColorInfo,
    suggestColor,
    validateColor,
    formatColorForDisplay
  } = useEventColors();

  // Test de suggestion de couleur
  const handleSuggestColor = async () => {
    if (!testTitle.trim()) {
      toast.error('Veuillez saisir un titre pour tester la suggestion');
      return;
    }

    try {
      const result = await suggestColor(testTitle);
      setSuggestion(result);
      if (result && result.suggested_color) {
        toast.success(`Couleur suggérée : ${result.description || result.suggested_color}`);
      } else {
        toast.info('Aucune suggestion trouvée pour ce titre');
      }
    } catch (error) {
      console.error('Erreur suggestion:', error);
      toast.error('Erreur lors de la suggestion de couleur');
    }
  };

  // Test de validation de couleur
  const handleValidateColor = async () => {
    try {
      const result = await validateColor(selectedColor);
      setValidation(result);
      if (result && result.is_valid) {
        toast.success('Couleur valide !');
      } else {
        toast.error('Couleur invalide');
      }
    } catch (error) {
      console.error('Erreur validation:', error);
      toast.error('Erreur lors de la validation');
    }
  };

  // Test d'informations de couleur
  const handleGetColorInfo = async () => {
    try {
      const info = await getColorInfo(selectedColor);
      setColorInfo(info);
      if (info) {
        toast.success(`Informations récupérées pour : ${info.name || info.description}`);
      } else {
        toast.info('Aucune information trouvée pour cette couleur');
      }
    } catch (error) {
      console.error('Erreur info couleur:', error);
      toast.error('Erreur lors de la récupération des informations');
    }
  };

  // Appliquer la suggestion
  const applySuggestion = () => {
    if (suggestion && suggestion.suggested_color) {
      setSelectedColor(suggestion.suggested_color);
      setSuggestion(null);
      toast.success('Suggestion appliquée !');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Chargement du système de couleurs...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 mb-4">
            <svg className="w-12 h-12 mx-auto" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <p className="text-red-600">Erreur lors du chargement du système de couleurs</p>
          <p className="text-gray-500 text-sm mt-2">Vérifiez que le backend est démarré</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* En-tête */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Test du Système de Couleurs Backend
          </h1>
          <p className="text-lg text-gray-600">
            Interface de test pour valider l'intégration avec l'API de couleurs
          </p>
        </div>

        {/* Statistiques */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6 text-center">
            <div className="text-2xl font-bold text-indigo-600">{palette.length}</div>
            <div className="text-sm text-gray-500">Couleurs disponibles</div>
          </div>
          <div className="bg-white rounded-lg shadow p-6 text-center">
            <div className="text-2xl font-bold text-green-600">{Object.keys(categories).length}</div>
            <div className="text-sm text-gray-500">Catégories</div>
          </div>
          <div className="bg-white rounded-lg shadow p-6 text-center">
            <div className="text-2xl font-bold text-purple-600">
              {Object.values(categories).reduce((total, cat) => total + cat.length, 0)}
            </div>
            <div className="text-sm text-gray-500">Total couleurs organisées</div>
          </div>
        </div>

        {/* Tests interactifs */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Test de suggestion */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Test de Suggestion de Couleur
            </h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Titre de l'événement
                </label>
                <input
                  type="text"
                  value={testTitle}
                  onChange={(e) => setTestTitle(e.target.value)}
                  placeholder="Ex: Réunion urgente, Formation équipe..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
              </div>
              <button
                onClick={handleSuggestColor}
                disabled={!testTitle.trim()}
                className="w-full bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Suggérer une couleur
              </button>

              {suggestion && (
                <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div
                        className="w-6 h-6 rounded-full border"
                        style={{ backgroundColor: suggestion.hex_code }}
                      />
                      <div>
                        <p className="font-medium text-blue-900">{suggestion.description}</p>
                        <p className="text-sm text-blue-600">{suggestion.hex_code}</p>
                      </div>
                    </div>
                    <button
                      onClick={applySuggestion}
                      className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700"
                    >
                      Appliquer
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Test de validation */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Test de Validation de Couleur
            </h3>
            <div className="space-y-4">
              <BackendColorPicker
                selectedColor={selectedColor}
                onColorChange={setSelectedColor}
                eventTitle={testTitle}
                label="Couleur à valider"
                showSuggestions={false}
              />

              <div className="flex gap-2">
                <button
                  onClick={handleValidateColor}
                  className="flex-1 bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700"
                >
                  Valider
                </button>
                <button
                  onClick={handleGetColorInfo}
                  className="flex-1 bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700"
                >
                  Info
                </button>
              </div>

              {validation && (
                <div className={`p-4 rounded-md ${validation.is_valid ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
                  <p className={`font-medium ${validation.is_valid ? 'text-green-900' : 'text-red-900'}`}>
                    {validation.is_valid ? '✅ Couleur valide' : '❌ Couleur invalide'}
                  </p>
                  {validation.message && (
                    <p className={`text-sm mt-1 ${validation.is_valid ? 'text-green-600' : 'text-red-600'}`}>
                      {validation.message}
                    </p>
                  )}
                </div>
              )}

              {colorInfo && (
                <div className="p-4 bg-purple-50 border border-purple-200 rounded-md">
                  <h4 className="font-medium text-purple-900 mb-2">Informations de la couleur</h4>
                  <div className="space-y-1 text-sm text-purple-700">
                    <p><strong>Nom :</strong> {colorInfo.name}</p>
                    <p><strong>Description :</strong> {colorInfo.description}</p>
                    <p><strong>Code hex :</strong> {colorInfo.hex_code}</p>
                    <p><strong>Catégorie :</strong> {colorInfo.category}</p>
                    <p><strong>Usage :</strong> {colorInfo.usage_recommendation}</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Palette complète */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Palette Complète du Backend
          </h3>

          {Object.entries(categories).map(([categoryName, colors]) => (
            <div key={categoryName} className="mb-6">
              <h4 className="text-md font-medium text-gray-700 mb-3 capitalize">
                {categoryName} ({colors.length} couleurs)
              </h4>
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                {colors.map((color) => (
                  <div
                    key={color.id}
                    className="text-center cursor-pointer hover:scale-105 transition-transform"
                    onClick={() => setSelectedColor(color.id)}
                  >
                    <div
                      className="w-16 h-16 rounded-lg border-2 shadow-sm mx-auto mb-2"
                      style={{
                        backgroundColor: color.hex_code,
                        borderColor: selectedColor === color.id ? '#4F46E5' : '#E5E7EB'
                      }}
                    />
                    <p className="text-xs font-medium text-gray-700">{color.name}</p>
                    <p className="text-xs text-gray-500">{color.hex_code}</p>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ColorSystemTest;
