import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Loader2 } from 'lucide-react';
import ClientDashboard from '@/components/dashboard/ClientDashboard';
import AdminDashboard from '@/components/dashboard/AdminDashboard';
import EmployeeDashboard from '@/components/dashboard/EmployeeDashboard';
import SuperAdminDashboard from '@/components/dashboard/SuperAdminDashboard';

const Dashboard = () => {
    const { user, loading } = useAuth();

    if (loading) {
        return (
            <div className="flex justify-center items-center h-64">
                <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
            </div>
        );
    }

    if (!user) {
        return (
            <div className="text-center p-8">
                <p className="text-gray-500">Veuillez vous connecter pour accéder au tableau de bord.</p>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
            <header className="border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50">
                <div className="container mx-auto px-4 py-4 flex justify-between items-center">
                    <h1 className="text-2xl font-bold bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent">
                        Tableau de bord - {user?.name}
                    </h1>
                </div>
            </header>

            <main className="container mx-auto px-4 py-8">
                <div className="bg-white/80 backdrop-blur-sm rounded-xl p-6 shadow-lg">
                    {user.role === 'super_admin' && <SuperAdminDashboard />}
                    {user.role === 'admin' && <AdminDashboard />}
                    {user.role === 'employee' && <EmployeeDashboard />}
                    {user.role === 'client' && <ClientDashboard />}
                </div>
            </main>
        </div>
    );
};

export default Dashboard;