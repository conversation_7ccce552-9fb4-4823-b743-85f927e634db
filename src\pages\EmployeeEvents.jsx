import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useEvent } from '@/contexts/EventContext';
import { toast } from 'react-toastify';
import {
    Calendar,
    Search,
    Filter,
    CheckCircle,
    Clock,
    AlertCircle,
    User,
    Users
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import MemberNavigation from '@/components/MemberNavigation';
import EventCard from '@/components/events/EventCard';
import EventDetailsModal from '@/components/events/EventDetailsModal';
import { permissionService } from '@/services/permissionService';
import eventService from '@/services/eventService';

const EmployeeEvents = () => {
    const navigate = useNavigate();
    const { user } = useAuth();
    const { events, loading, error, fetchEvents } = useEvent();
    const [searchQuery, setSearchQuery] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [filteredEvents, setFilteredEvents] = useState([]);
    const [showDetailsModal, setShowDetailsModal] = useState(false);
    const [selectedEvent, setSelectedEvent] = useState(null);

    // Charger les événements au chargement du composant
    useEffect(() => {
        fetchEvents();
    }, [fetchEvents]);

    // Filtrer les événements en fonction des critères et des permissions
    useEffect(() => {
        if (events.length > 0 && user) {
            // Filtrer les événements dont l'utilisateur est membre ou assigné
            const userEvents = events.filter(event => {
                const permissions = permissionService.checkEventPermissions(user, event);
                return permissions.canView;
            });

            // Appliquer le filtre de statut
            let filtered = userEvents;
            if (statusFilter !== 'all') {
                filtered = filtered.filter(event => event.status === statusFilter);
            }

            // Appliquer le filtre de recherche
            if (searchQuery) {
                filtered = filtered.filter(event =>
                    event.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                    (event.description && event.description.toLowerCase().includes(searchQuery.toLowerCase()))
                );
            }

            setFilteredEvents(filtered);
        } else {
            setFilteredEvents([]);
        }
    }, [events, user, searchQuery, statusFilter]);

    // Ouvrir le modal de détails de l'événement
    const handleOpenDetailsModal = (event) => {
        setSelectedEvent(event);
        setShowDetailsModal(true);
    };

    // Fermer le modal de détails
    const handleCloseDetailsModal = () => {
        setShowDetailsModal(false);
        setSelectedEvent(null);
    };

    // Mettre à jour le statut d'un événement
    const handleUpdateStatus = async (eventId, status) => {
        try {
            await eventService.updateEventStatus(eventId, status);
            toast.success(`Statut de l'événement mis à jour avec succès`);
            fetchEvents();
        } catch (error) {
            console.error('Erreur lors de la mise à jour du statut:', error);
            toast.error(error.message || 'Erreur lors de la mise à jour du statut');
        }
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-gray-50 p-8">
                <div className="max-w-7xl mx-auto">
                    <MemberNavigation />
                    <div className="flex justify-center items-center py-20">
                        <div className="text-xl font-semibold">Chargement des événements...</div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50 p-8">
            <div className="max-w-7xl mx-auto">
                <MemberNavigation />

                <div className="mb-8">
                    <h1 className="text-2xl font-bold text-gray-900 mb-2">Mes Événements</h1>
                    <p className="text-gray-600">
                        Consultez et gérez les événements qui vous sont assignés ou qui concernent vos équipes.
                    </p>
                </div>

                <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
                    <div className="flex flex-col md:flex-row gap-4 mb-6">
                        <div className="flex-grow relative">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                            <input
                                id="search-events"
                                name="search-events"
                                type="text"
                                placeholder="Rechercher un événement..."
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                                className="w-full pl-10 pr-4 py-2 bg-white border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#6B4EFF] focus:border-transparent"
                            />
                        </div>
                        <div className="flex-shrink-0">
                            <div className="relative">
                                <select
                                    id="status-filter-employee"
                                    name="status-filter-employee"
                                    value={statusFilter}
                                    onChange={(e) => setStatusFilter(e.target.value)}
                                    className="appearance-none pl-10 pr-8 py-2 bg-white border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#6B4EFF] focus:border-transparent"
                                >
                                    <option value="all">Tous les statuts</option>
                                    <option value="pending">En attente</option>
                                    <option value="completed">Terminés</option>
                                    <option value="archived">Archivés</option>
                                </select>
                                <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                            </div>
                        </div>
                    </div>

                    {error && (
                        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
                            {error}
                        </div>
                    )}

                    {filteredEvents.length === 0 ? (
                        <div className="text-center py-12 bg-white rounded-xl border border-gray-100 shadow-sm">
                            <Calendar className="w-12 h-12 mx-auto text-[#6B4EFF] mb-4" />
                            <h3 className="text-lg font-medium text-gray-900 mb-2">
                                Aucun événement trouvé
                            </h3>
                            <p className="text-gray-500">
                                Aucun événement ne correspond à vos critères de recherche
                            </p>
                        </div>
                    ) : (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {filteredEvents.map(event => (
                                <div
                                    key={event.id}
                                    className="cursor-pointer"
                                    onClick={() => handleOpenDetailsModal(event)}
                                >
                                    <EventCard
                                        event={event}
                                        onUpdateStatus={() => handleUpdateStatus(event.id, event.status === 'pending' ? 'completed' : 'pending')}
                                    />
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            </div>

            {/* Modal de détails d'événement */}
            {showDetailsModal && selectedEvent && (
                <EventDetailsModal
                    event={selectedEvent}
                    onClose={handleCloseDetailsModal}
                    onUpdateStatus={handleUpdateStatus}
                />
            )}
        </div>
    );
};

export default EmployeeEvents;
