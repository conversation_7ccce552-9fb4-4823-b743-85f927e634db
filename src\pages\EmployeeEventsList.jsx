import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useEvent } from '@/contexts/EventContext';
import { useTeam } from '@/contexts/TeamContext';
import { toast } from 'react-toastify';
import {
    Calendar,
    Search,
    Filter,
    CheckCircle,
    Clock,
    Archive,
    RefreshCw,
    Users,
    User,
    Info
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import EventCard from '@/components/events/EventCard';
import MemberNavigation from '@/components/MemberNavigation';
import EventPermissionGate from '@/components/events/EventPermissionGate';

const EmployeeEventsList = () => {
    const { user } = useAuth();
    const { events, loading, error, fetchEvents, updateEventStatus } = useEvent();
    const { teams } = useTeam();
    const [searchQuery, setSearchQuery] = useState('');
    const [teamFilter, setTeamFilter] = useState('');
    const [statusFilter, setStatusFilter] = useState('');
    const [showStatusModal, setShowStatusModal] = useState(false);
    const [selectedEvent, setSelectedEvent] = useState(null);

    useEffect(() => {
        fetchEvents();
    }, [fetchEvents]);

    // Filtrer les événements en fonction des critères
    const filteredEvents = events.filter(event => {
        // Filtre de recherche
        const matchesSearch =
            event.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
            event.description?.toLowerCase().includes(searchQuery.toLowerCase());

        // Filtre d'équipe
        const matchesTeam = !teamFilter || event.team_id === teamFilter;

        // Filtre de statut
        const matchesStatus = !statusFilter || event.status === statusFilter;

        return matchesSearch && matchesTeam && matchesStatus;
    });

    // Ouvrir le modal de mise à jour du statut
    const handleOpenStatusModal = (event) => {
        setSelectedEvent(event);
        setShowStatusModal(true);
    };

    // Mettre à jour le statut d'un événement
    const handleUpdateStatus = async (status) => {
        if (!selectedEvent) return;

        try {
            await updateEventStatus(selectedEvent.id, status);
            toast.success(`Statut de l'événement mis à jour avec succès`);
            setShowStatusModal(false);
            // Rafraîchir les événements
            fetchEvents();
        } catch (error) {
            console.error('Erreur lors de la mise à jour du statut:', error);
            toast.error(error.message || 'Erreur lors de la mise à jour du statut');
        }
    };

    // Formater la date
    const formatDate = (dateString) => {
        if (!dateString) return '';
        return new Date(dateString).toLocaleDateString('fr-FR', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-gray-50 p-8">
                <div className="max-w-7xl mx-auto">
                    <MemberNavigation />
                    <div className="flex justify-center items-center py-20">
                        <div className="text-xl font-semibold">Chargement des événements...</div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50 p-8">
            <div className="max-w-7xl mx-auto">
                <MemberNavigation />

                <div className="mb-8">
                    <h1 className="text-2xl font-bold text-gray-900 mb-2">Mes Événements</h1>
                    <p className="text-gray-600">
                        Consultez et gérez les événements qui vous sont assignés ou qui concernent vos équipes.
                    </p>
                </div>

                <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
                    <div className="flex flex-col md:flex-row gap-4 mb-6">
                        <div className="flex-grow relative">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                            <Input
                                type="text"
                                placeholder="Rechercher un événement..."
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                                className="pl-10"
                            />
                        </div>
                        <div className="flex gap-2">
                            <select
                                id="team-filter-employee"
                                name="team-filter-employee"
                                value={teamFilter}
                                onChange={(e) => setTeamFilter(e.target.value)}
                                className="border border-gray-300 rounded-md px-3 py-2 text-sm"
                            >
                                <option value="">Toutes les équipes</option>
                                {teams.map(team => (
                                    <option key={team.id} value={team.id}>{team.name}</option>
                                ))}
                            </select>
                            <select
                                id="status-filter-employee-events"
                                name="status-filter-employee-events"
                                value={statusFilter}
                                onChange={(e) => setStatusFilter(e.target.value)}
                                className="border border-gray-300 rounded-md px-3 py-2 text-sm"
                            >
                                <option value="">Tous les statuts</option>
                                <option value="pending">En attente</option>
                                <option value="completed">Terminé</option>
                                <option value="archived">Archivé</option>
                            </select>
                        </div>
                    </div>

                    {error && (
                        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
                            {error}
                        </div>
                    )}

                    {filteredEvents.length === 0 ? (
                        <div className="text-center py-12 bg-white rounded-xl border border-gray-100 shadow-sm">
                            <Calendar className="w-12 h-12 mx-auto text-[#6B4EFF] mb-4" />
                            <h3 className="text-lg font-medium text-gray-900 mb-2">
                                Aucun événement trouvé
                            </h3>
                            <p className="text-gray-500">
                                Aucun événement ne correspond à vos critères de recherche
                            </p>
                        </div>
                    ) : (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {filteredEvents.map(event => (
                                <EventCard
                                    key={event.id}
                                    event={event}
                                    onUpdateStatus={() => handleOpenStatusModal(event)}
                                />
                            ))}
                        </div>
                    )}
                </div>
            </div>

            {/* Modal de mise à jour du statut */}
            {showStatusModal && selectedEvent && (
                <div className="fixed inset-0 flex items-center justify-center p-4 z-50">
                    <div className="fixed inset-0 bg-black/30 backdrop-blur-sm" onClick={() => setShowStatusModal(false)}></div>
                    <div className="bg-white rounded-2xl shadow-xl w-full max-w-md relative">
                        <div className="p-6">
                            <div className="flex justify-between items-center mb-6">
                                <h2 className="text-xl font-semibold text-gray-900">Mettre à jour le statut</h2>
                                <button
                                    onClick={() => setShowStatusModal(false)}
                                    className="text-gray-400 hover:text-gray-500 transition-colors"
                                >
                                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>

                            <div className="mb-4">
                                <h3 className="font-medium text-gray-900">{selectedEvent.title}</h3>
                                <p className="text-sm text-gray-500">{formatDate(selectedEvent.start_date)}</p>
                            </div>

                            <div className="space-y-3">
                                <button
                                    onClick={() => handleUpdateStatus('pending')}
                                    className="w-full flex items-center justify-between p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
                                    disabled={selectedEvent.status === 'pending'}
                                >
                                    <div className="flex items-center">
                                        <Clock className="w-5 h-5 text-yellow-500 mr-3" />
                                        <span>En attente</span>
                                    </div>
                                    {selectedEvent.status === 'pending' && (
                                        <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">Actuel</span>
                                    )}
                                </button>

                                <button
                                    onClick={() => handleUpdateStatus('completed')}
                                    className="w-full flex items-center justify-between p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
                                    disabled={selectedEvent.status === 'completed'}
                                >
                                    <div className="flex items-center">
                                        <CheckCircle className="w-5 h-5 text-green-500 mr-3" />
                                        <span>Terminé</span>
                                    </div>
                                    {selectedEvent.status === 'completed' && (
                                        <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">Actuel</span>
                                    )}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default EmployeeEventsList;
