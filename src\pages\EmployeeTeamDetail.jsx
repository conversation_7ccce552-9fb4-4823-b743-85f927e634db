import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useTeam } from '@/contexts/TeamContext';
import { useEvent } from '@/contexts/EventContext';
import { toast } from 'react-toastify';
import {
    Users,
    Calendar,
    ArrowLeft,
    User,
    Clock,
    CheckCircle,
    AlertCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import MemberNavigation from '@/components/MemberNavigation';
import EventCard from '@/components/events/EventCard';
import { permissionService } from '@/services/permissionService';
import eventService from '@/services/eventService';

const EmployeeTeamDetail = () => {
    const { teamId } = useParams();
    const navigate = useNavigate();
    const { user } = useAuth();
    const { teams, loading: teamsLoading, error: teamsError, fetchTeams } = useTeam();
    const { events, loading: eventsLoading, error: eventsError, fetchEvents } = useEvent();
    const [team, setTeam] = useState(null);
    const [teamEvents, setTeamEvents] = useState([]);
    const [activeTab, setActiveTab] = useState('overview');
    const [showStatusModal, setShowStatusModal] = useState(false);
    const [selectedEvent, setSelectedEvent] = useState(null);

    // Charger les données de l'équipe et des événements
    useEffect(() => {
        const loadData = async () => {
            await fetchTeams();
            await fetchEvents();
        };

        loadData();
    }, [fetchTeams, fetchEvents]);

    // Filtrer l'équipe et les événements une fois les données chargées
    useEffect(() => {
        if (teams.length > 0) {
            const foundTeam = teams.find(t => t.id === teamId);

            if (foundTeam) {
                // Vérifier si l'utilisateur est membre de cette équipe
                const permissions = permissionService.checkTeamPermissions(user, foundTeam);

                if (!permissions.isMember) {
                    toast.error("Vous n'avez pas accès à cette équipe");
                    navigate('/employee-teams');
                    return;
                }

                setTeam(foundTeam);
            } else {
                toast.error("Équipe non trouvée");
                navigate('/employee-teams');
            }
        }

        if (events.length > 0 && teamId) {
            const filteredEvents = events.filter(event => event.team_id === teamId);
            setTeamEvents(filteredEvents);
        }
    }, [teams, events, teamId, user, navigate]);

    // Ouvrir le modal de mise à jour du statut
    const handleOpenStatusModal = (event) => {
        setSelectedEvent(event);
        setShowStatusModal(true);
    };

    // Mettre à jour le statut d'un événement
    const handleUpdateStatus = async (status) => {
        if (!selectedEvent) return;

        try {
            await eventService.updateEventStatus(selectedEvent.id, status);
            toast.success(`Statut de l'événement mis à jour avec succès`);
            setShowStatusModal(false);
            // Rafraîchir les événements
            fetchEvents();
        } catch (error) {
            console.error('Erreur lors de la mise à jour du statut:', error);
            toast.error(error.message || 'Erreur lors de la mise à jour du statut');
        }
    };

    // Formater la date
    const formatDate = (dateString) => {
        if (!dateString) return '';
        return new Date(dateString).toLocaleDateString('fr-FR', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    if (teamsLoading || eventsLoading) {
        return (
            <div className="min-h-screen bg-gray-50 p-8">
                <div className="max-w-7xl mx-auto">
                    <MemberNavigation />
                    <div className="flex justify-center items-center py-20">
                        <div className="text-xl font-semibold">Chargement...</div>
                    </div>
                </div>
            </div>
        );
    }

    if (teamsError || eventsError) {
        return (
            <div className="min-h-screen bg-gray-50 p-8">
                <div className="max-w-7xl mx-auto">
                    <MemberNavigation />
                    <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
                        {teamsError || eventsError}
                    </div>
                </div>
            </div>
        );
    }

    if (!team) {
        return (
            <div className="min-h-screen bg-gray-50 p-8">
                <div className="max-w-7xl mx-auto">
                    <MemberNavigation />
                    <div className="flex justify-center items-center py-20">
                        <div className="text-xl font-semibold">Équipe non trouvée</div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50 p-8">
            <div className="max-w-7xl mx-auto">
                <MemberNavigation />

                <div className="mb-6">
                    <button
                        onClick={() => navigate('/employee-teams')}
                        className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
                    >
                        <ArrowLeft className="w-4 h-4 mr-2" />
                        <span>Retour aux équipes</span>
                    </button>
                </div>

                <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200 mb-8">
                    <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6">
                        <div>
                            <h1 className="text-2xl font-bold text-gray-900">{team.name}</h1>
                            <p className="text-gray-600 mt-1">{team.description}</p>
                        </div>
                    </div>

                    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                        <TabsList className="mb-6">
                            <TabsTrigger value="overview">Vue d'ensemble</TabsTrigger>
                            <TabsTrigger value="events">Événements</TabsTrigger>
                            <TabsTrigger value="members">Membres</TabsTrigger>
                        </TabsList>

                        <TabsContent value="overview" className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="bg-gray-50 rounded-lg p-6 border border-gray-100">
                                    <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                        <Users className="w-5 h-5 mr-2 text-[#6B4EFF]" />
                                        Responsable
                                    </h3>
                                    <div className="flex items-center">
                                        <div className="w-10 h-10 rounded-full bg-[#6B4EFF]/10 flex items-center justify-center text-sm font-medium text-[#6B4EFF]">
                                            {team.responsable_name?.charAt(0).toUpperCase() || 'A'}
                                        </div>
                                        <div className="ml-3">
                                            <div className="text-sm font-medium text-gray-900">{team.responsable_name || 'Administrateur'}</div>
                                            <div className="text-xs text-gray-500">Administrateur</div>
                                        </div>
                                    </div>
                                </div>

                                <div className="bg-gray-50 rounded-lg p-6 border border-gray-100">
                                    <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                        <Calendar className="w-5 h-5 mr-2 text-[#6B4EFF]" />
                                        Événements
                                    </h3>
                                    <div className="space-y-2">
                                        <div className="flex justify-between items-center">
                                            <span className="text-sm text-gray-600">Total</span>
                                            <span className="font-medium">{teamEvents.length}</span>
                                        </div>
                                        <div className="flex justify-between items-center">
                                            <span className="text-sm text-gray-600">En attente</span>
                                            <span className="font-medium">{teamEvents.filter(e => e.status === 'pending').length}</span>
                                        </div>
                                        <div className="flex justify-between items-center">
                                            <span className="text-sm text-gray-600">Terminés</span>
                                            <span className="font-medium">{teamEvents.filter(e => e.status === 'completed').length}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div className="bg-gray-50 rounded-lg p-6 border border-gray-100">
                                <h3 className="text-lg font-semibold text-gray-900 mb-4">Événements à venir</h3>
                                {teamEvents.filter(e => e.status === 'pending').length === 0 ? (
                                    <p className="text-gray-500">Aucun événement en attente</p>
                                ) : (
                                    <div className="space-y-4">
                                        {teamEvents
                                            .filter(e => e.status === 'pending')
                                            .slice(0, 3)
                                            .map(event => (
                                                <div key={event.id} className="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-100">
                                                    <div className="flex items-center">
                                                        <div className="w-2 h-10 bg-yellow-400 rounded-l-lg mr-3"></div>
                                                        <div>
                                                            <div className="font-medium text-gray-900">{event.title}</div>
                                                            <div className="text-xs text-gray-500">{formatDate(event.start_date)}</div>
                                                        </div>
                                                    </div>
                                                    {event.can_update_status && (
                                                        <Button
                                                            variant="outline"
                                                            size="sm"
                                                            onClick={() => handleOpenStatusModal(event)}
                                                        >
                                                            Mettre à jour
                                                        </Button>
                                                    )}
                                                </div>
                                            ))
                                        }
                                    </div>
                                )}
                            </div>
                        </TabsContent>

                        <TabsContent value="events">
                            {teamEvents.length === 0 ? (
                                <div className="text-center py-12 bg-white rounded-xl border border-gray-100 shadow-sm">
                                    <Calendar className="w-12 h-12 mx-auto text-[#6B4EFF] mb-4" />
                                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                                        Aucun événement trouvé
                                    </h3>
                                    <p className="text-gray-500">
                                        Cette équipe n'a pas encore d'événements
                                    </p>
                                </div>
                            ) : (
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                    {teamEvents.map(event => (
                                        <EventCard
                                            key={event.id}
                                            event={event}
                                            onUpdateStatus={() => handleOpenStatusModal(event)}
                                        />
                                    ))}
                                </div>
                            )}
                        </TabsContent>

                        <TabsContent value="members">
                            <div className="space-y-4">
                                <div className="bg-gray-50 rounded-lg p-6 border border-gray-100">
                                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Responsable</h3>
                                    <div className="flex items-center">
                                        <div className="w-10 h-10 rounded-full bg-[#6B4EFF]/10 flex items-center justify-center text-sm font-medium text-[#6B4EFF]">
                                            {team.responsable_name?.charAt(0).toUpperCase() || 'A'}
                                        </div>
                                        <div className="ml-3">
                                            <div className="text-sm font-medium text-gray-900">{team.responsable_name || 'Administrateur'}</div>
                                            <div className="text-xs text-gray-500">Administrateur</div>
                                        </div>
                                    </div>
                                </div>

                                <div className="bg-gray-50 rounded-lg p-6 border border-gray-100">
                                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Membres ({team.members_count || 0})</h3>
                                    {team.members_list && team.members_list.length > 0 ? (
                                        <div className="space-y-3">
                                            {team.members_list.map(member => (
                                                <div key={member.id} className="flex items-center justify-between">
                                                    <div className="flex items-center">
                                                        <div className="w-8 h-8 rounded-full bg-[#6B4EFF]/10 flex items-center justify-center text-xs font-medium text-[#6B4EFF]">
                                                            {member.name?.charAt(0).toUpperCase() || 'U'}
                                                        </div>
                                                        <div className="ml-3">
                                                            <div className="text-sm font-medium text-gray-900">{member.name || 'Utilisateur'}</div>
                                                            <div className="text-xs text-gray-500">{member.email || ''}</div>
                                                        </div>
                                                    </div>
                                                    {member.id === user.id && (
                                                        <span className="text-xs bg-[#6B4EFF]/10 text-[#6B4EFF] px-2 py-1 rounded-full">
                                                            Vous
                                                        </span>
                                                    )}
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <p className="text-gray-500">Aucun membre dans cette équipe</p>
                                    )}
                                </div>
                            </div>
                        </TabsContent>
                    </Tabs>
                </div>
            </div>
        </div>
    );
};

export default EmployeeTeamDetail;
