import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useTeam } from '@/contexts/TeamContext';
import { toast } from 'react-toastify';
import {
    Users,
    ArrowLeft
} from 'lucide-react';
import { permissionService } from '@/services/permissionService';

const EmployeeTeamDetail = () => {
    const { teamId } = useParams();
    const navigate = useNavigate();
    const { user } = useAuth();
    const { teams, loading: teamsLoading, error: teamsError, fetchTeams } = useTeam();
    const [team, setTeam] = useState(null);

    // Charger les données de l'équipe
    useEffect(() => {
        fetchTeams();
    }, [fetchTeams]);

    // Filtrer l'équipe une fois les données chargées
    useEffect(() => {
        if (teams.length > 0) {
            const foundTeam = teams.find(t => t.id === teamId);

            if (foundTeam) {
                // Vérifier si l'utilisateur est membre de cette équipe
                const permissions = permissionService.checkTeamPermissions(user, foundTeam);

                if (!permissions.isMember) {
                    toast.error("Vous n'avez pas accès à cette équipe");
                    navigate('/employee-teams');
                    return;
                }

                setTeam(foundTeam);
            } else {
                toast.error("Équipe non trouvée");
                navigate('/employee-teams');
            }
        }
    }, [teams, teamId, user, navigate]);



    if (teamsLoading) {
        return (
            <div className="min-h-screen bg-gray-50 p-8">
                <div className="max-w-7xl mx-auto">
                    <div className="flex justify-center items-center py-20">
                        <div className="text-xl font-semibold">Chargement...</div>
                    </div>
                </div>
            </div>
        );
    }

    if (teamsError) {
        return (
            <div className="min-h-screen bg-gray-50 p-8">
                <div className="max-w-7xl mx-auto">
                    <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
                        {teamsError}
                    </div>
                </div>
            </div>
        );
    }

    if (!team) {
        return (
            <div className="min-h-screen bg-gray-50 p-8">
                <div className="max-w-7xl mx-auto">
                    <div className="flex justify-center items-center py-20">
                        <div className="text-xl font-semibold">Équipe non trouvée</div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50 p-8">
            <div className="max-w-7xl mx-auto">
                <div className="mb-6">
                    <button
                        onClick={() => navigate('/employee-teams')}
                        className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
                    >
                        <ArrowLeft className="w-4 h-4 mr-2" />
                        <span>Retour aux équipes</span>
                    </button>
                </div>

                <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200 mb-8">
                    <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6">
                        <div>
                            <h1 className="text-2xl font-bold text-gray-900">{team.name}</h1>
                            <p className="text-gray-600 mt-1">{team.description}</p>
                        </div>
                    </div>

                    <div className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="bg-gray-50 rounded-lg p-6 border border-gray-100">
                                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                    <Users className="w-5 h-5 mr-2 text-[#6B4EFF]" />
                                    Responsable
                                </h3>
                                <div className="flex items-center">
                                    <div className="w-10 h-10 rounded-full bg-[#6B4EFF]/10 flex items-center justify-center text-sm font-medium text-[#6B4EFF]">
                                        {team.responsable_name?.charAt(0).toUpperCase() || 'A'}
                                    </div>
                                    <div className="ml-3">
                                        <div className="text-sm font-medium text-gray-900">{team.responsable_name || 'Administrateur'}</div>
                                        <div className="text-xs text-gray-500">Administrateur</div>
                                    </div>
                                </div>
                            </div>

                            <div className="bg-gray-50 rounded-lg p-6 border border-gray-100">
                                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                    <Users className="w-5 h-5 mr-2 text-[#6B4EFF]" />
                                    Membres ({team.members_count || 0})
                                </h3>
                                {team.members_list && team.members_list.length > 0 ? (
                                    <div className="space-y-3 max-h-32 overflow-y-auto">
                                        {team.members_list.map(member => (
                                            <div key={member.id} className="flex items-center justify-between">
                                                <div className="flex items-center">
                                                    <div className="w-8 h-8 rounded-full bg-[#6B4EFF]/10 flex items-center justify-center text-xs font-medium text-[#6B4EFF]">
                                                        {member.name?.charAt(0).toUpperCase() || 'U'}
                                                    </div>
                                                    <div className="ml-3">
                                                        <div className="text-sm font-medium text-gray-900">{member.name || 'Utilisateur'}</div>
                                                        <div className="text-xs text-gray-500">{member.email || ''}</div>
                                                    </div>
                                                </div>
                                                {member.id === user.id && (
                                                    <span className="text-xs bg-[#6B4EFF]/10 text-[#6B4EFF] px-2 py-1 rounded-full">
                                                        Vous
                                                    </span>
                                                )}
                                            </div>
                                        ))}
                                    </div>
                                ) : (
                                    <p className="text-gray-500">Aucun membre dans cette équipe</p>
                                )}
                            </div>
                        </div>
                    </div>


                </div>
            </div>
        </div >
    );
};

export default EmployeeTeamDetail;
