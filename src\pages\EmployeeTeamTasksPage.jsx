import React, { useState, useEffect } from 'react';
import { useTeamTask } from '@/contexts/TeamTaskContext';
import { useTeam } from '@/contexts/TeamContext';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'react-toastify';
import {
  Search,
  Filter,
  CheckCircle,
  Clock,
  Circle,
  RefreshCw,
  Loader2,
  ListFilter,
  X,
  Kanban,
  LayoutList,
  LayoutGrid
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import TeamTaskCard from '@/components/tasks/TeamTaskCard';
import MemberNavigation from '@/components/MemberNavigation';

const EmployeeTeamTasksPage = () => {
  const { user } = useAuth();
  const { teams } = useTeam();
  const {
    tasks,
    loading,
    error,
    filters,
    updateFilters,
    resetFilters,
    fetchTasks,
    updateTaskStatus
  } = useTeamTask();

  // États locaux
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);
  const [displayMode, setDisplayMode] = useState('card'); // 'list', 'card', 'kanban'
  const [activeTab, setActiveTab] = useState('a_faire');

  // Mettre à jour les filtres lorsque la recherche change
  useEffect(() => {
    const timer = setTimeout(() => {
      updateFilters({ search: searchQuery });
    }, 300);
    return () => clearTimeout(timer);
  }, [searchQuery, updateFilters]);

  // Filtrer les tâches selon le statut actif
  const filteredTasks = tasks.filter(task => {
    // Filtrer par statut (onglet actif)
    if (activeTab !== 'all' && task.status !== activeTab) return false;

    // Filtrer par recherche (déjà géré par le contexte)
    return true;
  });

  // Grouper les tâches par statut pour l'affichage Kanban
  const tasksByStatus = {
    a_faire: filteredTasks.filter(task => task.status === 'a_faire'),
    en_cours: filteredTasks.filter(task => task.status === 'en_cours'),
    achevee: filteredTasks.filter(task => task.status === 'achevee'),
    archived: filteredTasks.filter(task => task.status === 'archived')
  };

  // Gérer la mise à jour du statut d'une tâche
  const handleUpdateStatus = async (taskId, status) => {
    setActionLoading(true);
    try {
      await updateTaskStatus(taskId, status);
    } catch (error) {
      console.error('Erreur lors de la mise à jour du statut:', error);
    } finally {
      setActionLoading(false);
    }
  };

  // Rafraîchir les tâches
  const handleRefresh = () => {
    fetchTasks();
  };

  // Rendu du contenu selon le mode d'affichage
  const renderContent = () => {
    if (loading) {
      return (
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
        </div>
      );
    }

    if (error) {
      return (
        <div className="bg-red-50 p-4 rounded-lg">
          <p className="text-red-600">{error}</p>
        </div>
      );
    }

    if (filteredTasks.length === 0) {
      return (
        <div className="text-center py-12">
          <Clock className="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900">Aucune tâche trouvée</h3>
          <p className="text-gray-500 mt-2">
            {searchQuery ? 'Aucune tâche ne correspond à votre recherche.' : 'Vous n\'avez pas de tâches assignées.'}
          </p>
        </div>
      );
    }

    if (displayMode === 'kanban') {
      return (
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Colonne "À faire" */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center mb-4">
              <Clock className="h-5 w-5 text-yellow-500 mr-2" />
              <h3 className="font-medium">À faire</h3>
              <span className="ml-2 bg-yellow-100 text-yellow-800 text-xs font-medium px-2 py-0.5 rounded-full">
                {tasksByStatus.a_faire.length}
              </span>
            </div>
            <div className="space-y-4">
              {tasksByStatus.a_faire.map(task => (
                <TeamTaskCard
                  key={task.id}
                  task={task}
                  onUpdateStatus={handleUpdateStatus}
                />
              ))}
            </div>
          </div>

          {/* Colonne "En cours" */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center mb-4">
              <Circle className="h-5 w-5 text-blue-500 mr-2" />
              <h3 className="font-medium">En cours</h3>
              <span className="ml-2 bg-blue-100 text-blue-800 text-xs font-medium px-2 py-0.5 rounded-full">
                {tasksByStatus.en_cours.length}
              </span>
            </div>
            <div className="space-y-4">
              {tasksByStatus.en_cours.map(task => (
                <TeamTaskCard
                  key={task.id}
                  task={task}
                  onUpdateStatus={handleUpdateStatus}
                />
              ))}
            </div>
          </div>

          {/* Colonne "Achevée" */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center mb-4">
              <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
              <h3 className="font-medium">Achevée</h3>
              <span className="ml-2 bg-green-100 text-green-800 text-xs font-medium px-2 py-0.5 rounded-full">
                {tasksByStatus.achevee.length}
              </span>
            </div>
            <div className="space-y-4">
              {tasksByStatus.achevee.map(task => (
                <TeamTaskCard
                  key={task.id}
                  task={task}
                  onUpdateStatus={handleUpdateStatus}
                />
              ))}
            </div>
          </div>

          {/* Colonne "Archivée" */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center mb-4">
              <X className="h-5 w-5 text-gray-500 mr-2" />
              <h3 className="font-medium">Archivée</h3>
              <span className="ml-2 bg-gray-100 text-gray-800 text-xs font-medium px-2 py-0.5 rounded-full">
                {tasksByStatus.archived.length}
              </span>
            </div>
            <div className="space-y-4">
              {tasksByStatus.archived.map(task => (
                <TeamTaskCard
                  key={task.id}
                  task={task}
                  onUpdateStatus={handleUpdateStatus}
                />
              ))}
            </div>
          </div>
        </div>
      );
    }

    // Mode d'affichage par défaut (card ou list)
    return (
      <div className={`grid ${displayMode === 'card' ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1'} gap-6`}>
        {filteredTasks.map(task => (
          <TeamTaskCard
            key={task.id}
            task={task}
            onUpdateStatus={handleUpdateStatus}
          />
        ))}
      </div>
    );
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <MemberNavigation />

      <div className="bg-white rounded-xl shadow-lg overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <h1 className="text-2xl font-bold text-gray-900">Mes tâches d'équipe</h1>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                onClick={handleRefresh}
                className="flex items-center gap-2"
                disabled={loading}
              >
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                Actualiser
              </Button>
            </div>
          </div>
        </div>

        <div className="p-6 border-b border-gray-200 bg-gray-50">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div className="relative w-full md:w-64">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                type="text"
                placeholder="Rechercher une tâche..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            <div className="flex items-center gap-2 w-full md:w-auto">
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center gap-2"
              >
                <Filter className="h-4 w-4" />
                Filtres
              </Button>

              <div className="flex items-center border rounded-md overflow-hidden">
                <Button
                  variant={displayMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setDisplayMode('list')}
                  className="rounded-none"
                >
                  <LayoutList className="h-4 w-4" />
                </Button>
                <Button
                  variant={displayMode === 'card' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setDisplayMode('card')}
                  className="rounded-none"
                >
                  <LayoutGrid className="h-4 w-4" />
                </Button>
                <Button
                  variant={displayMode === 'kanban' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setDisplayMode('kanban')}
                  className="rounded-none"
                >
                  <Kanban className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {showFilters && (
            <div className="mt-4 p-4 bg-white rounded-lg border border-gray-200">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-sm font-medium text-gray-700">Filtres avancés</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowFilters(false)}
                  className="h-8 w-8 p-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">Équipe</label>
                  <Select
                    value={filters.team_id}
                    onValueChange={(value) => updateFilters({ team_id: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Toutes les équipes" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">Toutes les équipes</SelectItem>
                      {teams.map((team) => (
                        <SelectItem key={team.id} value={team.id}>
                          {team.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-end">
                  <Button
                    variant="outline"
                    onClick={resetFilters}
                    className="w-full"
                  >
                    Réinitialiser les filtres
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>

        {displayMode !== 'kanban' && (
          <div className="px-6 pt-6">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="mb-6">
                <TabsTrigger value="all">Toutes</TabsTrigger>
                <TabsTrigger value="a_faire">À faire</TabsTrigger>
                <TabsTrigger value="en_cours">En cours</TabsTrigger>
                <TabsTrigger value="achevee">Achevées</TabsTrigger>
                <TabsTrigger value="archived">Archivées</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        )}

        <div className="p-6">
          {renderContent()}
        </div>
      </div>
    </div>
  );
};

export default EmployeeTeamTasksPage;
