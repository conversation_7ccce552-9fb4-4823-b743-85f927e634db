import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useTeam } from '@/contexts/TeamContext';
import { toast } from 'react-toastify';
import {
    Users,
    Search,
    Users as UserGroup,
    User,
    Calendar
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { permissionService } from '@/services/permissionService';

const EmployeeTeams = () => {
    const navigate = useNavigate();
    const { user } = useAuth();
    const { teams, loading, error, fetchTeams } = useTeam();
    const [searchQuery, setSearchQuery] = useState('');
    const [filteredTeams, setFilteredTeams] = useState([]);

    // Charger les équipes au chargement du composant
    useEffect(() => {
        fetchTeams();
    }, [fetchTeams]);

    // Filtrer les équipes en fonction des critères et des permissions
    useEffect(() => {
        if (teams.length > 0 && user) {
            // Filtrer les équipes dont l'utilisateur est membre
            const userTeams = teams.filter(team => {
                const permissions = permissionService.checkTeamPermissions(user, team);
                return permissions.canView;
            });

            // Appliquer le filtre de recherche
            const filtered = userTeams.filter(team =>
                team.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                (team.description && team.description.toLowerCase().includes(searchQuery.toLowerCase()))
            );

            setFilteredTeams(filtered);
        } else {
            setFilteredTeams([]);
        }
    }, [teams, user, searchQuery]);

    // Naviguer vers la page de détails d'une équipe
    const handleViewTeam = (teamId) => {
        navigate(`/employee-teams/${teamId}`);
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-gray-50 p-8">
                <div className="max-w-7xl mx-auto">
                    <div className="flex justify-center items-center py-20">
                        <div className="text-xl font-semibold">Chargement des équipes...</div>
                    </div>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="min-h-screen bg-gray-50 p-8">
                <div className="max-w-7xl mx-auto">
                    <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
                        {error}
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50 p-8">
            <div className="max-w-7xl mx-auto">
                <div className="mb-8">
                    <h1 className="text-2xl font-bold text-gray-900 mb-2">Mes Équipes</h1>
                    <p className="text-gray-600">
                        Consultez les équipes dont vous êtes membre et accédez à leurs événements.
                    </p>
                </div>

                <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
                    <div className="flex flex-col md:flex-row gap-4 mb-6">
                        <div className="flex-grow relative">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                            <input
                                type="text"
                                placeholder="Rechercher une équipe..."
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                                className="w-full pl-10 pr-4 py-2 bg-white border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#6B4EFF] focus:border-transparent"
                            />
                        </div>
                    </div>

                    {filteredTeams.length === 0 ? (
                        <div className="text-center py-12 bg-white rounded-xl border border-gray-100 shadow-sm">
                            <UserGroup className="w-12 h-12 mx-auto text-[#6B4EFF] mb-4" />
                            <h3 className="text-lg font-medium text-gray-900 mb-2">
                                Aucune équipe trouvée
                            </h3>
                            <p className="text-gray-500">
                                Vous n'êtes membre d'aucune équipe ou aucune équipe ne correspond à votre recherche
                            </p>
                        </div>
                    ) : (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {filteredTeams.map(team => (
                                <div
                                    key={team.id}
                                    className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden hover:shadow-md transition-shadow"
                                >
                                    <div className="p-6">
                                        <div className="flex items-center justify-between mb-4">
                                            <div className="w-10 h-10 rounded-full bg-[#6B4EFF]/10 flex items-center justify-center">
                                                <Users className="w-5 h-5 text-[#6B4EFF]" />
                                            </div>
                                            <div className="text-xs font-medium px-2 py-1 rounded-full bg-gray-100 text-gray-700">
                                                {team.members_count || (team.members ? Object.keys(team.members).length : 0)} membres
                                            </div>
                                        </div>
                                        <h3 className="text-lg font-semibold text-gray-900 mb-2">{team.name}</h3>
                                        <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                                            {team.description || 'Aucune description'}
                                        </p>
                                        <div className="flex items-center text-sm text-gray-500 mb-4">
                                            <User className="w-4 h-4 mr-2" />
                                            <span>Responsable: {team.responsable_name || 'Non défini'}</span>
                                        </div>
                                        <Button
                                            variant="outline"
                                            className="w-full"
                                            onClick={() => handleViewTeam(team.id)}
                                        >
                                            Voir les détails
                                        </Button>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default EmployeeTeams;