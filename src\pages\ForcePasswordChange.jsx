import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { authService } from '@/services/authService';
import { toast } from 'react-toastify';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Eye, EyeOff, Lock } from 'lucide-react';

const ForcePasswordChange = () => {
    const { completePasswordChange, user } = useAuth();
    const navigate = useNavigate();
    const [formData, setFormData] = useState({
        current_password: '',
        new_password: '',
        confirm_password: ''
    });
    const [errors, setErrors] = useState({});
    const [isLoading, setIsLoading] = useState(false);
    const [showCurrentPassword, setShowCurrentPassword] = useState(false);
    const [showNewPassword, setShowNewPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [generalError, setGeneralError] = useState('');

    useEffect(() => {
        // Rediriger vers la page de connexion si l'utilisateur n'est pas connecté
        if (!user) {
            navigate('/login');
        }
    }, [user, navigate]);

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
        // Clear error when user starts typing
        if (errors[name]) {
            setErrors(prev => ({
                ...prev,
                [name]: ''
            }));
        }
    };

    const validateForm = () => {
        const newErrors = {};
        
        // Validate current password
        if (!formData.current_password) {
            newErrors.current_password = 'Le mot de passe actuel est requis';
        }
        
        // Validate new password
        if (!formData.new_password) {
            newErrors.new_password = 'Le nouveau mot de passe est requis';
        } else if (formData.new_password.length < 6) {
            newErrors.new_password = 'Le mot de passe doit contenir au moins 6 caractères';
        } else if (!/[A-Z]/.test(formData.new_password)) {
            newErrors.new_password = 'Le mot de passe doit contenir au moins une lettre majuscule';
        } else if (!/[a-z]/.test(formData.new_password)) {
            newErrors.new_password = 'Le mot de passe doit contenir au moins une lettre minuscule';
        } else if (!/\d/.test(formData.new_password)) {
            newErrors.new_password = 'Le mot de passe doit contenir au moins un chiffre';
        }
        
        // Validate password confirmation
        if (!formData.confirm_password) {
            newErrors.confirm_password = 'La confirmation du mot de passe est requise';
        } else if (formData.new_password !== formData.confirm_password) {
            newErrors.confirm_password = 'Les mots de passe ne correspondent pas';
        }
        
        // Check if new password is different from current
        if (formData.current_password && formData.new_password && 
            formData.current_password === formData.new_password) {
            newErrors.new_password = 'Le nouveau mot de passe doit être différent de l\'ancien';
        }
        
        return newErrors;
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setGeneralError('');
        
        const validationErrors = validateForm();
        if (Object.keys(validationErrors).length > 0) {
            setErrors(validationErrors);
            return;
        }
        
        setIsLoading(true);
        try {
            const response = await authService.changePassword(formData);
            
            if (response.requiresRelogin) {
                toast.success('Votre mot de passe a été changé avec succès. Veuillez vous reconnecter avec votre nouveau mot de passe.');
                navigate('/login');
            } else {
                completePasswordChange();
                toast.success('Votre mot de passe a été changé avec succès');
                navigate(authService.getRedirectPath());
            }
        } catch (err) {
            console.error('Password change error:', err);
            const errorMessage = err.error || err.message || 'Échec du changement de mot de passe';
            toast.error(errorMessage);
            setGeneralError(errorMessage);
            
            // Réinitialiser le mot de passe actuel pour permettre une nouvelle tentative
            setFormData(prev => ({
                ...prev,
                current_password: ''
            }));
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-white via-blue-50/30 to-indigo-50/30 dark:from-gray-900 dark:via-gray-800/30 dark:to-gray-900/30 flex items-center justify-center p-4">
            <div className="w-full max-w-md bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm py-8 px-4 shadow-xl rounded-2xl sm:px-10 relative">
                <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-purple-500/5 dark:from-primary/10 dark:to-purple-500/10 rounded-2xl transform rotate-1"></div>
                <div className="relative">
                    <h2 className="text-2xl font-bold mb-6 text-center">Changement de mot de passe requis</h2>
                    <p className="text-gray-600 dark:text-gray-300 mb-6 text-center">
                        Pour des raisons de sécurité, vous devez changer votre mot de passe temporaire avant de continuer.
                    </p>
                    
                    {generalError && (
                        <div className="bg-red-50 dark:bg-red-900/50 text-red-800 dark:text-red-200 p-3 rounded-lg mb-4 text-sm">
                            {generalError}
                        </div>
                    )}
                    
                    <form onSubmit={handleSubmit} className="space-y-6">
                        <div>
                            <Label htmlFor="current_password" className="flex items-center gap-2 text-gray-700 dark:text-gray-200">
                                <Lock className="w-4 h-4" />
                                Mot de passe temporaire
                            </Label>
                            <div className="mt-1 relative">
                                <Input
                                    id="current_password"
                                    name="current_password"
                                    type={showCurrentPassword ? "text" : "password"}
                                    autoComplete="current-password"
                                    placeholder="Entrez votre mot de passe temporaire"
                                    value={formData.current_password}
                                    onChange={handleChange}
                                    className={`block w-full rounded-lg border ${errors.current_password ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-gray-600'} pr-10`}
                                />
                                <button
                                    type="button"
                                    onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                                    className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600 dark:text-gray-300 dark:hover:text-gray-100"
                                >
                                    {showCurrentPassword ? (
                                        <EyeOff className="h-5 w-5" />
                                    ) : (
                                        <Eye className="h-5 w-5" />
                                    )}
                                </button>
                            </div>
                            {errors.current_password && (
                                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.current_password}</p>
                            )}
                        </div>
                        
                        <div>
                            <Label htmlFor="new_password" className="flex items-center gap-2 text-gray-700 dark:text-gray-200">
                                <Lock className="w-4 h-4" />
                                Nouveau mot de passe
                            </Label>
                            <div className="mt-1 relative">
                                <Input
                                    id="new_password"
                                    name="new_password"
                                    type={showNewPassword ? "text" : "password"}
                                    autoComplete="new-password"
                                    placeholder="Entrez votre nouveau mot de passe"
                                    value={formData.new_password}
                                    onChange={handleChange}
                                    className={`block w-full rounded-lg border ${errors.new_password ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-gray-600'} pr-10`}
                                />
                                <button
                                    type="button"
                                    onClick={() => setShowNewPassword(!showNewPassword)}
                                    className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600 dark:text-gray-300 dark:hover:text-gray-100"
                                >
                                    {showNewPassword ? (
                                        <EyeOff className="h-5 w-5" />
                                    ) : (
                                        <Eye className="h-5 w-5" />
                                    )}
                                </button>
                            </div>
                            {errors.new_password && (
                                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.new_password}</p>
                            )}
                        </div>
                        
                        <div>
                            <Label htmlFor="confirm_password" className="flex items-center gap-2 text-gray-700 dark:text-gray-200">
                                <Lock className="w-4 h-4" />
                                Confirmer le mot de passe
                            </Label>
                            <div className="mt-1 relative">
                                <Input
                                    id="confirm_password"
                                    name="confirm_password"
                                    type={showConfirmPassword ? "text" : "password"}
                                    autoComplete="new-password"
                                    placeholder="Confirmez votre nouveau mot de passe"
                                    value={formData.confirm_password}
                                    onChange={handleChange}
                                    className={`block w-full rounded-lg border ${errors.confirm_password ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-gray-600'} pr-10`}
                                />
                                <button
                                    type="button"
                                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                    className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600 dark:text-gray-300 dark:hover:text-gray-100"
                                >
                                    {showConfirmPassword ? (
                                        <EyeOff className="h-5 w-5" />
                                    ) : (
                                        <Eye className="h-5 w-5" />
                                    )}
                                </button>
                            </div>
                            {errors.confirm_password && (
                                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.confirm_password}</p>
                            )}
                        </div>
                        
                        <div>
                            <Button 
                                type="submit" 
                                className="w-full" 
                                disabled={isLoading}
                            >
                                {isLoading ? 'Chargement...' : 'Changer le mot de passe'}
                            </Button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
};

export default ForcePasswordChange;