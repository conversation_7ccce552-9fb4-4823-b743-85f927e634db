import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { showSuccessToast, showErrorToast } from '@/utils/toastUtils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Mail, ArrowLeft } from 'lucide-react';
import { passwordService } from '@/services/passwordService';

const ForgotPassword = () => {
    const { t } = useTranslation();
    const [email, setEmail] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [isSubmitted, setIsSubmitted] = useState(false);
    const [error, setError] = useState('');
    const [countdown, setCountdown] = useState(0);

    useEffect(() => {
        let timer;
        if (countdown > 0) {
            timer = setInterval(() => {
                setCountdown(prev => prev - 1);
            }, 1000);
        }
        return () => clearInterval(timer);
    }, [countdown]);

    const validateEmail = (email) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        if (!email) {
            setError('Veuillez entrer votre adresse email');
            return;
        }
        if (!validateEmail(email)) {
            setError('Veuillez entrer une adresse email valide');
            return;
        }

        setIsLoading(true);
        setError('');

        try {
            const response = await passwordService.requestPasswordReset(email);

            // Fonction pour traduire les messages en français si nécessaire
            const translateMessage = (message) => {
                if (!message) return message;

                const lowerMessage = message.toLowerCase();
                if (lowerMessage.includes('if this email is associated') ||
                    lowerMessage.includes('reset link will be sent')) {
                    return 'Si cette adresse email est associée à un compte, un lien de réinitialisation sera envoyé';
                }
                return message;
            };

            // Traiter les réponses de succès et les messages informatifs
            if (response.type === 'success' || response.status === 200) {
                const translatedMessage = translateMessage(response.message);
                setIsSubmitted(true);
                setCountdown(60);
                showSuccessToast(translatedMessage);
                setEmail('');
                setError(''); // Effacer les erreurs précédentes
            } else if (response.type === 'info' ||
                (response.message && (
                    response.message.toLowerCase().includes('reset link will be sent') ||
                    response.message.toLowerCase().includes('lien de réinitialisation sera envoyé') ||
                    response.message.toLowerCase().includes('si cette adresse email est associée')
                ))) {
                // Messages informatifs (français et anglais)
                const translatedMessage = translateMessage(response.message);
                setIsSubmitted(true);
                setCountdown(60);
                showSuccessToast(translatedMessage);
                setEmail('');
                setError(''); // Effacer les erreurs précédentes
            } else {
                // Vraies erreurs (compte non trouvé, etc.)
                setError(response.message);
                showErrorToast(response.message);
            }
        } catch (error) {
            const errorMessage = 'Une erreur inattendue est survenue. Veuillez réessayer plus tard.';
            setError(errorMessage);
            showErrorToast(errorMessage);
            setEmail('');
            setIsSubmitted(false);
        } finally {
            setIsLoading(false);
        }
    };

    const handleResend = () => {
        setIsSubmitted(false);
        setEmail('');
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-white via-blue-50/30 to-indigo-50/30 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
            <div className="sm:mx-auto sm:w-full sm:max-w-md">
                <Link
                    to="/login"
                    className="flex items-center text-sm text-gray-600 hover:text-gray-900 mb-8 mx-4"
                >
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Retour à la connexion
                </Link>

                <div className="text-center">
                    <h2 className="text-2xl font-bold text-gray-900">
                        Réinitialisation du mot de passe
                    </h2>
                    <p className="mt-2 text-sm text-gray-600">
                        Entrez votre adresse email pour recevoir un lien de réinitialisation
                    </p>
                </div>
            </div>

            <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
                <div className="bg-white/80 backdrop-blur-sm py-8 px-4 shadow-xl rounded-2xl sm:px-10">
                    {!isSubmitted ? (
                        <form onSubmit={handleSubmit} className="space-y-6">
                            <div>
                                <Label htmlFor="email" className="flex items-center gap-2 text-gray-700">
                                    <Mail className="w-4 h-4" />
                                    Adresse email
                                </Label>
                                <div className="mt-1">
                                    <Input
                                        id="email"
                                        name="email"
                                        type="email"
                                        autoComplete="email"
                                        required
                                        value={email}
                                        onChange={(e) => setEmail(e.target.value)}
                                        placeholder="Entrez votre adresse email"
                                        className={`block w-full rounded-lg ${error ? 'border-red-500' : 'border-gray-300'}`}
                                    />
                                    {error && (
                                        <p className="mt-2 text-sm text-red-600">{error}</p>
                                    )}
                                </div>
                            </div>

                            <Button
                                type="submit"
                                className="w-full flex justify-center py-2 px-4"
                                disabled={isLoading}
                            >
                                {isLoading ? 'Envoi en cours...' : 'Envoyer le lien de réinitialisation'}
                            </Button>
                        </form>
                    ) : (
                        <div className="text-center">
                            <div className="rounded-full bg-green-100 p-3 mx-auto w-fit">
                                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                </svg>
                            </div>
                            <h3 className="mt-4 text-lg font-medium text-gray-900">
                                Email envoyé avec succès
                            </h3>
                            <p className="mt-2 text-sm text-gray-600">
                                Veuillez vérifier votre boîte de réception et suivre les instructions pour réinitialiser votre mot de passe.
                            </p>
                            <Button
                                onClick={handleResend}
                                className="mt-6"
                                variant="outline"
                                disabled={countdown > 0}
                            >
                                {countdown > 0 ? `Renvoyer l'email (${countdown}s)` : "Renvoyer l'email"}
                            </Button>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default ForgotPassword;