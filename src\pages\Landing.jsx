import { Clipboard<PERSON>ist, Calendar, Users, ArrowRight, CheckCircle, Star, Zap, Clock, TrendingUp, Check } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

import NewLogo from '@/components/NewLogo';

// Import des images
import todoListPreview from '/images/todo-list-preview.jpg';
import calendarPreview from '/images/calendar-preview.jpg';
import webDevPreview from '/images/image2.jpg';

export default function Landing() {
    const { t } = useTranslation();

    const benefits = [
        { icon: <CheckCircle className="w-5 h-5 text-green-500" />, text: "Interface intuitive" },
        { icon: <Star className="w-5 h-5 text-yellow-500" />, text: "Support 24/7" },
        { icon: <Zap className="w-5 h-5 text-blue-500" />, text: "Mises à jour régulières" }
    ];

    return (
        <div className="min-h-screen bg-gradient-to-br from-white via-blue-50/30 to-indigo-50/30">
            {/* Floating Elements */}
            <div className="fixed inset-0 overflow-hidden pointer-events-none">
                <div className="absolute top-20 left-10 w-64 h-64 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob"></div>
                <div className="absolute top-40 right-10 w-64 h-64 bg-yellow-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-2000"></div>
                <div className="absolute -bottom-8 left-20 w-64 h-64 bg-pink-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-4000"></div>
            </div>

            <header className="border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50">
                <div className="container py-4">
                    <nav className="flex items-center justify-between">
                        <div className="flex items-center gap-6">
                            <Link to="/" className="hover:opacity-80 transition-opacity flex items-center gap-3">
                                <NewLogo size={48} />
                                <div className="flex flex-col">
                                    <span className="text-xl font-bold">Notora</span>
                                    <span className="text-sm text-purple-600">Gestion intelligente</span>
                                </div>
                            </Link>
                        </div>
                        <div className="flex items-center gap-6">
                            <Link
                                to="/login"
                                className="nav-link px-4 py-2 rounded-full hover:bg-gray-100 hover:text-primary transition-all"
                            >
                                {t('common.login')}
                            </Link>
                            <Link
                                to="/register"
                                className="px-6 py-3 bg-purple-600/90 text-white rounded-full shadow-lg hover:bg-purple-700 transition-all backdrop-blur-sm"
                            >
                                {t('common.register')}
                            </Link>
                        </div>
                    </nav>
                </div>
            </header>

            <main className="relative">
                {/* Hero Section */}
                <section className="py-32 relative overflow-hidden">
                    <div className="container relative z-10">
                        <div className="grid lg:grid-cols-2 gap-12 items-center">
                            <div className="text-center lg:text-left">
                                <div className="inline-block mb-4 px-6 py-2 bg-white rounded-full shadow-md">
                                    <span className="text-sm font-medium text-purple-600">✨ Solution de gestion professionnelle</span>
                                </div>
                                <h1 className="text-6xl font-bold mb-6">
                                    <span className="block">Plateforme de</span>
                                    <span className="block text-purple-600">Gestion</span>
                                    <span className="block">Intelligente</span>
                                </h1>
                                <p className="text-lg text-gray-600 mb-8">
                                    Une interface intuitive et puissante pour gérer tous vos besoins professionnels efficacement.
                                </p>
                                <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                                    <Link
                                        to="/register"
                                        className="px-6 py-3 bg-purple-600 text-white rounded-full hover:bg-purple-700 transition-all flex items-center gap-2"
                                    >
                                        Commencer
                                        <ArrowRight className="w-4 h-4" />
                                    </Link>
                                    <button className="px-6 py-3 text-gray-700 hover:text-purple-600 transition-all">
                                        En savoir plus
                                    </button>
                                </div>
                            </div>
                            <div className="hidden lg:block relative">
                                <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-3xl transform rotate-3"></div>
                                <div className="relative bg-white/80 backdrop-blur-sm rounded-3xl p-6 shadow-xl transform -rotate-3 hover:rotate-0 transition-transform duration-500">
                                    <img
                                        src={webDevPreview}
                                        alt="Web Development Preview"
                                        className="rounded-2xl shadow-lg w-full h-auto"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Stats Section */}
                <section className="py-12 relative">
                    <div className="container">
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
                            {[
                                { number: "10K+", label: t('landing.stats.users') },
                                { number: "50+", label: t('landing.stats.features') },
                                { number: "99%", label: t('landing.stats.satisfaction') },
                                { number: "24/7", label: t('landing.stats.support') }
                            ].map((stat, index) => (
                                <div key={index} className="text-center">
                                    <div className="text-3xl font-bold bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent">
                                        {stat.number}
                                    </div>
                                    <div className="text-sm text-gray-600 mt-1">{stat.label}</div>
                                </div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Features Section */}
                <section id="features" className="py-20 relative">
                    <div className="absolute inset-0 bg-gradient-to-b from-white via-blue-50/30 to-purple-50/30"></div>
                    <div className="container relative">
                        <div className="text-center mb-16">
                            <div className="inline-block mb-4 px-4 py-1 bg-white/50 backdrop-blur-sm rounded-full border shadow-sm">
                                <span className="text-sm font-medium text-primary">Fonctionnalités</span>
                            </div>
                            <h2 className="heading mb-4 bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent">
                                {t('landing.features.title')}
                            </h2>
                            <p className="text-gray-600 max-w-2xl mx-auto">
                                {t('landing.subtitle')}
                            </p>
                        </div>
                        <div className="grid md:grid-cols-3 gap-8">
                            <div className="feature-card group hover:scale-105">
                                <div className="feature-icon bg-gradient-to-br from-blue-500 to-primary text-white">
                                    <Calendar className="h-6 w-6" />
                                </div>
                                <h3 className="font-semibold mb-2 group-hover:text-primary transition-colors">
                                    {t('landing.features.calendar.title')}
                                </h3>
                                <p className="text-muted-foreground">
                                    {t('landing.features.calendar.description')}
                                </p>
                            </div>
                            <div className="feature-card group hover:scale-105">
                                <div className="feature-icon bg-gradient-to-br from-purple-500 to-indigo-500 text-white">
                                    <Users className="h-6 w-6" />
                                </div>
                                <h3 className="font-semibold mb-2 group-hover:text-primary transition-colors">
                                    {t('landing.features.collaboration.title')}
                                </h3>
                                <p className="text-muted-foreground">
                                    {t('landing.features.collaboration.description')}
                                </p>
                            </div>
                            <div className="feature-card group hover:scale-105">
                                <div className="feature-icon bg-gradient-to-br from-pink-500 to-purple-500 text-white">
                                    <ClipboardList className="h-6 w-6" />
                                </div>
                                <h3 className="font-semibold mb-2 group-hover:text-primary transition-colors">
                                    {t('landing.features.taskManagement.title')}
                                </h3>
                                <p className="text-muted-foreground">
                                    {t('landing.features.taskManagement.description')}
                                </p>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Planning Section */}
                <section className="py-16 bg-gradient-to-br from-white to-gray-50/50">
                    <div className="container">
                        <div className="grid lg:grid-cols-2 gap-12 items-center">
                            <div>
                                <div className="inline-block mb-4 px-4 py-1 bg-blue-100/50 backdrop-blur-sm rounded-full">
                                    <span className="text-sm font-medium text-blue-600">{t('landing.planning.badge')}</span>
                                </div>
                                <h2 className="heading mb-6">
                                    {t('landing.planning.title')}
                                </h2>
                                <p className="text-gray-600 max-w-2xl">
                                    {t('landing.planning.description')}
                                </p>
                            </div>
                            <div className="relative">
                                <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-blue-500/10 rounded-3xl transform rotate-3"></div>
                                <div className="relative bg-white/80 backdrop-blur-sm rounded-3xl p-6 shadow-xl transform -rotate-3 hover:rotate-0 transition-transform duration-500">
                                    <img
                                        src={todoListPreview}
                                        alt="Todo List Preview"
                                        className="rounded-2xl shadow-lg"
                                    />
                                    <div className="absolute bottom-4 right-4 bg-white rounded-full px-4 py-2 shadow-lg flex items-center gap-2">
                                        <Check className="w-4 h-4 text-green-500" />
                                        <span className="text-sm font-medium">{t('landing.notifications.taskCompleted')}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Time Management Section */}
                <section className="py-16">
                    <div className="container">
                        <div className="grid lg:grid-cols-2 gap-12 items-center">
                            <div className="relative order-2 lg:order-1">
                                <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-blue-500/10 rounded-3xl transform -rotate-3"></div>
                                <div className="relative bg-white/80 backdrop-blur-sm rounded-3xl p-6 shadow-xl transform rotate-3 hover:rotate-0 transition-transform duration-500">
                                    <img
                                        src={calendarPreview}
                                        alt="Calendar Preview"
                                        className="rounded-2xl shadow-lg"
                                    />
                                    <div className="absolute bottom-4 right-4 bg-white rounded-full px-4 py-2 shadow-lg flex items-center gap-2">
                                        <Check className="w-4 h-4 text-green-500" />
                                        <span className="text-sm font-medium">{t('landing.notifications.meetingScheduled')}</span>
                                    </div>
                                </div>
                            </div>
                            <div className="order-1 lg:order-2">
                                <div className="inline-block mb-4 px-6 py-2 bg-white rounded-full shadow-md">
                                    <span className="text-sm font-medium text-purple-600">✨ Solution de gestion professionnelle</span>
                                </div>
                                <h2 className="heading mb-6">
                                    {t('landing.timeManagement.title')}
                                </h2>
                                <p className="text-gray-600 max-w-2xl">
                                    {t('landing.timeManagement.description')}
                                </p>
                            </div>
                        </div>
                    </div>
                </section>

                {/* CTA Section */}
                <section className="py-20 relative overflow-hidden">
                    <div className="container relative z-10">
                        <div className="max-w-2xl mx-auto text-center">
                            <h2 className="heading mb-4">
                                {t('landing.cta.title')}
                            </h2>
                            <p className="text-gray-600 mb-8">
                                {t('landing.cta.subtitle')}
                            </p>
                            <Link
                                to="/register"
                                className="px-6 py-3 bg-purple-600/90 text-white rounded-full shadow-lg hover:bg-purple-700 transition-all backdrop-blur-sm inline-flex items-center gap-2 group"
                            >
                                {t('landing.cta.button')}
                                <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                            </Link>
                        </div>
                    </div>
                </section>
            </main>

            <footer className="border-t py-8 bg-white/50 backdrop-blur-sm">
                <div className="container text-center text-sm text-muted-foreground">
                    © 2025 Notora. {t('common.welcome')}
                </div>
            </footer>
        </div>
    );
}
