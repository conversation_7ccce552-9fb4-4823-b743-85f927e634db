import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { authService } from '@/services/authService';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import NewLogo from '@/components/NewLogo';
import { Eye, EyeOff, Mail, Lock } from 'lucide-react';

const Login = () => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const { login, error: authError } = useAuth();
    const [showPassword, setShowPassword] = useState(false);
    const [formData, setFormData] = useState({
        email: '',
        password: ''
    });
    const [errors, setErrors] = useState({});
    const [isSubmitted, setIsSubmitted] = useState(false);
    const [isLoading, setIsLoading] = useState(false);

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
        // Clear error when user starts typing
        if (errors[name]) {
            setErrors(prev => ({
                ...prev,
                [name]: ''
            }));
        }
    };

    const validateForm = () => {
        const newErrors = {};
        if (!formData.email) {
            newErrors.email = t('auth.emailRequired');
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
            newErrors.email = t('auth.validEmail');
        }
        if (!formData.password) {
            newErrors.password = t('auth.passwordRequired');
        } else if (formData.password.length < 6) {
            newErrors.password = t('validation.passwordLength');
        }
        return newErrors;
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setIsSubmitted(true);

        // Nettoyer les erreurs précédentes
        setErrors({});

        // Valider le formulaire
        const validationErrors = validateForm();
        if (Object.keys(validationErrors).length > 0) {
            setErrors(validationErrors);
            return;
        }

        setIsLoading(true);
        try {
            console.log('Tentative de connexion avec:', { email: formData.email });

            // Nettoyer les données du formulaire
            const cleanedFormData = {
                email: formData.email.trim(),
                password: formData.password
            };

            // Tenter de se connecter
            await login(cleanedFormData);
            console.log('Connexion réussie, redirection vers:', authService.getRedirectPath());

            // Rediriger vers la page appropriée
            navigate(authService.getRedirectPath());
        } catch (error) {
            console.error('Erreur de connexion:', error);

            // Afficher un message d'erreur approprié
            let errorMessage = t('auth.loginError');

            if (error.response) {
                if (error.response.status === 401) {
                    errorMessage = t('auth.invalidCredentials');
                } else if (error.response.status === 404) {
                    errorMessage = t('auth.serverNotFound');
                } else if (error.response.data && error.response.data.message) {
                    errorMessage = error.response.data.message;
                }
            } else if (error.message) {
                errorMessage = error.message;
            }

            setErrors({
                submit: errorMessage
            });

            // Réinitialiser le mot de passe en cas d'erreur pour permettre une nouvelle tentative
            setFormData(prev => ({
                ...prev,
                password: ''
            }));
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-white via-blue-50/30 to-indigo-50/30 dark:from-gray-900 dark:via-gray-800/30 dark:to-gray-900/30 relative">
            {/* Floating Elements */}
            <div className="fixed inset-0 overflow-hidden pointer-events-none">
                <div className="absolute top-20 left-10 w-64 h-64 bg-purple-200/30 dark:bg-purple-500/10 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob"></div>
                <div className="absolute top-40 right-10 w-64 h-64 bg-yellow-200/30 dark:bg-yellow-500/10 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-2000"></div>
                <div className="absolute -bottom-8 left-20 w-64 h-64 bg-pink-200/30 dark:bg-pink-500/10 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-4000"></div>
            </div>

            <div className="flex flex-col justify-center min-h-screen py-12 sm:px-6 lg:px-8 relative z-10">
                <div className="sm:mx-auto sm:w-full sm:max-w-md">


                    <div className="flex justify-center">
                        <Link
                            to="/"
                            className="flex items-center gap-3 text-3xl font-bold mb-6 hover:opacity-80 transition-opacity"
                        >
                            <NewLogo size={48} />
                            <span className="text-xl font-bold text-gray-900 dark:text-white">Notora</span>
                        </Link>
                    </div>
                    <div className="text-center mb-8">
                        <h1 className="text-2xl font-bold mb-2">
                            {t('auth.loginTitle')}
                        </h1>
                        <p className="text-gray-600">
                            {t('auth.loginSubtitle')}
                        </p>
                    </div>
                </div>

                <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
                    <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm py-8 px-4 shadow-xl rounded-2xl sm:px-10 relative">
                        <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-purple-500/5 dark:from-primary/10 dark:to-purple-500/10 rounded-2xl transform rotate-1"></div>
                        <div className="relative">
                            {(authError || errors.submit) && (
                                <div className="bg-red-50 dark:bg-red-900/50 text-red-800 dark:text-red-200 p-3 rounded-lg mb-4 text-sm flex items-center gap-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                    </svg>
                                    <span>Échec de la connexion. Veuillez vérifier vos identifiants et réessayer.</span>
                                </div>
                            )}

                            <form className="space-y-6" onSubmit={handleSubmit} noValidate>
                                <div>
                                    <Label htmlFor="email" className="flex items-center gap-2 text-gray-700 dark:text-gray-200">
                                        <Mail className="w-4 h-4" />
                                        Adresse email
                                    </Label>
                                    <div className="mt-1">
                                        <Input
                                            id="email"
                                            name="email"
                                            type="email"
                                            autoComplete="email"
                                            placeholder={t('placeholders.email')}
                                            value={formData.email}
                                            onChange={handleChange}
                                            className={`block w-full rounded-lg border ${isSubmitted && errors.email
                                                ? 'border-red-500 dark:border-red-400'
                                                : 'border-gray-300 dark:border-gray-600'
                                                } px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:border-primary dark:focus:border-primary-dark focus:outline-none focus:ring-1 focus:ring-primary dark:focus:ring-primary-dark`}
                                        />
                                        {isSubmitted && errors.email && (
                                            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.email}</p>
                                        )}
                                    </div>
                                </div>

                                <div>
                                    <div className="flex justify-between">
                                        <Label htmlFor="password" className="flex items-center gap-2 text-gray-700 dark:text-gray-200">
                                            <Lock className="w-4 h-4" />
                                            {t('common.password')}
                                        </Label>
                                        <Link
                                            to="/forgot-password"
                                            className="text-sm text-primary dark:text-primary-dark hover:text-primary/80 dark:hover:text-primary-dark/80 transition-colors"
                                        >
                                            {t('common.forgotPassword')}
                                        </Link>
                                    </div>
                                    <div className="mt-1 relative">
                                        <Input
                                            id="password"
                                            name="password"
                                            type={showPassword ? "text" : "password"}
                                            autoComplete="current-password"
                                            placeholder={t('placeholders.password')}
                                            value={formData.password}
                                            onChange={handleChange}
                                            className={`block w-full rounded-lg border ${isSubmitted && errors.password
                                                ? 'border-red-500 dark:border-red-400'
                                                : 'border-gray-300 dark:border-gray-600'
                                                } px-3 py-2 pr-10 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:border-primary dark:focus:border-primary-dark focus:outline-none focus:ring-1 focus:ring-primary dark:focus:ring-primary-dark`}
                                        />
                                        <button
                                            type="button"
                                            onClick={() => setShowPassword(!showPassword)}
                                            className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600 dark:text-gray-300 dark:hover:text-gray-100"
                                        >
                                            {showPassword ? (
                                                <EyeOff className="h-5 w-5" />
                                            ) : (
                                                <Eye className="h-5 w-5" />
                                            )}
                                        </button>
                                        {isSubmitted && errors.password && (
                                            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.password}</p>
                                        )}
                                    </div>
                                </div>

                                <Button
                                    type="submit"
                                    className="w-full bg-gradient-to-r from-primary to-purple-600 text-white rounded-lg py-2.5 hover:from-primary-dark hover:to-purple-700 dark:from-primary-dark dark:to-purple-700 dark:hover:from-primary dark:hover:to-purple-600"
                                    disabled={isLoading}
                                >
                                    {isLoading ? "..." : t('common.login')}
                                </Button>
                            </form>

                            <div className="mt-6">
                                <div className="relative">
                                    <div className="absolute inset-0 flex items-center">
                                        <div className="w-full border-t border-gray-300 dark:border-gray-600" />
                                    </div>
                                    <div className="relative flex justify-center text-sm">
                                        <span className="bg-white dark:bg-gray-800 px-4 text-gray-500 dark:text-gray-400">
                                            {t('auth.noAccount')}
                                        </span>
                                    </div>
                                </div>

                                <div className="text-center mt-6">
                                    <p className="text-gray-600">
                                        {t('auth.noAccount')}{' '}
                                        <Link to="/register" className="text-primary hover:underline">
                                            {t('common.register')}
                                        </Link>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Login;
