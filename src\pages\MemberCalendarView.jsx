import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useEvent } from '@/contexts/EventContext';
import { useTeam } from '@/contexts/TeamContext';
import { toast } from 'react-toastify';
import { Calendar as CalendarIcon, Filter, Search, CheckCircle, Clock, AlertCircle, Calendar as CalendarLucide } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { EVENT_STATUS } from '@/config/constants';

// Composants
import EventCard from '@/components/events/EventCard';

// Modals
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';

const MemberCalendarView = () => {
    const { user } = useAuth();
    const { teams } = useTeam();
    const { 
        events, 
        loading, 
        error, 
        fetchEvents, 
        updateEventStatus
    } = useEvent();

    // États pour la gestion des modales
    const [showStatusModal, setShowStatusModal] = useState(false);
    const [selectedEvent, setSelectedEvent] = useState(null);
    
    // États pour les filtres
    const [searchQuery, setSearchQuery] = useState('');
    const [teamFilter, setTeamFilter] = useState('');
    const [statusFilter, setStatusFilter] = useState('');
    const [dateFilter, setDateFilter] = useState('');
    
    // État pour le chargement des actions
    const [actionLoading, setActionLoading] = useState(false);

    // Charger les événements au montage du composant
    useEffect(() => {
        const loadEvents = async () => {
            try {
                await fetchEvents();
            } catch (err) {
                console.error('Erreur lors du chargement des événements:', err);
                toast.error('Erreur lors du chargement des événements');
            }
        };

        loadEvents();
    }, [fetchEvents]);

    // Filtrer les événements
    const filteredEvents = events.filter(event => {
        // Filtre par recherche (titre ou description)
        const matchesSearch = searchQuery === '' || 
            (event.title && event.title.toLowerCase().includes(searchQuery.toLowerCase())) ||
            (event.description && event.description.toLowerCase().includes(searchQuery.toLowerCase()));
        
        // Filtre par équipe
        const matchesTeam = teamFilter === '' || event.team_id === teamFilter;
        
        // Filtre par statut
        const matchesStatus = statusFilter === '' || event.status === statusFilter;
        
        // Filtre par date
        const matchesDate = dateFilter === '' || (
            event.start_date && new Date(event.start_date).toISOString().split('T')[0] === dateFilter
        );
        
        return matchesSearch && matchesTeam && matchesStatus && matchesDate;
    });

    // Réinitialiser les filtres
    const handleResetFilters = () => {
        setSearchQuery('');
        setTeamFilter('');
        setStatusFilter('');
        setDateFilter('');
    };

    // Ouvrir la modale de mise à jour du statut
    const handleOpenStatusModal = (event) => {
        setSelectedEvent(event);
        setShowStatusModal(true);
    };

    // Mettre à jour le statut d'un événement
    const handleUpdateStatus = async (status) => {
        if (!selectedEvent) return;
        
        setActionLoading(true);
        try {
            await updateEventStatus(selectedEvent.id, status);
            toast.success(`Statut de l'événement mis à jour avec succès`);
            setShowStatusModal(false);
            setSelectedEvent(null);
            // Rafraîchir les événements
            await fetchEvents();
        } catch (error) {
            console.error('Erreur lors de la mise à jour du statut:', error);
            toast.error('Erreur lors de la mise à jour du statut');
        } finally {
            setActionLoading(false);
        }
    };

    // Formater la date pour l'affichage
    const formatDate = (dateString) => {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleDateString('fr-FR', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    if (loading) {
        return (
            <div className="flex justify-center items-center min-h-screen">
                <div className="text-xl font-semibold">Chargement des événements...</div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="flex justify-center items-center min-h-screen">
                <div className="text-xl font-semibold text-red-500">{error}</div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50 p-8">
            <div className="max-w-7xl mx-auto space-y-8">
                <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-8">
                    <h1 className="text-2xl font-bold text-gray-900">Mon Calendrier</h1>
                    
                    <div className="flex flex-col md:flex-row gap-4 w-full md:w-auto">
                        <div className="relative flex-grow">
                            <input
                                type="text"
                                placeholder="Rechercher un événement..."
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                                className="w-full pl-10 pr-4 py-2 bg-white border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#6B4EFF] focus:border-transparent shadow-sm"
                            />
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                        </div>
                        
                        <div className="flex gap-2">
                            <select
                                value={teamFilter}
                                onChange={(e) => setTeamFilter(e.target.value)}
                                className="px-3 py-2 bg-white border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#6B4EFF] focus:border-transparent shadow-sm"
                            >
                                <option value="">Toutes les équipes</option>
                                {teams.filter(team => team.is_member).map(team => (
                                    <option key={team.id} value={team.id}>{team.name}</option>
                                ))}
                            </select>
                            
                            <select
                                value={statusFilter}
                                onChange={(e) => setStatusFilter(e.target.value)}
                                className="px-3 py-2 bg-white border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#6B4EFF] focus:border-transparent shadow-sm"
                            >
                                <option value="">Tous les statuts</option>
                                <option value={EVENT_STATUS.PENDING}>En attente</option>
                                <option value={EVENT_STATUS.COMPLETED}>Terminé</option>
                                <option value={EVENT_STATUS.ARCHIVED}>Archivé</option>
                            </select>
                            
                            <Input
                                type="date"
                                value={dateFilter}
                                onChange={(e) => setDateFilter(e.target.value)}
                                className="px-3 py-2 bg-white border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#6B4EFF] focus:border-transparent shadow-sm"
                            />
                            
                            <Button 
                                variant="outline" 
                                onClick={handleResetFilters}
                                className="px-3 py-2 border border-gray-200 rounded-lg hover:bg-gray-100"
                            >
                                Réinitialiser
                            </Button>
                        </div>
                    </div>
                </div>

                {filteredEvents.length === 0 ? (
                    <div className="text-center py-12 bg-white rounded-xl border border-gray-100 shadow-sm">
                        <CalendarLucide className="w-12 h-12 mx-auto text-[#6B4EFF] mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 mb-2">
                            Aucun événement trouvé
                        </h3>
                        <p className="text-gray-500">
                            Aucun événement ne correspond à vos critères de recherche
                        </p>
                    </div>
                ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {filteredEvents.map(event => (
                            <EventCard
                                key={event.id}
                                event={event}
                                onUpdateStatus={() => handleOpenStatusModal(event)}
                            />
                        ))}
                    </div>
                )}
            </div>

            {/* Modal pour mettre à jour le statut */}
            <Dialog open={showStatusModal} onOpenChange={setShowStatusModal}>
                <DialogContent className="sm:max-w-md">
                    <DialogHeader>
                        <DialogTitle>Mettre à jour le statut</DialogTitle>
                        <DialogDescription>
                            Choisissez le nouveau statut pour l'événement "{selectedEvent?.title}"
                        </DialogDescription>
                    </DialogHeader>
                    <div className="grid gap-4 py-4">
                        <div className="flex flex-col gap-3">
                            <Button
                                onClick={() => handleUpdateStatus(EVENT_STATUS.PENDING)}
                                disabled={actionLoading || selectedEvent?.status === EVENT_STATUS.PENDING}
                                className="flex items-center justify-start gap-2 bg-yellow-100 hover:bg-yellow-200 text-yellow-800 border-yellow-300"
                                variant="outline"
                            >
                                <Clock className="h-5 w-5" />
                                En attente
                            </Button>
                            <Button
                                onClick={() => handleUpdateStatus(EVENT_STATUS.COMPLETED)}
                                disabled={actionLoading || selectedEvent?.status === EVENT_STATUS.COMPLETED}
                                className="flex items-center justify-start gap-2 bg-green-100 hover:bg-green-200 text-green-800 border-green-300"
                                variant="outline"
                            >
                                <CheckCircle className="h-5 w-5" />
                                Terminé
                            </Button>
                        </div>
                    </div>
                    <div className="flex justify-end gap-3">
                        <Button
                            variant="outline"
                            onClick={() => setShowStatusModal(false)}
                            disabled={actionLoading}
                        >
                            Annuler
                        </Button>
                    </div>
                </DialogContent>
            </Dialog>
        </div>
    );
};

export default MemberCalendarView;