import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useTeam } from '@/contexts/TeamContext';
import { toast } from 'react-toastify';
import { 
    Users,
    Search,
    Users as UserGroup,
    Calendar,
    CheckCircle,
    Clock,
    AlertCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';

const MemberTeamView = () => {
    const { user } = useAuth();
    const { teams, loading, error, fetchTeams } = useTeam();
    const [searchQuery, setSearchQuery] = useState('');

    useEffect(() => {
        fetchTeams();
    }, [fetchTeams]);

    const filteredTeams = teams.filter(team =>
        team.is_member && (
            team.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            team.description.toLowerCase().includes(searchQuery.toLowerCase())
        )
    );

    if (loading) {
        return (
            <div className="flex justify-center items-center min-h-screen">
                <div className="text-xl font-semibold">Chargement...</div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="flex justify-center items-center min-h-screen">
                <div className="text-xl font-semibold text-red-500">{error}</div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50 p-8">
            <div className="max-w-7xl mx-auto space-y-8">
                <div className="mb-8">
                    <h1 className="text-2xl font-bold text-gray-900 mb-6">Mes Équipes</h1>
                    <div className="relative max-w-2xl mx-auto">
                        <input
                            type="text"
                            placeholder="Rechercher des équipes, projets, membres..."
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            className="w-full pl-10 pr-4 py-3 bg-white border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#6B4EFF] focus:border-transparent shadow-sm"
                        />
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    </div>
                </div>

                {filteredTeams.length === 0 && (
                    <div className="col-span-full text-center py-12 bg-white rounded-xl border border-gray-100 shadow-sm">
                        <UserGroup className="w-12 h-12 mx-auto text-[#6B4EFF] mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 mb-2">
                            Aucune équipe trouvée
                        </h3>
                        <p className="text-gray-500">
                            Vous n'êtes assigné à aucune équipe pour le moment
                        </p>
                    </div>
                )}
                {filteredTeams.length > 0 && (
                    <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8">
                        {filteredTeams.map((team) => (
                            <div key={team.id} className="bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-all duration-300 relative group border border-gray-100 hover:border-[#6B4EFF]/20">
                                <div className="flex flex-col h-full">
                                    <div className="mb-6">
                                        <div className="flex items-center gap-2 mb-2">
                                            <div className="w-8 h-8 rounded-lg bg-[#6B4EFF]/10 flex items-center justify-center text-sm font-medium text-[#6B4EFF]">
                                                {team.name.charAt(0).toUpperCase()}
                                            </div>
                                            <h3 className="text-lg font-semibold text-gray-900">{team.name}</h3>
                                        </div>
                                        <p className="text-sm text-gray-500 line-clamp-2">{team.description}</p>
                                    </div>
                                    
                                    {/* Affichage des événements à venir pour cette équipe */}
                                    <div className="border-t border-gray-100 pt-4 mb-4">
                                        <div className="flex items-center justify-between mb-3">
                                            <h4 className="text-sm font-medium text-gray-900">
                                                Événements à venir
                                            </h4>
                                            <Link to="/calendar" className="text-xs text-[#6B4EFF] hover:underline flex items-center gap-1">
                                                <Calendar className="w-3 h-3" />
                                                Voir tous
                                            </Link>
                                        </div>
                                        <div className="space-y-2">
                                            {team.upcoming_events && team.upcoming_events.length > 0 ? (
                                                team.upcoming_events.slice(0, 2).map(event => (
                                                    <div key={event.id} className="flex items-center justify-between bg-gray-50 rounded-lg p-2.5 text-sm">
                                                        <div className="flex items-center gap-2">
                                                            {event.status === 'pending' && <Clock className="w-4 h-4 text-yellow-500" />}
                                                            {event.status === 'completed' && <CheckCircle className="w-4 h-4 text-green-500" />}
                                                            {event.status === 'archived' && <AlertCircle className="w-4 h-4 text-gray-500" />}
                                                            <span className="font-medium truncate max-w-[150px]">{event.title}</span>
                                                        </div>
                                                        <span className="text-xs text-gray-500">{new Date(event.start_date).toLocaleDateString()}</span>
                                                    </div>
                                                ))
                                            ) : (
                                                <p className="text-xs text-gray-500 italic">Aucun événement à venir</p>
                                            )}
                                        </div>
                                    </div>
                                    
                                    <div className="border-t border-gray-100 pt-4 mt-auto">
                                        <h4 className="text-sm font-medium text-gray-900 mb-3">
                                            Membres de l'équipe
                                        </h4>
                                        <div className="grid gap-2">
                                            {team.members && team.members.length > 0 ? (
                                                team.members.map(member => (
                                                    <div key={member.id} className="flex items-center justify-between bg-gray-50/50 rounded-lg p-2.5 hover:bg-gray-50 transition-colors">
                                                        <div className="flex items-center gap-3">
                                                            <div className="w-8 h-8 rounded-lg bg-[#6B4EFF]/10 flex items-center justify-center text-sm font-medium text-[#6B4EFF]">
                                                                {member.name.charAt(0).toUpperCase()}
                                                            </div>
                                                            <div>
                                                                <div className="text-sm font-medium text-gray-900 flex items-center gap-2">
                                                                    {member.name}
                                                                    {member.is_admin && (
                                                                        <span className="text-xs bg-purple-100 text-purple-800 px-2 py-0.5 rounded-full">Admin</span>
                                                                    )}
                                                                </div>
                                                                <div className="text-xs text-gray-500">{member.email}</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                ))
                                            ) : (
                                                <p className="text-xs text-gray-500 italic">Aucun membre dans cette équipe</p>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
};

export default MemberTeamView;