import { useTranslation } from 'react-i18next';
import { <PERSON> } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';

const NotFound = () => {
    const { t } = useTranslation();

    return (
        <div className="min-h-screen flex flex-col items-center justify-center px-4">
            <h1 className="text-9xl font-bold text-gray-200">404</h1>
            <h2 className="text-2xl font-bold text-gray-800 mt-4">Page non trouvée</h2>
            <p className="text-gray-600 mt-2 text-center">
                La page que vous recherchez n'existe pas ou a été déplacée.
            </p>
            <Link to="/" className="mt-8">
                <Button>Retour à l'accueil</Button>
            </Link>
        </div>
    );
};

export default NotFound;