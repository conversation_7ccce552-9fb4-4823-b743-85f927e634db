import React, { useState, useEffect, useCallback } from 'react';
import { usePersonalEvent } from '@/contexts/PersonalEventContext';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'react-toastify';
import {
  Plus,
  Search,
  Filter,
  Clock,
  RefreshCw,
  Loader2,
  X,
  LayoutList,
  LayoutGrid,
  Calendar as CalendarIcon,
  Archive,
  ArrowLeft,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import PersonalEventCard from '@/components/events/PersonalEventCard';
import PersonalEventList from '@/components/events/PersonalEventList';
import PersonalEventForm from '@/components/events/PersonalEventForm';
import PersonalEventCalendar from '@/components/events/PersonalEventCalendar';
import PersonalEventAgenda from '@/components/events/PersonalEventAgenda';
import PersonalEventArchives from '@/components/events/PersonalEventArchives';
import ConfirmDialog from '@/components/ui/ConfirmDialog';

const PersonalEventsPage = () => {
  const { user } = useAuth();
  const {
    personalEvents,
    loading,
    error,
    filters,
    fetchPersonalEvents,
    fetchArchivedPersonalEvents,
    createPersonalEvent,
    updatePersonalEvent,
    updatePersonalEventStatus,
    deletePersonalEvent,
    archivePersonalEvent,
    unarchivePersonalEvent,
    updateFilters,
    resetFilters
  } = usePersonalEvent();

  // États locaux
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showDeleteArchivedModal, setShowDeleteArchivedModal] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [selectedArchivedEventId, setSelectedArchivedEventId] = useState(null);
  const [actionLoading, setActionLoading] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState('all'); // 'all', 'active', 'archived'

  // Debug: Afficher le mode de vue actuel
  console.log('🔍 DEBUG - Mode de vue actuel:', viewMode);
  const [archivedEvents, setArchivedEvents] = useState([]); // État pour les événements archivés
  const [calendarView, setCalendarView] = useState('month'); // 'month', 'week', 'day'
  const [currentView, setCurrentView] = useState(() => {
    // Récupérer la préférence de l'utilisateur depuis le localStorage
    const savedView = localStorage.getItem('personalEventsView');
    return savedView || 'month'; // 'month', 'week', 'day', 'agenda', 'archives'
  });
  const [displayView, setDisplayView] = useState(() => {
    // Récupérer la préférence de l'utilisateur depuis le localStorage
    const savedView = localStorage.getItem('personalEventsDisplayView');
    return savedView || 'calendar'; // 'calendar', 'cards' ou 'list'
  });
  const [currentDate, setCurrentDate] = useState(new Date());
  const [currentMonth, setCurrentMonth] = useState(
    new Date().toLocaleString('fr-FR', { month: 'long', year: 'numeric' })
  );

  // Charger les événements archivés
  const loadArchivedEvents = useCallback(async () => {
    try {
      console.log('🔄 Chargement des événements archivés...');
      const archived = await fetchArchivedPersonalEvents();
      console.log('✅ Événements archivés chargés:', archived);
      setArchivedEvents(archived);
    } catch (error) {
      console.error('❌ Erreur lors du chargement des événements archivés:', error);
      setArchivedEvents([]);
    }
  }, [fetchArchivedPersonalEvents]);

  // Charger les événements personnels au montage de la page
  useEffect(() => {
    if (user) {
      console.log('Chargement des événements personnels au montage de la page');
      fetchPersonalEvents();
      // Charger aussi les événements archivés pour le calendrier
      loadArchivedEvents();
    }
  }, [user, fetchPersonalEvents, loadArchivedEvents]);

  // Mettre à jour les filtres lorsque la recherche change
  useEffect(() => {
    const timer = setTimeout(() => {
      updateFilters({ search: searchQuery });
    }, 300);
    return () => clearTimeout(timer);
  }, [searchQuery, updateFilters]);

  // Filtrer les événements selon le mode d'affichage
  const filteredEvents = personalEvents.filter(event => {
    if (viewMode === 'all') return true;
    if (viewMode === 'active') return !(event.status === 'archived' || event.is_archived);
    if (viewMode === 'archived') return (event.status === 'archived' || event.is_archived);
    return true;
  }).filter(event => {
    if (!searchQuery) return true;
    const query = searchQuery.toLowerCase();
    return (
      event.title.toLowerCase().includes(query) ||
      (event.description && event.description.toLowerCase().includes(query)) ||
      (event.location && event.location.toLowerCase().includes(query))
    );
  });

  // Debug: Afficher tous les événements et leur statut
  console.log('🔍 DEBUG - Tous les événements personnels:', personalEvents.map(e => ({
    id: e.id,
    title: e.title,
    status: e.status,
    is_archived: e.is_archived
  })));

  console.log('🔍 DEBUG - Événements filtrés pour le calendrier:', filteredEvents.map(e => ({
    id: e.id,
    title: e.title,
    status: e.status,
    is_archived: e.is_archived
  })));

  // Fonction utilitaire pour gérer les opérations asynchrones avec gestion d'erreur
  const safeAsyncOperation = async (operation, errorMessage) => {
    setActionLoading(true);
    try {
      await operation();
      // Pas besoin de rafraîchir car le contexte gère déjà la mise à jour
      return true;
    } catch (error) {
      console.error(errorMessage, error);
      toast.error(`${errorMessage}: ${error.message || 'Erreur inconnue'}`);
      return false;
    } finally {
      setActionLoading(false);
    }
  };

  // Gérer la création d'un événement
  const handleCreateEvent = async (eventData) => {
    setActionLoading(true);
    try {
      await createPersonalEvent(eventData);
      console.log('✅ Événement créé avec succès');
      setShowCreateModal(false);
      // Rafraîchir les données après création
      await fetchPersonalEvents();
    } catch (error) {
      console.error('❌ Erreur lors de la création de l\'événement:', error);
      toast.error(`Erreur lors de la création de l'événement: ${error.message || 'Erreur inconnue'}`);
    } finally {
      setActionLoading(false);
    }
  };

  // Gérer la modification d'un événement
  const handleEditEvent = async (eventData) => {
    if (!selectedEvent) return;

    console.log('handleEditEvent appelé avec:', eventData);

    setActionLoading(true);
    try {
      await updatePersonalEvent(selectedEvent.id, eventData);
      console.log('✅ Événement modifié avec succès');
      setShowEditModal(false);
      setSelectedEvent(null);
      // Rafraîchir les données après modification
      await fetchPersonalEvents();
    } catch (error) {
      console.error('❌ Erreur lors de la modification de l\'événement:', error);
      toast.error(`Erreur lors de la modification de l'événement: ${error.message || 'Erreur inconnue'}`);
    } finally {
      setActionLoading(false);
    }
  };

  // Gérer l'archivage d'un événement
  const handleArchiveEvent = async (eventId) => {
    console.log(`handleArchiveEvent appelé pour l'événement ${eventId}`);

    const success = await safeAsyncOperation(
      () => archivePersonalEvent(eventId),
      'Erreur lors de l\'archivage de l\'événement'
    );

    if (success) {
      console.log(`Archivage réussi pour l'événement ${eventId}`);
      toast.success('Événement archivé avec succès !');
      // Le contexte se charge de mettre à jour l'état via fetchPersonalEvents dans safeAsyncOperation
      // Si on est dans la vue archives, rafraîchir aussi les événements archivés
      if (currentView === 'archives') {
        loadArchivedEvents();
      }
    }

    return success;
  };

  // Gérer le désarchivage d'un événement depuis les archives
  const handleUnarchiveEvent = async (eventId) => {
    console.log(`handleUnarchiveEvent appelé pour l'événement ${eventId}`);

    try {
      setActionLoading(true);

      // Appeler le service de désarchivage
      const response = await unarchivePersonalEvent(eventId);

      if (response) {
        // Supprimer de la liste archivée locale
        setArchivedEvents(prev => prev.filter(event => event.id !== eventId));

        // Recharger les événements actifs pour l'afficher dans les vues normales
        await fetchPersonalEvents();

        console.log(`✅ Désarchivage réussi pour l'événement ${eventId}`);
        toast.success('Événement désarchivé avec succès !');
        return true;
      }
    } catch (error) {
      console.error('❌ Erreur lors du désarchivage:', error);
      toast.error('Erreur lors du désarchivage de l\'événement');
      return false;
    } finally {
      setActionLoading(false);
    }
  };

  // Gérer la suppression définitive d'un événement archivé
  const handleDeleteArchivedEvent = (eventId) => {
    setSelectedArchivedEventId(eventId);
    setShowDeleteArchivedModal(true);
  };

  // Confirmer la suppression définitive d'un événement archivé
  const confirmDeleteArchivedEvent = async () => {
    if (!selectedArchivedEventId) return;

    console.log(`confirmDeleteArchivedEvent appelé pour l'événement ${selectedArchivedEventId}`);

    try {
      setActionLoading(true);

      // Appeler le service de suppression
      await deletePersonalEvent(selectedArchivedEventId);

      // Supprimer de la liste archivée locale
      setArchivedEvents(prev => prev.filter(event => event.id !== selectedArchivedEventId));

      console.log(`✅ Suppression définitive réussie pour l'événement ${selectedArchivedEventId}`);
      toast.success('Événement supprimé avec succès !');

      // Réinitialiser l'état
      setSelectedArchivedEventId(null);
      return true;
    } catch (error) {
      console.error('❌ Erreur lors de la suppression définitive:', error);
      toast.error('Erreur lors de la suppression de l\'événement');
      return false;
    } finally {
      setActionLoading(false);
    }
  };

  // Gérer la suppression d'un événement
  const handleDeleteEvent = async () => {
    if (!selectedEvent) return;

    const success = await safeAsyncOperation(
      () => deletePersonalEvent(selectedEvent.id),
      'Erreur lors de la suppression de l\'événement'
    );

    if (success) {
      toast.success('Événement supprimé avec succès !');
      setShowDeleteModal(false);
      setSelectedEvent(null);
    }
  };

  // Ouvrir le modal de modification
  const handleEditClick = (event) => {
    setSelectedEvent(event);
    setShowEditModal(true);
  };

  // Ouvrir le modal de suppression
  const handleDeleteClick = (eventId) => {
    const event = personalEvents.find(e => e.id === eventId);
    if (event) {
      setSelectedEvent(event);
      setShowDeleteModal(true);
    }
  };

  // Changer le mode d'affichage et sauvegarder la préférence
  const handleViewChange = (view) => {
    setDisplayView(view);
    localStorage.setItem('personalEventsDisplayView', view);
  };

  // Changer la vue actuelle (mois, semaine, jour, agenda, archives)
  const handleCurrentViewChange = (view) => {
    setCurrentView(view);
    localStorage.setItem('personalEventsView', view);

    // Si on passe à une vue de calendrier, mettre à jour la vue du calendrier
    if (['month', 'week', 'day'].includes(view)) {
      setCalendarView(view);
    }

    // Si on passe à la vue archives, charger les événements archivés
    if (view === 'archives') {
      console.log('🔄 Passage à la vue Archives - Chargement des événements archivés...');
      loadArchivedEvents();
    }
  };

  // Naviguer vers le mois/semaine/jour précédent
  const handlePrevious = () => {
    const date = new Date(currentDate);
    if (calendarView === 'month') {
      date.setMonth(date.getMonth() - 1);
    } else if (calendarView === 'week') {
      date.setDate(date.getDate() - 7);
    } else if (calendarView === 'day') {
      date.setDate(date.getDate() - 1);
    }
    setCurrentDate(date);
    setCurrentMonth(date.toLocaleString('fr-FR', { month: 'long', year: 'numeric' }));
  };

  // Naviguer vers le mois/semaine/jour suivant
  const handleNext = () => {
    const date = new Date(currentDate);
    if (calendarView === 'month') {
      date.setMonth(date.getMonth() + 1);
    } else if (calendarView === 'week') {
      date.setDate(date.getDate() + 7);
    } else if (calendarView === 'day') {
      date.setDate(date.getDate() + 1);
    }
    setCurrentDate(date);
    setCurrentMonth(date.toLocaleString('fr-FR', { month: 'long', year: 'numeric' }));
  };

  // Naviguer vers aujourd'hui
  const handleToday = () => {
    const today = new Date();
    setCurrentDate(today);
    setCurrentMonth(today.toLocaleString('fr-FR', { month: 'long', year: 'numeric' }));
  };

  // Retourner au calendrier depuis l'agenda ou les archives
  const handleReturnToCalendar = () => {
    handleCurrentViewChange('month');
  };

  // Rafraîchir les événements
  const handleRefresh = () => {
    fetchPersonalEvents();
    if (currentView === 'archives') {
      loadArchivedEvents();
    }
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="bg-white rounded-xl shadow-lg overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div className="flex items-center gap-2">
              <CalendarIcon className="h-6 w-6 text-indigo-600" />
              <h1 className="text-2xl font-bold text-gray-900">{currentMonth}</h1>
            </div>

            <div className="flex items-center gap-2">
              <div className="relative w-full md:w-64">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" aria-hidden="false" />
                <Input
                  type="text"
                  placeholder="Rechercher un événement..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>

              <Button
                onClick={() => setShowCreateModal(true)}
                className="bg-indigo-600 hover:bg-indigo-700 text-white flex items-center gap-2"
              >
                <Plus className="h-4 w-4" aria-hidden="false" />
                Nouvel événement
              </Button>
              <Button
                variant="outline"
                onClick={handleRefresh}
                className="flex items-center gap-2"
                disabled={loading}
              >
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} aria-hidden="false" />
                Actualiser
              </Button>
            </div>
          </div>
        </div>

        <div className="p-4 border-b border-gray-200 bg-gray-50">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleToday}
                className="text-indigo-600 hover:text-indigo-800"
              >
                Aujourd'hui
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={handlePrevious}
                className="text-gray-600 hover:text-gray-800"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleNext}
                className="text-gray-600 hover:text-gray-800"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>

            <div className="flex items-center gap-2">
              <div className="bg-white rounded-lg border border-gray-200 flex">
                <Button
                  variant={currentView === 'month' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => handleCurrentViewChange('month')}
                  className={`rounded-r-none ${currentView === 'month' ? 'bg-indigo-600 text-white' : 'text-gray-600'}`}
                >
                  Mois
                </Button>
                <Button
                  variant={currentView === 'week' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => handleCurrentViewChange('week')}
                  className={`rounded-none ${currentView === 'week' ? 'bg-indigo-600 text-white' : 'text-gray-600'}`}
                >
                  Semaine
                </Button>
                <Button
                  variant={currentView === 'day' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => handleCurrentViewChange('day')}
                  className={`rounded-none ${currentView === 'day' ? 'bg-indigo-600 text-white' : 'text-gray-600'}`}
                >
                  Jour
                </Button>
                <Button
                  variant={currentView === 'agenda' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => handleCurrentViewChange('agenda')}
                  className={`rounded-none ${currentView === 'agenda' ? 'bg-indigo-600 text-white' : 'text-gray-600'}`}
                >
                  Agenda
                </Button>
                <Button
                  variant={currentView === 'archives' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => handleCurrentViewChange('archives')}
                  className={`rounded-l-none ${currentView === 'archives' ? 'bg-indigo-600 text-white' : 'text-gray-600'}`}
                >
                  <Archive className="h-4 w-4 mr-1" />
                  Archives
                </Button>
              </div>
            </div>
          </div>
        </div>

        <div className="p-6">
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-indigo-600" aria-hidden="false" />
            </div>
          ) : error ? (
            <div className="bg-red-50 p-4 rounded-lg">
              <p className="text-red-600">{error}</p>
            </div>
          ) : (
            <>
              {/* Vue Mois, Semaine, Jour - Afficher le calendrier avec TOUS les événements (y compris archivés) */}
              {['month', 'week', 'day'].includes(currentView) && (
                <PersonalEventCalendar
                  events={[...personalEvents, ...archivedEvents]}
                  onEdit={handleEditClick}
                  onDelete={handleDeleteClick}
                  onArchive={handleArchiveEvent}
                  onUnarchive={handleUnarchiveEvent}
                  calendarView={currentView}
                  date={currentDate}
                />
              )}

              {/* Message si aucun événement dans les autres vues */}
              {filteredEvents.length === 0 && !['month', 'week', 'day'].includes(currentView) && currentView !== 'archives' && (
                <div className="text-center py-12">
                  <Clock className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900">Aucun événement trouvé</h3>
                  <p className="text-gray-500 mt-2">
                    {searchQuery ? 'Aucun événement ne correspond à votre recherche.' : 'Commencez par créer un nouvel événement.'}
                  </p>
                </div>
              )}

              {/* Vue Agenda */}
              {currentView === 'agenda' && (() => {
                const agendaEvents = personalEvents.filter(event => {
                  // Filtrer seulement les événements non archivés
                  const isNotArchived = !(event.status === 'archived' || event.is_archived);
                  // Appliquer le filtre de recherche si nécessaire
                  if (!searchQuery) return isNotArchived;
                  const query = searchQuery.toLowerCase();
                  const matchesSearch = (
                    event.title.toLowerCase().includes(query) ||
                    (event.description && event.description.toLowerCase().includes(query)) ||
                    (event.location && event.location.toLowerCase().includes(query))
                  );
                  return isNotArchived && matchesSearch;
                });

                console.log('🔍 DEBUG - Vue Agenda - Événements filtrés:', agendaEvents.map(e => ({
                  id: e.id,
                  title: e.title,
                  status: e.status,
                  is_archived: e.is_archived
                })));

                return (
                  <PersonalEventAgenda
                    events={agendaEvents}
                    onEdit={handleEditClick}
                    onDelete={handleDeleteClick}
                    onArchive={handleArchiveEvent}
                    onUnarchive={handleUnarchiveEvent}
                    onUpdateStatus={updatePersonalEventStatus}
                    onReturnToCalendar={handleReturnToCalendar}
                  />
                );
              })()}

              {/* Vue Archives */}
              {currentView === 'archives' && (
                <PersonalEventArchives
                  events={archivedEvents}
                  onDelete={handleDeleteArchivedEvent}
                  onUnarchive={handleUnarchiveEvent}
                  onReturnToCalendar={handleReturnToCalendar}
                  loading={actionLoading}
                />
              )}
            </>
          )}
        </div>
      </div>

      {/* Modal de création d'événement */}
      <Dialog open={showCreateModal} onOpenChange={setShowCreateModal}>
        <DialogContent className="sm:max-w-[550px] max-h-[90vh] overflow-y-auto">
          <DialogHeader className="pb-2">
            <DialogTitle className="text-lg">Créer un nouvel événement</DialogTitle>
            <DialogDescription className="text-sm">
              Remplissez les informations pour créer un nouvel événement personnel.
            </DialogDescription>
          </DialogHeader>
          <PersonalEventForm
            onSubmit={handleCreateEvent}
            onCancel={() => setShowCreateModal(false)}
            isSubmitting={actionLoading}
            personalEvents={personalEvents}
          />
        </DialogContent>
      </Dialog>

      {/* Modal de modification d'événement */}
      <Dialog open={showEditModal} onOpenChange={setShowEditModal}>
        <DialogContent className="sm:max-w-[550px] max-h-[90vh] overflow-y-auto">
          <DialogHeader className="pb-2">
            <DialogTitle className="text-lg">Modifier l'événement</DialogTitle>
            <DialogDescription className="text-sm">
              Modifiez les informations de l'événement.
            </DialogDescription>
          </DialogHeader>
          {selectedEvent && (
            <PersonalEventForm
              event={selectedEvent}
              onSubmit={handleEditEvent}
              onCancel={() => setShowEditModal(false)}
              isSubmitting={actionLoading}
              personalEvents={personalEvents}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Modal de confirmation de suppression */}
      <Dialog open={showDeleteModal} onOpenChange={setShowDeleteModal}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Êtes-vous sûr de vouloir supprimer cet événement ?</DialogTitle>
            <DialogDescription>
              Cette action est irréversible. L'événement sera définitivement supprimé.
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              variant="outline"
              onClick={() => setShowDeleteModal(false)}
              disabled={actionLoading}
            >
              Annuler
            </Button>
            <Button
              onClick={handleDeleteEvent}
              disabled={actionLoading}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              {actionLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" aria-hidden="false" />
                  Suppression...
                </>
              ) : (
                'Supprimer'
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Modal de confirmation de suppression d'événement archivé */}
      <ConfirmDialog
        open={showDeleteArchivedModal}
        onOpenChange={setShowDeleteArchivedModal}
        title="localhost:5173 indique"
        description="Supprimer définitivement cet événement archivé ? Cette action est irréversible."
        confirmText="OK"
        cancelText="Annuler"
        onConfirm={confirmDeleteArchivedEvent}
        variant="destructive"
      />
    </div>
  );
};

export default PersonalEventsPage;
