import React, { useState, useEffect } from 'react';
import { usePersonalTask } from '@/contexts/PersonalTaskContext';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'react-toastify';
import {
  Plus,
  Search,
  Filter,
  Clock,
  RefreshCw,
  Loader2,
  X,
  LayoutList,
  LayoutGrid,
  Kanban
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import PersonalTaskCard from '@/components/tasks/PersonalTaskCard';
import PersonalTaskListSimple from '@/components/tasks/PersonalTaskListSimple';
import PersonalTaskKanban from '@/components/tasks/PersonalTaskKanban';
import PersonalTaskForm from '@/components/tasks/PersonalTaskForm';

const PersonalTasksPage = () => {
  const { user } = useAuth();
  const {
    personalTasks,
    loading,
    error,
    filters,
    fetchPersonalTasks,
    createPersonalTask,
    updatePersonalTask,
    updatePersonalTaskStatus,
    archivePersonalTask,
    unarchivePersonalTask,
    deletePersonalTask,
    updateFilters,
    resetFilters
  } = usePersonalTask();

  // États locaux
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedTask, setSelectedTask] = useState(null);
  const [actionLoading, setActionLoading] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all'); // 'all', 'a_faire', 'en_cours', 'achevee', 'archived'
  const [priorityFilter, setPriorityFilter] = useState('all'); // 'all', 'faible', 'moyenne', 'haute'
  const [displayView, setDisplayView] = useState(() => {
    // Récupérer la préférence de l'utilisateur depuis le localStorage
    const savedView = localStorage.getItem('personalTasksDisplayView');
    return savedView || 'list'; // 'list', 'cards' ou 'kanban'
  });

  // Charger les tâches au montage de la page
  useEffect(() => {
    if (user) {
      console.log('Chargement initial des tâches personnelles...');
      fetchPersonalTasks();
    }
  }, [user, fetchPersonalTasks]);

  // Log pour debug - surveiller les changements de personalTasks
  useEffect(() => {
    console.log('PersonalTasksPage - personalTasks mis à jour:', personalTasks);
  }, [personalTasks]);

  // Mettre à jour les filtres lorsque la recherche change
  useEffect(() => {
    const timer = setTimeout(() => {
      const newFilters = {};
      if (searchQuery) newFilters.search = searchQuery;
      if (statusFilter !== 'all') newFilters.status = statusFilter;
      if (priorityFilter !== 'all') newFilters.priority = priorityFilter;

      updateFilters(newFilters);
    }, 300);
    return () => clearTimeout(timer);
  }, [searchQuery, statusFilter, priorityFilter, updateFilters]);

  // Filtrer les tâches selon les critères de recherche
  const filteredTasks = personalTasks.filter(task => {
    // Filtrer par statut
    if (statusFilter !== 'all' && task.status !== statusFilter) {
      return false;
    }

    // Filtrer par priorité
    if (priorityFilter !== 'all' && task.priority !== priorityFilter) {
      return false;
    }

    // Filtrer par recherche
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        task.title.toLowerCase().includes(query) ||
        (task.description && task.description.toLowerCase().includes(query))
      );
    }

    return true;
  });

  // Fonction utilitaire pour gérer les opérations asynchrones avec gestion d'erreur
  const safeAsyncOperation = async (operation, errorMessage) => {
    setActionLoading(true);
    try {
      await operation();
      // Forcer un rafraîchissement de l'interface après l'opération
      setTimeout(() => {
        fetchPersonalTasks();
      }, 300);
      return true;
    } catch (error) {
      console.error(errorMessage, error);
      toast.error(`${errorMessage}: ${error.message || 'Erreur inconnue'}`);
      return false;
    } finally {
      setActionLoading(false);
    }
  };

  // Gérer la création d'une tâche
  const handleCreateTask = async (taskData) => {
    setActionLoading(true);
    try {
      console.log('Création de la tâche avec les données:', taskData);
      const newTask = await createPersonalTask(taskData);
      console.log('Tâche créée avec succès:', newTask);

      // Fermer le modal
      setShowCreateModal(false);

      // Rafraîchir la liste des tâches pour s'assurer qu'elle est à jour
      setTimeout(() => {
        fetchPersonalTasks();
      }, 100);

      return true;
    } catch (error) {
      console.error('Erreur lors de la création de la tâche:', error);
      toast.error(`Erreur lors de la création de la tâche: ${error.message || 'Erreur inconnue'}`);
      return false;
    } finally {
      setActionLoading(false);
    }
  };

  // Gérer la modification d'une tâche (sans statut)
  const handleEditTask = async (taskData) => {
    if (!selectedTask) return;

    setActionLoading(true);
    try {
      console.log('PersonalTasksPage - Modification de la tâche avec les données (sans statut):', taskData);

      // Utiliser updatePersonalTask pour modifier les champs (titre, description, dates, priorité)
      // Le statut est géré séparément par les boutons de changement rapide
      const updatedTask = await updatePersonalTask(selectedTask.id, taskData);

      console.log('PersonalTasksPage - Tâche modifiée avec succès:', updatedTask);

      // Fermer le modal
      setShowEditModal(false);
      setSelectedTask(null);

      toast.success('Tâche modifiée avec succès !');

      return true;
    } catch (error) {
      console.error('PersonalTasksPage - Erreur lors de la modification de la tâche:', error);
      toast.error(`Erreur lors de la modification de la tâche: ${error.message || 'Erreur inconnue'}`);
      return false;
    } finally {
      setActionLoading(false);
    }
  };

  // Gérer la mise à jour du statut d'une tâche
  const handleUpdateStatus = async (taskId, status) => {
    console.log(`PersonalTasksPage - Mise à jour du statut de la tâche ${taskId} vers ${status}`);

    setActionLoading(true);
    try {
      const updatedTask = await updatePersonalTaskStatus(taskId, status);
      console.log('PersonalTasksPage - Tâche mise à jour avec succès:', updatedTask);

      // Si c'est un changement rapide depuis le formulaire, fermer le modal
      if (showEditModal && selectedTask && selectedTask.id === taskId) {
        setShowEditModal(false);
        setSelectedTask(null);
        // Le message de succès est déjà affiché par le contexte
      }

      return true;
    } catch (error) {
      console.error('PersonalTasksPage - Erreur lors de la mise à jour du statut:', error);
      toast.error(`Erreur lors de la mise à jour du statut: ${error.message || 'Erreur inconnue'}`);
      return false;
    } finally {
      setActionLoading(false);
    }
  };

  // Gérer l'archivage d'une tâche
  const handleArchiveTask = async (taskId) => {
    return safeAsyncOperation(
      () => archivePersonalTask(taskId),
      'Erreur lors de l\'archivage de la tâche'
    );
  };

  // Gérer le désarchivage d'une tâche
  const handleUnarchiveTask = async (taskId) => {
    return safeAsyncOperation(
      () => unarchivePersonalTask(taskId),
      'Erreur lors du désarchivage de la tâche'
    );
  };

  // Gérer la suppression d'une tâche
  const handleDeleteTask = async () => {
    if (!selectedTask) return;

    const success = await safeAsyncOperation(
      () => deletePersonalTask(selectedTask.id),
      'Erreur lors de la suppression de la tâche'
    );

    if (success) {
      setShowDeleteModal(false);
      setSelectedTask(null);
    }
  };

  // Ouvrir le modal de modification
  const handleEditClick = (task) => {
    setSelectedTask(task);
    setShowEditModal(true);
  };

  // Ouvrir le modal de suppression
  const handleDeleteClick = (taskId) => {
    const task = personalTasks.find(t => t.id === taskId);
    if (task) {
      setSelectedTask(task);
      setShowDeleteModal(true);
    }
  };

  // Changer le mode d'affichage et sauvegarder la préférence
  const handleViewChange = (view) => {
    setDisplayView(view);
    localStorage.setItem('personalTasksDisplayView', view);
  };

  // Rafraîchir les tâches
  const handleRefresh = () => {
    fetchPersonalTasks();
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="bg-white rounded-xl shadow-lg overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <h1 className="text-2xl font-bold text-gray-900">Mes tâches personnelles</h1>

            <div className="flex items-center gap-2">
              <Button
                onClick={() => setShowCreateModal(true)}
                className="bg-indigo-600 hover:bg-indigo-700 text-white flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Nouvelle tâche
              </Button>
              <Button
                variant="outline"
                onClick={handleRefresh}
                className="flex items-center gap-2"
                disabled={loading}
              >
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                Actualiser
              </Button>
            </div>
          </div>
        </div>

        <div className="p-6 border-b border-gray-200 bg-gray-50">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div className="relative w-full md:w-64">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                type="text"
                placeholder="Rechercher une tâche..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            <div className="flex items-center gap-2 w-full md:w-auto">
              <Tabs value={displayView} onValueChange={handleViewChange} className="w-full md:w-auto">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="list" className="flex items-center gap-1">
                    <LayoutList className="h-4 w-4" />
                    <span className="hidden sm:inline">Liste</span>
                  </TabsTrigger>
                  <TabsTrigger value="cards" className="flex items-center gap-1">
                    <LayoutGrid className="h-4 w-4" />
                    <span className="hidden sm:inline">Cartes</span>
                  </TabsTrigger>
                  <TabsTrigger value="kanban" className="flex items-center gap-1">
                    <Kanban className="h-4 w-4" />
                    <span className="hidden sm:inline">Kanban</span>
                  </TabsTrigger>
                </TabsList>
              </Tabs>

              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center gap-2"
              >
                <Filter className="h-4 w-4" />
                Filtres
              </Button>
            </div>
          </div>

          {showFilters && (
            <div className="mt-4 p-4 bg-white rounded-lg border border-gray-200">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-sm font-medium text-gray-700">Filtres avancés</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowFilters(false)}
                  className="h-8 w-8 p-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-1 block">Statut</label>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Tous les statuts" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Tous les statuts</SelectItem>
                      <SelectItem value="a_faire">À faire</SelectItem>
                      <SelectItem value="en_cours">En cours</SelectItem>
                      <SelectItem value="en_revision">En révision</SelectItem>
                      <SelectItem value="achevee">Achevée</SelectItem>
                      <SelectItem value="archived">Archivée</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-1 block">Priorité</label>
                  <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Toutes les priorités" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Toutes les priorités</SelectItem>
                      <SelectItem value="faible">Faible</SelectItem>
                      <SelectItem value="moyenne">Moyenne</SelectItem>
                      <SelectItem value="haute">Haute</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-end">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setSearchQuery('');
                      setStatusFilter('all');
                      setPriorityFilter('all');
                      resetFilters();
                    }}
                    className="w-full"
                  >
                    Réinitialiser les filtres
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="p-6">
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
            </div>
          ) : error ? (
            <div className="bg-red-50 p-4 rounded-lg">
              <p className="text-red-600">{error}</p>
            </div>
          ) : filteredTasks.length === 0 ? (
            <div className="text-center py-12">
              <Clock className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900">Aucune tâche trouvée</h3>
              <p className="text-gray-500 mt-2">
                {searchQuery || statusFilter !== 'all' || priorityFilter !== 'all'
                  ? 'Aucune tâche ne correspond à vos critères de recherche.'
                  : 'Commencez par créer une nouvelle tâche.'}
              </p>
            </div>
          ) : (
            <>
              {displayView === 'list' && (
                <PersonalTaskListSimple
                  tasks={filteredTasks}
                  onEdit={handleEditClick}
                  onDelete={handleDeleteClick}
                  onArchive={handleArchiveTask}
                  onUnarchive={handleUnarchiveTask}
                  onUpdateStatus={handleUpdateStatus}
                />
              )}

              {displayView === 'cards' && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredTasks.map((task) => (
                    <PersonalTaskCard
                      key={task.id}
                      task={task}
                      onEdit={handleEditClick}
                      onDelete={handleDeleteClick}
                      onArchive={handleArchiveTask}
                      onUnarchive={handleUnarchiveTask}
                      onUpdateStatus={handleUpdateStatus}
                    />
                  ))}
                </div>
              )}

              {displayView === 'kanban' && (
                <PersonalTaskKanban
                  tasks={personalTasks} // Utiliser toutes les tâches pour le Kanban
                  onEdit={handleEditClick}
                  onDelete={handleDeleteClick}
                  onArchive={handleArchiveTask}
                  onUnarchive={handleUnarchiveTask}
                  onUpdateStatus={handleUpdateStatus}
                />
              )}
            </>
          )}
        </div>
      </div>

      {/* Modal de création de tâche */}
      <Dialog open={showCreateModal} onOpenChange={setShowCreateModal}>
        <DialogContent className="sm:max-w-[550px] max-h-[90vh] overflow-y-auto">
          <DialogHeader className="pb-2">
            <DialogTitle className="text-lg">Créer une nouvelle tâche</DialogTitle>
            <DialogDescription className="text-sm">
              Remplissez les informations pour créer une nouvelle tâche personnelle.
            </DialogDescription>
          </DialogHeader>
          <PersonalTaskForm
            onSubmit={handleCreateTask}
            onCancel={() => setShowCreateModal(false)}
            isSubmitting={actionLoading}
            personalTasks={personalTasks}
          />
        </DialogContent>
      </Dialog>

      {/* Modal de modification de tâche */}
      <Dialog open={showEditModal} onOpenChange={setShowEditModal}>
        <DialogContent className="sm:max-w-[550px] max-h-[90vh] overflow-y-auto">
          <DialogHeader className="pb-2">
            <DialogTitle className="text-lg">Modifier la tâche</DialogTitle>
            <DialogDescription className="text-sm">
              Modifiez les informations de la tâche.
            </DialogDescription>
          </DialogHeader>
          {selectedTask && (
            <PersonalTaskForm
              task={selectedTask}
              onSubmit={handleEditTask}
              onCancel={() => setShowEditModal(false)}
              isSubmitting={actionLoading}
              onQuickStatusChange={handleUpdateStatus}
              personalTasks={personalTasks}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Modal de confirmation de suppression */}
      <Dialog open={showDeleteModal} onOpenChange={setShowDeleteModal}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Êtes-vous sûr de vouloir supprimer cette tâche ?</DialogTitle>
            <DialogDescription>
              Cette action est irréversible. La tâche sera définitivement supprimée.
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              variant="outline"
              onClick={() => setShowDeleteModal(false)}
              disabled={actionLoading}
            >
              Annuler
            </Button>
            <Button
              onClick={handleDeleteTask}
              disabled={actionLoading}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              {actionLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Suppression...
                </>
              ) : (
                'Supprimer'
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default PersonalTasksPage;
