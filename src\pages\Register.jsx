import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Eye, EyeOff, Mail, Lock, User } from 'lucide-react';

import NewLogo from '@/components/NewLogo';

const Register = () => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const { register } = useAuth();
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        password: '',
        confirmPassword: '',
        role: 'client'
    });
    const [errors, setErrors] = useState({});
    const [isLoading, setIsLoading] = useState(false);

    const validatePassword = (password) => {
        const minLength = 6;
        const hasUpperCase = /[A-Z]/.test(password);
        const hasLowerCase = /[a-z]/.test(password);
        const hasNumbers = /\d/.test(password);

        if (password.length < minLength) {
            return t('validation.passwordLength');
        }
        if (!hasUpperCase) {
            return t('validation.passwordUpperCase');
        }
        if (!hasLowerCase) {
            return t('validation.passwordLowerCase');
        }
        if (!hasNumbers) {
            return t('validation.passwordNumber');
        }
        return '';
    };

    const validateForm = () => {
        const newErrors = {};

        if (!formData.name) {
            newErrors.name = t('auth.nameRequired');
        }

        if (!formData.email) {
            newErrors.email = t('auth.emailRequired');
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
            newErrors.email = t('auth.validEmail');
        }

        const passwordError = validatePassword(formData.password);
        if (!formData.password) {
            newErrors.password = t('auth.passwordRequired');
        } else if (passwordError) {
            newErrors.password = passwordError;
        }

        if (formData.password !== formData.confirmPassword) {
            newErrors.confirmPassword = t('validation.passwordMatch');
        }

        return newErrors;
    };

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
        // Clear error when user starts typing
        if (errors[name]) {
            setErrors(prev => ({
                ...prev,
                [name]: ''
            }));
        }
    };

    const handleRoleChange = (value) => {
        setFormData(prev => ({
            ...prev,
            role: value
        }));
        // Clear error when user selects a role
        if (errors.role) {
            setErrors(prev => ({
                ...prev,
                role: ''
            }));
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        const validationErrors = validateForm();

        if (Object.keys(validationErrors).length > 0) {
            setErrors(validationErrors);
            return;
        }

        setIsLoading(true);
        try {
            const { confirmPassword, ...registrationData } = formData;
            await register(registrationData);
            navigate('/login');
        } catch (error) {
            console.error('Registration error:', error);
            setErrors({
                submit: error.message || t('auth.registerError')
            });
        } finally {
            setIsLoading(false);
        }
    };

    const roles = [
        { value: 'client', label: t('roles.client') },
        { value: 'employee', label: t('roles.employee') },
        { value: 'admin', label: t('roles.admin') }
    ];

    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
            <div className="max-w-md w-full space-y-8">


                {/* Logo */}
                <div className="flex justify-center">
                    <Link
                        to="/"
                        className="flex items-center gap-3 text-3xl font-bold mb-6 hover:opacity-80 transition-opacity"
                    >
                        <NewLogo size={48} />
                        <span className="text-xl font-bold text-gray-900 dark:text-white">Notora</span>
                    </Link>
                </div>

                {/* Header */}
                <div className="text-center">
                    <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                        {t('auth.registerTitle')}
                    </h2>
                    <p className="text-gray-600 dark:text-gray-400">
                        {t('auth.registerSubtitle')}
                    </p>
                </div>

                {/* Form Container */}
                <div className="bg-white dark:bg-gray-800 py-8 px-6 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700">
                    <div className="space-y-6">
                        {errors.submit && (
                            <div className="bg-red-50 dark:bg-red-900/50 text-red-800 dark:text-red-200 p-3 rounded-lg mb-4 text-sm">
                                {errors.submit}
                            </div>
                        )}

                        <form className="space-y-6" onSubmit={handleSubmit} noValidate>
                            {/* Nom complet */}
                            <div>
                                <Label htmlFor="name" className="flex items-center gap-2 text-gray-700 dark:text-gray-300 font-medium mb-2">
                                    <User className="w-4 h-4" />
                                    Nom complet
                                </Label>
                                <Input
                                    id="name"
                                    name="name"
                                    type="text"
                                    autoComplete="name"
                                    placeholder="Entrez votre nom complet"
                                    value={formData.name}
                                    onChange={handleChange}
                                    className={`w-full px-4 py-3 border rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${errors.name
                                        ? 'border-red-500 dark:border-red-400'
                                        : 'border-gray-300 dark:border-gray-600'
                                        }`}
                                />
                                {errors.name && (
                                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.name}</p>
                                )}
                            </div>

                            {/* Adresse email */}
                            <div>
                                <Label htmlFor="email" className="flex items-center gap-2 text-gray-700 dark:text-gray-300 font-medium mb-2">
                                    <Mail className="w-4 h-4" />
                                    Adresse email
                                </Label>
                                <Input
                                    id="email"
                                    name="email"
                                    type="email"
                                    autoComplete="email"
                                    placeholder="<EMAIL>"
                                    value={formData.email}
                                    onChange={handleChange}
                                    className={`w-full px-4 py-3 border rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${errors.email
                                        ? 'border-red-500 dark:border-red-400'
                                        : 'border-gray-300 dark:border-gray-600'
                                        }`}
                                />
                                {errors.email && (
                                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.email}</p>
                                )}
                            </div>

                            {/* Mot de passe */}
                            <div>
                                <Label htmlFor="password" className="flex items-center gap-2 text-gray-700 dark:text-gray-300 font-medium mb-2">
                                    <Lock className="w-4 h-4" />
                                    Mot de passe
                                </Label>
                                <div className="relative">
                                    <Input
                                        id="password"
                                        name="password"
                                        type={showPassword ? "text" : "password"}
                                        autoComplete="new-password"
                                        placeholder="Entrez votre mot de passe"
                                        value={formData.password}
                                        onChange={handleChange}
                                        className={`w-full px-4 py-3 pr-12 border rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${errors.password
                                            ? 'border-red-500 dark:border-red-400'
                                            : 'border-gray-300 dark:border-gray-600'
                                            }`}
                                    />
                                    <button
                                        type="button"
                                        onClick={() => setShowPassword(!showPassword)}
                                        className="absolute inset-y-0 right-0 flex items-center pr-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                                    >
                                        {showPassword ? (
                                            <EyeOff className="h-5 w-5" />
                                        ) : (
                                            <Eye className="h-5 w-5" />
                                        )}
                                    </button>
                                </div>
                                {errors.password && (
                                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.password}</p>
                                )}
                            </div>

                            {/* Confirmer le mot de passe */}
                            <div>
                                <Label htmlFor="confirmPassword" className="flex items-center gap-2 text-gray-700 dark:text-gray-300 font-medium mb-2">
                                    <Lock className="w-4 h-4" />
                                    Confirmer le mot de passe
                                </Label>
                                <div className="relative">
                                    <Input
                                        id="confirmPassword"
                                        name="confirmPassword"
                                        type={showConfirmPassword ? "text" : "password"}
                                        autoComplete="new-password"
                                        placeholder="Confirmez votre mot de passe"
                                        value={formData.confirmPassword}
                                        onChange={handleChange}
                                        className={`w-full px-4 py-3 pr-12 border rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${errors.confirmPassword
                                            ? 'border-red-500 dark:border-red-400'
                                            : 'border-gray-300 dark:border-gray-600'
                                            }`}
                                    />
                                    <button
                                        type="button"
                                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                        className="absolute inset-y-0 right-0 flex items-center pr-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                                    >
                                        {showConfirmPassword ? (
                                            <EyeOff className="h-5 w-5" />
                                        ) : (
                                            <Eye className="h-5 w-5" />
                                        )}
                                    </button>
                                </div>
                                {errors.confirmPassword && (
                                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.confirmPassword}</p>
                                )}
                            </div>

                            {/* Rôle */}
                            <div>
                                <Label htmlFor="role" className="text-gray-700 dark:text-gray-300 font-medium mb-2 block">
                                    Rôle
                                </Label>
                                <Select value={formData.role} onValueChange={handleRoleChange}>
                                    <SelectTrigger className={`w-full px-4 py-3 border rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${errors.role
                                        ? 'border-red-500 dark:border-red-400'
                                        : 'border-gray-300 dark:border-gray-600'
                                        }`}>
                                        <SelectValue placeholder="Sélectionnez votre rôle">
                                            {roles.find(role => role.value === formData.role)?.label}
                                        </SelectValue>
                                    </SelectTrigger>
                                    <SelectContent className="bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg">
                                        {roles.map(role => (
                                            <SelectItem
                                                key={role.value}
                                                value={role.value}
                                                className="px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer"
                                            >
                                                {role.label}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                {errors.role && (
                                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.role}</p>
                                )}
                            </div>

                            {/* Bouton d'inscription */}
                            <Button
                                type="submit"
                                className="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg py-3 px-4 font-medium hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                disabled={isLoading}
                            >
                                {isLoading ? "Inscription en cours..." : "S'inscrire"}
                            </Button>
                        </form>

                        {/* Lien de connexion */}
                        <div className="text-center mt-6">
                            <p className="text-gray-600 dark:text-gray-400">
                                Vous avez déjà un compte ?{' '}
                                <Link to="/login" className="text-blue-600 hover:text-blue-500 font-medium">
                                    Se connecter
                                </Link>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Register;