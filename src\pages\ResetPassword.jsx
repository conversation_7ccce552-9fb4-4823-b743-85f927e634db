import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import { showSuccessToast, showErrorToast, showInfoToast } from '@/utils/toastUtils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Eye, EyeOff, Lock } from 'lucide-react';
import { passwordService } from '@/services/passwordService';

const ResetPassword = () => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const { token } = useParams();
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [formData, setFormData] = useState({
        new_password: '',
        confirm_password: ''
    });
    const [errors, setErrors] = useState({});

    const validatePassword = (password) => {
        const minLength = 6;
        const hasUpperCase = /[A-Z]/.test(password);
        const hasLowerCase = /[a-z]/.test(password);
        const hasNumbers = /\d/.test(password);

        const errors = [];

        if (password.length < minLength) {
            errors.push(`Le mot de passe doit contenir au moins ${minLength} caractères`);
        }
        if (!hasUpperCase) {
            errors.push('Au moins une lettre majuscule');
        }
        if (!hasLowerCase) {
            errors.push('Au moins une lettre minuscule');
        }
        if (!hasNumbers) {
            errors.push('Au moins un chiffre');
        }

        return errors.length > 0 ? errors.join('\n') : '';
    };

    const validateForm = () => {
        const newErrors = {};

        // Validation du nouveau mot de passe
        if (!formData.new_password) {
            newErrors.new_password = 'Le nouveau mot de passe est requis';
        } else {
            const passwordError = validatePassword(formData.new_password);
            if (passwordError) {
                newErrors.new_password = passwordError;
            }
        }

        // Validation de la confirmation du mot de passe
        if (!formData.confirm_password) {
            newErrors.confirm_password = 'La confirmation du mot de passe est requise';
        } else if (formData.new_password !== formData.confirm_password) {
            newErrors.confirm_password = 'Les mots de passe ne correspondent pas';
        }

        // Affichage des messages d'erreur une seule fois
        if (Object.keys(newErrors).length > 0) {
            if (newErrors.new_password) {
                toast.error('Le mot de passe ne respecte pas les critères de sécurité');
            } else if (newErrors.confirm_password) {
                toast.error('Les mots de passe ne correspondent pas');
            }
        }

        return newErrors;
    };

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
        if (errors[name]) {
            setErrors(prev => ({
                ...prev,
                [name]: ''
            }));
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setErrors({});

        // Vérifier si le token est présent
        if (!token) {
            toast.error("Token de réinitialisation manquant. Veuillez utiliser le lien complet envoyé par email.");
            setErrors({ submit: "Token de réinitialisation manquant. Veuillez utiliser le lien complet envoyé par email." });
            return;
        }

        console.log("Réinitialisation du mot de passe avec le token:", token);

        const validationErrors = validateForm();
        if (Object.keys(validationErrors).length > 0) {
            setErrors(validationErrors);
            return;
        }

        setIsLoading(true);
        try {
            // Vérifier que les mots de passe correspondent
            if (formData.new_password !== formData.confirm_password) {
                throw new Error("Les mots de passe ne correspondent pas");
            }

            console.log("Envoi de la demande de réinitialisation...");
            const response = await passwordService.resetPassword(
                token,
                {
                    new_password: formData.new_password,
                    confirm_password: formData.confirm_password
                }
            );

            console.log("Réponse reçue:", response);

            // Utilisation du type de message pour déterminer l'affichage
            if (response.type === 'success') {
                showSuccessToast(response.message || "Mot de passe réinitialisé avec succès");
                setTimeout(() => {
                    navigate('/login');
                }, 2000);
            } else {
                // Si le type n'est pas 'success', on traite comme une erreur
                const errorMsg = response.message || "Erreur lors de la réinitialisation du mot de passe";
                showErrorToast(errorMsg);

                if (response.status === 401 || response.status === 404) {
                    showInfoToast("Vous allez être redirigé vers la page de demande de réinitialisation");
                    setTimeout(() => {
                        navigate('/forgot-password');
                    }, 3000);
                }
                setErrors({ submit: errorMsg });
            }
        } catch (error) {
            console.error("Erreur lors de la réinitialisation:", error);

            // Message d'erreur plus spécifique
            const errorMsg = error.message || 'Une erreur inattendue est survenue. Veuillez réessayer.';
            showErrorToast(errorMsg);
            setErrors({
                submit: errorMsg
            });
        } finally {
            setIsLoading(false);
        }
    };

    // Effet pour afficher un message si le token est manquant
    React.useEffect(() => {
        if (!token) {
            console.error("Token manquant dans l'URL");
            showErrorToast("Token de réinitialisation manquant. Veuillez utiliser le lien complet envoyé par email.");
        } else {
            console.log("Token de réinitialisation détecté:", token);
        }
    }, [token]);

    return (
        <div className="min-h-screen bg-gradient-to-br from-white via-blue-50/30 to-indigo-50/30 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
            <div className="sm:mx-auto sm:w-full sm:max-w-md">
                <div className="text-center">
                    <h2 className="text-2xl font-bold text-gray-900">
                        Définir un nouveau mot de passe
                    </h2>
                    <p className="mt-2 text-sm text-gray-600">
                        Choisissez un nouveau mot de passe sécurisé pour votre compte
                    </p>
                    {!token && (
                        <div className="mt-4 p-2 bg-red-50 text-red-600 rounded-md text-sm">
                            Token de réinitialisation manquant. Veuillez utiliser le lien complet envoyé par email.
                        </div>
                    )}
                </div>
            </div>

            <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
                <div className="bg-white/80 backdrop-blur-sm py-8 px-4 shadow-xl rounded-2xl sm:px-10">
                    {errors.submit && (
                        <div className="mb-4 p-4 bg-red-50 text-red-600 rounded-lg text-sm">
                            {errors.submit}
                        </div>
                    )}

                    <form onSubmit={handleSubmit} className="space-y-6">
                        <div>
                            <Label htmlFor="new_password" className="flex items-center gap-2 text-gray-700">
                                <Lock className="w-4 h-4" />
                                Nouveau mot de passe
                            </Label>
                            <div className="mt-1 relative">
                                <Input
                                    id="new_password"
                                    name="new_password"
                                    type={showPassword ? "text" : "password"}
                                    autoComplete="new-password"
                                    required
                                    value={formData.new_password}
                                    onChange={handleChange}
                                    className={`block w-full pr-10 ${errors.new_password ? 'border-red-500' : 'border-gray-300'}`}
                                    placeholder="Entrez votre nouveau mot de passe"
                                />
                                <button
                                    type="button"
                                    onClick={() => setShowPassword(!showPassword)}
                                    className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600"
                                >
                                    {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                                </button>
                            </div>
                            {errors.new_password && (
                                <p className="mt-2 text-sm text-red-600">{errors.new_password}</p>
                            )}
                        </div>

                        <div>
                            <Label htmlFor="confirm_password" className="flex items-center gap-2 text-gray-700">
                                <Lock className="w-4 h-4" />
                                Confirmer le mot de passe
                            </Label>
                            <div className="mt-1 relative">
                                <Input
                                    id="confirm_password"
                                    name="confirm_password"
                                    type={showConfirmPassword ? "text" : "password"}
                                    autoComplete="new-password"
                                    required
                                    value={formData.confirm_password}
                                    onChange={handleChange}
                                    className={`block w-full pr-10 ${errors.confirm_password ? 'border-red-500' : 'border-gray-300'}`}
                                    placeholder="Confirmez votre nouveau mot de passe"
                                />
                                <button
                                    type="button"
                                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                    className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600"
                                >
                                    {showConfirmPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                                </button>
                            </div>
                            {errors.confirm_password && (
                                <p className="mt-2 text-sm text-red-600">{errors.confirm_password}</p>
                            )}
                        </div>

                        <Button
                            type="submit"
                            className="w-full flex justify-center py-2 px-4"
                            disabled={isLoading}
                        >
                            {isLoading ? 'Réinitialisation en cours...' : 'Réinitialiser le mot de passe'}
                        </Button>
                    </form>
                </div>
            </div>
        </div>
    );
};

export default ResetPassword;