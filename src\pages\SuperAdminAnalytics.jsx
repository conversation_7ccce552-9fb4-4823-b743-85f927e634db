import React, { useState } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import SuperAdminBIDashboard from '@/components/dashboard/SuperAdminBIDashboard';
import { <PERSON><PERSON>hart3, <PERSON><PERSON><PERSON>, LineChart, RefreshCw } from 'lucide-react';

const SuperAdminAnalytics = () => {
  const [activeTab, setActiveTab] = useState('users');
  const [refreshInterval, setRefreshInterval] = useState('30');
  const [lockedTab, setLockedTab] = useState(null);
  const [lastClickTime, setLastClickTime] = useState(0);

  // Fonction pour gérer le double-clic sur un onglet
  const handleTabClick = (tabValue) => {
    const now = Date.now();

    // Si l'utilisateur clique sur l'onglet déjà actif
    if (tabValue === activeTab) {
      // Vérifier si c'est un double-clic (moins de 500ms entre les clics)
      if (now - lastClickTime < 500) {
        // Si l'onglet est déjà verrouillé, le déverrouiller
        if (lockedTab === tabValue) {
          setLockedTab(null);
          // Notification de déverrouillage
          console.log(`Mode d'affichage "${tabValue}" déverrouillé`);
        } else {
          // Sinon, verrouiller l'onglet
          setLockedTab(tabValue);
          // Notification de verrouillage
          console.log(`Mode d'affichage "${tabValue}" verrouillé`);
        }
      }
    } else {
      // Si l'onglet est verrouillé et que l'utilisateur essaie de changer d'onglet
      if (lockedTab !== null && lockedTab !== tabValue) {
        // Notification d'avertissement
        console.log(`Le mode d'affichage "${lockedTab}" est verrouillé. Double-cliquez dessus pour le déverrouiller.`);
        return; // Ne pas changer d'onglet
      }

      // Changer d'onglet normalement
      setActiveTab(tabValue);
    }

    // Mettre à jour le temps du dernier clic
    setLastClickTime(now);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-2xl font-bold">Tableau de Bord d'Analyse</h1>

          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-500">Rafraîchir tous les</span>
              <Select
                value={refreshInterval}
                onValueChange={setRefreshInterval}
              >
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Intervalle" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10 secondes</SelectItem>
                  <SelectItem value="30">30 secondes</SelectItem>
                  <SelectItem value="60">1 minute</SelectItem>
                  <SelectItem value="300">5 minutes</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <button className="p-2 rounded-full bg-purple-100 text-purple-800 hover:bg-purple-200 transition-colors">
              <RefreshCw className="h-5 w-5" />
            </button>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <Tabs value={activeTab} className="w-full">
            <div className="flex justify-between items-center mb-6">
              <TabsList className="grid w-full max-w-md grid-cols-3">
                <TabsTrigger
                  value="users"
                  onClick={() => handleTabClick('users')}
                  className={lockedTab === 'users' ? 'border-2 border-purple-500' : ''}
                >
                  <PieChart className="h-4 w-4 mr-2" />
                  {lockedTab === 'users' && '🔒 '}Utilisateurs
                </TabsTrigger>
                <TabsTrigger
                  value="activity"
                  onClick={() => handleTabClick('activity')}
                  className={lockedTab === 'activity' ? 'border-2 border-purple-500' : ''}
                >
                  <BarChart3 className="h-4 w-4 mr-2" />
                  {lockedTab === 'activity' && '🔒 '}Activité
                </TabsTrigger>
                <TabsTrigger
                  value="trends"
                  onClick={() => handleTabClick('trends')}
                  className={lockedTab === 'trends' ? 'border-2 border-purple-500' : ''}
                >
                  <LineChart className="h-4 w-4 mr-2" />
                  {lockedTab === 'trends' && '🔒 '}Tendances
                </TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="users" className="mt-6">
              <SuperAdminBIDashboard refreshInterval={parseInt(refreshInterval) * 1000} />
            </TabsContent>

            <TabsContent value="activity" className="mt-6">
              <div className="flex justify-center items-center h-64 bg-gray-50 rounded-lg border border-dashed border-gray-300">
                <div className="text-center">
                  <BarChart3 className="h-12 w-12 mx-auto text-gray-400" />
                  <h3 className="mt-2 text-lg font-medium">Tableau d'activité</h3>
                  <p className="text-sm text-gray-500">Cette fonctionnalité sera disponible prochainement.</p>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="trends" className="mt-6">
              <div className="flex justify-center items-center h-64 bg-gray-50 rounded-lg border border-dashed border-gray-300">
                <div className="text-center">
                  <LineChart className="h-12 w-12 mx-auto text-gray-400" />
                  <h3 className="mt-2 text-lg font-medium">Analyse des tendances</h3>
                  <p className="text-sm text-gray-500">Cette fonctionnalité sera disponible prochainement.</p>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default SuperAdminAnalytics;
