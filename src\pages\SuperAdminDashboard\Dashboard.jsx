import React from 'react';
import { useAuth } from '@/contexts/AuthContext';

const Dashboard = () => {
    const { user } = useAuth();

    return (
        <div className="p-8">
            <h1 className="text-2xl font-semibold text-[#6B4EFF] mb-6">
                Bien<PERSON>ue, {user?.name}
            </h1>

            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <p className="text-gray-600">
                    Cette fonctionnalité arrive bientôt. Restez à l'écoute !
                </p>
            </div>
        </div>
    );
};

export default Dashboard;