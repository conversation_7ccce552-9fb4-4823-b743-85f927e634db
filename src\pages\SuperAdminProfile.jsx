import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

const containerVariants = {
    hidden: { opacity: 0 },
    show: {
        opacity: 1,
        transition: {
            staggerChildren: 0.1
        }
    }
};

const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 }
};
import { useTranslation } from 'react-i18next';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'react-toastify';
import axios from 'axios';
import {
    User,
    Mail,
    Lock,
    Eye,
    EyeOff,
    Save,
    X
} from 'lucide-react';

const API_URL = 'http://localhost:8000/api';

const SuperAdminProfile = () => {
    const { t } = useTranslation();
    const { user, getAuthHeader, updateUser, completePasswordChange, changePassword } = useAuth();
    const [loading, setLoading] = useState(true);
    const [passwordLoading, setPasswordLoading] = useState(false);
    const [showCurrentPassword, setShowCurrentPassword] = useState(false);
    const [showNewPassword, setShowNewPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [isEditing, setIsEditing] = useState(false);
    const [showPasswordHistory, setShowPasswordHistory] = useState(false);
    const [lastPasswordChange, setLastPasswordChange] = useState(null);
    const [profileData, setProfileData] = useState({
        name: '',
        email: '',
        role: '',
        lastLogin: null,
        accountCreated: null
    });
    const [passwordData, setPasswordData] = useState({
        current_password: '',
        new_password: '',
        confirm_password: ''
    });
    const [passwordStrength, setPasswordStrength] = useState({
        score: 0,
        feedback: ''
    });

    useEffect(() => {
        if (user) {
            setProfileData({
                name: user.name,
                email: user.email,
                role: user.role || 'Super Admin',
                lastLogin: user.last_login || new Date().toISOString(),
                accountCreated: user.created_at || new Date().toISOString()
            });
            setLoading(false);
            fetchLastPasswordChange();
        } else {
            fetchUserProfile();
        }
    }, [user]);

    const fetchLastPasswordChange = async () => {
        // Désactivé temporairement jusqu'à ce que le backend soit prêt
        return;
        /* try {
            const response = await axios.get(`${API_URL}/users/password-history/last-change`, {
                headers: getAuthHeader()
            });
            if (response.data) {
                setLastPasswordChange(response.data.last_change);
            }
        } catch (error) {
            console.error('Error fetching password history:', error);
            if (error.response?.status !== 404) {
                toast.error(t('errors.fetchingPasswordHistory'));
            }
        } */
    };

    const evaluatePasswordStrength = (password) => {
        let score = 0;
        let feedback = [];

        if (password.length >= 8) score += 1;
        if (password.match(/[A-Z]/)) score += 1;
        if (password.match(/[a-z]/)) score += 1;
        if (password.match(/[0-9]/)) score += 1;
        if (password.match(/[^A-Za-z0-9]/)) score += 1;

        if (score < 2) feedback.push('Mot de passe faible');
        else if (score < 4) feedback.push('Mot de passe moyen');
        else feedback.push('Mot de passe fort');

        return { score, feedback: feedback.join(', ') };
    };

    const fetchUserProfile = async () => {
        try {
            const response = await axios.get(`${API_URL}/user/`, {
                headers: getAuthHeader()
            });
            if (response.data) {
                setProfileData({
                    name: response.data.name,
                    email: response.data.email
                });
                updateUser(response.data);
            }
        } catch (error) {
            console.error('Error fetching profile:', error);
            toast.error('Erreur lors du chargement du profil');
        } finally {
            setLoading(false);
        }
    };

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setProfileData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handlePasswordChange = (e) => {
        const { name, value } = e.target;
        setPasswordData(prev => ({
            ...prev,
            [name]: value
        }));

        if (name === 'new_password') {
            setPasswordStrength(evaluatePasswordStrength(value));
        }
    };

    const getPasswordStrengthColor = (score) => {
        switch (score) {
            case 0:
            case 1:
                return 'bg-red-500';
            case 2:
            case 3:
                return 'bg-yellow-500';
            case 4:
            case 5:
                return 'bg-green-500';
            default:
                return 'bg-gray-200';
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        try {
            console.log('📤 Données envoyées (SuperAdmin):', profileData);

            // Séparer le nom en first_name et last_name si nécessaire
            const nameParts = profileData.name ? profileData.name.split(' ') : [];
            const firstName = nameParts[0] || '';
            const lastName = nameParts.slice(1).join(' ') || '';

            const response = await axios.put(`${API_URL}/profile/update/`, {
                first_name: firstName,
                last_name: lastName,
                email: profileData.email
            }, {
                headers: {
                    ...getAuthHeader(),
                    'Content-Type': 'application/json'
                }
            });

            console.log('📥 Réponse reçue (SuperAdmin):', response.data);

            if (response.data && response.data.message) {
                // Mettre à jour avec la réponse du backend
                updateUser(response.data.user);
                setProfileData({
                    name: response.data.user.name,
                    email: response.data.user.email
                });
                toast.success(response.data.message || 'Profil mis à jour avec succès');
                setIsEditing(false);
            }
        } catch (error) {
            console.error('❌ Erreur lors de la mise à jour du profil (SuperAdmin):', error);
            const errorMessage = error.response?.data?.error || 'Erreur lors de la mise à jour du profil';
            toast.error(errorMessage);
            setProfileData({
                name: user.name,
                email: user.email
            });
        }
    };

    const handleUpdatePassword = async (e) => {
        e.preventDefault();

        // Vérifier que les champs ne sont pas vides
        if (!passwordData.current_password || !passwordData.new_password || !passwordData.confirm_password) {
            toast.error('Tous les champs sont obligatoires');
            return;
        }

        // Vérifier que les mots de passe correspondent (en supprimant les espaces avant et après)
        const newPassword = passwordData.new_password.trim();
        const confirmPassword = passwordData.confirm_password.trim();

        if (newPassword !== confirmPassword) {
            console.log('Mots de passe différents:', {
                newPasswordLength: newPassword.length,
                confirmPasswordLength: confirmPassword.length,
                newPasswordChars: newPassword.split('').map(c => c.charCodeAt(0)),
                confirmPasswordChars: confirmPassword.split('').map(c => c.charCodeAt(0))
            });
            toast.error('Les mots de passe ne correspondent pas');
            return;
        }

        // Vérifier la complexité du mot de passe
        if (passwordData.new_password.length < 8) {
            toast.error('Le mot de passe doit contenir au moins 8 caractères');
            return;
        }

        try {
            setPasswordLoading(true);
            console.log('Tentative de changement de mot de passe pour le super admin');

            // Utiliser la fonction du contexte d'authentification avec les valeurs nettoyées
            await changePassword({
                current_password: passwordData.current_password.trim(),
                new_password: newPassword, // Déjà nettoyé plus haut
                confirm_password: confirmPassword // Déjà nettoyé plus haut
            });

            // Réinitialiser les champs de mot de passe
            setPasswordData({
                current_password: '',
                new_password: '',
                confirm_password: ''
            });

            // La gestion des erreurs et des messages est maintenant dans le contexte d'authentification
        } catch (error) {
            console.error('Erreur lors du changement de mot de passe dans le profil:', error);
            // Les messages d'erreur sont déjà gérés dans le contexte d'authentification
        } finally {
            setPasswordLoading(false);
        }
    };

    const getInitials = (name) => {
        return name
            .split(' ')
            .map(word => word[0])
            .join('')
            .toUpperCase();
    };

    if (loading) {
        return (
            <div className="flex justify-center items-center min-h-screen">
                <div className="text-xl font-semibold">{t('common.loading')}</div>
            </div>
        );
    }

    const getAvatarColor = (name) => {
        const colors = [
            'bg-purple-500',
            'bg-blue-500',
            'bg-green-500',
            'bg-yellow-500',
            'bg-red-500',
            'bg-pink-500',
            'bg-indigo-500',
            'bg-teal-500'
        ];
        const index = name.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
        return colors[index % colors.length];
    };

    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
            <motion.div
                className="max-w-4xl mx-auto space-y-8"
                variants={containerVariants}
                initial="hidden"
                animate="show"
            >
                <motion.div variants={itemVariants}>
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Mon Profil</h1>
                    <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Gérez vos informations personnelles</p>
                </motion.div>

                <motion.div
                    className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700"
                    variants={itemVariants}
                >
                    <div className="flex items-center gap-6">
                        <div className="flex-shrink-0 w-20 h-20 rounded-full bg-[#6B4EFF] flex items-center justify-center text-white text-2xl font-semibold shadow-lg transition-all duration-200">
                            {getInitials(profileData.name)}
                        </div>
                        <motion.div variants={itemVariants}>
                            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                                {profileData.name}
                            </h2>
                            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                {profileData.role?.replace('super_admin', 'Super Admin')}
                            </p>
                        </motion.div>
                    </div>
                    {user?.temp_password_required && (
                        <div className="mt-6 p-4 bg-amber-50 dark:bg-amber-900/30 border-l-4 border-amber-500 text-amber-700 dark:text-amber-200">
                            <p className="font-medium">Vous utilisez actuellement un mot de passe temporaire</p>
                            <p className="text-sm mt-1">Pour des raisons de sécurité, nous vous recommandons de le changer dès que possible.</p>
                        </div>
                    )}
                </motion.div>
                <>
                    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700 transition-all duration-300">
                        <div className="flex justify-between items-center mb-6">
                            <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                                <User className="w-5 h-5 text-purple-500" />
                                Informations Personnelles
                            </h2>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                                <p className="text-sm text-gray-500 dark:text-gray-400">Rôle</p>
                                <p className="text-base font-medium text-gray-900 dark:text-white mt-1">{profileData.role}</p>
                            </div>
                            <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                                <p className="text-sm text-gray-500 dark:text-gray-400">Dernière connexion</p>
                                <p className="text-base font-medium text-gray-900 dark:text-white mt-1">
                                    {new Date(profileData.lastLogin).toLocaleDateString('fr-FR', {
                                        day: 'numeric',
                                        month: 'long',
                                        year: 'numeric',
                                        hour: '2-digit',
                                        minute: '2-digit'
                                    })}
                                </p>
                            </div>
                            <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                                <p className="text-sm text-gray-500 dark:text-gray-400">Compte créé le</p>
                                <p className="text-base font-medium text-gray-900 dark:text-white mt-1">
                                    {new Date(profileData.accountCreated).toLocaleDateString('fr-FR', {
                                        day: 'numeric',
                                        month: 'long',
                                        year: 'numeric'
                                    })}
                                </p>
                            </div>
                            {lastPasswordChange && (
                                <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                                    <p className="text-sm text-gray-500 dark:text-gray-400">Dernier changement de mot de passe</p>
                                    <p className="text-base font-medium text-gray-900 dark:text-white mt-1">
                                        {new Date(lastPasswordChange).toLocaleDateString('fr-FR', {
                                            day: 'numeric',
                                            month: 'long',
                                            year: 'numeric'
                                        })}
                                    </p>
                                </div>
                            )}
                        </div>

                        {!isEditing ? (
                            <button
                                type="button"
                                onClick={() => setIsEditing(true)}
                                className="flex items-center gap-2 px-4 py-2 text-purple-600 hover:text-purple-700 transition-all duration-200"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />
                                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" />
                                </svg>
                                Modifier
                            </button>
                        ) : (
                            <form onSubmit={handleSubmit} className="space-y-6">
                                <motion.div variants={itemVariants}>
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Nom
                                    </label>
                                    <div className="relative">
                                        <input
                                            type="text"
                                            name="name"
                                            value={profileData.name}
                                            onChange={handleInputChange}
                                            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600"
                                        />
                                        <User className="w-5 h-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                                    </div>
                                </motion.div>

                                <motion.div variants={itemVariants}>
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Email
                                    </label>
                                    <div className="relative">
                                        <input
                                            type="email"
                                            name="email"
                                            value={profileData.email}
                                            onChange={handleInputChange}
                                            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600"
                                        />
                                        <Mail className="w-5 h-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                                    </div>
                                </motion.div>

                                <div className="flex justify-end gap-3">
                                    <button
                                        type="button"
                                        onClick={() => {
                                            setIsEditing(false);
                                            setProfileData({
                                                name: user.name,
                                                email: user.email
                                            });
                                        }}
                                        className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-all duration-200 dark:text-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 hover:shadow-md"
                                    >
                                        Annuler
                                    </button>
                                    <button
                                        type="submit"
                                        className="flex items-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-all duration-200 hover:shadow-md"
                                    >
                                        <Save className="w-4 h-4" />
                                        Enregistrer
                                    </button>
                                </div>
                            </form>
                        )}
                    </div>

                    <motion.div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700 transition-all duration-300">
                        <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
                            <Lock className="w-5 h-5 text-purple-500" />
                            Sécurité
                        </h2>
                        <h3 className="text-md font-medium text-gray-700 dark:text-gray-300 mb-4">
                            Changer le mot de passe
                        </h3>

                        {/* Message d'information pour le super admin */}
                        <div className="p-4 bg-blue-50 dark:bg-blue-900/30 border-l-4 border-blue-500 text-blue-700 dark:text-blue-200 mb-6">
                            <p className="font-medium">Information importante</p>
                            <p className="text-sm mt-1">
                                En tant que super administrateur, vous pouvez changer votre mot de passe ici.
                                Si vous rencontrez des problèmes, veuillez contacter le support technique.
                            </p>
                        </div>

                        <form onSubmit={handleUpdatePassword} className="space-y-4">
                            <div variants={itemVariants}>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Mot de passe actuel
                                </label>
                                <div className="relative">
                                    <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                                    <input
                                        type={showCurrentPassword ? "text" : "password"}
                                        name="current_password"
                                        value={passwordData.current_password}
                                        onChange={handlePasswordChange}
                                        placeholder="Entrez votre mot de passe actuel"
                                        className="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 transition-all"
                                        required
                                    />
                                    <button
                                        type="button"
                                        onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-purple-500"
                                    >
                                        {showCurrentPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                                    </button>
                                </div>
                            </div>

                            <div variants={itemVariants}>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Nouveau mot de passe
                                </label>
                                <div className="relative">
                                    <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                                    <input
                                        type={showNewPassword ? "text" : "password"}
                                        name="new_password"
                                        value={passwordData.new_password}
                                        onChange={handlePasswordChange}
                                        placeholder="Entrez votre nouveau mot de passe"
                                        className="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 transition-all"
                                        required
                                    />
                                    <button
                                        type="button"
                                        onClick={() => setShowNewPassword(!showNewPassword)}
                                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-purple-500"
                                    >
                                        {showNewPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                                    </button>
                                </div>
                                {passwordData.new_password && (
                                    <div className="mt-2">
                                        <div className="h-2 w-full bg-gray-200 rounded-full overflow-hidden">
                                            <div
                                                className={`h-full ${getPasswordStrengthColor(passwordStrength.score)} transition-all duration-300`}
                                                style={{ width: `${(passwordStrength.score / 5) * 100}%` }}
                                            />
                                        </div>
                                        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{passwordStrength.feedback}</p>
                                    </div>
                                )}
                            </div>

                            <div variants={itemVariants}>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Confirmer le nouveau mot de passe
                                </label>
                                <div className="relative">
                                    <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                                    <input
                                        type={showConfirmPassword ? "text" : "password"}
                                        name="confirm_password"
                                        value={passwordData.confirm_password}
                                        onChange={handlePasswordChange}
                                        placeholder="Confirmez votre nouveau mot de passe"
                                        className="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 transition-all"
                                        required
                                    />
                                    <button
                                        type="button"
                                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-purple-500"
                                    >
                                        {showConfirmPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                                    </button>
                                </div>
                            </div>

                            <div className="flex justify-end mt-6">
                                <button
                                    type="submit"
                                    disabled={passwordLoading}
                                    className={`flex items-center justify-center gap-2 px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-all duration-200 hover:shadow-md w-full md:w-auto ${passwordLoading ? 'opacity-70 cursor-not-allowed' : ''}`}
                                >
                                    {passwordLoading ? (
                                        <>
                                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </svg>
                                            Traitement en cours...
                                        </>
                                    ) : (
                                        <>
                                            <Save className="w-5 h-5" />
                                            Changer le mot de passe
                                        </>
                                    )}
                                </button>
                            </div>
                        </form>
                    </motion.div>
                </>
            </motion.div>
        </div>
    );
};

export default SuperAdminProfile;