import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Settings, Moon, Sun, Globe } from 'lucide-react';
import { toast } from 'react-toastify';

const SuperAdminSettings = () => {
    const { t, i18n } = useTranslation();
    const [darkMode, setDarkMode] = useState(document.documentElement.classList.contains('dark'));
    const [language, setLanguage] = useState(i18n.language);

    const toggleDarkMode = () => {
        if (darkMode) {
            document.documentElement.classList.remove('dark');
        } else {
            document.documentElement.classList.add('dark');
        }
        setDarkMode(!darkMode);
        toast.success(t('messages.themeChanged'));
    };

    const handleLanguageChange = (e) => {
        const newLang = e.target.value;
        i18n.changeLanguage(newLang);
        setLanguage(newLang);
        toast.success(t('messages.languageChanged'));
    };

    return (
        <div className="ml-64 p-8">
            <div className="max-w-4xl mx-auto">
                <div className="mb-8">
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                        Paramètres
                    </h1>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                        Personnalisez votre expérience
                    </p>
                </div>

                <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 space-y-6">
                    {/* Thème */}
                    <div>
                        <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                            Thème
                        </h2>
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                                {darkMode ? <Moon className="w-5 h-5" /> : <Sun className="w-5 h-5" />}
                                <span className="text-sm text-gray-700 dark:text-gray-300">
                                    {darkMode ? 'Mode sombre' : 'Mode clair'}
                                </span>
                            </div>
                            <button
                                onClick={toggleDarkMode}
                                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                            >
                                Changer de thème
                            </button>
                        </div>
                    </div>

                    {/* Langue */}
                    <div>
                        <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                            Langue
                        </h2>
                        <div className="flex items-center gap-3">
                            <Globe className="w-5 h-5" />
                            <select
                                id="language-selector"
                                name="language-selector"
                                value={language}
                                onChange={handleLanguageChange}
                                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600"
                            >
                                <option value="fr">Français</option>
                                <option value="en">English</option>

                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default SuperAdminSettings;