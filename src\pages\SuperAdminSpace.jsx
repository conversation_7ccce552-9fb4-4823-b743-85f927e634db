import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'react-toastify';
import {
    Users,
    Search,
    UserPlus,
    Pencil,
    Trash2,
    X,
    <PERSON><PERSON><PERSON><PERSON>gle,
    BarChart3,
    Settings
} from 'lucide-react';
import { Link } from 'react-router-dom';
import { userService } from '@/services/userService';
import DeleteUserConfirmModal from '@/components/DeleteUserConfirmModal';

const SuperAdminSpace = () => {
    const { t } = useTranslation();
    const { getAuthHeader, isAuthenticated } = useAuth();

    // Vérifier l'authentification au chargement du composant
    useEffect(() => {
        if (!isAuthenticated()) {
            console.error('SuperAdminSpace - User not authenticated');
            toast.error('Vous devez être connecté pour accéder à cette page');
            // Rediriger vers la page de connexion
            window.location.href = '/login';
        }
    }, [isAuthenticated]);
    const [users, setUsers] = useState([]);
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [showAddModal, setShowAddModal] = useState(false);
    const [showEditModal, setShowEditModal] = useState(false);
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [selectedUser, setSelectedUser] = useState(null);
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        role: 'client',
        permissions: {
            manage_users: false,
            activate_user_permissions: false,
            manage_teams: false,
            manage_team_tasks: false,
            manage_team_calendars: false,
            view_team_dashboards: false
        }
    });

    const handlePermissionChange = (permission) => {
        setFormData(prev => ({
            ...prev,
            permissions: {
                ...prev.permissions,
                [permission]: !prev.permissions[permission]
            }
        }));
    };

    const handleAddUser = async (e) => {
        e.preventDefault();
        const validationErrors = validateForm();

        if (Object.keys(validationErrors).length > 0) {
            setErrors(validationErrors);
            return;
        }

        try {
            // Afficher un message de chargement
            const loadingToast = toast.loading('Création de l\'utilisateur en cours...');

            // S'assurer que les valeurs sont des chaînes de caractères
            const userData = {
                name: String(formData.name).trim(),
                email: String(formData.email).trim(),
                role: String(formData.role).trim(),
                password: formData.password || undefined,
                generate_temp_password: !formData.password,
                permissions: formData.permissions
            };

            console.log('Adding user with data:', { ...userData, password: userData.password ? '***' : undefined });

            const result = await userService.createUser(userData);

            // Fermer le toast de chargement
            toast.dismiss(loadingToast);

            if (result.success) {
                toast.success(t('messages.userAddSuccess') || 'Utilisateur créé avec succès');
                setShowAddModal(false);
                fetchUsers();
                setFormData({
                    name: '',
                    email: '',
                    role: 'client',
                    permissions: {
                        manage_users: false,
                        activate_user_permissions: false,
                        manage_teams: false,
                        manage_team_tasks: false,
                        manage_team_calendars: false,
                        view_team_dashboards: false
                    }
                });
            } else {
                toast.error(result.message || t('messages.userAddError') || 'Erreur lors de la création de l\'utilisateur');
            }
        } catch (error) {
            console.error('Error adding user:', error);

            // Afficher plus de détails sur l'erreur
            if (error.response) {
                console.error('Error response:', error.response);
                toast.error(`Erreur ${error.response.status}: ${error.response.data?.message || 'Erreur serveur'}`);
            } else if (error.request) {
                console.error('Error request:', error.request);
                toast.error('Erreur de connexion au serveur. Veuillez vérifier votre connexion internet.');
            } else {
                toast.error(error.message || t('messages.userAddError') || 'Erreur lors de la création de l\'utilisateur');
            }
        }
    };
    const [errors, setErrors] = useState({});

    useEffect(() => {
        fetchUsers();
    }, []);

    const fetchUsers = async () => {
        setLoading(true);
        try {
            // Vérifier l'authentification avant de faire la requête
            if (!isAuthenticated()) {
                console.error('SuperAdminSpace - User not authenticated when fetching users');
                toast.error('Vous devez être connecté pour accéder à cette page');
                window.location.href = '/login';
                return;
            }

            console.log('SuperAdminSpace - Fetching users with auth headers');
            const result = await userService.getUsers();

            if (result.success) {
                console.log('SuperAdminSpace - Users fetched successfully:', result.data.length);

                // Vérifier une dernière fois qu'aucun super_admin n'est inclus dans la liste
                const filteredUsers = result.data.filter(user => user.role !== 'super_admin');
                console.log('SuperAdminSpace - Filtered users (removed super_admin):', filteredUsers.length);

                setUsers(filteredUsers);
            } else {
                console.error('SuperAdminSpace - Error fetching users:', result);
                if (result.status === 403) {
                    toast.error("Accès refusé: Seuls les super administrateurs peuvent gérer les utilisateurs");
                } else {
                    toast.error(t('errors.loadingUsers') || 'Erreur lors du chargement des utilisateurs');
                }
            }
        } catch (error) {
            console.error('SuperAdminSpace - Error fetching users:', error);

            if (error.response?.status === 401) {
                toast.error('Session expirée. Veuillez vous reconnecter.');
                window.location.href = '/login';
            } else if (error.response) {
                toast.error(`Erreur ${error.response.status}: ${error.response.data?.message || 'Erreur serveur'}`);
            } else if (error.request) {
                toast.error('Erreur de connexion au serveur. Veuillez vérifier votre connexion internet.');
            } else {
                toast.error(t('errors.loadingUsers') || 'Erreur lors du chargement des utilisateurs');
            }
        } finally {
            setLoading(false);
        }
    };

    const validateForm = () => {
        const newErrors = {};

        // Validation du nom
        if (!formData.name) {
            newErrors.name = 'Le nom est requis';
        } else if (formData.name.trim().length < 2) {
            newErrors.name = 'Le nom doit contenir au moins 2 caractères';
        } else if (!/^[a-zA-ZÀ-ÿ\s'-]+$/.test(formData.name.trim())) {
            newErrors.name = 'Le nom ne peut contenir que des lettres, espaces, apostrophes et tirets';
        } else if (formData.name.trim().length > 50) {
            newErrors.name = 'Le nom ne peut pas dépasser 50 caractères';
        }

        // Validation de l'email
        if (!formData.email) {
            newErrors.email = 'L\'email est requis';
        } else if (!/^[^@]+@[^@]+\.[^@]+/.test(formData.email)) {
            newErrors.email = 'Veuillez entrer un email valide';
        }

        // Validation du rôle
        const validRoles = ['admin', 'employee', 'client'];
        if (!validRoles.includes(formData.role)) {
            newErrors.role = 'Rôle invalide';
        }

        return newErrors;
    };

    const handleInputChange = (e) => {
        const { name, value } = e.target;

        // Permettre la saisie de tous les caractères
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));

        // Clear error when user starts typing
        if (errors[name]) {
            setErrors(prev => ({
                ...prev,
                [name]: ''
            }));
        }
    };

    const handleSearchChange = (e) => {
        setSearchTerm(e.target.value);
    };

    // Filtrer les utilisateurs par nom et s'assurer qu'aucun super_admin n'est affiché
    const filteredUsers = users.filter(user =>
        user.role !== 'super_admin' &&
        user.name.toLowerCase().includes(searchTerm.toLowerCase())
    );

    const openAddModal = () => {
        setFormData({
            name: '',
            email: '',
            password: '',
            role: 'client',
            permissions: {
                manage_users: false,
                activate_user_permissions: false,
                manage_teams: false,
                manage_team_tasks: false,
                manage_team_calendars: false,
                view_team_dashboards: false
            }
        });
        setErrors({});
        setShowAddModal(true);
    };

    const openEditModal = (user) => {
        setSelectedUser(user);
        setFormData({
            name: user.name,
            email: user.email,
            role: user.role,
            permissions: user.permissions || {
                manage_users: false,
                activate_user_permissions: false,
                manage_teams: false,
                manage_team_tasks: false,
                manage_team_calendars: false,
                view_team_dashboards: false
            }
        });
        setErrors({});
        setShowEditModal(true);
    };

    const handleEditUser = async (e) => {
        e.preventDefault();
        const validationErrors = validateForm();

        if (Object.keys(validationErrors).length > 0) {
            setErrors(validationErrors);
            return;
        }

        try {
            // S'assurer que les valeurs sont des chaînes de caractères
            const dataToUpdate = {
                name: String(formData.name).trim(),
                email: String(formData.email).trim(),
                role: String(formData.role).trim(),
                permissions: formData.permissions
            };

            console.log('Updating user with data:', dataToUpdate);

            const result = await userService.updateUser(selectedUser.id, dataToUpdate);

            if (result.success) {
                toast.success(t('messages.userUpdateSuccess'));
                setShowEditModal(false);
                fetchUsers();
            } else {
                toast.error(result.message || t('messages.userUpdateError'));
            }
        } catch (error) {
            console.error('Error updating user:', error);
            toast.error(error.message || t('messages.userUpdateError'));
        }
    };

    // Ouvrir le modal de confirmation de suppression
    const openDeleteModal = (user) => {
        // Vérifier si l'utilisateur à supprimer est un super_admin
        if (user.role === 'super_admin') {
            toast.error('Il est interdit de supprimer un super_admin', {
                icon: <AlertTriangle className="text-red-500" />
            });
            return;
        }

        setSelectedUser(user);
        setShowDeleteModal(true);
    };

    // Exécuter la suppression après confirmation
    const handleDeleteUser = async () => {
        if (!selectedUser) return;

        // Vérifier si l'utilisateur essaie de supprimer son propre compte
        const currentUser = JSON.parse(localStorage.getItem('user'));
        if (currentUser && currentUser.id === selectedUser.id) {
            toast.error('Vous ne pouvez pas supprimer votre propre compte');
            setShowDeleteModal(false);
            return;
        }

        // Vérifier à nouveau si l'utilisateur est un super_admin (double vérification)
        if (selectedUser.role === 'super_admin') {
            toast.error('Il est interdit de supprimer un super_admin', {
                icon: <AlertTriangle className="text-red-500" />
            });
            setShowDeleteModal(false);
            return;
        }

        try {
            // Fermer le modal de confirmation
            setShowDeleteModal(false);

            // Afficher un message de chargement pour indiquer que la suppression en cascade est en cours
            const loadingToast = toast.loading('Suppression en cours. Cela peut prendre un moment...', {
                autoClose: false,
                closeOnClick: false,
                draggable: false
            });

            // S'assurer que l'ID est une chaîne de caractères valide
            console.log('Deleting user with ID:', selectedUser.id);

            // Vérifier que l'ID existe et est valide
            if (!selectedUser.id) {
                toast.error('ID utilisateur invalide ou manquant');
                toast.dismiss(loadingToast);
                return;
            }

            // Convertir en chaîne et nettoyer
            const userId = String(selectedUser.id).trim();
            console.log('Formatted user ID for deletion:', userId);

            const result = await userService.deleteUser(userId);

            // Fermer le toast de chargement
            toast.dismiss(loadingToast);

            if (result.success) {
                setUsers(prevUsers => prevUsers.filter(user => user.id !== selectedUser.id));
                toast.success(result.message || 'Utilisateur et toutes ses données associées supprimés avec succès');
                // Rafraîchir la liste des utilisateurs pour s'assurer que les changements sont reflétés
                fetchUsers();
            } else {
                // Gérer les erreurs spécifiques selon le code de statut
                switch (result.status) {
                    case 400:
                        toast.error('Vous ne pouvez pas supprimer votre propre compte');
                        break;
                    case 403:
                        if (result.message && result.message.includes('super_admin')) {
                            toast.error('Il est interdit de supprimer un super_admin', {
                                icon: <AlertTriangle className="text-red-500" />
                            });
                        } else {
                            toast.error(result.message || 'Accès refusé pour cette opération');
                        }
                        break;
                    case 404:
                        toast.error('Utilisateur non trouvé');
                        break;
                    default:
                        toast.error(result.message || t('messages.userDeleteError'));
                }
            }
        } catch (error) {
            console.error('Error deleting user:', error);

            // Fermer le toast de chargement s'il existe encore
            toast.dismiss();

            // Afficher plus de détails sur l'erreur
            if (error.response) {
                console.error('Error response:', error.response);
                toast.error(`Erreur ${error.response.status}: ${error.response.data?.message || 'Erreur serveur'}`);
            } else if (error.request) {
                console.error('Error request:', error.request);
                toast.error('Erreur de connexion au serveur');
            } else {
                toast.error(error.message || t('messages.userDeleteError'));
            }
        } finally {
            // Réinitialiser l'utilisateur sélectionné
            setSelectedUser(null);
        }
    };

    const getRoleColor = (role) => {
        switch (role) {
            case 'admin':
                return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
            case 'employee':
                return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
            default:
                return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
        }
    };

    const getAvatarColor = (name) => {
        const colors = [
            'bg-violet-100',
            'bg-blue-100',
            'bg-emerald-100',
            'bg-amber-100',
            'bg-rose-100',
            'bg-fuchsia-100',
            'bg-indigo-100',
            'bg-cyan-100'
        ];
        const index = name.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
        return colors[index % colors.length];
    };

    const getInitialBgColor = (name) => {
        const colors = [
            'bg-purple-100', 'bg-blue-100', 'bg-green-100',
            'bg-yellow-100', 'bg-red-100', 'bg-indigo-100'
        ];
        const index = name.charCodeAt(0) % colors.length;
        return colors[index];
    };

    if (loading) {
        return (
            <div className="flex justify-center items-center min-h-screen">
                <div className="text-xl font-semibold">{t('common.loading')}</div>
            </div>
        );
    }

    const getInitials = (name) => {
        if (!name) return '';
        return name
            .split(' ')
            .map(word => word[0])
            .join('')
            .toUpperCase();
    };

    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
            <div className="max-w-7xl mx-auto px-8 pt-8">
                <div className="flex items-center justify-between mb-8">
                    <div className="flex items-center gap-3">
                        <div className="h-10 w-10 rounded-lg bg-[#6B4EFF] flex items-center justify-center text-white">
                            <Users className="w-5 h-5" />
                        </div>
                        <h1 className="text-2xl font-bold text-gray-900">Espace Super Admin</h1>
                    </div>

                </div>
            </div>



            <div className="max-w-7xl mx-auto px-8">
                <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8 border border-gray-200 dark:border-gray-700">
                    <div className="flex items-center justify-between">
                        <div>
                            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Utilisateurs</h2>
                            <div className="flex items-center gap-6">
                                <div>
                                    <div className="flex items-center gap-2">
                                        <span className="text-4xl font-bold text-purple-600">{users.length}</span>
                                        <span className="text-gray-500 dark:text-gray-400">documents</span>
                                    </div>
                                    <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">Total des utilisateurs</div>
                                </div>
                                <div>
                                    <div className="flex items-center gap-2">
                                        <span className="text-4xl font-bold text-blue-600">{users.filter(user => user.role === 'admin').length}</span>
                                        <span className="text-gray-500 dark:text-gray-400">admins</span>
                                    </div>
                                    <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">Administrateurs</div>
                                </div>
                                <div>
                                    <div className="flex items-center gap-2">
                                        <span className="text-4xl font-bold text-green-600">{users.filter(user => user.role === 'employee').length}</span>
                                        <span className="text-gray-500 dark:text-gray-400">employés</span>
                                    </div>
                                    <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">Employés actifs</div>
                                </div>
                                <div>
                                    <div className="flex items-center gap-2">
                                        <span className="text-4xl font-bold text-cyan-600">{users.filter(user => user.role === 'client').length}</span>
                                        <span className="text-gray-500 dark:text-gray-400">clients</span>
                                    </div>
                                    <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">Clients actifs</div>
                                </div>
                            </div>
                        </div>
                        <div className="text-gray-500 dark:text-gray-400 text-sm flex flex-col items-end">
                            <span className="font-medium">Collection</span>
                            <span className="mt-1">Dernière mise à jour: {new Date().toLocaleDateString('fr-FR')}</span>
                        </div>
                    </div>
                </div>

                <div className="mt-8">

                    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                        <div className="p-6">
                            <div className="flex justify-between items-center mb-6">
                                <div className="relative w-96">
                                    <input
                                        type="text"
                                        placeholder="Rechercher des utilisateurs..."
                                        value={searchTerm}
                                        onChange={handleSearchChange}
                                        className="w-full pl-10 pr-4 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#6B4EFF] focus:border-transparent"
                                    />
                                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                                </div>
                                <button
                                    onClick={openAddModal}
                                    className="flex items-center gap-3 px-5 py-2.5 bg-[#6B4EFF] text-white text-sm font-medium rounded-full hover:bg-[#5B3EEF] transition-all duration-200 shadow-[0_4px_12px_-2px_rgba(107,78,255,0.3)] hover:shadow-[0_8px_24px_-4px_rgba(107,78,255,0.5)]"
                                >
                                    <UserPlus className="w-4 h-4" />
                                    Ajouter un utilisateur
                                </button>
                            </div>

                            <div className="overflow-x-auto">
                                <table className="w-full">
                                    <thead>
                                        <tr className="border-y border-gray-200 bg-gray-50 text-left">
                                            <th className="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Utilisateur</th>
                                            <th className="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                                            <th className="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider w-32">Rôle</th>
                                            <th className="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider text-right w-20">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody className="divide-y divide-gray-200">
                                        {filteredUsers.length > 0 ? (
                                            filteredUsers.map(user => (
                                                <tr key={user.id} className="hover:bg-gray-50 transition-colors">
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="flex items-center">
                                                            <div className={`w-8 h-8 rounded-lg ${getAvatarColor(user.name)} flex items-center justify-center text-gray-700 text-sm font-medium`}>
                                                                {user.name.charAt(0).toUpperCase()}
                                                            </div>
                                                            <div className="ml-3">
                                                                <Link
                                                                    to={`/super-admin/users/${user.id}`}
                                                                    className="text-sm font-medium text-gray-900 hover:text-purple-600 transition-colors"
                                                                >
                                                                    {user.name}
                                                                </Link>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="text-sm text-gray-500">{user.email}</div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <span className={`inline-flex px-2 py-1 text-xs rounded-full font-medium ${getRoleColor(user.role)}`}>
                                                            {user.role === 'admin' ? 'Administrateur' : user.role === 'employee' ? 'Employé' : 'Client'}
                                                        </span>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-right">
                                                        <div className="flex items-center justify-end space-x-3">
                                                            <button
                                                                onClick={() => openEditModal(user)}
                                                                className="text-gray-400 hover:text-gray-600 transition-colors"
                                                            >
                                                                <Pencil className="w-4 h-4" />
                                                            </button>
                                                            <button
                                                                onClick={() => openDeleteModal(user)}
                                                                className={`${user.role === 'super_admin' ? 'text-gray-300 cursor-not-allowed' : 'text-gray-400 hover:text-red-600'} transition-colors`}
                                                                disabled={user.role === 'super_admin'}
                                                                title={user.role === 'super_admin' ? 'Il est interdit de supprimer un super_admin' : 'Supprimer cet utilisateur'}
                                                            >
                                                                <Trash2 className="w-4 h-4" />
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            ))
                                        ) : (
                                            <tr>
                                                <td colSpan="4" className="px-6 py-8 text-center text-sm text-gray-500">
                                                    Aucun utilisateur trouvé
                                                </td>
                                            </tr>
                                        )}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
            {showAddModal && (
                <div className="fixed inset-0 flex items-center justify-center p-4 z-50">
                    <div className="fixed inset-0 bg-black/30 backdrop-blur-sm" onClick={() => setShowAddModal(false)}></div>
                    <div className="bg-white rounded-2xl shadow-xl w-full max-w-md relative">
                        <div className="p-6">
                            <div className="flex justify-between items-center mb-6">
                                <h2 className="text-xl font-semibold text-gray-900">Ajouter un utilisateur</h2>
                                <button
                                    onClick={() => setShowAddModal(false)}
                                    className="text-gray-400 hover:text-gray-500 transition-colors"
                                >
                                    <X className="w-5 h-5" />
                                </button>
                            </div>
                            <form onSubmit={handleAddUser} className="space-y-5">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">Nom</label>
                                    <input
                                        type="text"
                                        name="name"
                                        value={formData.name}
                                        onChange={handleInputChange}
                                        className={`w-full px-3.5 py-2.5 bg-white border ${errors.name ? 'border-red-500' : 'border-gray-300'} rounded-lg shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent`}
                                        placeholder="Ex: Jean Dupont"
                                        maxLength="50"
                                    />
                                    {!errors.name && (
                                        <p className="mt-1 text-xs text-gray-500">
                                            Le nom doit contenir uniquement des lettres, espaces, apostrophes et tirets
                                        </p>
                                    )}
                                    {errors.name && <p className="mt-2 text-sm text-red-500">{errors.name}</p>}
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                                    <input
                                        type="email"
                                        name="email"
                                        value={formData.email}
                                        onChange={handleInputChange}
                                        className={`w-full px-3.5 py-2.5 bg-white border ${errors.email ? 'border-red-500' : 'border-gray-300'} rounded-lg shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent`}
                                        placeholder="Entrez l'email"
                                    />
                                    {errors.email && <p className="mt-2 text-sm text-red-500">{errors.email}</p>}
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">Rôle</label>
                                    <select
                                        name="role"
                                        value={formData.role}
                                        onChange={handleInputChange}
                                        className="w-full px-3.5 py-2.5 bg-white border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                                    >
                                        <option value="client">Client</option>
                                        <option value="admin">Administrateur</option>
                                        <option value="employee">Employé</option>
                                    </select>
                                </div>

                                <div className="flex justify-end gap-3 mt-6">
                                    <button
                                        type="button"
                                        onClick={() => setShowAddModal(false)}
                                        className="px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-800 transition-colors"
                                    >
                                        Annuler
                                    </button>
                                    <button
                                        type="submit"
                                        className="px-4 py-2 bg-[#6B4EFF] text-white text-sm font-medium rounded-lg hover:bg-[#5B3EEF] transition-colors shadow-[0_4px_12px_-2px_rgba(107,78,255,0.3)] hover:shadow-[0_8px_24px_-4px_rgba(107,78,255,0.5)]"
                                    >
                                        Ajouter
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            )}

            {showEditModal && (
                <div className="fixed inset-0 flex items-center justify-center p-4 z-50">
                    <div className="fixed inset-0 bg-black/30 backdrop-blur-sm" onClick={() => setShowEditModal(false)}></div>
                    <div className="bg-white rounded-2xl shadow-xl w-full max-w-md relative">
                        <div className="p-6">
                            <div className="flex justify-between items-center mb-6">
                                <h2 className="text-xl font-semibold text-gray-900">Modifier l'utilisateur</h2>
                                <button
                                    onClick={() => setShowEditModal(false)}
                                    className="text-gray-400 hover:text-gray-500 transition-colors"
                                >
                                    <X className="w-5 h-5" />
                                </button>
                            </div>
                            <form onSubmit={handleEditUser} className="space-y-5">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">Nom</label>
                                    <input
                                        type="text"
                                        name="name"
                                        value={formData.name}
                                        onChange={handleInputChange}
                                        className={`w-full px-3.5 py-2.5 bg-white border ${errors.name ? 'border-red-500' : 'border-gray-300'} rounded-lg shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent`}
                                        placeholder="Ex: Jean Dupont"
                                        maxLength="50"
                                    />
                                    {!errors.name && (
                                        <p className="mt-1 text-xs text-gray-500">
                                            Le nom doit contenir uniquement des lettres, espaces, apostrophes et tirets
                                        </p>
                                    )}
                                    {errors.name && <p className="mt-2 text-sm text-red-500">{errors.name}</p>}
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                                    <input
                                        type="email"
                                        name="email"
                                        value={formData.email}
                                        onChange={handleInputChange}
                                        className={`w-full px-3.5 py-2.5 bg-white border ${errors.email ? 'border-red-500' : 'border-gray-300'} rounded-lg shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent`}
                                        placeholder="Entrez l'email"
                                    />
                                    {errors.email && <p className="mt-2 text-sm text-red-500">{errors.email}</p>}
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">Rôle</label>
                                    <select
                                        name="role"
                                        value={formData.role}
                                        onChange={handleInputChange}
                                        className="w-full px-3.5 py-2.5 bg-white border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                                    >
                                        <option value="client">Client</option>
                                        <option value="admin">Administrateur</option>
                                        <option value="employee">Employé</option>
                                    </select>
                                </div>

                                <div className="flex justify-end gap-3 mt-6">
                                    <button
                                        type="button"
                                        onClick={() => setShowEditModal(false)}
                                        className="px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-800 transition-colors"
                                    >
                                        Annuler
                                    </button>
                                    <button
                                        type="submit"
                                        className="px-4 py-2 bg-[#6B4EFF] text-white text-sm font-medium rounded-lg hover:bg-[#5B3EEF] transition-colors shadow-[0_4px_12px_-2px_rgba(107,78,255,0.3)] hover:shadow-[0_8px_24px_-4px_rgba(107,78,255,0.5)]"
                                    >
                                        Modifier
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            )}

            {/* Modal de confirmation de suppression */}
            <DeleteUserConfirmModal
                isOpen={showDeleteModal}
                onClose={() => setShowDeleteModal(false)}
                onConfirm={handleDeleteUser}
                userName={selectedUser?.name || ''}
                userRole={selectedUser?.role || ''}
            />
        </div>
    );
};

export default SuperAdminSpace;