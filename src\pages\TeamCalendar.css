/* Styles généraux du calendrier - Style Notion */
.rbc-calendar {
  font-family: 'Inter', sans-serif;
  border-radius: 8px;
  box-shadow: none;
  background-color: #ffffff;
}

.rbc-toolbar {
  padding: 10px;
  margin-bottom: 15px;
  color: #37352f;
}

.rbc-toolbar button {
  border-radius: 4px;
  padding: 6px 12px;
  font-weight: 500;
  transition: all 0.2s ease;
  border: 1px solid #e5e5e5;
  color: #37352f;
}

.rbc-toolbar button.rbc-active {
  background-color: #f1f1f0;
  color: #37352f;
  box-shadow: none;
  border-color: #d3d3d3;
  font-weight: 600;
}

.rbc-toolbar button:hover {
  background-color: #f7f7f7;
  color: #37352f;
}

.rbc-header {
  padding: 10px 0;
  font-weight: 500;
  text-transform: uppercase;
  font-size: 0.75rem;
  color: #6b7280;
  border-bottom: 1px solid #f1f1f0;
}

.rbc-month-view {
  border-radius: 8px;
  border: 1px solid #f1f1f0;
  overflow: hidden;
}

.rbc-day-bg {
  transition: background-color 0.2s ease;
}

.rbc-day-bg:hover {
  background-color: #f9fafb;
}

.rbc-off-range-bg {
  background-color: #fafafa;
}

.rbc-today {
  background-color: #f7f6f3;
}

.rbc-date-cell {
  padding: 5px 8px;
  text-align: left;
  font-size: 0.85rem;
  color: #37352f;
}

.rbc-month-row+.rbc-month-row {
  border-top: 1px solid #f1f1f0;
}

.rbc-day-bg+.rbc-day-bg {
  border-left: 1px solid #f1f1f0;
}

/* Styles pour les événements dans les cellules */
.rbc-event {
  border: none !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15) !important;
  padding: 3px 6px !important;
  border-radius: 4px !important;
  font-size: 0.85rem !important;
  font-weight: 600 !important;
  margin: 2px 0 !important;
  cursor: pointer !important;
  transition: transform 0.1s ease, box-shadow 0.1s ease !important;
  opacity: 1 !important;
  color: #000000 !important;
}

.rbc-event:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15) !important;
}

/* Règle pour forcer l'opacité et la couleur de fond */
.rbc-event {
  opacity: 1 !important;
  background-color: inherit !important;
  color: #000000 !important;
}

/* Règle pour s'assurer que les couleurs sont visibles */
.rbc-event[data-color] {
  opacity: 1 !important;
}

/* Règle pour forcer les styles inline */
.rbc-event[style] {
  opacity: 1 !important;
}

/* Style pour les événements archivés */
.event-archived,
.rbc-event.event-archived,
.rbc-event[data-status="archived"] {
  text-decoration: line-through !important;
  font-style: italic !important;
  opacity: 0.7 !important;
}

.rbc-event-label {
  font-size: 0.7rem !important;
  font-weight: 500 !important;
}

.rbc-event-content {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* Styles pour les événements en attente */
.event-pending {
  background-color: #FFF4DE !important;
  color: #B76E00 !important;
  border-left: 3px solid #FFB74D !important;
}

/* Styles pour les événements terminés */
.event-completed {
  background-color: #E6F7ED !important;
  color: #1B5E20 !important;
  border-left: 3px solid #66BB6A !important;
}

/* Styles pour les événements archivés */
.event-archived {
  background-color: #F5F5F5 !important;
  color: #424242 !important;
  border-left: 3px solid #9E9E9E !important;
  text-decoration: line-through !important;
}

/* Style pour la liste des événements */
.event-list-container {
  margin-top: 2rem;
}

.event-card {
  transition: all 0.2s ease;
}

.event-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Badges de statut */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-badge-pending {
  background-color: #FFF4DE;
  color: #B76E00;
}

.status-badge-completed {
  background-color: #E6F7ED;
  color: #1B5E20;
}

.status-badge-archived {
  background-color: #F5F5F5;
  color: #424242;
  text-decoration: line-through;
}

/* Styles pour les modales */
.rbc-overlay {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #e5e7eb;
}

/* Styles pour l'affichage responsive */
@media (max-width: 768px) {
  .rbc-toolbar {
    flex-direction: column;
    align-items: flex-start;
  }

  .rbc-toolbar-label {
    margin: 10px 0;
  }

  .rbc-btn-group {
    margin-bottom: 10px;
  }
}

/* Style spécifique pour le calendrier façon Notion */
.notion-calendar-style {
  --notion-bg-color: #ffffff;
  --notion-text-color: #37352f;
  --notion-border-color: #f1f1f0;
  --notion-hover-color: #f7f6f3;
  --notion-active-color: #f1f1f0;

  background-color: var(--notion-bg-color);
  color: var(--notion-text-color);
}

/* Masquer la barre d'outils du calendrier pour éviter la redondance */
.hide-toolbar .rbc-toolbar {
  display: none;
}

.notion-calendar-style .rbc-month-view {
  border-color: var(--notion-border-color);
  border-radius: 8px;
  overflow: hidden;
}

.notion-calendar-style .rbc-header {
  font-weight: 500;
  color: #6b7280;
  text-transform: capitalize;
  padding: 12px 0;
  border-bottom: 1px solid var(--notion-border-color);
}

.notion-calendar-style .rbc-date-cell {
  font-size: 14px;
  padding: 8px;
  text-align: left;
}

.notion-calendar-style .rbc-day-bg {
  transition: background-color 0.2s;
}

.notion-calendar-style .rbc-day-bg:hover {
  background-color: var(--notion-hover-color);
}

.notion-calendar-style .rbc-today {
  background-color: var(--notion-hover-color);
}

.notion-calendar-style .rbc-month-row+.rbc-month-row,
.notion-calendar-style .rbc-day-bg+.rbc-day-bg {
  border-color: var(--notion-border-color);
}

.notion-calendar-style .rbc-toolbar button {
  color: var(--notion-text-color);
  border: 1px solid var(--notion-border-color);
  border-radius: 4px;
  background: transparent;
  padding: 6px 12px;
  font-weight: 500;
}

.notion-calendar-style .rbc-toolbar button.rbc-active {
  background-color: var(--notion-active-color);
  color: var(--notion-text-color);
  font-weight: 600;
}

.notion-calendar-style .rbc-toolbar button:hover {
  background-color: var(--notion-hover-color);
  color: var(--notion-text-color);
}

/* SOLUTION ULTRA-RADICALE: Styles pour forcer les couleurs des événements */
/* Ces styles ont une priorité maximale pour s'assurer qu'ils sont appliqués */

/* Forcer les couleurs des événements avec !important */
.rbc-event[style] {
  /* Permettre aux styles inline d'être appliqués sans modification */
  background-color: inherit !important;
  opacity: 1.0 !important;
  color: #000000 !important;
  font-weight: 600 !important;
}

/* Styles pour les événements avec des couleurs spécifiques */
[data-event-id] {
  /* Utiliser l'attribut data-event-id pour cibler les événements */
  background-color: attr(data-color) !important;
  opacity: 1.0 !important;
  color: #000000 !important;
  font-weight: 600 !important;
}

/* Forcer les styles pour les événements avec des classes spécifiques */
[class*="force-color-"] {
  /* Cibler les événements avec la classe force-color-* */
  background-color: inherit !important;
  opacity: 1.0 !important;
  color: #000000 !important;
  font-weight: 600 !important;
}

/* Styles spécifiques pour les couleurs de la palette */
.rbc-event[data-color="#CDB4DB"] {
  background-color: #CDB4DB !important;
  border-left: 4px solid #CDB4DB !important;
}

.rbc-event[data-color="#FFC8DD"] {
  background-color: #FFC8DD !important;
  border-left: 4px solid #FFC8DD !important;
}

.rbc-event[data-color="#FFAFCC"] {
  background-color: #FFAFCC !important;
  border-left: 4px solid #FFAFCC !important;
}

.rbc-event[data-color="#BDE0FE"] {
  background-color: #BDE0FE !important;
  border-left: 4px solid #BDE0FE !important;
}

.rbc-event[data-color="#A2D2FF"] {
  background-color: #A2D2FF !important;
  border-left: 4px solid #A2D2FF !important;
}

/* Styles pour les événements au survol */
.rbc-event:hover {
  filter: brightness(0.95) !important;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2) !important;
  transition: all 0.2s ease !important;
  z-index: 10 !important;
}

/* Styles pour les événements sélectionnés */
.rbc-selected {
  box-shadow: 0 0 0 3px #3b82f6 !important;
  filter: brightness(1.05) !important;
  z-index: 20 !important;
}

/* SOLUTION ULTRA-RADICALE: Styles supplémentaires pour s'assurer que les couleurs sont correctement appliquées */
/* Cibler spécifiquement les éléments du calendrier */
.rbc-calendar .rbc-event {
  opacity: 1.0 !important;
  color: #000000 !important;
  font-weight: 600 !important;
}

/* Cibler les événements dans les cellules du mois */
.rbc-month-view .rbc-event {
  opacity: 1.0 !important;
  color: #000000 !important;
  font-weight: 600 !important;
}

/* Cibler les événements dans les cellules de la semaine */
.rbc-time-view .rbc-event {
  opacity: 1.0 !important;
  color: #000000 !important;
  font-weight: 600 !important;
}

/* Cibler les événements dans les cellules du jour */
.rbc-day-slot .rbc-event {
  opacity: 1.0 !important;
  color: #000000 !important;
  font-weight: 600 !important;
}