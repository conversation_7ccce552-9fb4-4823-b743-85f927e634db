import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useEvent } from '@/contexts/EventContext';
import { useTeam } from '@/contexts/TeamContext';
import { toast } from 'react-toastify';
import { Calendar as CalendarIcon, Plus, Filter, Search, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Calendar, momentLocalizer } from 'react-big-calendar';
import moment from 'moment';
import 'moment/locale/fr';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import './TeamCalendar.css';
import { getEventStyle } from '@/utils/eventStyleHelper';
import {
    getModernEventStyle,
    FRENCH_CALENDAR_MESSAGES,
    FRENCH_CALENDAR_FORMATS,
    formatEventsForCalendar
} from '@/utils/modernCalendarHelper';
import colorService from '@/services/colorService';
import '@/styles/modern-calendar.css';

// Composants
import EventCard from '@/components/events/EventCard';
import EventForm from '@/components/events/EventForm';
import EventPermissionGate from '@/components/events/EventPermissionGate';
import EventDetailsModal from '@/components/events/EventDetailsModal';

// Services
import { permissionService } from '@/services/permissionService';

// Modals
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';

// Configuration de moment en français
moment.locale('fr');
const localizer = momentLocalizer(moment);

// Messages personnalisés en français pour le calendrier
const messages = {
    allDay: 'Journée',
    previous: 'Précédent',
    next: 'Suivant',
    today: 'Aujourd\'hui',
    month: 'Mois',
    week: 'Semaine',
    day: 'Jour',
    agenda: 'Agenda',
    date: 'Date',
    time: 'Heure',
    event: 'Événement',
    noEventsInRange: 'Aucun événement dans cette période',
    showMore: total => `+ ${total} événement(s) supplémentaire(s)`
};

const TeamCalendar = () => {
    const { user } = useAuth();
    const { teams } = useTeam();
    const {
        events,
        loading,
        error,
        fetchEvents,
        createEvent,
        updateEvent,
        updateEventStatus,
        deleteEvent,
        archiveEvent,
        unarchiveEvent,
        updateFilters,
        resetFilters
    } = useEvent();

    // États pour la gestion des modales
    const [showCreateModal, setShowCreateModal] = useState(false);
    const [showEditModal, setShowEditModal] = useState(false);
    const [showDetailsModal, setShowDetailsModal] = useState(false);
    const [selectedEvent, setSelectedEvent] = useState(null);

    // États pour les filtres
    const [searchQuery, setSearchQuery] = useState('');
    const [teamFilter, setTeamFilter] = useState('');
    const [statusFilter, setStatusFilter] = useState('');
    const [dateFilter, setDateFilter] = useState('');

    // État pour le chargement des actions
    const [actionLoading, setActionLoading] = useState(false);

    // Charger les événements au montage du composant
    useEffect(() => {
        const loadEvents = async () => {
            try {
                await fetchEvents();
                console.log('Événements chargés avec succès');
                console.log('TeamCalendar - Nombre d\'événements chargés:', events.length);
                console.log('TeamCalendar - Utilisateur actuel:', user);
                console.log('TeamCalendar - Équipes de l\'utilisateur:', user?.teams);
            } catch (err) {
                console.error('Erreur lors du chargement des événements:', err);
                toast.error('Erreur lors du chargement des événements');
            }
        };

        loadEvents();
    }, [fetchEvents]);

    // Initialiser les couleurs des événements au chargement
    useEffect(() => {
        if (!loading && events.length > 0) {
            console.log('TeamCalendar - Initialisation des couleurs pour', events.length, 'événements');

            // Initialiser la variable globale si nécessaire
            if (typeof window.eventColors === 'undefined') {
                window.eventColors = {};
                console.log('TeamCalendar - Variable globale eventColors initialisée');
            }

            let colorsUpdated = false;

            // Parcourir tous les événements et stocker leurs couleurs
            events.forEach(event => {
                if (event.id) {
                    if (event.color && typeof event.color === 'string' && event.color.startsWith('#')) {
                        // Si la couleur est différente de celle déjà stockée, la mettre à jour
                        if (window.eventColors[event.id] !== event.color) {
                            window.eventColors[event.id] = event.color;
                            colorsUpdated = true;
                            console.log(`TeamCalendar - Couleur initialisée pour l'événement ${event.id}:`, event.color);
                        }
                    } else if (!window.eventColors[event.id]) {
                        // Utiliser une couleur par défaut si aucune couleur n'est définie
                        window.eventColors[event.id] = '#BDE0FE';
                        colorsUpdated = true;
                        console.log(`TeamCalendar - Couleur par défaut utilisée pour l'événement ${event.id}:`, window.eventColors[event.id]);
                    }
                }
            });

            // Forcer un rafraîchissement du composant seulement si des couleurs ont été mises à jour
            if (colorsUpdated) {
                console.log('TeamCalendar - Couleurs mises à jour, rafraîchissement du composant');
                setRefreshKey(Date.now());
            }
        }
    }, [events, loading]);

    // Filtrer les événements
    const filteredEvents = events.filter(event => {
        // Filtre par recherche (titre ou description)
        const matchesSearch = searchQuery === '' ||
            (event.title && event.title.toLowerCase().includes(searchQuery.toLowerCase())) ||
            (event.description && event.description.toLowerCase().includes(searchQuery.toLowerCase()));

        // Filtre par équipe
        const matchesTeam = teamFilter === '' || event.team_id === teamFilter;

        // Filtre par statut
        const matchesStatus = statusFilter === '' || event.status === statusFilter;

        // Filtre par date
        const matchesDate = dateFilter === '' || (
            event.start_date && new Date(event.start_date).toISOString().split('T')[0] === dateFilter
        );

        return matchesSearch && matchesTeam && matchesStatus && matchesDate;
    });

    // Appliquer les filtres
    const applyFilters = () => {
        updateFilters({
            team_id: teamFilter,
            status: statusFilter,
            date: dateFilter
        });
    };

    // Réinitialiser les filtres
    const handleResetFilters = () => {
        setSearchQuery('');
        setTeamFilter('');
        setStatusFilter('');
        setDateFilter('');
        resetFilters();
    };

    // Gérer la création d'un événement
    const handleCreateEvent = async (eventData) => {
        setActionLoading(true);
        try {
            await createEvent(eventData);
            setShowCreateModal(false);
            toast.success('Événement créé avec succès');
        } catch (err) {
            console.error('Erreur lors de la création de l\'événement:', err);
            toast.error(err.message || 'Erreur lors de la création de l\'événement');
        } finally {
            setActionLoading(false);
        }
    };

    // Gérer la modification d'un événement
    const handleEditEvent = async (eventData) => {
        if (!selectedEvent) {
            toast.error("Aucun événement sélectionné");
            return;
        }

        console.log("TeamCalendar - Modification de l'événement:", selectedEvent.id);
        console.log("TeamCalendar - Données de modification:", eventData);
        console.log("TeamCalendar - Données de l'événement sélectionné:", selectedEvent);

        setActionLoading(true);
        try {
            // Vérifier et valider la couleur
            let colorValue = eventData.color;
            console.log('TeamCalendar - Couleur reçue dans handleEditEvent:', colorValue);

            if (colorValue === undefined || colorValue === null) {
                // Utiliser la couleur existante ou une couleur de la palette
                if (selectedEvent.color) {
                    colorValue = selectedEvent.color;
                } else {
                    // Utiliser une des couleurs de la palette
                    const defaultColors = [
                        '#CDB4DB', // Violet pastel
                        '#FFC8DD', // Rose clair pastel
                        '#FFAFCC', // Rose vif pastel
                        '#BDE0FE', // Bleu clair pastel
                        '#A2D2FF'  // Bleu pastel
                    ];
                    // Utiliser l'ID de l'événement pour choisir une couleur de manière déterministe
                    const colorIndex = selectedEvent.id ? Math.abs(selectedEvent.id.toString().split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)) % defaultColors.length : 0;
                    colorValue = defaultColors[colorIndex];
                }
                console.log('TeamCalendar - Couleur non définie, utilisation de la couleur existante ou de la palette:', colorValue);
            } else if (typeof colorValue !== 'string' || !colorValue.startsWith('#')) {
                console.error('TeamCalendar - Format de couleur invalide:', colorValue);
                // Utiliser la couleur existante ou une couleur de la palette
                if (selectedEvent.color && typeof selectedEvent.color === 'string' && selectedEvent.color.startsWith('#')) {
                    colorValue = selectedEvent.color;
                } else {
                    // Utiliser une des couleurs de la palette
                    const defaultColors = [
                        '#CDB4DB', // Violet pastel
                        '#FFC8DD', // Rose clair pastel
                        '#FFAFCC', // Rose vif pastel
                        '#BDE0FE', // Bleu clair pastel
                        '#A2D2FF'  // Bleu pastel
                    ];
                    // Utiliser l'ID de l'événement pour choisir une couleur de manière déterministe
                    const colorIndex = selectedEvent.id ? Math.abs(selectedEvent.id.toString().split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)) % defaultColors.length : 0;
                    colorValue = defaultColors[colorIndex];
                }
                console.log('TeamCalendar - Format invalide, utilisation de la couleur existante ou de la palette:', colorValue);
            }

            // Mettre à jour la couleur dans la variable globale immédiatement
            if (typeof window.eventColors === 'undefined') {
                window.eventColors = {};
                console.log('TeamCalendar - Variable globale eventColors initialisée');
            }

            // S'assurer que la couleur est valide et la mettre à jour dans la variable globale
            if (colorValue && typeof colorValue === 'string' && colorValue.startsWith('#')) {
                window.eventColors[selectedEvent.id] = colorValue;
                console.log(`TeamCalendar - Couleur mise à jour dans la variable globale pour l'événement ${selectedEvent.id}:`, colorValue);
            } else {
                console.warn(`TeamCalendar - Format de couleur invalide, utilisation de la couleur par défaut`);
                colorValue = '#BDE0FE'; // Bleu clair pastel par défaut
                window.eventColors[selectedEvent.id] = colorValue;
                console.log(`TeamCalendar - Couleur par défaut utilisée:`, colorValue);
            }

            console.log('TeamCalendar - Couleur avant traitement:', eventData.color);
            console.log('TeamCalendar - Couleur après validation:', colorValue);

            // S'assurer que toutes les données nécessaires sont présentes
            const completeEventData = {
                ...eventData,
                // Conserver l'ID de l'équipe si non fourni
                team_id: eventData.team_id || selectedEvent.team_id,
                // Pour member_id, utiliser la valeur fournie même si c'est une chaîne vide
                // Une chaîne vide signifie "tous les membres de l'équipe"
                member_id: eventData.member_id !== undefined ? eventData.member_id : selectedEvent.member_id,
                // Pour la couleur, utiliser la valeur validée
                color: colorValue,
                // Conserver le statut actuel
                status: selectedEvent.status
            };

            // Forcer la couleur à être une propriété explicite de l'objet
            Object.defineProperty(completeEventData, 'color', {
                value: colorValue,
                enumerable: true,
                writable: true,
                configurable: true
            });

            console.log("TeamCalendar - Données complètes pour la mise à jour:", completeEventData);
            console.log("TeamCalendar - Couleur envoyée:", completeEventData.color);
            console.log("TeamCalendar - Member ID envoyé:", completeEventData.member_id);

            // Mettre à jour l'événement
            const updatedEvent = await updateEvent(selectedEvent.id, completeEventData);

            if (!updatedEvent) {
                throw new Error("La mise à jour a échoué - aucune donnée retournée");
            }

            console.log("TeamCalendar - Événement mis à jour:", updatedEvent);

            // Fermer le modal et réinitialiser l'événement sélectionné
            setShowEditModal(false);
            setSelectedEvent(null);

            // Rafraîchir les événements pour s'assurer que tout est à jour
            await fetchEvents();

            // Forcer un rafraîchissement du composant pour appliquer les nouvelles couleurs
            // S'assurer que la variable globale est initialisée
            if (typeof window.eventColors === 'undefined') {
                window.eventColors = {};
            }

            // S'assurer que la couleur est valide et à jour dans la variable globale
            if (colorValue && typeof colorValue === 'string' && colorValue.startsWith('#')) {
                window.eventColors[selectedEvent.id] = colorValue;
                console.log(`TeamCalendar - Couleur finale mise à jour dans la variable globale pour l'événement ${selectedEvent.id}:`, colorValue);
            } else {
                console.warn(`TeamCalendar - Couleur finale invalide pour l'événement ${selectedEvent.id}:`, colorValue);
                // Utiliser une des couleurs de la palette
                const defaultColors = [
                    '#CDB4DB', // Violet pastel
                    '#FFC8DD', // Rose clair pastel
                    '#FFAFCC', // Rose vif pastel
                    '#BDE0FE', // Bleu clair pastel
                    '#A2D2FF'  // Bleu pastel
                ];
                // Utiliser l'ID de l'événement pour choisir une couleur de manière déterministe
                const colorIndex = selectedEvent.id ? Math.abs(selectedEvent.id.toString().split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)) % defaultColors.length : 0;
                colorValue = defaultColors[colorIndex];
                window.eventColors[selectedEvent.id] = colorValue;
                console.log(`TeamCalendar - Couleur de la palette utilisée pour l'événement ${selectedEvent.id}:`, colorValue);
            }

            // Mettre à jour directement les éléments DOM si possible
            try {
                // Sélectionner tous les éléments avec l'ID de l'événement
                const eventElements = document.querySelectorAll(`.event-id-${selectedEvent.id}`);
                if (eventElements.length > 0) {
                    console.log(`TeamCalendar - ${eventElements.length} éléments DOM trouvés pour l'événement ${selectedEvent.id}`);
                    eventElements.forEach(element => {
                        // Appliquer la couleur directement à l'élément
                        element.style.backgroundColor = colorValue;

                        // Ajouter une bordure pour rendre le changement plus visible
                        const hexToRgb = (hex) => {
                            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
                            return result ? {
                                r: parseInt(result[1], 16),
                                g: parseInt(result[2], 16),
                                b: parseInt(result[3], 16)
                            } : null;
                        };

                        const rgb = hexToRgb(colorValue);
                        if (rgb) {
                            element.style.borderLeft = `3px solid rgba(${Math.max(0, rgb.r - 40)}, ${Math.max(0, rgb.g - 40)}, ${Math.max(0, rgb.b - 40)}, 0.8)`;
                        }

                        // Si l'événement est archivé, s'assurer que le style est correctement appliqué
                        const isArchived = element.classList.contains('event-archived');
                        if (isArchived) {
                            element.style.textDecoration = 'line-through';
                            element.style.fontStyle = 'italic';
                            element.style.opacity = '0.7';
                        }
                    });
                } else {
                    console.log(`TeamCalendar - Aucun élément DOM trouvé pour l'événement ${selectedEvent.id}, forçage du rafraîchissement`);
                }
            } catch (domError) {
                console.error('TeamCalendar - Erreur lors de la mise à jour directe du DOM:', domError);
            }

            // Forcer un rafraîchissement du composant
            const refreshKey = Date.now();
            setRefreshKey(refreshKey);
            console.log(`TeamCalendar - Rafraîchissement du composant avec la clé: ${refreshKey}`);

            // Mettre à jour la couleur de l'événement
            console.log(`TeamCalendar - Mise à jour de la couleur depuis handleEditEvent`);

            // Utiliser notre fonction pour mettre à jour la couleur
            handleColorUpdate(selectedEvent.id, colorValue);

            // Émettre un événement pour informer les autres composants
            if (window.dispatchEvent) {
                window.dispatchEvent(new CustomEvent('eventColorUpdated', {
                    detail: {
                        eventId: selectedEvent.id,
                        color: colorValue,
                        timestamp: Date.now(),
                        source: 'TeamCalendar'
                    }
                }));
                console.log(`TeamCalendar - Événement eventColorUpdated émis avec la couleur: ${colorValue}`);
            }

            // Forcer un rafraîchissement complet du calendrier
            setTimeout(() => {
                // Rafraîchir les événements depuis le backend
                fetchEvents().then(() => {
                    console.log('TeamCalendar - Événements rafraîchis depuis le backend');

                    // Puis forcer un rafraîchissement du composant
                    setTimeout(() => {
                        setRefreshKey(Date.now());
                        console.log(`TeamCalendar - Composant rafraîchi avec la clé: ${Date.now()}`);

                        // Émettre un événement pour forcer le rafraîchissement
                        window.dispatchEvent(new Event('refreshCalendar'));
                        console.log(`TeamCalendar - Événement refreshCalendar émis`);
                    }, 100);
                });
            }, 200);

            toast.success('Événement mis à jour avec succès');
        } catch (err) {
            console.error(`Erreur lors de la mise à jour de l'événement ${selectedEvent.id}:`, err);
            toast.error(err.message || `Erreur lors de la mise à jour de l'événement`);
        } finally {
            setActionLoading(false);
        }
    };

    // Ajouter un gestionnaire d'événements pour fermer les menus déroulants lorsqu'on clique ailleurs
    useEffect(() => {
        const handleClickOutside = (event) => {
            const dropdowns = document.querySelectorAll('[id^="dropdown-menu-"]');
            dropdowns.forEach(dropdown => {
                if (!dropdown.classList.contains('hidden') && !dropdown.contains(event.target) &&
                    !event.target.closest('button[aria-label="Menu d\'actions"]')) {
                    dropdown.classList.add('hidden');
                }
            });
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    // Ajouter un écouteur pour forcer le rafraîchissement du calendrier
    useEffect(() => {
        const handleRefreshCalendar = () => {
            console.log('TeamCalendar - Événement refreshCalendar reçu, rafraîchissement du composant');
            setRefreshKey(Date.now());

            // Rafraîchir les événements depuis le backend
            fetchEvents().then(() => {
                console.log('TeamCalendar - Événements rafraîchis depuis le backend');
            }).catch(error => {
                console.error('TeamCalendar - Erreur lors du rafraîchissement des événements:', error);
            });
        };

        window.addEventListener('refreshCalendar', handleRefreshCalendar);
        return () => {
            window.removeEventListener('refreshCalendar', handleRefreshCalendar);
        };
    }, [fetchEvents]);

    // Fonction pour mettre à jour la couleur d'un événement
    const handleColorUpdate = useCallback((eventId, colorValue) => {
        console.log(`TeamCalendar - handleColorUpdate appelé pour l'événement ${eventId} avec la couleur ${colorValue}`);

        // Vérifier que la couleur est valide
        if (!colorValue || typeof colorValue !== 'string' || !colorValue.startsWith('#')) {
            console.warn(`TeamCalendar - Format de couleur invalide dans handleColorUpdate:`, colorValue);
            // Utiliser une des couleurs de la palette
            const defaultColors = [
                '#CDB4DB', // Violet pastel
                '#FFC8DD', // Rose clair pastel
                '#FFAFCC', // Rose vif pastel
                '#BDE0FE', // Bleu clair pastel
                '#A2D2FF'  // Bleu pastel
            ];
            // Utiliser un index aléatoire pour choisir une couleur
            const colorIndex = Math.floor(Math.random() * defaultColors.length);
            colorValue = defaultColors[colorIndex];
        }

        // Mettre à jour la variable globale
        if (typeof window.eventColors === 'undefined') {
            window.eventColors = {};
        }
        window.eventColors[eventId] = colorValue;

        // Mettre à jour directement les éléments DOM
        try {
            const eventElements = document.querySelectorAll(`.event-id-${eventId}, .rbc-event[data-event-id="${eventId}"]`);
            if (eventElements.length > 0) {
                console.log(`TeamCalendar - Application directe de la couleur sur ${eventElements.length} éléments DOM`);
                eventElements.forEach(element => {
                    element.style.backgroundColor = colorValue;
                    element.style.borderLeft = `4px solid ${colorValue}`;
                    element.style.opacity = '1';
                    element.setAttribute('data-color', colorValue);
                });
            }
        } catch (error) {
            console.error('TeamCalendar - Erreur lors de la mise à jour directe du DOM:', error);
        }

        // Forcer un rafraîchissement du calendrier
        setRefreshKey(Date.now());

        // Émettre un événement pour informer les autres composants
        if (window.dispatchEvent) {
            window.dispatchEvent(new CustomEvent('eventColorUpdated', {
                detail: {
                    eventId: eventId,
                    color: colorValue,
                    timestamp: Date.now(),
                    source: 'TeamCalendar.handleColorUpdate'
                }
            }));
            console.log(`TeamCalendar - Événement eventColorUpdated émis avec la couleur: ${colorValue}`);
        }
    }, []);

    // Fonction utilitaire pour convertir une couleur hexadécimale en RGB
    const hexToRgb = (hex) => {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    };

    // Ajouter un écouteur d'événements pour les mises à jour de couleurs
    useEffect(() => {
        const handleColorUpdateEvent = (e) => {
            try {
                console.log('TeamCalendar - Événement de mise à jour de couleur reçu:', e.detail);

                // Vérifier que les données sont valides
                if (!e.detail || !e.detail.eventId || !e.detail.color) {
                    console.warn('TeamCalendar - Données d\'événement eventColorUpdated invalides:', e.detail);
                    return;
                }

                const { eventId, color, timestamp, source } = e.detail;

                // Vérifier que la couleur est valide
                if (!color || typeof color !== 'string' || !color.startsWith('#')) {
                    console.warn('TeamCalendar - Format de couleur invalide dans eventColorUpdated:', color);
                    return;
                }

                // Mettre à jour la variable globale
                if (typeof window.eventColors === 'undefined') {
                    window.eventColors = {};
                    console.log('TeamCalendar - Variable globale eventColors initialisée');
                }

                // Mettre à jour la couleur dans la variable globale
                window.eventColors[eventId] = color;
                console.log(`TeamCalendar - Couleur mise à jour dans la variable globale pour l'événement ${eventId}:`, color);

                // Mettre à jour directement les éléments DOM si possible
                try {
                    // Sélectionner tous les éléments avec l'ID de l'événement
                    const eventElements = document.querySelectorAll(`.event-id-${eventId}, .rbc-event[data-event-id="${eventId}"]`);
                    if (eventElements.length > 0) {
                        console.log(`TeamCalendar - ${eventElements.length} éléments DOM trouvés pour l'événement ${eventId}`);
                        eventElements.forEach(element => {
                            // Appliquer la couleur directement à l'élément
                            element.style.backgroundColor = color;
                            element.style.borderLeft = `4px solid ${color}`;
                            element.style.opacity = '1';
                            element.setAttribute('data-color', color);

                            // Si l'événement est archivé, s'assurer que le style est correctement appliqué
                            const isArchived = element.classList.contains('event-archived');
                            if (isArchived) {
                                element.style.textDecoration = 'line-through';
                                element.style.fontStyle = 'italic';
                                element.style.opacity = '0.7';
                            }
                        });
                    } else {
                        console.log(`TeamCalendar - Aucun élément DOM trouvé pour l'événement ${eventId}, forçage du rafraîchissement`);
                    }

                    // Forcer un rafraîchissement du calendrier
                    setRefreshKey(Date.now());

                    // Rafraîchir les événements depuis le backend après un court délai
                    setTimeout(() => {
                        fetchEvents().then(() => {
                            console.log('TeamCalendar - Événements rafraîchis depuis le backend');
                            // Puis forcer un rafraîchissement du composant
                            setTimeout(() => {
                                setRefreshKey(Date.now());
                            }, 100);
                        }).catch(error => {
                            console.error('TeamCalendar - Erreur lors du rafraîchissement des événements:', error);
                            setRefreshKey(Date.now());
                        });
                    }, 200);

                } catch (domError) {
                    console.error('TeamCalendar - Erreur lors de la mise à jour directe du DOM:', domError);
                    // En cas d'erreur, forcer un rafraîchissement complet
                    fetchEvents().then(() => setRefreshKey(Date.now()));
                }
            } catch (error) {
                console.error('TeamCalendar - Erreur lors du traitement de l\'événement eventColorUpdated:', error);
                // En cas d'erreur, forcer un rafraîchissement complet
                fetchEvents().then(() => setRefreshKey(Date.now()));
            }
        };

        // Ajouter l'écouteur d'événements
        try {
            console.log('TeamCalendar - Ajout de l\'écouteur d\'événements eventColorUpdated');
            window.addEventListener('eventColorUpdated', handleColorUpdateEvent);
        } catch (error) {
            console.error('Erreur lors de l\'ajout de l\'écouteur d\'événements:', error);
        }

        return () => {
            try {
                window.removeEventListener('eventColorUpdated', handleColorUpdateEvent);
            } catch (error) {
                console.error('Erreur lors de la suppression de l\'écouteur d\'événements:', error);
            }
        };
    }, [fetchEvents]);

    // Gérer la suppression d'un événement
    const handleDeleteEvent = async (eventId) => {
        if (!window.confirm('Êtes-vous sûr de vouloir supprimer cet événement ?')) return;

        setActionLoading(true);
        try {
            await deleteEvent(eventId);
            toast.success('Événement supprimé avec succès');
        } catch (err) {
            console.error(`Erreur lors de la suppression de l'événement ${eventId}:`, err);
            toast.error(err.message || `Erreur lors de la suppression de l'événement`);
        } finally {
            setActionLoading(false);
        }
    };

    // Gérer l'archivage d'un événement
    const handleArchiveEvent = async (eventId) => {
        if (!window.confirm('Êtes-vous sûr de vouloir archiver cet événement ?')) return;

        setActionLoading(true);
        try {
            await archiveEvent(eventId);
            // Rafraîchir la liste des événements après l'archivage
            await fetchEvents();
            toast.success('Événement archivé avec succès');
        } catch (err) {
            console.error(`Erreur lors de l'archivage de l'événement ${eventId}:`, err);
            toast.error(err.message || `Erreur lors de l'archivage de l'événement`);
        } finally {
            setActionLoading(false);
        }
    };

    // Gérer le désarchivage d'un événement
    const handleUnarchiveEvent = async (eventId) => {
        if (!window.confirm('Êtes-vous sûr de vouloir désarchiver cet événement ?')) return;

        setActionLoading(true);
        try {
            await unarchiveEvent(eventId);
            // Rafraîchir la liste des événements après le désarchivage
            await fetchEvents();
            toast.success('Événement désarchivé avec succès');
        } catch (err) {
            console.error(`Erreur lors du désarchivage de l'événement ${eventId}:`, err);
            toast.error(err.message || `Erreur lors du désarchivage de l'événement`);
        } finally {
            setActionLoading(false);
        }
    };

    // Gérer la mise à jour du statut d'un événement
    const handleUpdateStatus = async (eventId, status) => {
        setActionLoading(true);
        try {
            await updateEventStatus(eventId, status);
            // Rafraîchir la liste des événements après la mise à jour du statut
            await fetchEvents();
            toast.success(status === 'archived' ? 'Événement archivé avec succès' : 'Statut mis à jour avec succès');
        } catch (err) {
            console.error(`Erreur lors de la mise à jour du statut de l'événement ${eventId}:`, err);
            toast.error(err.message || `Erreur lors de la mise à jour du statut`);
        } finally {
            setActionLoading(false);
        }
    };

    // Ouvrir le modal d'édition
    const handleEditClick = (event) => {
        setSelectedEvent(event);
        setShowEditModal(true);
    };

    // Formater les événements pour le calendrier
    const calendarEvents = filteredEvents.map(event => {
        // Vérifier si les données de date et heure sont présentes
        if (!event.start_date || !event.start_time || !event.end_date || !event.end_time) {
            console.error('Event missing date/time data:', event);
            // Fournir des valeurs par défaut pour éviter les erreurs
            return {
                id: event.id,
                title: event.title || 'Événement sans titre',
                start: new Date(),
                end: new Date(new Date().getTime() + 60 * 60 * 1000), // +1 heure
                description: event.description || '',
                note: event.note || '',
                status: event.status || 'pending',
                team_id: event.team_id,
                team_name: event.team_name || 'Équipe inconnue',
                member_id: event.member_id,
                member_name: event.member_name || '',
                color: event.color || '#6B4EFF',
                allDay: false,
                resource: event
            };
        }

        // Combiner date et heure pour créer des objets Date
        try {
            const startDateTime = moment(`${event.start_date} ${event.start_time}`, 'YYYY-MM-DD HH:mm').toDate();
            const endDateTime = moment(`${event.end_date} ${event.end_time}`, 'YYYY-MM-DD HH:mm').toDate();

            return {
                id: event.id,
                title: event.title,
                start: startDateTime,
                end: endDateTime,
                description: event.description,
                note: event.note,
                status: event.status,
                team_id: event.team_id,
                team_name: event.team_name,
                member_id: event.member_id,
                member_name: event.member_name,
                color: event.color, // Ajouter la couleur de l'événement
                allDay: false,
                resource: event // Stocker l'événement complet pour l'affichage des détails
            };
        } catch (error) {
            console.error('Error formatting event date/time:', error, event);
            // Fournir un événement avec des valeurs par défaut en cas d'erreur
            return {
                id: event.id,
                title: event.title || 'Événement sans titre',
                start: new Date(),
                end: new Date(new Date().getTime() + 60 * 60 * 1000), // +1 heure
                description: event.description || '',
                note: event.note || '',
                status: event.status || 'pending',
                team_id: event.team_id,
                team_name: event.team_name || 'Équipe inconnue',
                member_id: event.member_id,
                member_name: event.member_name || '',
                color: event.color || '#6B4EFF',
                allDay: false,
                resource: event
            };
        }
    });

    // Personnalisation des événements dans le calendrier avec les couleurs simples
    const eventStyleGetter = (event) => {
        // Récupérer l'ID de l'événement
        const eventId = event.id || (event.resource && event.resource.id);

        // Déterminer si l'événement est archivé
        const isArchived = event.status === 'archived' || (event.resource && event.resource.status === 'archived');

        // Récupérer la couleur de l'événement
        const eventColor = event.color || (event.resource && event.resource.color) || '#3B82F6';

        // Style simple et moderne
        const style = {
            backgroundColor: eventColor,
            border: `2px solid ${eventColor}`,
            borderRadius: '6px',
            color: '#ffffff',
            fontWeight: '600',
            fontSize: '0.75rem',
            cursor: 'pointer',
            padding: '2px 6px',
            textDecoration: isArchived ? 'line-through' : 'none',
            opacity: isArchived ? 0.7 : 1,
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
        };

        // Ajouter les attributs pour le CSS
        const attributes = {
            'data-event-id': eventId,
            'data-color': eventColor,
            'data-status': isArchived ? 'archived' : (event.status || (event.resource && event.resource.status) || 'pending')
        };

        if (isArchived) {
            attributes['data-archived'] = 'true';
        }

        return {
            style: style,
            className: `team-event ${isArchived ? 'event-archived' : ''}`.trim(),
            attributes
        };
    };

    // Format personnalisé pour les événements
    const formats = {
        eventTimeRangeFormat: ({ start, end }) => {
            return `${moment(start).format('HH:mm')} - ${moment(end).format('HH:mm')}`;
        }
    };

    // Affichage des détails de l'événement au clic
    const handleSelectEvent = (event) => {
        const eventData = event.resource;
        setSelectedEvent(eventData);

        // Vérifier les permissions de l'utilisateur pour cet événement
        const permissions = permissionService.checkEventPermissions(user, eventData);

        // Si l'utilisateur est un employé et ne peut que voir les détails (pas éditer)
        if (user.role === 'employee' && !permissions.canEdit) {
            // Ouvrir le modal de détails seulement
            setShowDetailsModal(true);
        } else {
            // Ouvrir le modal d'édition pour les admins ou si l'employé peut éditer
            setShowEditModal(true);
        }
    };

    // Vue du calendrier (mois, semaine, jour)
    const [calendarView, setCalendarView] = useState('month');
    // État pour la date actuelle du calendrier
    const [currentDate, setCurrentDate] = useState(new Date());
    // États pour les vues Agenda et Archives
    const [showAgendaView, setShowAgendaView] = useState(false);
    const [showArchiveView, setShowArchiveView] = useState(false);

    // État pour forcer le rafraîchissement du composant
    const [refreshKey, setRefreshKey] = useState(Date.now());

    // Fonctions pour naviguer entre les mois
    const navigateToPrevious = () => {
        const newDate = new Date(currentDate);
        if (calendarView === 'month') {
            newDate.setMonth(newDate.getMonth() - 1);
        } else if (calendarView === 'week') {
            newDate.setDate(newDate.getDate() - 7);
        } else if (calendarView === 'day') {
            newDate.setDate(newDate.getDate() - 1);
        }
        setCurrentDate(newDate);
    };

    const navigateToNext = () => {
        const newDate = new Date(currentDate);
        if (calendarView === 'month') {
            newDate.setMonth(newDate.getMonth() + 1);
        } else if (calendarView === 'week') {
            newDate.setDate(newDate.getDate() + 7);
        } else if (calendarView === 'day') {
            newDate.setDate(newDate.getDate() + 1);
        }
        setCurrentDate(newDate);
    };

    const navigateToToday = () => {
        setCurrentDate(new Date());
    };

    return (
        <div className="h-screen flex flex-col">
            {/* Barre supérieure avec titre et bouton de création */}
            <div className="flex justify-between items-center p-4 bg-white border-b border-gray-200">
                <div className="flex items-center gap-2">
                    <CalendarIcon className="h-6 w-6 text-indigo-600" />
                    <h1 className="text-xl font-bold text-gray-900">{moment(currentDate).format('MMMM YYYY')}</h1>
                </div>

                <div className="flex items-center gap-4">
                    <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                        <Input
                            type="text"
                            placeholder="Rechercher un événement..."
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            className="pl-10 w-64"
                        />
                    </div>

                    {user.role === 'admin' && (
                        <Button
                            onClick={() => setShowCreateModal(true)}
                            className="bg-indigo-600 hover:bg-indigo-700 text-white flex items-center gap-2"
                        >
                            <Plus className="h-5 w-5" />
                            Créer un événement
                        </Button>
                    )}
                </div>
            </div>

            {/* Sélecteur de vue du calendrier - Design amélioré */}
            <div className="flex items-center justify-between p-4 bg-white border-b border-gray-200">
                <div className="flex items-center gap-3">
                    <Button
                        variant="outline"
                        className="text-gray-700 hover:bg-indigo-50 hover:text-indigo-700 font-medium"
                        onClick={navigateToToday}
                    >
                        Aujourd'hui
                    </Button>
                    <div className="flex items-center gap-1 bg-gray-50 rounded-md p-1">
                        <Button variant="ghost" size="icon" className="h-8 w-8 rounded-full hover:bg-indigo-100" onClick={navigateToPrevious}>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                                <path d="m15 18-6-6 6-6" />
                            </svg>
                        </Button>
                        <Button variant="ghost" size="icon" className="h-8 w-8 rounded-full hover:bg-indigo-100" onClick={navigateToNext}>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                                <path d="m9 18 6-6-6-6" />
                            </svg>
                        </Button>
                    </div>
                    <h2 className="text-lg font-semibold text-gray-800">{moment(currentDate).format('MMMM YYYY')}</h2>
                </div>

                <div className="flex items-center">
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-1">
                        <div className="inline-flex rounded-md">
                            <button
                                type="button"
                                onClick={() => {
                                    setCalendarView('month');
                                    setShowAgendaView(false);
                                    setShowArchiveView(false);
                                }}
                                className={`px-4 py-2 text-sm font-medium transition-colors ${calendarView === 'month' && !showAgendaView && !showArchiveView
                                    ? 'bg-indigo-600 text-white'
                                    : 'bg-white text-gray-700 hover:bg-gray-50'
                                    } rounded-l-md focus:outline-none`}
                            >
                                Mois
                            </button>
                            <button
                                type="button"
                                onClick={() => {
                                    setCalendarView('week');
                                    setShowAgendaView(false);
                                    setShowArchiveView(false);
                                }}
                                className={`px-4 py-2 text-sm font-medium transition-colors ${calendarView === 'week' && !showAgendaView && !showArchiveView
                                    ? 'bg-indigo-600 text-white'
                                    : 'bg-white text-gray-700 hover:bg-gray-50'
                                    } focus:outline-none`}
                            >
                                Semaine
                            </button>
                            <button
                                type="button"
                                onClick={() => {
                                    setCalendarView('day');
                                    setShowAgendaView(false);
                                    setShowArchiveView(false);
                                }}
                                className={`px-4 py-2 text-sm font-medium transition-colors ${calendarView === 'day' && !showAgendaView && !showArchiveView
                                    ? 'bg-indigo-600 text-white'
                                    : 'bg-white text-gray-700 hover:bg-gray-50'
                                    } focus:outline-none`}
                            >
                                Jour
                            </button>
                            <button
                                type="button"
                                onClick={() => {
                                    setShowAgendaView(true);
                                    setShowArchiveView(false);
                                }}
                                className={`px-4 py-2 text-sm font-medium transition-colors ${showAgendaView && !showArchiveView
                                    ? 'bg-indigo-600 text-white'
                                    : 'bg-white text-gray-700 hover:bg-gray-50'
                                    } focus:outline-none`}
                            >
                                Agenda
                            </button>
                            <button
                                type="button"
                                onClick={() => {
                                    setShowArchiveView(true);
                                    setShowAgendaView(false);
                                }}
                                className={`px-4 py-2 text-sm font-medium transition-colors ${showArchiveView
                                    ? 'bg-indigo-600 text-white'
                                    : 'bg-white text-gray-700 hover:bg-gray-50'
                                    } rounded-r-md focus:outline-none flex items-center gap-1`}
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1">
                                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                                    <line x1="16" y1="8" x2="8" y2="8"></line>
                                    <line x1="16" y1="12" x2="8" y2="12"></line>
                                    <line x1="16" y1="16" x2="8" y2="16"></line>
                                </svg>
                                Archives
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            {/* Contenu principal: Calendrier, Agenda ou Archives */}
            {loading ? (
                <div className="flex-1 flex justify-center items-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
                </div>
            ) : error ? (
                <div className="flex-1 bg-red-50 p-4">
                    <p className="font-medium">Erreur lors du chargement des événements</p>
                    <p className="text-sm">{error}</p>
                </div>
            ) : showAgendaView ? (
                <div className="flex-1 overflow-auto p-6">
                    <div className="bg-white rounded-xl shadow-lg overflow-hidden">
                        <div className="p-6 border-b border-gray-200 bg-gray-50 flex justify-between items-center">
                            <div>
                                <h2 className="text-xl font-bold text-gray-900">Agenda des événements</h2>
                                <p className="text-gray-500 mt-1">Liste de tous les événements à venir</p>
                            </div>
                            <div className="flex items-center gap-2">
                                <Button
                                    variant="outline"
                                    className="text-gray-700 hover:bg-indigo-50 hover:text-indigo-700"
                                    onClick={() => {
                                        setShowAgendaView(false);
                                        setCalendarView('month');
                                    }}
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1">
                                        <path d="M19 12H5M12 19l-7-7 7-7" />
                                    </svg>
                                    Retour au calendrier
                                </Button>
                                <Button
                                    variant="outline"
                                    className="text-gray-700 hover:bg-indigo-50 hover:text-indigo-700"
                                    onClick={() => {
                                        setShowArchiveView(true);
                                        setShowAgendaView(false);
                                    }}
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1">
                                        <path d="M21 8v13H3V8M1 3h22v5H1zM10 12h4" />
                                    </svg>
                                    Voir les archives
                                </Button>
                            </div>
                        </div>
                        <div className="p-6">
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Titre</th>
                                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Équipe</th>
                                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">ACTIONS</th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {filteredEvents.filter(event => event.status !== 'archived').length === 0 ? (
                                        <tr>
                                            <td colSpan="5" className="px-6 py-4 text-center text-sm text-gray-500">Aucun événement trouvé</td>
                                        </tr>
                                    ) : (
                                        filteredEvents.filter(event => event.status !== 'archived').map((event, index) => (
                                            <tr key={event.id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} hover="bg-gray-100">
                                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{event.title}</td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{event.team_name}</td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    {moment(event.start_date).format('DD/MM/YYYY')} {event.start_time} - {event.end_time}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${event.status === 'completed' ? 'bg-green-100 text-green-800' :
                                                        'bg-yellow-100 text-yellow-800'
                                                        }`}>
                                                        {event.status === 'completed' ? 'Terminé' : 'En attente'}
                                                    </span>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                    <div className="flex justify-end space-x-2">
                                                        <EventPermissionGate event={event} action="updateStatus">
                                                            <button
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    handleUpdateStatus(event.id, event.status === 'pending' ? 'completed' : 'pending');
                                                                }}
                                                                className={`${event.status === 'pending' ? 'text-green-600 hover:text-green-800' : 'text-yellow-600 hover:text-yellow-800'} p-1 rounded-md focus:outline-none`}
                                                                disabled={actionLoading}
                                                                title={event.status === 'pending' ? 'Marquer comme terminé' : 'Marquer comme en attente'}
                                                            >
                                                                {event.status === 'pending' ? (
                                                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                                                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                                                                        <polyline points="22 4 12 14.01 9 11.01"></polyline>
                                                                    </svg>
                                                                ) : (
                                                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                                                        <circle cx="12" cy="12" r="10"></circle>
                                                                        <polyline points="12 6 12 12 16 14"></polyline>
                                                                    </svg>
                                                                )}
                                                            </button>
                                                        </EventPermissionGate>
                                                        <EventPermissionGate event={event} action="manage">
                                                            <button
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    handleEditClick(event);
                                                                }}
                                                                className="text-blue-600 hover:text-blue-800 p-1 rounded-md focus:outline-none"
                                                                disabled={actionLoading}
                                                                title="Modifier"
                                                            >
                                                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                                                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                                                                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                                                                </svg>
                                                            </button>
                                                        </EventPermissionGate>
                                                        <EventPermissionGate event={event} action="archive">
                                                            <button
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    handleArchiveEvent(event.id);
                                                                }}
                                                                className="text-gray-600 hover:text-gray-800 p-1 rounded-md focus:outline-none"
                                                                disabled={actionLoading}
                                                                title="Archiver"
                                                            >
                                                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                                                    <path d="M21 8v13H3V8M1 3h22v5H1zM10 12h4"></path>
                                                                </svg>
                                                            </button>
                                                        </EventPermissionGate>
                                                        <EventPermissionGate event={event} action="manage">
                                                            <button
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    handleDeleteEvent(event.id);
                                                                }}
                                                                className="text-red-600 hover:text-red-800 p-1 rounded-md focus:outline-none"
                                                                disabled={actionLoading}
                                                                title="Supprimer"
                                                            >
                                                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                                                    <path d="M3 6h18M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6m3 0V4a2 2 0 012-2h4a2 2 0 012 2v2"></path>
                                                                    <line x1="10" y1="11" x2="10" y2="17"></line>
                                                                    <line x1="14" y1="11" x2="14" y2="17"></line>
                                                                </svg>
                                                            </button>
                                                        </EventPermissionGate>
                                                    </div>
                                                </td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            ) : showArchiveView ? (
                <div className="flex-1 overflow-auto p-6">
                    <div className="bg-white rounded-xl shadow-lg overflow-hidden">
                        <div className="p-6 border-b border-gray-200 bg-gray-50 flex justify-between items-center">
                            <div>
                                <h2 className="text-xl font-bold text-gray-900">Archives</h2>
                                <p className="text-gray-500 mt-1">Liste des événements archivés</p>
                            </div>
                            <div className="flex items-center gap-2">
                                <Button
                                    variant="outline"
                                    className="text-gray-700 hover:bg-indigo-50 hover:text-indigo-700"
                                    onClick={() => {
                                        setShowArchiveView(false);
                                        setCalendarView('month');
                                    }}
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1">
                                        <path d="M19 12H5M12 19l-7-7 7-7" />
                                    </svg>
                                    Retour au calendrier
                                </Button>
                            </div>
                        </div>
                        <div className="p-6">
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Titre</th>
                                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Équipe</th>
                                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {filteredEvents.filter(event => event.status === 'archived').length === 0 ? (
                                        <tr>
                                            <td colSpan="5" className="px-6 py-4 text-center text-sm text-gray-500">Aucun événement archivé trouvé</td>
                                        </tr>
                                    ) : (
                                        filteredEvents.filter(event => event.status === 'archived').map((event, index) => (
                                            <tr key={event.id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50 hover:bg-gray-100'}>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-500 line-through">{event.title}</td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 line-through">{event.team_name}</td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 line-through">
                                                    {moment(event.start_date).format('DD/MM/YYYY')} {event.start_time} - {event.end_time}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                                        Archivé
                                                    </span>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                    <div className="flex justify-end space-x-3">
                                                        {/* Bouton de désarchivage - Design amélioré */}
                                                        <EventPermissionGate event={event} action="unarchive">
                                                            <button
                                                                onClick={() => handleUnarchiveEvent(event.id)}
                                                                className="inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                                                                disabled={actionLoading}
                                                                title="Désarchiver cet événement"
                                                            >
                                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1.5">
                                                                    <path d="M3 2v6h6m2.5-2.5L7 10"></path>
                                                                    <path d="M21 12A9 9 0 1 0 6 5.3a8.5 8.5 0 0 1 10.8 7.4"></path>
                                                                </svg>
                                                                Désarchiver
                                                            </button>
                                                        </EventPermissionGate>

                                                        {/* Bouton de suppression - Design amélioré */}
                                                        <EventPermissionGate event={event} action="manage">
                                                            <button
                                                                onClick={() => handleDeleteEvent(event.id)}
                                                                className="inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                                                                disabled={actionLoading}
                                                                title="Supprimer définitivement"
                                                            >
                                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1.5">
                                                                    <path d="M3 6h18M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6m3 0V4a2 2 0 012-2h4a2 2 0 012 2v2"></path>
                                                                    <line x1="10" y1="11" x2="10" y2="17"></line>
                                                                    <line x1="14" y1="11" x2="14" y2="17"></line>
                                                                </svg>
                                                                Supprimer
                                                            </button>
                                                        </EventPermissionGate>
                                                    </div>
                                                </td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            ) : (
                <div className="flex-1 overflow-auto p-4">
                    <div className="modern-calendar-container">
                        <Calendar
                            key={`calendar-${refreshKey}`} // Ajouter la clé de rafraîchissement avec un préfixe
                            localizer={localizer}
                            events={calendarEvents}
                            startAccessor="start"
                            endAccessor="end"
                            style={{ height: '650px' }}
                            eventPropGetter={eventStyleGetter}
                            formats={FRENCH_CALENDAR_FORMATS}
                            onSelectEvent={handleSelectEvent}
                            view={calendarView}
                            onView={setCalendarView}
                            messages={FRENCH_CALENDAR_MESSAGES}
                            culture="fr"
                            className="modern-calendar-style hide-toolbar"
                            date={currentDate}
                            onNavigate={setCurrentDate}
                            toolbar={false}
                            popup={true}
                            popupOffset={{ x: 30, y: 20 }}
                            showMultiDayTimes={true}
                            step={30}
                            timeslots={2}
                        />
                    </div>
                </div>
            )}

            {/* Modal de création d'événement */}
            <Dialog open={showCreateModal} onOpenChange={setShowCreateModal}>
                <DialogContent className="sm:max-w-[600px] p-0 overflow-hidden border-0 shadow-xl">
                    <div className="bg-gradient-to-r from-indigo-700 to-purple-700 p-5 text-white">
                        <DialogTitle className="text-xl font-bold">Créer un nouvel événement</DialogTitle>
                        <DialogDescription className="text-indigo-100 mt-1">
                            Remplissez les informations pour créer un nouvel événement pour votre équipe.
                        </DialogDescription>
                    </div>
                    <div className="p-1">
                        <EventForm
                            onSubmit={handleCreateEvent}
                            onCancel={() => setShowCreateModal(false)}
                            isLoading={actionLoading}
                        />
                    </div>
                </DialogContent>
            </Dialog>

            {/* Modal d'édition d'événement */}
            <Dialog open={showEditModal} onOpenChange={setShowEditModal}>
                <DialogContent className="sm:max-w-[600px] p-0 overflow-hidden border-0 shadow-xl">
                    <div className="bg-gradient-to-r from-indigo-700 to-purple-700 p-5 text-white">
                        <DialogTitle className="text-xl font-bold">Modifier l'événement</DialogTitle>
                        <DialogDescription className="text-indigo-100 mt-1">
                            Modifiez les informations de l'événement.
                        </DialogDescription>
                    </div>
                    <div className="p-1">
                        {selectedEvent && (
                            <EventForm
                                event={selectedEvent}
                                onSubmit={handleEditEvent}
                                onCancel={() => {
                                    setShowEditModal(false);
                                    setSelectedEvent(null);
                                }}
                                isLoading={actionLoading}
                            />
                        )}
                    </div>
                </DialogContent>
            </Dialog>

            {/* Modal de détails d'événement pour les employés */}
            {showDetailsModal && selectedEvent && (
                <EventDetailsModal
                    event={selectedEvent}
                    onClose={() => {
                        setShowDetailsModal(false);
                        setSelectedEvent(null);
                    }}
                    onUpdateStatus={handleUpdateStatus}
                />
            )}
        </div>
    );
};

export default TeamCalendar;