import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import TeamCalendar from './TeamCalendar';
import { EventProvider } from '@/contexts/EventContext';
import { TeamProvider } from '@/contexts/TeamContext';

const TeamCalendarPage = () => {
  const { user } = useAuth();

  return (
    <TeamProvider>
      <EventProvider>
        <div className="container mx-auto py-6 px-4">
          <TeamCalendar />
        </div>
      </EventProvider>
    </TeamProvider>
  );
};

export default TeamCalendarPage;