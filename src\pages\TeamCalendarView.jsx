import React, { useState, useEffect } from 'react';
import { Calendar, momentLocalizer } from 'react-big-calendar';
import moment from 'moment';
import 'moment/locale/fr';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'react-toastify';
import { Loader2 } from 'lucide-react';

// Configuration de moment en français
moment.locale('fr');
const localizer = momentLocalizer(moment);

const TeamCalendarView = () => {
    const [events, setEvents] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const { getAuthHeader } = useAuth();

    useEffect(() => {
        const fetchEvents = async () => {
            try {
                setLoading(true);
                const headers = await getAuthHeader();
                const response = await fetch('http://localhost:8000/api/events/', {
                    method: 'GET',
                    headers
                });

                if (!response.ok) {
                    throw new Error(`Erreur lors de la récupération des événements: ${response.status}`);
                }

                const data = await response.json();
                
                // Transformation des événements pour le format du calendrier
                const formattedEvents = data.map(event => {
                    // Combiner date et heure pour créer des objets Date
                    const startDateTime = moment(`${event.start_date} ${event.start_time}`, 'YYYY-MM-DD HH:mm').toDate();
                    const endDateTime = moment(`${event.end_date} ${event.end_time}`, 'YYYY-MM-DD HH:mm').toDate();
                    
                    return {
                        id: event.id,
                        title: event.title,
                        start: startDateTime,
                        end: endDateTime,
                        description: event.description,
                        note: event.note,
                        status: event.status,
                        team_id: event.team_id,
                        allDay: false
                    };
                });

                setEvents(formattedEvents);
            } catch (err) {
                console.error('Erreur lors du chargement des événements:', err);
                setError(err.message);
                toast.error('Impossible de charger les événements');
            } finally {
                setLoading(false);
            }
        };

        fetchEvents();
    }, [getAuthHeader]);

    // Personnalisation des événements dans le calendrier
    const eventStyleGetter = (event) => {
        let style = {
            backgroundColor: '#6B4EFF',
            borderRadius: '5px',
            opacity: 0.8,
            color: 'white',
            border: '0px',
            display: 'block'
        };

        // Différentes couleurs selon le statut
        if (event.status === 'completed') {
            style.backgroundColor = '#10B981'; // vert pour complété
        } else if (event.status === 'archived') {
            style.backgroundColor = '#6B7280'; // gris pour archivé
        }

        return {
            style
        };
    };

    // Format personnalisé pour les événements
    const formats = {
        eventTimeRangeFormat: ({ start, end }) => {
            return `${moment(start).format('HH:mm')} - ${moment(end).format('HH:mm')}`;
        }
    };

    // Affichage des détails de l'événement au clic
    const handleSelectEvent = (event) => {
        toast.info(
            <div>
                <h3 className="font-bold">{event.title}</h3>
                <p>{event.description}</p>
                <p><strong>Date:</strong> {moment(event.start).format('DD/MM/YYYY')}</p>
                <p><strong>Horaire:</strong> {moment(event.start).format('HH:mm')} - {moment(event.end).format('HH:mm')}</p>
                <p><strong>Statut:</strong> {event.status === 'pending' ? 'En attente' : event.status === 'completed' ? 'Terminé' : 'Archivé'}</p>
                {event.note && <p><strong>Note:</strong> {event.note}</p>}
            </div>,
            {
                autoClose: 5000,
                closeButton: true,
                position: toast.POSITION.TOP_CENTER
            }
        );
    };

    if (loading) {
        return (
            <div className="flex justify-center items-center h-64">
                <Loader2 className="w-8 h-8 animate-spin text-[#6B4EFF]" />
                <span className="ml-2 text-gray-600">Chargement des événements...</span>
            </div>
        );
    }

    if (error) {
        return (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                <p>Impossible de charger les événements. Veuillez réessayer plus tard.</p>
                <p className="text-sm">{error}</p>
            </div>
        );
    }

    return (
        <div className="bg-white rounded-xl shadow-md p-6">
            <h1 className="text-2xl font-bold mb-6 text-gray-800">Calendrier D'équipes</h1>
            <div className="h-[600px]">
                <Calendar
                    localizer={localizer}
                    events={events}
                    startAccessor="start"
                    endAccessor="end"
                    style={{ height: '100%' }}
                    eventPropGetter={eventStyleGetter}
                    formats={formats}
                    onSelectEvent={handleSelectEvent}
                    messages={{
                        next: "Suivant",
                        previous: "Précédent",
                        today: "Aujourd'hui",
                        month: "Mois",
                        week: "Semaine",
                        day: "Jour",
                        agenda: "Agenda",
                        date: "Date",
                        time: "Heure",
                        event: "Événement",
                        noEventsInRange: "Aucun événement dans cette période"
                    }}
                />
            </div>
        </div>
    );
};

export default TeamCalendarView;