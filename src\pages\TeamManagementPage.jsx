import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { permissionService } from '@/services/permissionService';
import teamService from '@/services/teamService';
import TeamActions from '@/components/TeamActions';
import TeamPermissionGate from '@/components/TeamPermissionGate';

const TeamManagementPage = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [teams, setTeams] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Vérification des permissions au chargement de la page
  useEffect(() => {
    if (!user || user.role !== 'admin') {
      navigate('/unauthorized');
      return;
    }

    if (!permissionService.hasPermission(user, 'manage_teams')) {
      navigate('/unauthorized', { 
        state: { message: "Vous n'avez pas les permissions nécessaires pour gérer les équipes" } 
      });
      return;
    }

    // Charger les équipes
    loadTeams();
  }, [user, navigate]);

  const loadTeams = async () => {
    try {
      setLoading(true);
      const teamsData = await teamService.getTeams();
      setTeams(teamsData);
      setError(null);
    } catch (err) {
      console.error('Erreur lors du chargement des équipes:', err);
      setError("Une erreur est survenue lors du chargement des équipes");
    } finally {
      setLoading(false);
    }
  };

  const handleEditTeam = async (team) => {
    // Vérifier si l'utilisateur peut gérer cette équipe
    const permissions = permissionService.checkTeamPermissions(user, team);
    if (!permissions.canManage) {
      setError("Vous n'avez pas les permissions nécessaires pour modifier cette équipe");
      return;
    }
    
    // Rediriger vers la page d'édition
    navigate(`/teams/${team.id}/edit`);
  };

  const handleDeleteTeam = async (team) => {
    // Vérifier si l'utilisateur peut gérer cette équipe
    const permissions = permissionService.checkTeamPermissions(user, team);
    if (!permissions.canManage) {
      setError("Vous n'avez pas les permissions nécessaires pour supprimer cette équipe");
      return;
    }
    
    // Demander confirmation
    if (window.confirm(`Êtes-vous sûr de vouloir supprimer l'équipe ${team.name} ?`)) {
      try {
        await teamService.deleteTeam(team.id);
        // Recharger les équipes après la suppression
        loadTeams();
      } catch (err) {
        console.error('Erreur lors de la suppression de l\'équipe:', err);
        setError("Une erreur est survenue lors de la suppression de l'équipe");
      }
    }
  };

  const handleAddMember = (team) => {
    // Vérifier si l'utilisateur peut ajouter des membres à cette équipe
    const permissions = permissionService.checkTeamPermissions(user, team);
    if (!permissions.canAddMembers) {
      setError("Vous n'avez pas les permissions nécessaires pour ajouter des membres à cette équipe");
      return;
    }
    
    // Rediriger vers la page d'ajout de membre
    navigate(`/teams/${team.id}/members/add`);
  };

  if (loading) {
    return <div>Chargement des équipes...</div>;
  }

  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }

  return (
    <div className="team-management-page">
      <h1>Gestion des Équipes</h1>
      
      {/* Bouton de création d'équipe - visible uniquement si l'utilisateur a la permission */}
      <TeamPermissionGate 
        team={{}} // Équipe vide pour les permissions globales
        permissionType="canCreateTeam"
      >
        <button 
          className="btn btn-primary mb-4" 
          onClick={() => navigate('/teams/create')}
        >
          Créer une nouvelle équipe
        </button>
      </TeamPermissionGate>

      {teams.length === 0 ? (
        <p>Aucune équipe disponible.</p>
      ) : (
        <div className="teams-list">
          {teams.map(team => (
            <div key={team.id} className="team-card">
              <h3>{team.name}</h3>
              <p>{team.description}</p>
              
              {/* Afficher les actions disponibles selon les permissions */}
              <TeamActions 
                team={team}
                onEdit={handleEditTeam}
                onDelete={handleDeleteTeam}
                onAddMember={handleAddMember}
              />
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default TeamManagementPage;