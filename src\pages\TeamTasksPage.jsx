import React, { useState, useEffect } from 'react';
import { useTeamTask } from '@/contexts/TeamTaskContext';
import { useTeam } from '@/contexts/TeamContext';
import { useAuth } from '@/contexts/AuthContext';
import { showSuccessToast, showErrorToast, showWarningToast } from '@/utils/toastUtils';
import {
  Plus,
  Search,
  Filter,
  CheckCircle,
  Clock,
  Circle,
  Archive,
  RefreshCw,
  Loader2,
  ListFilter,
  X
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
// Suppression de l'import AlertDialog qui n'existe pas
import TeamTaskCard from '@/components/tasks/TeamTaskCard';
import TeamTaskList from '@/components/tasks/TeamTaskList';
import Team<PERSON><PERSON><PERSON><PERSON>ban from '@/components/tasks/TeamTaskKanban';
import ViewSelector from '@/components/tasks/ViewSelector';
import TeamTaskForm from '@/components/tasks/TeamTaskForm';

const TeamTasksPage = () => {
  const { user } = useAuth();
  const { teams } = useTeam();
  const {
    tasks,
    loading,
    error,
    filters,
    updateFilters,
    resetFilters,
    fetchTasks,
    createTask,
    updateTask,
    updateTaskStatus,
    archiveTask,
    unarchiveTask,
    deleteTask
  } = useTeamTask();

  // États locaux
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedTask, setSelectedTask] = useState(null);
  const [actionLoading, setActionLoading] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState('all'); // 'all', 'active', 'archived'
  // Récupérer la préférence de vue et l'état de verrouillage depuis le localStorage
  const [displayView, setDisplayView] = useState(() => {
    // Utiliser l'ID de l'utilisateur connecté pour des préférences personnalisées
    const userId = user?.id;

    // Vérifier d'abord s'il y a une préférence pour l'utilisateur et l'équipe sélectionnée
    const teamId = filters.team_id;
    if (userId && teamId) {
      const userTeamView = localStorage.getItem(`teamTasksDisplayView_user_${userId}_team_${teamId}`);
      if (userTeamView) return userTeamView;
    }

    // Ensuite, vérifier s'il y a une préférence pour l'utilisateur
    if (userId) {
      const userView = localStorage.getItem(`teamTasksDisplayView_user_${userId}`);
      if (userView) return userView;
    }

    // Sinon, utiliser la préférence par défaut
    return 'cards'; // 'cards', 'list', 'kanban'
  });

  // État pour suivre si le choix de vue est verrouillé par l'utilisateur actuel
  const [isViewLocked, setIsViewLocked] = useState(() => {
    // Utiliser l'ID de l'utilisateur connecté pour des préférences personnalisées
    const userId = user?.id;

    // Vérifier d'abord s'il y a un verrouillage pour l'utilisateur et l'équipe sélectionnée
    const teamId = filters.team_id;
    if (userId && teamId) {
      const userTeamLocked = localStorage.getItem(`teamTasksViewLocked_user_${userId}_team_${teamId}`);
      if (userTeamLocked === 'true') return true;
    }

    // Ensuite, vérifier s'il y a un verrouillage pour l'utilisateur
    if (userId) {
      const userLocked = localStorage.getItem(`teamTasksViewLocked_user_${userId}`);
      if (userLocked === 'true') return true;
    }

    // Par défaut, la vue n'est pas verrouillée
    return false;
  });

  // Fonction pour changer la vue et sauvegarder la préférence
  const handleViewChange = (view) => {
    if (!isViewLocked) {
      setDisplayView(view);

      // Récupérer l'ID de l'utilisateur connecté
      const userId = user?.id;
      if (!userId) return;

      // Si une équipe est sélectionnée, sauvegarder la préférence pour cet utilisateur et cette équipe
      const teamId = filters.team_id;
      if (teamId) {
        localStorage.setItem(`teamTasksDisplayView_user_${userId}_team_${teamId}`, view);
        console.log(`Vue sauvegardée pour l'utilisateur ${userId} et l'équipe ${teamId}: ${view}`);
      } else {
        // Sinon, sauvegarder la préférence pour cet utilisateur
        localStorage.setItem(`teamTasksDisplayView_user_${userId}`, view);
        console.log(`Vue sauvegardée pour l'utilisateur ${userId}: ${view}`);
      }
    }
  };

  // Fonction pour verrouiller le choix de vue pour l'utilisateur actuel uniquement
  const handleLockView = (view, teamId = null) => {
    // Vérifier si l'utilisateur est un administrateur
    if (user.role !== 'admin') {
      toast.error("Seuls les administrateurs peuvent verrouiller le mode d'affichage.");
      return;
    }

    // Récupérer l'ID de l'utilisateur connecté
    const userId = user?.id;
    if (!userId) {
      toast.error("Impossible d'identifier l'utilisateur connecté.");
      return;
    }

    // Utiliser l'ID d'équipe passé en paramètre ou celui des filtres
    const targetTeamId = teamId || filters.team_id;

    // S'assurer que la vue est correctement définie avant de verrouiller
    setDisplayView(view);

    if (targetTeamId) {
      // Verrouillage spécifique à l'utilisateur et à l'équipe
      localStorage.setItem(`teamTasksDisplayView_user_${userId}_team_${targetTeamId}`, view);
      localStorage.setItem(`teamTasksViewLocked_user_${userId}_team_${targetTeamId}`, 'true');
      console.log(`Vue verrouillée pour l'utilisateur ${userId} et l'équipe ${targetTeamId}: ${view}`);

      // Vérifier si l'administrateur est responsable de cette équipe
      const isTeamResponsible = teams.some(team =>
        team.id === targetTeamId && (team.is_responsable === true || team.is_responsable === 'true')
      );

      if (!isTeamResponsible) {
        toast.warning("Vous n'êtes pas responsable de cette équipe. Votre préférence ne s'appliquera qu'à votre compte.");
      }
    } else {
      // Verrouillage global pour cet utilisateur uniquement
      localStorage.setItem(`teamTasksDisplayView_user_${userId}`, view);
      localStorage.setItem(`teamTasksViewLocked_user_${userId}`, 'true');
      console.log(`Vue verrouillée pour l'utilisateur ${userId}: ${view}`);
    }

    // Mettre à jour l'état local
    setIsViewLocked(true);

    // Notification de succès
    showSuccessToast(`Mode d'affichage verrouillé en "${view === 'list' ? 'Liste' : view === 'kanban' ? 'Kanban' : 'Cartes'}" pour votre compte.`);
  };

  // Mettre à jour les filtres lorsque la recherche change
  useEffect(() => {
    const timer = setTimeout(() => {
      updateFilters({ search: searchQuery });
    }, 300);
    return () => clearTimeout(timer);
  }, [searchQuery, updateFilters]);

  // Mettre à jour l'affichage et l'état de verrouillage lorsque l'équipe sélectionnée change
  useEffect(() => {
    const teamId = filters.team_id;
    const userId = user?.id;

    if (!userId) return;

    console.log(`Mise à jour des préférences d'affichage pour l'utilisateur ${userId} et l'équipe ${teamId || 'globale'}`);

    // Mettre à jour la vue en fonction de l'utilisateur et de l'équipe sélectionnée
    if (teamId) {
      // Vérifier d'abord les préférences spécifiques à l'utilisateur et à l'équipe
      const userTeamView = localStorage.getItem(`teamTasksDisplayView_user_${userId}_team_${teamId}`);
      if (userTeamView) {
        setDisplayView(userTeamView);
        console.log(`Vue chargée pour l'utilisateur ${userId} et l'équipe ${teamId}: ${userTeamView}`);
      } else {
        // Si pas de préférence spécifique, utiliser la préférence globale de l'utilisateur
        const userView = localStorage.getItem(`teamTasksDisplayView_user_${userId}`);
        if (userView) {
          setDisplayView(userView);
          console.log(`Vue globale chargée pour l'utilisateur ${userId}: ${userView}`);
        }
      }

      // Vérifier si la vue est verrouillée pour cet utilisateur et cette équipe
      const userTeamLocked = localStorage.getItem(`teamTasksViewLocked_user_${userId}_team_${teamId}`);
      const isLocked = userTeamLocked === 'true';
      setIsViewLocked(isLocked);
      console.log(`État de verrouillage pour l'utilisateur ${userId} et l'équipe ${teamId}: ${isLocked}`);
    } else {
      // Utiliser les préférences globales de l'utilisateur
      const userView = localStorage.getItem(`teamTasksDisplayView_user_${userId}`);
      if (userView) {
        setDisplayView(userView);
        console.log(`Vue globale chargée pour l'utilisateur ${userId}: ${userView}`);
      }

      // Vérifier si la vue globale est verrouillée pour cet utilisateur
      const userLocked = localStorage.getItem(`teamTasksViewLocked_user_${userId}`);
      const isLocked = userLocked === 'true';
      setIsViewLocked(isLocked);
      console.log(`État de verrouillage global pour l'utilisateur ${userId}: ${isLocked}`);
    }
  }, [filters.team_id, user?.id]);

  // Filtrer les tâches selon le mode d'affichage
  const filteredTasks = tasks.filter(task => {
    if (viewMode === 'all') return true;
    if (viewMode === 'active') return task.status !== 'archived';
    if (viewMode === 'archived') return task.status === 'archived';
    return true;
  });

  // Gérer la création d'une tâche
  const handleCreateTask = async (taskData) => {
    const success = await safeAsyncOperation(
      () => createTask(taskData),
      'Erreur lors de la création de la tâche'
    );

    if (success) {
      setShowCreateModal(false);
    }
  };

  // Gérer la modification d'une tâche
  const handleEditTask = async (taskData) => {
    if (!selectedTask) return;

    const success = await safeAsyncOperation(
      () => updateTask(selectedTask.id, taskData),
      'Erreur lors de la modification de la tâche'
    );

    if (success) {
      setShowEditModal(false);
      setSelectedTask(null);
    }
  };

  // Fonction utilitaire pour gérer les opérations asynchrones avec gestion d'erreur
  const safeAsyncOperation = async (operation, errorMessage) => {
    setActionLoading(true);
    try {
      await operation();
      // Forcer un rafraîchissement de l'interface après l'opération
      setTimeout(() => {
        fetchTasks();
      }, 300);
      return true;
    } catch (error) {
      console.error(errorMessage, error);
      showErrorToast(`${errorMessage}: ${error.message || 'Erreur inconnue'}`);
      return false;
    } finally {
      setActionLoading(false);
    }
  };

  // Gérer la mise à jour du statut d'une tâche
  const handleUpdateStatus = async (taskId, status) => {
    // Vérifier si la tâche existe
    const task = tasks.find(t => t.id === taskId);
    if (!task) {
      showErrorToast('Tâche introuvable');
      return false;
    }

    // Vérifier si la tâche est archivée
    if (task.status === 'archived') {
      showWarningToast('Les tâches archivées ne peuvent pas être modifiées. Désarchivez la tâche d\'abord.');
      return false;
    }

    // Vérifier les permissions
    if (!task.permissions?.canUpdateStatus) {
      showWarningToast('Vous n\'avez pas les droits pour modifier le statut de cette tâche.');
      return false;
    }

    return safeAsyncOperation(
      () => updateTaskStatus(taskId, status),
      'Erreur lors de la mise à jour du statut'
    );
  };

  // Gérer l'archivage d'une tâche
  const handleArchiveTask = async (taskId) => {
    return safeAsyncOperation(
      () => archiveTask(taskId),
      'Erreur lors de l\'archivage de la tâche'
    );
  };

  // Gérer le désarchivage d'une tâche
  const handleUnarchiveTask = async (taskId) => {
    return safeAsyncOperation(
      () => unarchiveTask(taskId),
      'Erreur lors du désarchivage de la tâche'
    );
  };

  // Gérer la suppression d'une tâche
  const handleDeleteTask = async () => {
    if (!selectedTask) return;

    const success = await safeAsyncOperation(
      () => deleteTask(selectedTask.id),
      'Erreur lors de la suppression de la tâche'
    );

    if (success) {
      setShowDeleteModal(false);
      setSelectedTask(null);
    }
  };

  // Ouvrir le modal de modification
  const handleEditClick = (task) => {
    // Vérifier si la tâche est archivée
    if (task.status === 'archived') {
      showWarningToast('Les tâches archivées ne peuvent pas être modifiées. Désarchivez la tâche d\'abord.');
      return;
    }

    // Vérifier les permissions
    if (!task.permissions?.canEdit) {
      showWarningToast('Vous n\'avez pas les droits pour modifier cette tâche.');
      return;
    }

    setSelectedTask(task);
    setShowEditModal(true);
  };

  // Ouvrir le modal de suppression
  const handleDeleteClick = (taskId) => {
    const task = tasks.find(t => t.id === taskId);
    if (task) {
      setSelectedTask(task);
      setShowDeleteModal(true);
    }
  };

  // Rafraîchir les tâches
  const handleRefresh = () => {
    fetchTasks();
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto p-6">
        {/* En-tête avec recherche et boutons de vue */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="p-6">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
              {/* Barre de recherche à gauche */}
              <div className="relative w-full md:w-80">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  type="text"
                  placeholder="Rechercher une tâche..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              {/* Boutons de vue à droite */}
              <div className="flex items-center gap-3">
                <ViewSelector
                  currentView={displayView}
                  onViewChange={handleViewChange}
                  isLocked={isViewLocked}
                  onLock={handleLockView}
                  isAdmin={user.role === 'admin'}
                  teamId={filters.team_id}
                />

                <Button
                  variant="outline"
                  onClick={() => setShowFilters(!showFilters)}
                  className="flex items-center gap-2 border-gray-300 text-gray-700 hover:bg-gray-50"
                >
                  <Filter className="h-4 w-4" />
                  Filtres
                </Button>

                {user.role === 'admin' && (
                  <Button
                    onClick={() => setShowCreateModal(true)}
                    className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2"
                  >
                    <Plus className="h-4 w-4" />
                    Nouvelle tâche
                  </Button>
                )}
              </div>
            </div>
          </div>

          {showFilters && (
            <div className="border-t border-gray-200 pt-4">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-sm font-medium text-gray-700">Filtres avancés</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowFilters(false)}
                  className="h-8 w-8 p-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">Équipe</label>
                  <Select
                    value={filters.team_id}
                    onValueChange={(value) => updateFilters({ team_id: value })}
                  >
                    <SelectTrigger className="border-gray-300 focus:border-blue-500 focus:ring-blue-500">
                      <SelectValue placeholder="Toutes les équipes" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">Toutes les équipes</SelectItem>
                      {teams.map((team) => (
                        <SelectItem key={team.id} value={team.id}>
                          {team.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">Statut</label>
                  <Select
                    value={filters.status}
                    onValueChange={(value) => updateFilters({ status: value })}
                  >
                    <SelectTrigger className="border-gray-300 focus:border-blue-500 focus:ring-blue-500">
                      <SelectValue placeholder="Tous les statuts" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">Tous les statuts</SelectItem>
                      <SelectItem value="a_faire">À faire</SelectItem>
                      <SelectItem value="en_cours">En cours</SelectItem>
                      <SelectItem value="achevee">Achevée</SelectItem>
                      <SelectItem value="archived">Archivée</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-end">
                  <Button
                    variant="outline"
                    onClick={resetFilters}
                    className="w-full border-gray-300 text-gray-700 hover:bg-gray-50"
                  >
                    Réinitialiser
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Contenu principal */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6">
            {loading ? (
              <div className="flex justify-center items-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
              </div>
            ) : error ? (
              <div className="bg-red-50 p-4 rounded-lg">
                <p className="text-red-600">{error}</p>
              </div>
            ) : filteredTasks.length === 0 ? (
              <div className="text-center py-12">
                <Clock className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900">Aucune tâche trouvée</h3>
                <p className="text-gray-500 mt-2">
                  {searchQuery ? 'Aucune tâche ne correspond à votre recherche.' : 'Commencez par créer une nouvelle tâche.'}
                </p>
              </div>
            ) : (
              <>
                {displayView === 'cards' && (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {filteredTasks.map((task) => (
                      <TeamTaskCard
                        key={task.id}
                        task={task}
                        onEdit={handleEditClick}
                        onDelete={handleDeleteClick}
                        onUpdateStatus={handleUpdateStatus}
                        onArchive={handleArchiveTask}
                        onUnarchive={handleUnarchiveTask}
                        modalOpen={showCreateModal || showEditModal || showDeleteModal}
                      />
                    ))}
                  </div>
                )}

                {displayView === 'list' && (
                  <TeamTaskList
                    tasks={filteredTasks}
                    onEdit={handleEditClick}
                    onDelete={handleDeleteClick}
                    onUpdateStatus={handleUpdateStatus}
                    onArchive={handleArchiveTask}
                    onUnarchive={handleUnarchiveTask}
                  />
                )}

                {displayView === 'kanban' && (
                  <TeamTaskKanban
                    tasks={filteredTasks}
                    onEdit={handleEditClick}
                    onDelete={handleDeleteClick}
                    onUpdateStatus={handleUpdateStatus}
                    onArchive={handleArchiveTask}
                    onUnarchive={handleUnarchiveTask}
                  />
                )}
              </>
            )}
          </div>
        </div>
      </div>

      {/* Modal de création de tâche */}
      <Dialog open={showCreateModal} onOpenChange={setShowCreateModal}>
        <DialogContent className="sm:max-w-[550px] max-h-[90vh] overflow-y-auto">
          <DialogHeader className="pb-2">
            <DialogTitle className="text-lg">Créer une nouvelle tâche</DialogTitle>
            <DialogDescription className="text-sm">
              Remplissez les informations pour créer une nouvelle tâche d'équipe.
            </DialogDescription>
          </DialogHeader>
          <TeamTaskForm
            onSubmit={handleCreateTask}
            onCancel={() => setShowCreateModal(false)}
            isSubmitting={actionLoading}
            teamTasks={tasks}
          />
        </DialogContent>
      </Dialog>

      {/* Modal de modification de tâche */}
      <Dialog open={showEditModal} onOpenChange={setShowEditModal}>
        <DialogContent className="sm:max-w-[550px] max-h-[90vh] overflow-y-auto">
          <DialogHeader className="pb-2">
            <DialogTitle className="text-lg">Modifier la tâche</DialogTitle>
            <DialogDescription className="text-sm">
              Modifiez les informations de la tâche.
            </DialogDescription>
          </DialogHeader>
          {selectedTask && (
            <TeamTaskForm
              task={selectedTask}
              onSubmit={handleEditTask}
              onCancel={() => setShowEditModal(false)}
              isSubmitting={actionLoading}
              teamTasks={tasks}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Modal de confirmation de suppression */}
      <Dialog open={showDeleteModal} onOpenChange={setShowDeleteModal}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Êtes-vous sûr de vouloir supprimer cette tâche ?</DialogTitle>
            <DialogDescription>
              Cette action est irréversible. La tâche sera définitivement supprimée.
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              variant="outline"
              onClick={() => setShowDeleteModal(false)}
              disabled={actionLoading}
            >
              Annuler
            </Button>
            <Button
              onClick={handleDeleteTask}
              disabled={actionLoading}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              {actionLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" aria-hidden="true" />
                  Suppression...
                </>
              ) : (
                'Supprimer'
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default TeamTasksPage;
