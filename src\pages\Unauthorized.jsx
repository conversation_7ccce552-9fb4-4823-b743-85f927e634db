import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { AlertTriangle } from 'lucide-react';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import ThemeToggle from '@/components/ThemeToggle';

const Unauthorized = () => {
    const { t } = useTranslation();

    return (
        <div className="min-h-screen bg-gradient-to-br from-white via-blue-50/30 to-indigo-50/30 dark:from-gray-900 dark:via-gray-800/30 dark:to-gray-900/30 relative">
            {/* Floating Elements */}
            <div className="fixed inset-0 overflow-hidden pointer-events-none">
                <div className="absolute top-20 left-10 w-64 h-64 bg-purple-200/30 dark:bg-purple-500/10 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob"></div>
                <div className="absolute top-40 right-10 w-64 h-64 bg-yellow-200/30 dark:bg-yellow-500/10 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-2000"></div>
                <div className="absolute -bottom-8 left-20 w-64 h-64 bg-pink-200/30 dark:bg-pink-500/10 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-4000"></div>
            </div>

            <div className="flex flex-col justify-center min-h-screen py-12 sm:px-6 lg:px-8 relative z-10">
                <div className="sm:mx-auto sm:w-full sm:max-w-md">
                    <div className="flex justify-end mb-4 px-4 space-x-2">
                        <ThemeToggle />
                        <LanguageSwitcher />
                    </div>

                    <Link 
                        to="/" 
                        className="flex items-center justify-center gap-2 text-3xl font-bold mb-6 bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent hover:opacity-80 transition-opacity"
                    >
                        <div className="w-10 h-10 rounded-lg bg-gradient-to-r from-primary to-purple-600 flex items-center justify-center text-white">
                            N
                        </div>
                        Notora
                    </Link>
                </div>

                <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
                    <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm py-8 px-4 shadow-xl rounded-2xl sm:px-10 relative">
                        <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-purple-500/5 dark:from-primary/10 dark:to-purple-500/10 rounded-2xl transform rotate-1"></div>
                        <div className="relative">
                            <div className="flex flex-col items-center justify-center text-center">
                                <div className="w-16 h-16 rounded-full bg-red-100 dark:bg-red-900/30 flex items-center justify-center mb-4">
                                    <AlertTriangle className="w-8 h-8 text-red-600 dark:text-red-400" />
                                </div>
                                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                                    {t('errors.unauthorized.title')}
                                </h2>
                                <p className="text-gray-600 dark:text-gray-400 mb-6">
                                    {t('errors.unauthorized.message')}
                                </p>
                                <div className="space-y-4 w-full">
                                    <Button
                                        as={Link}
                                        to="/login"
                                        className="w-full bg-gradient-to-r from-primary to-purple-600 text-white rounded-lg py-2.5 hover:from-primary-dark hover:to-purple-700 dark:from-primary-dark dark:to-purple-700 dark:hover:from-primary dark:hover:to-purple-600"
                                    >
                                        {t('common.login')}
                                    </Button>
                                    <Button
                                        as={Link}
                                        to="/"
                                        variant="outline"
                                        className="w-full border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg py-2.5 hover:bg-gray-50 dark:hover:bg-gray-700/50"
                                    >
                                        {t('common.backToHome')}
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Unauthorized; 