import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { Users, ArrowLeft, Shield } from 'lucide-react';
import UserPermissionsDisplay from '@/components/users/UserPermissionsDisplay';
import { Link } from 'react-router-dom';
import { userService } from '@/services/userService';



const UserPermissions = () => {
    const { userId } = useParams();
    const navigate = useNavigate();
    const [userData, setUserData] = useState({
        name: '',
        email: '',
        role: '',
        permissions: {}
    });
    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);

    useEffect(() => {
        const fetchUserData = async () => {
            try {
                setLoading(true);

                // Récupérer les données de base de l'utilisateur
                const userResponse = await userService.getUserById(userId);
                if (!userResponse.success) {
                    throw new Error(userResponse.message || 'Erreur lors du chargement des données utilisateur');
                }

                // Récupérer les permissions réelles du backend
                const permissionsResponse = await userService.getUserPermissions(userId);
                if (!permissionsResponse.success) {
                    throw new Error(permissionsResponse.message || 'Erreur lors du chargement des permissions');
                }

                // Convertir le tableau de permissions en objet pour l'interface
                const permissionsArray = permissionsResponse.data.permissions || [];
                console.log('🔍 Permissions récupérées du backend pour', userResponse.data.role, ':', permissionsArray);

                const permissionsObject = {};
                permissionsArray.forEach(permission => {
                    permissionsObject[permission] = true;
                });

                // Forcer les permissions selon le rôle pour s'assurer qu'elles sont cochées
                if (userResponse.data.role === 'employee') {
                    // Forcer les permissions des employés selon vos spécifications
                    permissionsObject['manage_personal_tasks'] = true;
                    permissionsObject['manage_personal_calendar'] = true;
                    permissionsObject['modify_team_event_status'] = true;
                    permissionsObject['modify_team_task_status'] = true;
                } else if (userResponse.data.role === 'client') {
                    // Forcer les permissions des clients selon vos spécifications
                    permissionsObject['manage_personal_tasks'] = true;
                    permissionsObject['manage_personal_calendar'] = true;
                    permissionsObject['manage_journal_notes'] = true;
                    permissionsObject['activate_focus_mode'] = true;
                    permissionsObject['view_personal_dashboard'] = true;
                } else if (userResponse.data.role === 'admin') {
                    // Forcer les permissions des admins selon vos spécifications
                    permissionsObject['manage_teams'] = true;
                    permissionsObject['manage_team_tasks'] = true;
                    permissionsObject['manage_team_calendars'] = true;
                    permissionsObject['view_team_dashboards'] = true;
                }

                console.log('🔍 Permissions finales (avec corrections):', permissionsObject);

                setUserData({
                    ...userResponse.data,
                    permissions: permissionsObject
                });

            } catch (error) {
                console.error('Error fetching user data:', error);
                toast.error(error.message || 'Erreur de chargement des données utilisateur');
                navigate('/super-admin/users'); // Rediriger vers la liste des utilisateurs
            } finally {
                setLoading(false);
            }
        };

        fetchUserData();
    }, [userId, navigate]);

    const handlePermissionToggle = async (permissionKey) => {
        if (saving || !userData) return;

        try {
            setSaving(true);
            const newPermissions = {
                ...userData.permissions,
                [permissionKey]: !userData.permissions[permissionKey]
            };

            const response = await userService.updateUserPermissions(userId, newPermissions);

            if (response.success) {
                setUserData(prev => ({
                    ...prev,
                    permissions: newPermissions
                }));
                toast.success('Permission mise à jour avec succès');
            } else {
                throw new Error(response.message || 'Erreur lors de la mise à jour des permissions');
            }
        } catch (error) {
            console.error('Error updating permission:', error);
            toast.error(error.message || 'Échec de la mise à jour des permissions');
        } finally {
            setSaving(false);
        }
    };

    const getRoleColor = (role) => {
        switch (role) {
            case 'admin': return 'bg-purple-100 text-purple-800';
            case 'employee': return 'bg-green-100 text-green-800';
            default: return 'bg-blue-100 text-blue-800';
        }
    };

    const getRoleLabel = (role) => {
        switch (role) {
            case 'admin': return 'Administrateur';
            case 'employee': return 'Employé';
            default: return 'Client';
        }
    };

    if (loading) {
        return <div className="p-4 text-center">Chargement...</div>;
    }

    if (!userData) {
        return <div className="p-4 text-center">Utilisateur non trouvé</div>;
    }

    return (
        <div className="p-6 max-w-6xl mx-auto">
            {/* Header */}
            <div className="mb-8">
                <Link
                    to="/super-admin/users"
                    className="inline-flex items-center gap-2 text-gray-600 hover:text-gray-900 mb-4"
                >
                    <ArrowLeft className="w-4 h-4" />
                    Retour à la liste
                </Link>

                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <div className="h-12 w-12 rounded-lg bg-[#6B4EFF]/10 flex items-center justify-center">
                            <Users className="w-6 h-6 text-[#6B4EFF]" />
                        </div>
                        <div>
                            <h1 className="text-2xl font-bold text-gray-900">{userData.name}</h1>
                            <div className="flex items-center gap-2">
                                <p className="text-gray-500">{userData.email}</p>
                                <span className={`px-3 py-1 rounded-full text-sm font-medium ${getRoleColor(userData.role)}`}>
                                    {getRoleLabel(userData.role)}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Permissions */}
            <div className="mt-8">
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div className="flex items-center gap-3 mb-6">
                        <Shield className="w-6 h-6 text-indigo-600" />
                        <h2 className="text-xl font-semibold text-gray-900">
                            Permissions de l'utilisateur
                        </h2>
                    </div>

                    {/* Message d'information */}
                    <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <div className="flex items-start gap-3">
                            <div className="w-5 h-5 text-blue-600 mt-0.5">ℹ️</div>
                            <div>
                                <h3 className="text-sm font-medium text-blue-900">
                                    Permissions statiques basées sur le rôle
                                </h3>
                                <p className="text-sm text-blue-700 mt-1">
                                    Les permissions sont automatiquement attribuées selon le rôle "{getRoleLabel(userData.role)}" et ne peuvent pas être modifiées individuellement.
                                </p>
                            </div>
                        </div>
                    </div>

                    {/* Composant de permissions */}
                    <UserPermissionsDisplay
                        permissions={userData.permissions}
                        onToggle={handlePermissionToggle}
                        disabled={true}
                        userRole={userData.role}
                    />
                </div>
            </div>
        </div>
    );
};

export default UserPermissions;