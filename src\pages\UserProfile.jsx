import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'react-toastify';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Eye, EyeOff, Lock, User, Mail, Pencil, Save, X } from 'lucide-react';
import profileService from '@/services/profileService';

const UserProfile = () => {
    const { t } = useTranslation();
    const { user, getAuthHeader, completePasswordChange, updateUser } = useAuth();
    const [loading, setLoading] = useState(true);

    const [showPasswordForm, setShowPasswordForm] = useState(false);
    const [showCurrentPassword, setShowCurrentPassword] = useState(false);
    const [showNewPassword, setShowNewPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [profileData, setProfileData] = useState({
        first_name: '',
        last_name: '',
        name: '',
        email: '',
        phone: ''
    });
    const [passwordData, setPasswordData] = useState({
        current_password: '',
        new_password: '',
        confirm_password: ''
    });
    const [errors, setErrors] = useState({});
    const [isEditing, setIsEditing] = useState(false);

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setProfileData(prev => ({
            ...prev,
            [name]: value
        }));
        if (errors[name]) {
            setErrors(prev => ({
                ...prev,
                [name]: ''
            }));
        }
    };

    const handleUpdateProfile = async (e) => {
        e.preventDefault();
        try {
            const result = await profileService.updateProfile(profileData);

            if (result.success) {
                // Mettre à jour le contexte utilisateur avec la réponse complète
                updateUser(result.user);

                // Mettre à jour le localStorage
                localStorage.setItem('user', JSON.stringify(result.user));

                // Mettre à jour les données locales avec la réponse du serveur
                const updatedProfileData = profileService.prepareProfileData(result.user);
                setProfileData(updatedProfileData);

                toast.success(result.message);
                setIsEditing(false);
            } else {
                toast.error(result.error);
            }
        } catch (error) {
            console.error('❌ Erreur lors de la mise à jour du profil:', error);
            toast.error('Erreur lors de la mise à jour du profil');
        }
    };


    useEffect(() => {
        if (user) {
            // Utiliser le service pour préparer les données de profil
            const preparedData = profileService.prepareProfileData(user);
            setProfileData(preparedData);

            // Afficher automatiquement le formulaire de changement de mot de passe si nécessaire
            if (user.temp_password_required) {
                setShowPasswordForm(true);
                toast.warning('Attention : Votre mot de passe temporaire expirera après votre prochaine déconnexion. Veuillez le changer dès maintenant pour éviter tout problème d\'accès.');
            }
            setLoading(false);
        } else {
            fetchUserProfile();
        }
    }, [user]);

    const fetchUserProfile = async () => {
        try {
            const result = await profileService.getProfile();

            if (result.success) {
                const preparedData = profileService.prepareProfileData(result.user);
                setProfileData(preparedData);
            } else {
                toast.error(result.error);
            }
        } catch (error) {
            console.error('Error fetching profile:', error);
            toast.error('Erreur lors du chargement du profil');
        } finally {
            setLoading(false);
        }
    };

    const handlePasswordChange = (e) => {
        const { name, value } = e.target;
        setPasswordData(prev => ({
            ...prev,
            [name]: value
        }));
        // Effacer l'erreur lorsque l'utilisateur commence à taper
        if (errors[name]) {
            setErrors(prev => ({
                ...prev,
                [name]: ''
            }));
        }
    };

    const validateForm = () => {
        const newErrors = {};

        // Valider le mot de passe actuel
        if (!passwordData.current_password) {
            newErrors.current_password = 'Le mot de passe actuel est requis';
        }

        // Valider le nouveau mot de passe (selon les exigences backend)
        if (!passwordData.new_password) {
            newErrors.new_password = 'Le nouveau mot de passe est requis';
        } else if (passwordData.new_password.length < 8) {
            newErrors.new_password = 'Le mot de passe doit contenir au moins 8 caractères';
        }

        // Valider la confirmation du mot de passe
        if (!passwordData.confirm_password) {
            newErrors.confirm_password = 'La confirmation du mot de passe est requise';
        } else if (passwordData.new_password !== passwordData.confirm_password) {
            newErrors.confirm_password = 'Les mots de passe ne correspondent pas';
        }

        // Vérifier si le nouveau mot de passe est différent de l'actuel
        if (passwordData.current_password && passwordData.new_password &&
            passwordData.current_password === passwordData.new_password) {
            newErrors.new_password = 'Le nouveau mot de passe doit être différent de l\'ancien';
        }

        return newErrors;
    };

    const handleUpdatePassword = async (e) => {
        e.preventDefault();

        const validationErrors = validateForm();
        if (Object.keys(validationErrors).length > 0) {
            setErrors(validationErrors);
            return;
        }

        try {
            const result = await profileService.changePassword(passwordData);

            if (result.success) {
                toast.success(result.message);
                setPasswordData({
                    current_password: '',
                    new_password: '',
                    confirm_password: ''
                });
                setShowPasswordForm(false);

                // Mettre à jour le statut du mot de passe temporaire
                if (user?.temp_password_required) {
                    completePasswordChange();
                }
            } else {
                toast.error(result.error);
            }
        } catch (error) {
            console.error('❌ Erreur lors du changement de mot de passe:', error);
            toast.error('Erreur lors du changement de mot de passe');
        }
    };

    if (loading) {
        return (
            <div className="flex justify-center items-center min-h-screen">
                <div className="text-xl font-semibold">{t('common.loading')}</div>
            </div>
        );
    }

    const getInitials = (firstName, lastName) => {
        const first = firstName ? firstName[0] : '';
        const last = lastName ? lastName[0] : '';
        return (first + last).toUpperCase() || (profileData.name ? profileData.name[0].toUpperCase() : '');
    };

    const getDisplayName = () => {
        if (profileData.first_name || profileData.last_name) {
            return `${profileData.first_name} ${profileData.last_name}`.trim();
        }
        return profileData.name || user?.name || '';
    };

    return (
        <div className="max-w-4xl mx-auto p-6">
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 mb-6">
                <div className="flex items-center gap-4 mb-6">
                    <div className="h-16 w-16 rounded-lg bg-[#6B4EFF] flex items-center justify-center text-white text-xl font-medium shadow-lg transition-all duration-200">
                        {getInitials(profileData.first_name, profileData.last_name)}
                    </div>
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Mon Profil</h1>
                        <p className="text-lg text-gray-700 dark:text-gray-300">{getDisplayName()}</p>
                        <p className="text-gray-500 dark:text-gray-400">{user?.role}</p>
                    </div>

                </div>

                {user?.temp_password_required && (
                    <div className="mb-6 p-4 bg-amber-50 dark:bg-amber-900/30 border-l-4 border-amber-500 text-amber-700 dark:text-amber-200">
                        <p className="font-medium">Action requise : Changement de mot de passe</p>
                        <p className="text-sm mt-1">Pour des raisons de sécurité, vous devez changer votre mot de passe temporaire avant de continuer à utiliser l'application.</p>
                    </div>
                )}
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
                <div className="flex justify-between items-center mb-6">
                    <h2 className="text-xl font-semibold flex items-center gap-2">
                        <User className="w-5 h-5" />
                        Informations Personnelles
                    </h2>
                    {!isEditing ? (
                        <Button
                            onClick={() => setIsEditing(true)}
                            variant="outline"
                            className="flex items-center gap-2"
                        >
                            <Pencil className="w-4 h-4" />
                            Modifier
                        </Button>
                    ) : (
                        <div className="flex items-center gap-2">
                            <Button
                                onClick={handleUpdateProfile}
                                className="flex items-center gap-2 bg-purple-600 hover:bg-purple-700"
                            >
                                <Save className="w-4 h-4" />
                                Enregistrer
                            </Button>
                            <Button
                                onClick={() => {
                                    setIsEditing(false);
                                    // Restaurer les données originales
                                    const preparedData = profileService.prepareProfileData(user);
                                    setProfileData(preparedData);
                                    setErrors({});
                                }}
                                variant="outline"
                                className="flex items-center gap-2"
                            >
                                <X className="w-4 h-4" />
                                Annuler
                            </Button>
                        </div>
                    )}
                </div>

                <form onSubmit={handleUpdateProfile} className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <Label className="flex items-center gap-2 text-gray-700 dark:text-gray-200">
                                <User className="w-4 h-4" />
                                Prénom
                            </Label>
                            <div className="mt-1">
                                <Input
                                    type="text"
                                    name="first_name"
                                    value={profileData.first_name}
                                    onChange={handleInputChange}
                                    disabled={!isEditing}
                                    placeholder="Entrez votre prénom"
                                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 bg-gray-50"
                                />
                            </div>
                        </div>

                        <div>
                            <Label className="flex items-center gap-2 text-gray-700 dark:text-gray-200">
                                <User className="w-4 h-4" />
                                Nom de famille
                            </Label>
                            <div className="mt-1">
                                <Input
                                    type="text"
                                    name="last_name"
                                    value={profileData.last_name}
                                    onChange={handleInputChange}
                                    disabled={!isEditing}
                                    placeholder="Entrez votre nom de famille"
                                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 bg-gray-50"
                                />
                            </div>
                        </div>
                    </div>

                    <div>
                        <Label className="flex items-center gap-2 text-gray-700 dark:text-gray-200">
                            <Mail className="w-4 h-4" />
                            Email
                        </Label>
                        <div className="mt-1">
                            <Input
                                type="email"
                                name="email"
                                value={profileData.email}
                                onChange={handleInputChange}
                                disabled={!isEditing}
                                placeholder="Entrez votre email"
                                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 bg-gray-50"
                            />
                        </div>
                    </div>

                    <div>
                        <Label className="flex items-center gap-2 text-gray-700 dark:text-gray-200">
                            <User className="w-4 h-4" />
                            Téléphone (optionnel)
                        </Label>
                        <div className="mt-1">
                            <Input
                                type="tel"
                                name="phone"
                                value={profileData.phone}
                                onChange={handleInputChange}
                                disabled={!isEditing}
                                placeholder="Entrez votre numéro de téléphone"
                                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 bg-gray-50"
                            />
                        </div>
                    </div>
                </form>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <h2 className="text-xl font-semibold mb-6 flex items-center gap-2">
                    <Lock className="w-5 h-5" />
                    Sécurité
                </h2>

                {!showPasswordForm ? (
                    <Button
                        onClick={() => setShowPasswordForm(true)}
                        variant="outline"
                        className="w-full md:w-auto"
                    >
                        Changer mon mot de passe
                    </Button>
                ) : (
                    <form onSubmit={handleUpdatePassword} className="space-y-6">
                        <div>
                            <Label htmlFor="current_password" className="flex items-center gap-2 text-gray-700 dark:text-gray-200">
                                <Lock className="w-4 h-4" />
                                Mot de passe actuel
                            </Label>
                            <div className="mt-1 relative">
                                <Input
                                    id="current_password"
                                    name="current_password"
                                    type={showCurrentPassword ? "text" : "password"}
                                    autoComplete="current-password"
                                    placeholder="Entrez votre mot de passe actuel"
                                    value={passwordData.current_password}
                                    onChange={handlePasswordChange}
                                    className={`block w-full rounded-lg border ${errors.current_password ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-gray-600'} pr-10`}
                                />
                                <button
                                    type="button"
                                    onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                                    className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600 dark:text-gray-300 dark:hover:text-gray-100"
                                >
                                    {showCurrentPassword ? (
                                        <EyeOff className="h-5 w-5" />
                                    ) : (
                                        <Eye className="h-5 w-5" />
                                    )}
                                </button>
                            </div>
                            {errors.current_password && (
                                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.current_password}</p>
                            )}
                        </div>

                        <div>
                            <Label htmlFor="new_password" className="flex items-center gap-2 text-gray-700 dark:text-gray-200">
                                <Lock className="w-4 h-4" />
                                Nouveau mot de passe
                            </Label>
                            <div className="mt-1 relative">
                                <Input
                                    id="new_password"
                                    name="new_password"
                                    type={showNewPassword ? "text" : "password"}
                                    autoComplete="new-password"
                                    placeholder="Entrez votre nouveau mot de passe"
                                    value={passwordData.new_password}
                                    onChange={handlePasswordChange}
                                    className={`block w-full rounded-lg border ${errors.new_password ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-gray-600'} pr-10`}
                                />
                                <button
                                    type="button"
                                    onClick={() => setShowNewPassword(!showNewPassword)}
                                    className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600 dark:text-gray-300 dark:hover:text-gray-100"
                                >
                                    {showNewPassword ? (
                                        <EyeOff className="h-5 w-5" />
                                    ) : (
                                        <Eye className="h-5 w-5" />
                                    )}
                                </button>
                            </div>
                            {errors.new_password && (
                                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.new_password}</p>
                            )}
                        </div>

                        <div>
                            <Label htmlFor="confirm_password" className="flex items-center gap-2 text-gray-700 dark:text-gray-200">
                                <Lock className="w-4 h-4" />
                                Confirmer le nouveau mot de passe
                            </Label>
                            <div className="mt-1 relative">
                                <Input
                                    id="confirm_password"
                                    name="confirm_password"
                                    type={showConfirmPassword ? "text" : "password"}
                                    autoComplete="new-password"
                                    placeholder="Confirmez votre nouveau mot de passe"
                                    value={passwordData.confirm_password}
                                    onChange={handlePasswordChange}
                                    className={`block w-full rounded-lg border ${errors.confirm_password ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-gray-600'} pr-10`}
                                />
                                <button
                                    type="button"
                                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                    className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600 dark:text-gray-300 dark:hover:text-gray-100"
                                >
                                    {showConfirmPassword ? (
                                        <EyeOff className="h-5 w-5" />
                                    ) : (
                                        <Eye className="h-5 w-5" />
                                    )}
                                </button>
                            </div>
                            {errors.confirm_password && (
                                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.confirm_password}</p>
                            )}
                        </div>

                        <div className="flex gap-4">
                            <Button
                                type="submit"
                                className="flex-1 bg-primary hover:bg-primary-dark text-white"
                            >
                                Mettre à jour le mot de passe
                            </Button>
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => {
                                    setShowPasswordForm(false);
                                    setPasswordData({
                                        current_password: '',
                                        new_password: '',
                                        confirm_password: ''
                                    });
                                    setErrors({});
                                }}
                                className="flex-1"
                            >
                                Annuler
                            </Button>
                        </div>
                    </form>
                )}
            </div>
        </div>
    );
};

export default UserProfile;