import React from 'react';
import { Routes, Route, useLocation, Navigate } from 'react-router-dom';
import { AnimatePresence } from 'framer-motion';
import PageTransition from '@/components/ui/PageTransition';
import Login from '@/pages/Login';
import Register from '@/pages/Register';
import Dashboard from '@/pages/Dashboard';
import SuperAdminDashboard from '@/pages/SuperAdminDashboard';
import SuperAdminSpace from '@/pages/SuperAdminSpace';
import SuperAdminProfile from '@/pages/SuperAdminProfile';
import SuperAdminAnalytics from '@/pages/SuperAdminAnalytics';
import Landing from '@/pages/Landing';
import UserPermissions from '@/pages/UserPermissions';
import ForcePasswordChange from '@/pages/ForcePasswordChange';
import UserProfile from '@/pages/UserProfile';
import PrivateRoute from '@/components/PrivateRoute';
import UserLayout from '@/layouts/UserLayout';
import ForgotPassword from '@/pages/ForgotPassword';
import ResetPassword from '@/pages/ResetPassword';
import AdminTeams from '@/pages/AdminTeams';
import EmployeeTeams from '@/pages/EmployeeTeams';
import EmployeeTeamDetail from '@/pages/EmployeeTeamDetail';
import TeamCalendar from '@/pages/TeamCalendar';
import TeamCalendarView from '@/pages/TeamCalendarView';
import MemberTeamView from '@/pages/MemberTeamView';
import AdminCalendarView from '@/pages/AdminCalendarView';
import MemberCalendarView from '@/pages/MemberCalendarView';

import AdminEventsList from '@/pages/AdminEventsList';
import TeamTasksPage from '@/pages/TeamTasksPage';
import EmployeeTeamTasksPage from '@/pages/EmployeeTeamTasksPage';
import PersonalEventsPage from '@/pages/PersonalEventsPage';
import PersonalTasksPage from '@/pages/PersonalTasksPage';
import ClientDashboard from '@/pages/ClientDashboard';
import ClientNotes from '@/pages/ClientNotes';
import ClientJournals from '@/pages/ClientJournals';
import ClientPomodoro from '@/pages/ClientPomodoro';
import SuperAdminLayout from '@/layouts/SuperAdminLayout';
import NotFound from '@/pages/NotFound';
import DashboardTest from '@/components/dashboard/DashboardTest';
import BiTestComponent from '@/components/dashboard/BiTestComponent';
import SuperAdminBIDashboardFinal from '@/components/dashboard/SuperAdminBIDashboardFinal';
import BiApiTester from '@/components/debug/BiApiTester';
import AdminBIDashboard from '@/components/dashboard/AdminBIDashboard';
import AdminDashboardTester from '@/components/debug/AdminDashboardTester';
import DashboardErrorFixer from '@/components/debug/DashboardErrorFixer';


const AnimatedRoutes = () => {
  const location = useLocation();

  return (
    <AnimatePresence mode="wait" initial={false}>
      <Routes location={location} key={location.pathname}>
        {/* Routes publiques */}
        <Route path="/" element={<PageTransition><Landing /></PageTransition>} />
        <Route path="/login" element={<PageTransition><Login /></PageTransition>} />
        <Route path="/register" element={<PageTransition><Register /></PageTransition>} />
        <Route path="/forgot-password" element={<PageTransition><ForgotPassword /></PageTransition>} />
        <Route path="/reset-password/:token" element={<PageTransition><ResetPassword /></PageTransition>} />

        {/* Routes Super Admin */}
        <Route path="/super-admin" element={
          <PrivateRoute allowedRoles={['super_admin']}>
            <SuperAdminLayout />
          </PrivateRoute>
        }>
          <Route index element={<Navigate to="/super-admin/users" replace />} />
          <Route path="users" element={<SuperAdminSpace />} />
          <Route path="users/:userId" element={<UserPermissions />} />
          <Route path="analytics" element={<SuperAdminBIDashboardFinal />} />
          <Route path="profile" element={<SuperAdminProfile />} />
          <Route path="dashboard-test" element={<DashboardTest />} />
          <Route path="bi-test" element={<BiTestComponent />} />
          <Route path="api-tester" element={<BiApiTester />} />
        </Route>

        {/* Routes Utilisateur */}
        <Route path="/" element={
          <PrivateRoute>
            <UserLayout />
          </PrivateRoute>
        }>
          <Route path="dashboard" element={<Dashboard />} />
          <Route path="profile" element={<UserProfile />} />

          {/* Routes spécifiques aux admins - Dashboard BI */}
          <Route path="admin-bi-dashboard" element={
            <PrivateRoute allowedRoles={['admin']}>
              <AdminBIDashboard />
            </PrivateRoute>
          } />
          <Route path="admin-dashboard-test" element={
            <PrivateRoute allowedRoles={['admin']}>
              <AdminDashboardTester />
            </PrivateRoute>
          } />
          <Route path="dashboard-error-fixer" element={
            <PrivateRoute allowedRoles={['admin', 'super_admin']}>
              <DashboardErrorFixer />
            </PrivateRoute>
          } />

          {/* Routes d'équipes - non accessibles aux super_admin */}
          <Route path="teams" element={
            <PrivateRoute allowedRoles={['admin', 'employee']}>
              <AdminTeams />
            </PrivateRoute>
          } />
          <Route path="employee-teams" element={
            <PrivateRoute allowedRoles={['admin', 'employee']}>
              <EmployeeTeams />
            </PrivateRoute>
          } />
          <Route path="employee-teams/:teamId" element={
            <PrivateRoute allowedRoles={['admin', 'employee']}>
              <EmployeeTeamDetail />
            </PrivateRoute>
          } />

          <Route path="team-calendar" element={
            <PrivateRoute allowedRoles={['admin', 'employee']}>
              <TeamCalendar />
            </PrivateRoute>
          } />
          <Route path="team-calendar-view" element={
            <PrivateRoute allowedRoles={['admin', 'employee']}>
              <TeamCalendarView />
            </PrivateRoute>
          } />
          <Route path="member-team-view" element={
            <PrivateRoute allowedRoles={['admin', 'employee']}>
              <MemberTeamView />
            </PrivateRoute>
          } />
          <Route path="admin-calendar-view" element={
            <PrivateRoute allowedRoles={['admin', 'employee']}>
              <AdminCalendarView />
            </PrivateRoute>
          } />
          <Route path="member-calendar-view" element={
            <PrivateRoute allowedRoles={['admin', 'employee']}>
              <MemberCalendarView />
            </PrivateRoute>
          } />
          <Route path="team-tasks" element={
            <PrivateRoute allowedRoles={['admin']}>
              <TeamTasksPage />
            </PrivateRoute>
          } />
          <Route path="employee-team-tasks" element={
            <PrivateRoute allowedRoles={['admin', 'employee']}>
              <EmployeeTeamTasksPage />
            </PrivateRoute>
          } />

          {/* Routes pour les événements et tâches personnels - EXCLUS pour les admins */}
          <Route path="personal-events" element={
            <PrivateRoute allowedRoles={['employee', 'client']}>
              <PersonalEventsPage />
            </PrivateRoute>
          } />
          <Route path="personal-tasks" element={
            <PrivateRoute allowedRoles={['employee', 'client']}>
              <PersonalTasksPage />
            </PrivateRoute>
          } />

          {/* Routes pour les clients */}
          <Route path="client-dashboard" element={
            <PrivateRoute allowedRoles={['client']}>
              <ClientDashboard />
            </PrivateRoute>
          } />
          <Route path="client-notes" element={
            <PrivateRoute allowedRoles={['client']}>
              <ClientNotes />
            </PrivateRoute>
          } />
          <Route path="client-journals" element={
            <PrivateRoute allowedRoles={['client']}>
              <ClientJournals />
            </PrivateRoute>
          } />
          <Route path="client-pomodoro" element={
            <PrivateRoute allowedRoles={['client']}>
              <ClientPomodoro />
            </PrivateRoute>
          } />



          {/* Route supprimée à la demande de l'utilisateur
          <Route path="events" element={
            <PrivateRoute allowedRoles={['admin']}>
              <AdminEventsList />
            </PrivateRoute>
          } />
          */}

          <Route path="force-password-change" element={<ForcePasswordChange />} />
        </Route>

        {/* Route 404 */}
        <Route path="*" element={<NotFound />} />
      </Routes>
    </AnimatePresence>
  );
};

export default AnimatedRoutes;