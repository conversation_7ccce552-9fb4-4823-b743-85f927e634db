import axios from 'axios';
import { API_URL } from '@/config/constants';
import { authService } from './authService';

// Créer une instance axios avec la configuration de base
const axiosInstance = axios.create({
  baseURL: API_URL,
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json'
  },
  timeout: 10000 // Timeout de 10 secondes
});

// Intercepteur pour ajouter automatiquement le token d'authentification
axiosInstance.interceptors.request.use(
  (config) => {
    const token = authService.getAccessToken();
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
      console.log(`AdminDashboardService - Request to ${config.url} with auth token`);
    } else {
      console.warn(`AdminDashboardService - Request to ${config.url} without auth token`);
    }
    return config;
  },
  (error) => {
    console.error('AdminDashboardService - Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Intercepteur pour gérer les réponses et erreurs
axiosInstance.interceptors.response.use(
  (response) => {
    console.log(`AdminDashboardService - Response from ${response.config.url}:`, response.status);
    return response;
  },
  (error) => {
    console.error('AdminDashboardService - Response error:', error);

    if (error.response?.status === 401) {
      console.warn('AdminDashboardService - Token expiré, redirection vers login');
      authService.logout();
      window.location.href = '/login';
    }

    return Promise.reject(error.response?.data || error);
  }
);

const adminDashboardService = {
  /**
   * Récupère les données du tableau de bord Admin avec filtres de période
   * @param {string} period - Période : 'today' | '1h' | '24h' | '7d' | '30d'
   * @param {boolean} manualRefresh - Mise à jour manuelle
   * @returns {Promise<Object>} - Données du tableau de bord admin
   */
  async getAdminDashboard(period = 'today', manualRefresh = true) {
    try {
      console.log(`AdminDashboardService - Récupération du tableau de bord Admin (période: ${period}, manuel: ${manualRefresh})...`);

      // URL selon votre guide : GET /api/bi/admin/dashboard/
      const params = new URLSearchParams({
        period: period,
        manual_refresh: manualRefresh.toString()
      });

      const response = await axiosInstance.get(`/bi/admin/dashboard/?${params}`);
      console.log('AdminDashboardService - Tableau de bord Admin récupéré avec succès:', response.data);

      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('AdminDashboardService - Erreur lors de la récupération du tableau de bord Admin:', error);

      // Données mockées en cas d'erreur selon votre guide
      console.log('AdminDashboardService - Utilisation des données mockées pour le tableau de bord Admin');

      const mockAdminDashboardData = {
        timestamp: new Date().toISOString(),
        admin_id: "admin_123",
        admin_name: "Admin Test",
        is_team_leader: true,

        // Cartes de métriques selon votre guide
        metric_cards: [
          {
            title: "Équipes gérées",
            subtitle: "Total des équipes sous votre responsabilité",
            value: period === 'today' ? 3 : period === '1h' ? 2 : period === '24h' ? 3 : period === '7d' ? 4 : 5,
            trend: `${period === 'today' ? 3 : period === '1h' ? 2 : period === '24h' ? 3 : period === '7d' ? 4 : 5} équipes`,
            trend_period: "total",
            icon: "users",
            color: "#3B82F6",
            period: period,
            manual_refresh: manualRefresh,
            last_updated: new Date().toISOString()
          },
          {
            title: "Membres d'équipe",
            subtitle: "Total des employés dans vos équipes",
            value: period === 'today' ? 12 : period === '1h' ? 8 : period === '24h' ? 12 : period === '7d' ? 15 : 18,
            trend: `${period === 'today' ? 12 : period === '1h' ? 8 : period === '24h' ? 12 : period === '7d' ? 15 : 18} membres`,
            trend_period: "total",
            icon: "user-group",
            color: "#10B981",
            period: period,
            manual_refresh: manualRefresh,
            last_updated: new Date().toISOString()
          },
          {
            title: "Progression moyenne",
            subtitle: "Progression de vos équipes",
            value: period === 'today' ? "67.5%" : period === '1h' ? "45.2%" : period === '24h' ? "67.5%" : period === '7d' ? "72.8%" : "78.3%",
            trend: `${period === 'today' ? "67.5%" : period === '1h' ? "45.2%" : period === '24h' ? "67.5%" : period === '7d' ? "72.8%" : "78.3%"} de progression`,
            trend_period: "moyenne",
            icon: "trending-up",
            color: "#8B5CF6",
            period: period,
            manual_refresh: manualRefresh,
            last_updated: new Date().toISOString()
          }
        ],

        // Graphiques selon votre guide
        charts: {
          events_distribution: {
            type: "pie",
            title: period === 'today' ? "Distribution des Événements d'Équipe par Statut - Aujourd'hui" : `Distribution des Événements d'Équipe - ${period}`,
            subtitle: period === 'today' ? "Répartition des événements (aujourd'hui)" : `Période: ${period}`,
            data: [
              {
                name: "À faire",
                value: period === 'today' ? 8 : period === '1h' ? 3 : period === '24h' ? 8 : period === '7d' ? 12 : 15,
                color: "#3B82F6",
                percentage: period === 'today' ? 40.0 : period === '1h' ? 30.0 : period === '24h' ? 40.0 : period === '7d' ? 42.9 : 45.5
              },
              {
                name: "En cours",
                value: period === 'today' ? 7 : period === '1h' ? 4 : period === '24h' ? 7 : period === '7d' ? 10 : 12,
                color: "#F59E0B",
                percentage: period === 'today' ? 35.0 : period === '1h' ? 40.0 : period === '24h' ? 35.0 : period === '7d' ? 35.7 : 36.4
              },
              {
                name: "Terminés",
                value: period === 'today' ? 5 : period === '1h' ? 3 : period === '24h' ? 5 : period === '7d' ? 6 : 6,
                color: "#10B981",
                percentage: period === 'today' ? 25.0 : period === '1h' ? 30.0 : period === '24h' ? 25.0 : period === '7d' ? 21.4 : 18.2
              }
            ],
            legend: [
              { "label": "À faire", "color": "#3B82F6" },
              { "label": "En cours", "color": "#F59E0B" },
              { "label": "Terminés", "color": "#10B981" }
            ],
            period: period,
            period_name: period === 'today' ? 'Aujourd\'hui' : period === '1h' ? 'Dernière heure' : period === '24h' ? 'Dernières 24h' : period === '7d' ? 'Derniers 7 jours' : 'Derniers 30 jours',
            manual_refresh: manualRefresh,
            last_updated: new Date().toISOString()
          },
          tasks_distribution: {
            type: "pie",
            title: period === 'today' ? "Distribution des Tâches d'Équipe par Statut - Aujourd'hui" : `Distribution des Tâches d'Équipe - ${period}`,
            subtitle: period === 'today' ? "Répartition des tâches (aujourd'hui)" : `Période: ${period}`,
            data: [
              {
                name: "À faire",
                value: period === 'today' ? 15 : period === '1h' ? 8 : period === '24h' ? 15 : period === '7d' ? 20 : 25,
                color: "#3B82F6",
                percentage: period === 'today' ? 50.0 : period === '1h' ? 44.4 : period === '24h' ? 50.0 : period === '7d' ? 52.6 : 55.6
              },
              {
                name: "En cours",
                value: period === 'today' ? 10 : period === '1h' ? 6 : period === '24h' ? 10 : period === '7d' ? 12 : 14,
                color: "#F59E0B",
                percentage: period === 'today' ? 33.3 : period === '1h' ? 33.3 : period === '24h' ? 33.3 : period === '7d' ? 31.6 : 31.1
              },
              {
                name: "Terminées",
                value: period === 'today' ? 5 : period === '1h' ? 4 : period === '24h' ? 5 : period === '7d' ? 6 : 6,
                color: "#10B981",
                percentage: period === 'today' ? 16.7 : period === '1h' ? 22.2 : period === '24h' ? 16.7 : period === '7d' ? 15.8 : 13.3
              }
            ],
            legend: [
              { "label": "À faire", "color": "#3B82F6" },
              { "label": "En cours", "color": "#F59E0B" },
              { "label": "Terminées", "color": "#10B981" }
            ],
            period: period,
            period_name: period === 'today' ? 'Aujourd\'hui' : period === '1h' ? 'Dernière heure' : period === '24h' ? 'Dernières 24h' : period === '7d' ? 'Derniers 7 jours' : 'Derniers 30 jours',
            manual_refresh: manualRefresh,
            last_updated: new Date().toISOString()
          }
        },

        // Statistiques détaillées selon votre guide
        detailed_stats: {
          team_management: {
            total_teams: period === 'today' ? 3 : period === '1h' ? 2 : period === '24h' ? 3 : period === '7d' ? 4 : 5,
            total_team_members: period === 'today' ? 12 : period === '1h' ? 8 : period === '24h' ? 12 : period === '7d' ? 15 : 18,
            teams_managed: ["team_id_1", "team_id_2", "team_id_3"],
            most_active_team: {
              id: "team_id_2",
              name: "Équipe Marketing"
            },
            average_progress: period === 'today' ? 67.5 : period === '1h' ? 45.2 : period === '24h' ? 67.5 : period === '7d' ? 72.8 : 78.3
          },
          events_activity: {
            total: period === 'today' ? 20 : period === '1h' ? 10 : period === '24h' ? 20 : period === '7d' ? 28 : 33,
            created_in_period: period === 'today' ? 5 : period === '1h' ? 2 : period === '24h' ? 5 : period === '7d' ? 8 : 10,
            completed_in_period: period === 'today' ? 3 : period === '1h' ? 1 : period === '24h' ? 3 : period === '7d' ? 5 : 6,
            pending: period === 'today' ? 8 : period === '1h' ? 4 : period === '24h' ? 8 : period === '7d' ? 12 : 15,
            completion_rate: period === 'today' ? 25.0 : period === '1h' ? 20.0 : period === '24h' ? 25.0 : period === '7d' ? 27.8 : 30.3
          },
          tasks_activity: {
            total: period === 'today' ? 30 : period === '1h' ? 18 : period === '24h' ? 30 : period === '7d' ? 38 : 45,
            created_in_period: period === 'today' ? 8 : period === '1h' ? 3 : period === '24h' ? 8 : period === '7d' ? 12 : 15,
            completed_in_period: period === 'today' ? 5 : period === '1h' ? 2 : period === '24h' ? 5 : period === '7d' ? 7 : 8,
            pending: period === 'today' ? 15 : period === '1h' ? 8 : period === '24h' ? 15 : period === '7d' ? 20 : 25,
            completion_rate: period === 'today' ? 16.67 : period === '1h' ? 16.67 : period === '24h' ? 16.67 : period === '7d' ? 18.42 : 17.78
          }
        },

        // Métadonnées selon votre guide
        metadata: {
          last_updated: new Date().toISOString(),
          data_source: "AdminActivityTracker",
          refresh_mode: "manual",
          refresh_interval: null,
          dashboard_title: period === 'today' ? "Tableau de Bord Admin - Aujourd'hui" : `Tableau de Bord Admin - ${period}`,
          dashboard_subtitle: period === 'today' ? "Analyses de vos activités d'équipe (aujourd'hui)" : `Analyses pour la période: ${period}`,
          current_period: {
            period: period,
            period_name: period === 'today' ? 'Aujourd\'hui' : period === '1h' ? 'Dernière heure' : period === '24h' ? 'Dernières 24h' : period === '7d' ? 'Derniers 7 jours' : 'Derniers 30 jours',
            manual_refresh: manualRefresh
          },
          available_periods: [
            { "value": "today", "label": "Aujourd'hui" },
            { "value": "1h", "label": "Dernière heure" },
            { "value": "24h", "label": "Dernières 24h" },
            { "value": "7d", "label": "Derniers 7 jours" },
            { "value": "30d", "label": "Derniers 30 jours" }
          ],
          features: {
            period_filtering: true,
            manual_refresh: true,
            team_analytics: true,
            real_time_data: true
          }
        }
      };

      return {
        success: false,
        error: error.message || 'Erreur lors de la récupération du tableau de bord admin',
        data: mockAdminDashboardData
      };
    }
  },

  /**
   * Récupère les données de débogage des activités admin
   * @returns {Promise<Object>} - Données de débogage
   */
  async getAdminDebugData() {
    try {
      console.log('AdminDashboardService - Récupération des données de débogage admin...');

      // URL selon votre guide : GET /api/bi/admin/debug/
      const response = await axiosInstance.get('/bi/admin/debug/');
      console.log('AdminDashboardService - Données de débogage admin récupérées:', response.data);

      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('AdminDashboardService - Erreur lors de la récupération des données de débogage admin:', error);

      return {
        success: false,
        error: error.message || 'Erreur lors de la récupération des données de débogage admin'
      };
    }
  }
};

export default adminDashboardService;
