import axios from 'axios';

import { API_URL } from '@/config/constants';

export const authService = {
    async login(credentials) {
        try {
            console.log('Envoi des identifiants:', { email: credentials.email });

            // Nettoyer les identifiants avant l'envoi
            const cleanedCredentials = {
                email: credentials.email.trim(),
                password: credentials.password
            };

            console.log('Envoi des identifiants nettoyés à:', `${API_URL}/login/`);
            const response = await axios.post(`${API_URL}/login/`, cleanedCredentials);
            console.log('Réponse brute du serveur:', response.data);

            if (!response.data) {
                console.error('Réponse du serveur vide');
                throw new Error('Réponse du serveur vide');
            }

            if (!response.data.access || !response.data.user) {
                console.error('Données de connexion incomplètes:', response.data);
                throw new Error('Données de connexion incomplètes');
            }

            // Vérifier que toutes les données utilisateur requises sont présentes
            const { id, email, name, role } = response.data.user;
            if (!id || !email || !name || !role) {
                console.error('Données utilisateur incomplètes:', response.data.user);
                throw new Error('Données utilisateur incomplètes');
            }

            // Si le backend indique que le mot de passe temporaire a déjà été utilisé
            if (response.data.should_reset) {
                throw new Error('Ce mot de passe temporaire a déjà été utilisé. Veuillez réinitialiser votre mot de passe via "Mot de passe oublié".');
            }

            // Stocker les informations de l'utilisateur
            const userData = {
                id,
                email,
                name,
                role,
                temp_password_required: response.data.user.temp_password_required || false,
                temp_password_used: response.data.user.temp_password_used || false
            };

            // Ajouter les équipes de l'utilisateur s'il est un employé
            if (role === 'employee' && response.data.user.teams) {
                userData.teams = response.data.user.teams;
                console.log('Équipes de l\'utilisateur:', userData.teams);
            }

            console.log('Stockage des données utilisateur:', userData);

            // Vérifier que les tokens sont bien présents
            if (!response.data.access) {
                console.error('Token d\'accès manquant dans la réponse');
                throw new Error('Token d\'accès manquant dans la réponse');
            }

            if (!response.data.refresh) {
                console.error('Token de rafraîchissement manquant dans la réponse');
                throw new Error('Token de rafraîchissement manquant dans la réponse');
            }

            // Nettoyer le localStorage avant de stocker les nouvelles données
            localStorage.removeItem('authToken');
            localStorage.removeItem('refreshToken');
            localStorage.removeItem('user');

            // Stocker les nouvelles données
            localStorage.setItem('authToken', response.data.access);
            localStorage.setItem('refreshToken', response.data.refresh);
            localStorage.setItem('user', JSON.stringify(userData));

            console.log('Données d\'authentification stockées avec succès');

            // Si c'est un nouveau utilisateur avec un mot de passe temporaire non utilisé
            if (userData.temp_password_required && !userData.temp_password_used && !response.data.user.is_superuser) {
                console.log('Utilisateur avec mot de passe temporaire détecté');
                return { ...response.data, shouldChangePassword: true, user: userData };
            }

            return { ...response.data, user: userData };
        } catch (error) {
            console.error('Erreur détaillée lors de la connexion:', error);

            if (error.response) {
                console.error('Statut de l\'erreur:', error.response.status);
                console.error('Données d\'erreur:', error.response.data);
                console.error('URL de la requête:', error.config?.url);
                console.error('Méthode de la requête:', error.config?.method);
            } else if (error.request) {
                console.error('Pas de réponse reçue:', error.request);
            } else {
                console.error('Erreur de configuration:', error.message);
            }

            if (error.response?.status === 404) {
                throw new Error('URL de connexion incorrecte. Veuillez contacter l\'administrateur.');
            } else if (error.response?.status === 401) {
                throw new Error(error.response.data?.message || 'Identifiants incorrects. Veuillez vérifier votre email et mot de passe.');
            } else if (!error.response && error.request) {
                throw new Error('Impossible de se connecter au serveur. Veuillez vérifier votre connexion internet.');
            }

            throw error.response?.data || { message: 'Une erreur est survenue lors de la connexion' };
        }
    },

    async changePassword(data) {
        try {
            console.log('Tentative de changement de mot de passe avec les données:', {
                current_password: data.current_password ? '******' : 'non fourni',
                new_password: data.new_password ? '******' : 'non fourni',
                confirm_password: data.confirm_password ? '******' : 'non fourni'
            });

            // Vérifier si les mots de passe correspondent (seulement si confirm_password est fourni)
            if (data.confirm_password !== undefined && data.confirm_password !== null) {
                const newPassword = data.new_password ? data.new_password.trim() : '';
                const confirmPassword = data.confirm_password ? data.confirm_password.trim() : '';

                if (newPassword !== confirmPassword) {
                    console.log('Mots de passe différents dans authService:', {
                        newPasswordLength: newPassword.length,
                        confirmPasswordLength: confirmPassword.length,
                        newPasswordChars: newPassword.split('').map(c => c.charCodeAt(0)),
                        confirmPasswordChars: confirmPassword.split('').map(c => c.charCodeAt(0))
                    });
                    throw { error: 'Les mots de passe ne correspondent pas' };
                }
            }

            // Vérifier la complexité du mot de passe
            if (data.new_password && data.new_password.length < 8) {
                throw { error: 'Le mot de passe doit contenir au moins 8 caractères' };
            }

            // Vérifier si le nouveau mot de passe est différent de l'ancien
            if (data.current_password === data.new_password) {
                throw { error: 'Le nouveau mot de passe doit être différent de l\'ancien' };
            }

            const currentUser = this.getCurrentUser();
            console.log('Utilisateur actuel:', {
                id: currentUser?.id,
                role: currentUser?.role,
                temp_password_required: currentUser?.temp_password_required
            });

            // Utiliser la même URL pour tous les utilisateurs, y compris le super admin
            // Le backend devrait gérer les permissions en fonction du rôle
            const url = `${API_URL}/profile/change-password/`;

            console.log('URL utilisée pour le changement de mot de passe:', url);

            console.log('En-têtes pour la requête de changement de mot de passe:', {
                ...this.getAuthHeader(),
                'Content-Type': 'application/json'
            });

            try {
                // Préparer les données à envoyer au backend (sans confirm_password et en nettoyant les espaces)
                const dataToSend = {
                    current_password: data.current_password ? data.current_password.trim() : '',
                    new_password: data.new_password ? data.new_password.trim() : ''
                };

                console.log('Données envoyées au backend:', {
                    current_password: '******',
                    new_password: '******'
                });

                const response = await axios.post(
                    url,
                    dataToSend,
                    {
                        headers: {
                            ...this.getAuthHeader(),
                            'Content-Type': 'application/json'
                        }
                    }
                );

                console.log('Réponse du serveur pour le changement de mot de passe:', response.data);
                console.log('Statut de la réponse:', response.status);

                return response.data;
            } catch (axiosError) {
                console.error('Erreur Axios lors du changement de mot de passe:', axiosError);

                if (axiosError.response) {
                    console.error('Statut de l\'erreur:', axiosError.response.status);
                    console.error('Données d\'erreur:', axiosError.response.data);

                    // Si l'erreur est 401 pour le super admin, c'est probablement une restriction de permission
                    if (axiosError.response.status === 401 && currentUser?.role === 'super_admin') {
                        throw {
                            error: 'Le super administrateur ne peut pas changer son mot de passe via cette interface. Veuillez contacter le support technique.'
                        };
                    }

                    throw axiosError.response.data || { error: 'Échec du changement de mot de passe' };
                }

                throw { error: 'Erreur de connexion au serveur' };
            }
        } catch (error) {
            console.error('Erreur détaillée lors du changement de mot de passe:', error);

            // Afficher plus de détails sur l'erreur pour le débogage
            if (error.response) {
                console.error('Statut de l\'erreur:', error.response.status);
                console.error('Données d\'erreur:', error.response.data);
                console.error('URL de la requête:', error.config?.url);
                console.error('Méthode de la requête:', error.config?.method);
            } else if (error.request) {
                console.error('Pas de réponse reçue:', error.request);
            } else {
                console.error('Erreur de configuration:', error.message);
            }

            if (error.response?.status === 401) {
                throw { error: 'Mot de passe actuel incorrect' };
            } else if (error.response?.status === 400) {
                throw { error: error.response.data?.error || 'Données invalides pour le changement de mot de passe' };
            } else if (!error.response && error.request) {
                throw { error: 'Impossible de se connecter au serveur' };
            } else if (error.error) {
                throw error; // Si l'erreur a déjà été formatée
            }

            throw error.response?.data || { error: 'Échec du changement de mot de passe' };
        }
    },

    async register(userData) {
        try {
            const response = await axios.post(`${API_URL}/register/`, userData);
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'An error occurred during registration' };
        }
    },

    logout() {
        localStorage.removeItem('authToken');
        localStorage.removeItem('refreshToken');
        localStorage.removeItem('user');
    },

    getAccessToken() {
        console.log('Getting access token from localStorage');
        const token = localStorage.getItem('authToken');
        console.log('Token found:', !!token);
        return token;
    },

    getCurrentUser() {
        console.log('Getting current user from localStorage');
        const userStr = localStorage.getItem('user');
        const token = localStorage.getItem('authToken');
        console.log('User string found:', !!userStr);
        console.log('Token found:', !!token);

        // Si pas de token ou pas de données utilisateur, considérer comme non authentifié
        if (!token || !userStr) {
            console.log('Utilisateur non authentifié: token ou données utilisateur manquants');
            return null;
        }

        try {
            const user = JSON.parse(userStr);
            console.log('Parsed user:', { id: user.id, role: user.role });
            return user;
        } catch (error) {
            console.error('Error parsing user data:', error);
            this.logout(); // Clear invalid data
            return null;
        }
    },

    getAuthHeader() {
        const token = this.getAccessToken();
        console.log('Creating auth header with token:', !!token);

        if (!token) {
            console.warn('Tentative de création d\'en-têtes d\'authentification sans token');
            return {};
        }

        return {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        };
    },

    getRefreshToken() {
        return localStorage.getItem('refreshToken');
    },

    isAuthenticated() {
        return !!this.getAccessToken();
    },

    isSuperAdmin() {
        const user = this.getCurrentUser();
        return user?.role === 'super_admin';
    },

    getRedirectPath() {
        const user = this.getCurrentUser();
        if (!user) return '/login';

        switch (user.role) {
            case 'super_admin':
                return '/super-admin';
            case 'admin':
                return '/dashboard';
            case 'employee':
                return '/dashboard';
            case 'client':
                return '/dashboard';
            default:
                return '/login';
        }
    },

    // Fonction pour rafraîchir le token
    async refreshToken() {
        try {
            const refreshToken = this.getRefreshToken();
            if (!refreshToken) {
                console.error('Pas de refresh token disponible');
                this.logout();
                return false;
            }

            console.log('Tentative de rafraîchissement du token...');
            console.log('URL de rafraîchissement:', `${API_URL}/refresh-token/`);

            // Utiliser l'URL correcte pour le rafraîchissement du token
            const response = await axios.post(`${API_URL}/refresh-token/`, {
                refresh: refreshToken
            }, {
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            console.log('Réponse du serveur pour le rafraîchissement:', response.data);

            if (response.data && response.data.access) {
                // Stocker le nouveau token
                localStorage.setItem('authToken', response.data.access);

                // Si un nouveau refresh token est fourni, le stocker également
                if (response.data.refresh) {
                    localStorage.setItem('refreshToken', response.data.refresh);
                }

                console.log('Token rafraîchi avec succès');
                return true;
            } else {
                console.error('Réponse de rafraîchissement invalide:', response.data);
                this.logout();
                return false;
            }
        } catch (error) {
            console.error('Erreur lors du rafraîchissement du token:', error);

            // Afficher plus de détails sur l'erreur pour le débogage
            if (error.response) {
                console.error('Statut de l\'erreur:', error.response.status);
                console.error('Données d\'erreur:', error.response.data);
                console.error('URL de la requête:', error.config?.url);
                console.error('Méthode de la requête:', error.config?.method);

                // Si le token de rafraîchissement est invalide ou expiré (401)
                if (error.response.status === 401) {
                    console.error('Token de rafraîchissement invalide ou expiré');
                    this.logout();
                    return false;
                }
            } else if (error.request) {
                console.error('Pas de réponse reçue:', error.request);
            } else {
                console.error('Erreur de configuration:', error.message);
            }

            console.error('Configuration complète:', error.config);
            this.logout();
            return false;
        }
    }
};

// Export par défaut
export default authService;

// Export nommé pour la compatibilité
export const getAuthHeader = () => authService.getAuthHeader();