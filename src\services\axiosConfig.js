import axios from 'axios';
import { authService } from './authService';
import { API_URL } from '@/config/constants';

// Créer une instance Axios avec la configuration de base
const axiosInstance = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});

// Intercepteur pour ajouter le token d'authentification à chaque requête
axiosInstance.interceptors.request.use(
  (config) => {
    // Récupérer le token depuis le localStorage
    const token = localStorage.getItem('authToken');

    // Vérifier si la requête est pour l'authentification (login, refresh token)
    const isAuthRequest =
      config.url.includes('/login/') ||
      config.url.includes('/refresh-token/') ||
      config.url.includes('/register/') ||
      config.url.includes('/forgot-password/') ||
      config.url.includes('/reset-password/');

    if (token && !isAuthRequest) {
      // Ajouter le token d'authentification aux en-têtes
      config.headers['Authorization'] = `Bearer ${token}`;
      console.log(`Axios request to ${config.url} with auth token`);
    } else if (!isAuthRequest) {
      console.warn(`Axios request to ${config.url} without auth token`);

      // Vérifier si l'utilisateur existe dans le localStorage
      const userStr = localStorage.getItem('user');
      if (!userStr) {
        console.warn('No user found in localStorage during request');
      } else {
        console.warn('User exists in localStorage but no auth token found');
      }
    } else {
      console.log(`Auth request to ${config.url}, no token needed`);
    }

    return config;
  },
  (error) => {
    console.error('Axios request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Intercepteur pour gérer les erreurs de réponse
axiosInstance.interceptors.response.use(
  (response) => {
    console.log(`Axios response from ${response.config.url} successful:`, response.status);
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // Vérifier si la requête est pour l'authentification (login, refresh token)
    const isAuthRequest = originalRequest?.url && (
      originalRequest.url.includes('/login/') ||
      originalRequest.url.includes('/refresh-token/') ||
      originalRequest.url.includes('/register/') ||
      originalRequest.url.includes('/forgot-password/') ||
      originalRequest.url.includes('/reset-password/')
    );

    console.error(`Axios response error for ${originalRequest?.url || 'unknown URL'}:`, {
      status: error.response?.status,
      data: error.response?.data,
      message: error.message,
      isAuthRequest
    });

    // Si l'erreur est 401 (non authentifié) et que nous n'avons pas déjà essayé de rafraîchir le token
    // et que ce n'est pas une requête d'authentification
    if (error.response && error.response.status === 401 && !originalRequest._retry && !isAuthRequest) {
      console.log('Unauthorized error detected, attempting token refresh');
      originalRequest._retry = true;

      try {
        // Vérifier si un token de rafraîchissement existe
        const refreshToken = localStorage.getItem('refreshToken');
        if (!refreshToken) {
          console.error('No refresh token available, cannot refresh authentication');
          authService.logout();
          window.location.href = '/login?error=session_expired';
          return Promise.reject(error);
        }

        // Essayer de rafraîchir le token
        console.log('Attempting to refresh token...');
        const refreshed = await authService.refreshToken();

        if (refreshed) {
          // Si le token a été rafraîchi avec succès, mettre à jour le token dans la requête originale
          const token = localStorage.getItem('authToken');
          console.log('Token refreshed successfully, retrying original request');
          originalRequest.headers['Authorization'] = `Bearer ${token}`;

          // Réessayer la requête originale avec le nouveau token
          return axiosInstance(originalRequest);
        } else {
          // Si le rafraîchissement a échoué, rediriger vers la page de connexion
          console.log('Token refresh failed, redirecting to login');
          authService.logout();
          window.location.href = '/login?error=refresh_failed';
          return Promise.reject(error);
        }
      } catch (refreshError) {
        console.error('Error during token refresh:', refreshError);
        authService.logout();
        window.location.href = '/login?error=refresh_error';
        return Promise.reject(error);
      }
    }

    // Pour les erreurs d'authentification (401) sur les requêtes d'authentification
    if (error.response && error.response.status === 401 && isAuthRequest) {
      console.error('Authentication request failed:', error.response.data);
      // Ne pas rediriger, laisser le composant gérer l'erreur
      error.authenticationFailed = true;
      return Promise.reject(error);
    }

    // Pour les erreurs 403 (Forbidden), afficher un message plus clair
    if (error.response && error.response.status === 403) {
      console.error('Permission denied error:', error.response.data);
      // Ne pas rediriger, mais enrichir l'erreur
      error.permissionDenied = true;
    }

    // Pour les autres erreurs, simplement les rejeter
    return Promise.reject(error);
  }
);

export default axiosInstance;
