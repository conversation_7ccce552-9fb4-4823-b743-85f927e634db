import axios from 'axios';
import { API_URL } from '@/config/constants';
import { mockBiData } from '@/mocks/biMock';

// Créer une instance axios avec la configuration de base
const axiosInstance = axios.create({
  baseURL: API_URL,
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json'
  },
  timeout: 10000 // Timeout de 10 secondes
});

// Ajouter un intercepteur pour les requêtes
axiosInstance.interceptors.request.use(
  (config) => {
    console.log('BiService - Making request:', {
      url: config.url,
      method: config.method,
      data: config.data
    });

    // Récupérer le token depuis le localStorage
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    } else {
      console.error('BiService - No auth token found');
    }
    return config;
  },
  (error) => {
    console.error('BiService - Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Ajouter un intercepteur pour les réponses
axiosInstance.interceptors.response.use(
  (response) => {
    console.log('BiService - Response received:', {
      url: response.config.url,
      status: response.status,
      data: response.data
    });
    return response;
  },
  async (error) => {
    console.error('BiService - Response error:', {
      url: error.config?.url,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message
    });

    // Journaliser les détails de l'erreur pour le débogage
    if (error.response) {
      console.error('Données d\'erreur:', error.response.data);
      console.error('Statut d\'erreur:', error.response.status);
      console.error('En-têtes d\'erreur:', error.response.headers);
    } else if (error.request) {
      console.error('Requête sans réponse:', error.request);
    } else {
      console.error('Message d\'erreur:', error.message);
    }

    // Si l'erreur est due à un token expiré (401), essayer de rafraîchir le token
    if (error.response?.status === 401) {
      console.error('BiService - Authentication error');

      // Vérifier si nous sommes sur la page de connexion ou d'inscription
      const currentPath = window.location.pathname;
      if (currentPath === '/login' || currentPath === '/register') {
        console.log('BiService - Ignoring auth error on login/register page');
        return Promise.reject(error);
      }

      // Vérifier si nous avons déjà essayé de rafraîchir le token pour cette requête
      if (error.config.__isRetryAttempt) {
        console.log('BiService - Already attempted to refresh token, redirecting to login');
        // Importer le service d'authentification
        const { authService } = await import('@/services/authService');
        authService.logout();
        window.location.href = '/login';
        return Promise.reject(error);
      }

      try {
        // Importer le service d'authentification
        const { authService } = await import('@/services/authService');

        // Tenter de rafraîchir le token
        const refreshed = await authService.refreshToken();

        if (refreshed) {
          console.log('Token rafraîchi, nouvelle tentative de la requête...');
          // Récupérer le nouveau token
          const newToken = authService.getAccessToken();
          // Mettre à jour le token dans la requête originale
          error.config.headers.Authorization = `Bearer ${newToken}`;
          // Marquer cette requête comme une tentative de rafraîchissement
          error.config.__isRetryAttempt = true;
          // Retenter la requête originale avec le nouveau token
          return axios(error.config);
        } else {
          console.log('BiService - Token refresh failed, redirecting to login');
          window.location.href = '/login';
        }
      } catch (refreshError) {
        console.error('Échec du rafraîchissement du token:', refreshError);
        // Rediriger vers la page de connexion en cas d'échec
        window.location.href = '/login';
      }
    }

    return Promise.reject(error.response?.data || error);
  }
);

const biService = {
  // ❌ SUPPRIMÉ - Ancien endpoint incorrect /bi/metrics/
  // Utiliser seulement getSuperAdminDashboard() avec /api/bi/super-admin/dashboard/

  // ❌ SUPPRIMÉ - getDashboardStats() utilisait l'ancien endpoint
  // Utiliser seulement getSuperAdminDashboard() avec /api/bi/super-admin/dashboard/

  // ❌ SUPPRIMÉ - getRecentActivity() utilisait l'ancien endpoint /bi/recent-activity/
  // Utiliser seulement getSuperAdminDashboard() avec /api/bi/super-admin/dashboard/

  // ❌ SUPPRIMÉ - getSystemStatus() utilisait l'ancien endpoint /bi/system-status/
  // Utiliser seulement getSuperAdminDashboard() avec /api/bi/super-admin/dashboard/

  // ❌ SUPPRIMÉ - getHistoricalData() utilisait l'ancien endpoint /bi/historical-data/
  // Utiliser seulement getSuperAdminDashboard() avec /api/bi/super-admin/dashboard/

  /**
   * Récupère les données du tableau de bord Super Admin avec filtres de période
   * @param {string} period - Période : 'today' | '1h' | '24h' | '7d' | '30d'
   * @param {boolean} manualRefresh - Mise à jour manuelle
   * @returns {Promise<Object>} - Données du tableau de bord
   */
  async getSuperAdminDashboard(period = 'today', manualRefresh = true) {
    try {
      console.log(`BiService - Récupération du tableau de bord Super Admin (période: ${period}, manuel: ${manualRefresh})...`);

      // ✅ CORRECT - URL avec paramètres de période et refresh manuel
      const params = new URLSearchParams({
        period: period,
        manual_refresh: manualRefresh.toString()
      });

      const response = await axiosInstance.get(`/bi/super-admin/dashboard/?${params}`);
      console.log('BiService - Tableau de bord Super Admin récupéré avec succès:', response.data);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('BiService - Erreur lors de la récupération du tableau de bord Super Admin:', error);

      // ✅ CORRECT - Utiliser seulement l'endpoint : GET http://localhost:8000/api/bi/super-admin/dashboard/
      // ❌ INCORRECT - Ne pas utiliser /api/bi/historical-data/ ou /api/bi/metrics/

      // Utiliser des données mockées en cas d'erreur
      console.log('BiService - Utilisation des données mockées pour le tableau de bord Super Admin');

      // Données mockées basées sur la structure exacte de votre API
      const mockDashboardData = {
        timestamp: new Date().toISOString(),
        is_realtime: period === 'today',
        metric_cards: [
          {
            title: "Nombre total d'utilisateurs",
            value: 29,
            trend: "+100%",
            trend_period: "ce mois",
            icon: "users",
            color: "#3B82F6"
          },
          {
            title: "Utilisateurs actifs",
            subtitle: period === 'today' ? "Connectés (aujourd'hui)" : `Période: ${period}`,
            value: period === 'today' ? 4 : period === '1h' ? 2 : period === '24h' ? 6 : period === '7d' ? 12 : 18,
            trend: period === 'today' ? "+13.8%" : "+10%",
            trend_period: period === 'today' ? "aujourd'hui" : period,
            icon: "user-check",
            color: "#10B981",
            manual_refresh: true,
            last_updated: new Date().toISOString(),
            period: period,
            data_source: period === 'today' ? "DailyLoginTracker" : "User.last_login"
          },
          {
            title: "Utilisateurs inactifs",
            subtitle: period === 'today' ? "Non connectés (aujourd'hui)" : `Période: ${period}`,
            value: period === 'today' ? 25 : period === '1h' ? 27 : period === '24h' ? 23 : period === '7d' ? 17 : 11,
            trend: "86.2%",
            trend_period: period === 'today' ? "aujourd'hui" : period,
            icon: "user-x",
            color: "#EF4444",
            manual_refresh: true,
            last_updated: new Date().toISOString(),
            period: period,
            data_source: period === 'today' ? "DailyLoginTracker" : "User.last_login"
          }
        ],
        charts: {
          active_vs_inactive: {
            type: "doughnut",
            title: period === 'today' ? "Connexions - Aujourd'hui" : `Utilisateurs Actifs - ${period}`,
            subtitle: period === 'today' ? "Utilisateurs connectés vs non connectés (aujourd'hui)" : `Période: ${period}`,
            data: [
              {
                "name": period === 'today' ? "Connectés (aujourd'hui)" : "Actifs",
                "value": period === 'today' ? 4 : period === '1h' ? 2 : period === '24h' ? 6 : period === '7d' ? 12 : 18,
                "color": "#10B981"
              },
              {
                "name": period === 'today' ? "Non connectés (aujourd'hui)" : "Inactifs",
                "value": period === 'today' ? 25 : period === '1h' ? 27 : period === '24h' ? 23 : period === '7d' ? 17 : 11,
                "color": "#EF4444"
              }
            ],
            legend: [
              { "label": period === 'today' ? "Connectés (aujourd'hui)" : "Actifs", "color": "#10B981" },
              { "label": period === 'today' ? "Non connectés (aujourd'hui)" : "Inactifs", "color": "#EF4444" }
            ],
            period: period,
            period_name: period === 'today' ? 'Aujourd\'hui' : period === '1h' ? 'Dernière heure' : period === '24h' ? 'Dernières 24h' : period === '7d' ? 'Derniers 7 jours' : 'Derniers 30 jours',
            manual_refresh: true,
            last_updated: new Date().toISOString()
          },
          role_distribution: {
            type: "bar",
            title: "Distribution des Utilisateurs par Rôle",
            data: [
              { "name": "Super Admin", "value": 2, "color": "#8B5CF6" },
              { "name": "Admin", "value": 7, "color": "#3B82F6" },
              { "name": "Employés", "value": 15, "color": "#10B981" },
              { "name": "Clients", "value": 5, "color": "#F59E0B" }
            ],
            max_value: 15
          }
        },
        detailed_stats: {
          users_by_role: {
            super_admin: 2,
            admin: 7,
            employee: 15,
            client: 5
          },
          activity_stats: {
            total_users: 29,
            active_users: {
              last_24h: 4,
              last_7_days: 5,
              last_30_days: 7
            },
            inactive_users: 22,
            never_logged_in: 22,
            activity_rate: {
              last_24h: 13.79,
              last_7_days: 17.24,
              last_30_days: 24.14
            }
          },
          users_with_permissions: {
            super_admin: {
              count: 2,
              percentage: 6.9
            },
            admin: {
              count: 7,
              percentage: 24.14
            },
            employee: {
              count: 15,
              percentage: 51.72
            },
            client: {
              count: 5,
              percentage: 17.24
            }
          },
          engagement_metrics: {
            new_users_7d: 5,
            new_users_30d: 15,
            users_logged_today: 4, // Valeur fixe pour éviter les changements constants
            users_never_logged: 22,
            retention_rate: 24.14
          },
          trends: {
            total_users_trend: "+100%",
            active_users_trend: "+150.0%",
            inactive_users_trend: "0%"
          }
        },
        metadata: {
          last_updated: new Date().toISOString(),
          data_source: period === 'today' ? "DailyLoginTracker" : "User.last_login",
          refresh_mode: "manual",
          refresh_interval: null,
          dashboard_title: period === 'today' ? "Tableau de Bord Super Admin - Aujourd'hui" : `Tableau de Bord Super Admin - ${period}`,
          dashboard_subtitle: period === 'today' ? "Connexions et analyses (aujourd'hui)" : `Analyses pour la période: ${period}`,

          current_period: {
            period: period,
            period_name: period === 'today' ? 'Aujourd\'hui' : period === '1h' ? 'Dernière heure' : period === '24h' ? 'Dernières 24h' : period === '7d' ? 'Derniers 7 jours' : 'Derniers 30 jours',
            period_start: new Date().toISOString(),
            manual_refresh: true
          },

          available_periods: [
            { value: "today", label: "Aujourd'hui" },
            { value: "1h", label: "Dernière heure" },
            { value: "24h", label: "Dernières 24h" },
            { value: "7d", label: "Derniers 7 jours" },
            { value: "30d", label: "Derniers 30 jours" }
          ],

          features: {
            period_filtering: true,
            manual_refresh: true,
            real_time_data: period === 'today'
          },

          data_freshness: {
            login_data: period === 'today' ? "real_time" : "approximation",
            user_counts: "real_time",
            activity_stats: period === 'today' ? "real_time" : "approximation"
          }
        }
      };

      return {
        success: false,
        error: error.message || 'Erreur lors de la récupération du tableau de bord',
        data: mockDashboardData
      };
    }
  },

  /**
   * Récupère les statistiques de connexion en temps réel
   * @returns {Promise<Object>} - Statistiques de connexion
   */
  async getRealTimeLoginStats() {
    try {
      console.log('BiService - Récupération des statistiques de connexion en temps réel...');
      const response = await axiosInstance.get('/bi/realtime/login-stats/');
      console.log('BiService - Statistiques en temps réel récupérées:', response.data);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('BiService - Erreur lors de la récupération des statistiques en temps réel:', error);
      return {
        success: false,
        error: error.message || 'Erreur lors de la récupération des statistiques en temps réel'
      };
    }
  },

  /**
   * Récupère les données de débogage des connexions
   * @returns {Promise<Object>} - Données de débogage
   */
  async getDebugLoginData() {
    try {
      console.log('BiService - Récupération des données de débogage...');
      const response = await axiosInstance.get('/bi/debug/login-data/');
      console.log('BiService - Données de débogage récupérées:', response.data);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('BiService - Erreur lors de la récupération des données de débogage:', error);
      return {
        success: false,
        error: error.message || 'Erreur lors de la récupération des données de débogage'
      };
    }
  },

  /**
   * Récupère les métriques générales
   * @returns {Promise<Object>} - Métriques générales
   */
  async getMetrics() {
    try {
      console.log('BiService - Récupération des métriques générales...');
      const response = await axiosInstance.get('/bi/metrics/');
      console.log('BiService - Métriques générales récupérées:', response.data);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('BiService - Erreur lors de la récupération des métriques générales:', error);
      return {
        success: false,
        error: error.message || 'Erreur lors de la récupération des métriques générales'
      };
    }
  },

  /**
   * Récupère les données historiques
   * @param {string} dataType - Type de données
   * @param {string} period - Période
   * @returns {Promise<Object>} - Données historiques
   */
  async getHistoricalData(dataType, period) {
    try {
      console.log(`BiService - Récupération des données historiques (${dataType}, ${period})...`);
      const params = new URLSearchParams({
        data_type: dataType,
        period: period
      });
      const response = await axiosInstance.get(`/bi/historical-data/?${params}`);
      console.log('BiService - Données historiques récupérées:', response.data);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('BiService - Erreur lors de la récupération des données historiques:', error);
      return {
        success: false,
        error: error.message || 'Erreur lors de la récupération des données historiques'
      };
    }
  },

  /**
   * Génère des données mockées pour les graphiques historiques
   * @param {string} dataType - Type de données
   * @param {string} period - Période
   * @returns {Object} - Données mockées
   */
  generateMockHistoricalData(dataType, period) {
    // Générer des dates pour la période
    const days = period === '7d' ? 7 : period === '30d' ? 30 : 90;
    const dates = [];
    const now = new Date();

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      dates.push(date.toISOString().split('T')[0]); // Format YYYY-MM-DD
    }

    // Générer des données selon le type
    switch (dataType) {
      case 'active_users':
        return {
          labels: dates,
          datasets: [{
            label: 'Utilisateurs actifs',
            data: dates.map(() => Math.floor(Math.random() * 5) + 5), // Entre 5 et 10
            borderColor: '#8B5CF6',
            backgroundColor: 'rgba(139, 92, 246, 0.2)',
            fill: true
          }]
        };

      case 'role_distribution':
        return {
          labels: ['Super Admin', 'Admin', 'Employé', 'Client'],
          datasets: [{
            data: [1, 2, 6, 3],
            backgroundColor: ['#8B5CF6', '#EC4899', '#3B82F6', '#10B981']
          }]
        };

      case 'active_inactive_users':
        return {
          labels: ['Actifs', 'Inactifs'],
          datasets: [{
            data: [7, 5],
            backgroundColor: ['#8B5CF6', '#D1D5DB']
          }]
        };

      case 'role_usage':
        return {
          labels: dates,
          datasets: [
            {
              label: 'Super Admin',
              data: dates.map(() => 100),
              borderColor: '#9C27B0',
              backgroundColor: 'rgba(156, 39, 176, 0.1)',
              fill: false
            },
            {
              label: 'Admin',
              data: dates.map(() => Math.floor(Math.random() * 20) + 70), // Entre 70 et 90
              borderColor: '#2196F3',
              backgroundColor: 'rgba(33, 150, 243, 0.1)',
              fill: false
            },
            {
              label: 'Employé',
              data: dates.map(() => Math.floor(Math.random() * 30) + 50), // Entre 50 et 80
              borderColor: '#4CAF50',
              backgroundColor: 'rgba(76, 175, 80, 0.1)',
              fill: false
            },
            {
              label: 'Client',
              data: dates.map(() => Math.floor(Math.random() * 40) + 30), // Entre 30 et 70
              borderColor: '#FFC107',
              backgroundColor: 'rgba(255, 193, 7, 0.1)',
              fill: false
            }
          ]
        };

      default:
        return {
          labels: dates,
          datasets: [{
            label: 'Données',
            data: dates.map(() => Math.floor(Math.random() * 10)),
            borderColor: '#8B5CF6'
          }]
        };
    }
  }
};

export default biService;
