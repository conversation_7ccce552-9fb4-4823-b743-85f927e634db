import axios from 'axios';
import { API_URL } from '@/config/constants';

// Créer une instance axios avec la configuration de base
const axiosInstance = axios.create({
  baseURL: API_URL,
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json'
  }
});

// Ajouter un intercepteur pour les requêtes
axiosInstance.interceptors.request.use(
  (config) => {
    console.log('ClientService - Making request:', {
      url: config.url,
      method: config.method,
      data: config.data
    });

    // Récupérer le token depuis le localStorage
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    } else {
      console.error('ClientService - No auth token found');
    }
    return config;
  },
  (error) => {
    console.error('ClientService - Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Ajouter un intercepteur pour les réponses
axiosInstance.interceptors.response.use(
  (response) => {
    console.log('ClientService - Response received:', {
      url: response.config.url,
      status: response.status,
      data: response.data
    });
    return response;
  },
  async (error) => {
    console.error('ClientService - Response error:', {
      url: error.config?.url,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message
    });

    // Si l'erreur est due à un token expiré (401), essayer de rafraîchir le token
    if (error.response?.status === 401) {
      console.error('ClientService - Authentication error');

      try {
        // Importer le service d'authentification
        const { authService } = await import('@/services/authService');

        // Tenter de rafraîchir le token
        const refreshed = await authService.refreshToken();

        if (refreshed) {
          console.log('Token rafraîchi, nouvelle tentative de la requête...');
          // Récupérer le nouveau token
          const newToken = authService.getAccessToken();
          // Mettre à jour le token dans la requête originale
          error.config.headers.Authorization = `Bearer ${newToken}`;
          // Retenter la requête originale avec le nouveau token
          return axios(error.config);
        }
      } catch (refreshError) {
        console.error('Échec du rafraîchissement du token:', refreshError);
      }
    }

    return Promise.reject(error.response?.data || error);
  }
);

const clientService = {
  /**
   * Récupère les métriques BI personnelles du client
   * @returns {Promise<Object>} - Métriques BI personnelles
   */
  async getPersonalMetrics() {
    try {
      const response = await axiosInstance.get('/bi/metrics/');
      console.log('Réponse de getPersonalMetrics:', response.data);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des métriques personnelles:', error);
      throw error.response?.data || { message: 'Erreur lors de la récupération des métriques personnelles' };
    }
  },

  // ❌ SUPPRIMÉ - Anciens endpoints BI incorrects /bi/dashboard/
  // Pour les clients, utiliser les endpoints spécifiques aux clients si nécessaire
  // Ou utiliser /api/bi/super-admin/dashboard/ pour les super admins uniquement

  /**
   * Récupère les paramètres du mode Pomodoro
   * @returns {Promise<Object>} - Paramètres du mode Pomodoro
   */
  async getPomodoroSettings() {
    try {
      const response = await axiosInstance.get('/pomodoro/settings/');
      console.log('Réponse de getPomodoroSettings:', response.data);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des paramètres du mode Pomodoro:', error);
      throw error.response?.data || { message: 'Erreur lors de la récupération des paramètres du mode Pomodoro' };
    }
  },

  /**
   * Met à jour les paramètres du mode Pomodoro
   * @param {Object} settingsData - Paramètres du mode Pomodoro
   * @returns {Promise<Object>} - Paramètres du mode Pomodoro mis à jour
   */
  async updatePomodoroSettings(settingsData) {
    try {
      const response = await axiosInstance.put('/pomodoro/settings/', settingsData);
      console.log('Réponse de updatePomodoroSettings:', response.data);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la mise à jour des paramètres du mode Pomodoro:', error);
      throw error.response?.data || { message: 'Erreur lors de la mise à jour des paramètres du mode Pomodoro' };
    }
  },



  /**
   * Contrôle le mode Pomodoro
   * @param {string} action - Action à effectuer (start, pause, resume, complete, reset)
   * @returns {Promise<Object>} - État du mode Pomodoro
   */
  async controlPomodoro(action) {
    try {
      const response = await axiosInstance.post(`/pomodoro/control/${action}/`);
      console.log('Réponse de controlPomodoro:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Erreur lors du contrôle du mode Pomodoro (${action}):`, error);
      throw error.response?.data || { message: `Erreur lors du contrôle du mode Pomodoro (${action})` };
    }
  }
};

export default clientService;
