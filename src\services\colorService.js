import api from './axiosConfig';

class ColorService {
  /**
   * Récupère la palette complète de couleurs depuis le backend
   */
  async getPalette() {
    try {
      const response = await api.get('/colors/palette/');
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération de la palette:', error);
      throw error;
    }
  }

  /**
   * Récupère les couleurs organisées par catégories
   */
  async getColorsByCategories() {
    try {
      const response = await api.get('/colors/categories/');
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des catégories:', error);
      throw error;
    }
  }

  /**
   * Suggère une couleur basée sur le titre de l'événement
   */
  async suggestColor(title) {
    try {
      const response = await api.post('/colors/suggest/', { title });
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la suggestion de couleur:', error);
      throw error;
    }
  }

  /**
   * Valide une couleur (nom ou code hex)
   */
  async validateColor(color) {
    try {
      const response = await api.post('/colors/validate/', { color });
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la validation de couleur:', error);
      throw error;
    }
  }

  /**
   * Cache local pour éviter les appels répétés
   */
  _paletteCache = null;
  _categoriesCache = null;

  /**
   * Récupère la palette avec cache
   */
  async getCachedPalette() {
    if (!this._paletteCache) {
      this._paletteCache = await this.getPalette();
    }
    return this._paletteCache;
  }

  /**
   * Récupère les catégories avec cache
   */
  async getCachedCategories() {
    if (!this._categoriesCache) {
      this._categoriesCache = await this.getColorsByCategories();
    }
    return this._categoriesCache;
  }

  /**
   * Vide le cache (utile après des modifications)
   */
  clearCache() {
    this._paletteCache = null;
    this._categoriesCache = null;
  }

  /**
   * Convertit un nom de couleur en code hex
   */
  async getHexFromName(colorName) {
    try {
      const palette = await this.getCachedPalette();
      const color = palette.find(c => c.name === colorName || c.id === colorName);
      return color ? color.hex_code : null;
    } catch (error) {
      console.error('Erreur lors de la conversion nom -> hex:', error);
      return null;
    }
  }

  /**
   * Convertit un code hex en nom de couleur
   */
  async getNameFromHex(hexCode) {
    try {
      const palette = await this.getCachedPalette();
      const color = palette.find(c => c.hex_code.toLowerCase() === hexCode.toLowerCase());
      return color ? color.name : null;
    } catch (error) {
      console.error('Erreur lors de la conversion hex -> nom:', error);
      return null;
    }
  }

  /**
   * Obtient les informations complètes d'une couleur
   */
  async getColorInfo(colorIdentifier) {
    try {
      const palette = await this.getCachedPalette();
      return palette.find(c =>
        c.name === colorIdentifier ||
        c.id === colorIdentifier ||
        c.hex_code.toLowerCase() === colorIdentifier.toLowerCase()
      );
    } catch (error) {
      console.error('Erreur lors de la récupération des infos couleur:', error);
      return null;
    }
  }

  /**
   * Formate une couleur pour l'affichage dans le calendrier
   */
  async formatColorForCalendar(colorIdentifier) {
    try {
      const colorInfo = await this.getColorInfo(colorIdentifier);
      if (colorInfo) {
        return {
          background: colorInfo.hex_code + '20', // Ajouter transparence
          border: colorInfo.hex_code,
          text: this.getContrastColor(colorInfo.hex_code),
          name: colorInfo.description || colorInfo.name
        };
      }

      // Si c'est un code hex direct
      if (colorIdentifier && colorIdentifier.startsWith('#')) {
        return {
          background: colorIdentifier + '20',
          border: colorIdentifier,
          text: this.getContrastColor(colorIdentifier),
          name: 'Couleur personnalisée'
        };
      }

      // Couleur par défaut
      return {
        background: '#3788d820',
        border: '#3788d8',
        text: '#000000',
        name: 'Couleur par défaut'
      };
    } catch (error) {
      console.error('Erreur lors du formatage de couleur:', error);
      return {
        background: '#3788d820',
        border: '#3788d8',
        text: '#000000',
        name: 'Couleur par défaut'
      };
    }
  }

  /**
   * Calcule la couleur de texte contrastée
   */
  getContrastColor(hexColor) {
    // Convertir hex en RGB
    const r = parseInt(hexColor.slice(1, 3), 16);
    const g = parseInt(hexColor.slice(3, 5), 16);
    const b = parseInt(hexColor.slice(5, 7), 16);

    // Calculer la luminance
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

    // Retourner noir ou blanc selon la luminance
    return luminance > 0.5 ? '#000000' : '#ffffff';
  }

  /**
   * Obtient les couleurs recommandées pour un type d'événement
   */
  async getRecommendedColors(eventType = 'general') {
    try {
      const categories = await this.getCachedCategories();

      const typeMapping = {
        'urgent': 'urgence',
        'meeting': 'type',
        'formation': 'type',
        'personal': 'personnel',
        'team': 'type',
        'general': 'statut'
      };

      const category = typeMapping[eventType] || 'statut';
      return categories[category] || [];
    } catch (error) {
      console.error('Erreur lors de la récupération des couleurs recommandées:', error);
      return [];
    }
  }
}

// Instance singleton
const colorService = new ColorService();
export default colorService;
