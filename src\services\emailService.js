import axios from 'axios';
import { emailConfig } from '@/config/emailConfig';

import { API_URL } from '@/config/constants';

export const emailService = {

    sendPasswordResetEmail: async (email) => {
        try {
            const emailData = {
                email,
                subject: emailConfig.templates.resetPasswordSubject,
                from_email: emailConfig.templates.fromEmail,
                from_name: emailConfig.templates.fromName,
                app_url: emailConfig.appUrl,
                reset_url: emailConfig.resetPasswordUrl,
                expiration_hours: emailConfig.resetPasswordExpiration
            };

            console.log(`Envoi d'un email de réinitialisation à ${email}`);
            console.log(`URL de réinitialisation configurée: ${emailConfig.resetPasswordUrl}`);

            const response = await axios.post(
                `${API_URL}/password/reset-request/`,
                emailData,
                {
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }
            );

            if (!response.data.success) {
                throw new Error(response.data.message || 'Échec de l\'envoi de l\'email de réinitialisation');
            }

            console.log('Email de réinitialisation envoyé avec succès');
            return response.data;
        } catch (error) {
            console.error('Erreur lors de l\'envoi de l\'email de réinitialisation:', error);
            if (error.response?.status === 404) {
                throw new Error('Service de réinitialisation de mot de passe non disponible. Veuillez réessayer plus tard.');
            }
            throw error;
        }
    },

    isValidEmail: (email) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
};