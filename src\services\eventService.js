import axios from 'axios';
import { API_URL } from '@/config/constants';
import { permissionService } from '@/services/permissionService';

// Pour le débogage
console.log('EventService - Module loaded with API_URL:', API_URL);

// Créer une instance axios avec la configuration de base
const axiosInstance = axios.create({
  baseURL: API_URL,
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json'
  }
});

// Fonction pour vérifier si l'utilisateur est autorisé à accéder aux événements
const isAuthorized = () => {
  console.log('EventService - isAuthorized() called');
  const userStr = localStorage.getItem('user');
  console.log('EventService - isAuthorized() - User from localStorage:', userStr);

  if (!userStr) {
    console.error('EventService - isAuthorized() - No user found in localStorage');
    return false;
  }

  try {
    const user = JSON.parse(userStr);
    console.log('EventService - isAuthorized() - Parsed user:', user);

    // Les admin et les employee peuvent accéder aux événements (pas super_admin)
    const isAuthorized = user.role === 'admin' || user.role === 'employee';
    console.log('EventService - isAuthorized() - User role:', user.role, 'isAuthorized:', isAuthorized);

    return isAuthorized;
  } catch (error) {
    console.error('EventService - isAuthorized() - Error checking user role:', error);
    return false;
  }
};

// Ajouter un intercepteur pour les requêtes
axiosInstance.interceptors.request.use(
  (config) => {
    // Récupérer le token depuis le localStorage
    const token = localStorage.getItem('authToken');
    console.log('EventService - Interceptor - Auth token from localStorage:', token ? 'Token exists' : 'No token');

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
      console.log('EventService - Interceptor - Added Authorization header');
    } else {
      console.error('EventService - Interceptor - No auth token found');
    }

    console.log('EventService - Interceptor - Final request config:', {
      url: config.url,
      method: config.method,
      headers: config.headers
    });

    return config;
  },
  (error) => {
    console.error('EventService - Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Ajouter un intercepteur pour les réponses
axiosInstance.interceptors.response.use(
  (response) => {
    console.log('EventService - Response interceptor - Successful response:', {
      url: response.config?.url,
      status: response.status,
      statusText: response.statusText,
      dataSize: response.data ? (Array.isArray(response.data) ? response.data.length : 'object') : 'no data'
    });
    return response;
  },
  async (error) => {
    console.error('EventService - Response interceptor - Error:', {
      url: error.config?.url,
      method: error.config?.method,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message
    });

    // Si l'erreur est due à un token expiré (401), essayer de rafraîchir le token
    if (error.response?.status === 401) {
      console.error('EventService - Authentication error - Token expired');

      try {
        // Importer le service d'authentification
        const { authService } = await import('@/services/authService');

        // Tenter de rafraîchir le token
        console.log('EventService - Attempting to refresh token...');
        const refreshed = await authService.refreshToken();

        if (refreshed) {
          console.log('EventService - Token refreshed successfully, retrying request...');
          // Récupérer le nouveau token
          const newToken = authService.getAccessToken();
          console.log('EventService - New token obtained:', newToken ? 'Token exists' : 'No token');

          // Mettre à jour le token dans la requête originale
          error.config.headers.Authorization = `Bearer ${newToken}`;
          // Retenter la requête originale avec le nouveau token
          return axios(error.config);
        } else {
          console.error('EventService - Token refresh failed');
        }
      } catch (refreshError) {
        console.error('EventService - Error during token refresh:', refreshError);
      }
    }

    return Promise.reject(error.response?.data || error);
  }
);

const eventService = {
  /**
   * Récupère tous les événements selon les filtres et permissions de l'utilisateur
   * @param {Object} filters - Filtres optionnels (date, équipe, statut, etc.)
   * @returns {Promise<Array>} - Liste des événements
   */
  async getEvents(filters = {}) {
    try {
      console.log('EventService - getEvents called with filters:', filters);

      // Vérifier si l'utilisateur est autorisé (seulement admin ou employee, pas super_admin)
      if (!isAuthorized()) {
        console.error('EventService - Access denied: Only admins and employees can view events');
        throw {
          status: 403,
          error: "Vous n'avez pas les droits pour voir les événements"
        };
      }

      // Récupérer le rôle de l'utilisateur pour le logging
      const userStr = localStorage.getItem('user');
      console.log('EventService - User from localStorage:', userStr);

      if (userStr) {
        try {
          const user = JSON.parse(userStr);
          console.log('EventService - User role:', user.role, 'User ID:', user.id);

          if (user.role === 'super_admin') {
            console.error('EventService - Super admin cannot access events');
            throw {
              status: 403,
              error: "Les super administrateurs n'ont pas accès aux événements d'équipe"
            };
          }
        } catch (e) {
          console.error('EventService - Error parsing user data:', e);
        }
      } else {
        console.error('EventService - No user found in localStorage');
      }

      const queryParams = new URLSearchParams();

      // Récupérer l'utilisateur actuel
      let userId = null;
      let userRole = null;

      try {
        const user = JSON.parse(userStr);
        userId = user.id;
        userRole = user.role;
      } catch (e) {
        console.error('EventService - Error parsing user data:', e);
      }

      // Les administrateurs peuvent voir tous les événements, mais ne peuvent gérer que ceux qu'ils ont créés
      if (userRole === 'admin' && userId) {
        console.log('EventService - Admin user detected, original filters:', filters);
        // Ne pas filtrer par creator_id pour permettre aux admins de voir tous les événements
        console.log('EventService - Admin user can see all events, no creator_id filter applied');
      } else {
        console.log('EventService - User role:', userRole, 'User ID:', userId, 'No creator_id filter applied');
      }

      // Ajouter les filtres à la requête
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          queryParams.append(key, value);
        }
      });

      console.log('EventService - Sending request to:', `/events/?${queryParams.toString()}`);

      // Récupérer le token manuellement pour s'assurer qu'il est à jour
      const token = localStorage.getItem('authToken');
      console.log('EventService - Auth token for request:', token ? 'Token exists' : 'No token');

      if (!token) {
        console.error('EventService - No auth token found for request');
        throw {
          status: 401,
          error: "Vous devez être connecté pour accéder aux événements"
        };
      }

      // Créer des en-têtes personnalisés pour cette requête
      const headers = {
        'Authorization': `Bearer ${token}`,
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      };

      console.log('EventService - Request headers:', headers);

      // Utiliser axios directement au lieu de l'instance pour éviter les problèmes potentiels
      const response = await axios.get(`${API_URL}/events/?${queryParams.toString()}`, { headers });
      console.log('EventService - Response status:', response.status);
      console.log('EventService - Response data:', response.data);
      console.log('EventService - Nombre d\'événements reçus:', response.data?.length || 0);

      // Récupérer les couleurs sauvegardées dans la variable globale
      let savedColors = window.eventColors || {};

      // Ajouter les couleurs sauvegardées aux événements
      const events = response.data;
      if (Array.isArray(events)) {
        events.forEach(event => {
          if (!event.color && savedColors[event.id]) {
            console.log(`EventService - Ajout de la couleur sauvegardée pour l'événement ${event.id}:`, savedColors[event.id]);
            event.color = savedColors[event.id];
          } else if (!event.color) {
            // Utiliser une des couleurs de la palette
            const defaultColors = [
              '#CDB4DB', // Violet pastel
              '#FFC8DD', // Rose clair pastel
              '#FFAFCC', // Rose vif pastel
              '#BDE0FE', // Bleu clair pastel
              '#A2D2FF'  // Bleu pastel
            ];
            // Utiliser l'ID de l'événement pour choisir une couleur de manière déterministe
            const colorIndex = event.id ? Math.abs(event.id.toString().split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)) % defaultColors.length : 0;
            event.color = defaultColors[colorIndex];
          }
        });
      }

      return events;
    } catch (error) {
      console.error('Erreur lors de la récupération des événements:', error);
      throw error.response?.data || error || { message: 'Erreur lors de la récupération des événements' };
    }
  },

  /**
   * Récupère un événement par son ID
   * @param {string} eventId - ID de l'événement
   * @returns {Promise<Object>} - Détails de l'événement
   */
  async getEventById(eventId) {
    try {
      console.log(`EventService - getEventById called for event ID: ${eventId}`);

      // Vérifier si l'utilisateur est autorisé
      if (!isAuthorized()) {
        console.error('EventService - Access denied: Only admins and employees can view events');
        throw {
          status: 403,
          error: "Vous n'avez pas les droits pour voir les événements"
        };
      }

      // Récupérer le token manuellement pour s'assurer qu'il est à jour
      const token = localStorage.getItem('authToken');
      console.log('EventService - Auth token for request:', token ? 'Token exists' : 'No token');

      if (!token) {
        console.error('EventService - No auth token found for request');
        throw {
          status: 401,
          error: "Vous devez être connecté pour accéder aux événements"
        };
      }

      // Créer des en-têtes personnalisés pour cette requête
      const headers = {
        'Authorization': `Bearer ${token}`,
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      };

      console.log(`EventService - Sending request to: /events/${eventId}/`);
      console.log('EventService - Request headers:', headers);

      // Récupérer l'utilisateur actuel
      const userStr = localStorage.getItem('user');
      let userId = null;
      let userRole = null;

      if (userStr) {
        try {
          const user = JSON.parse(userStr);
          userId = user.id;
          userRole = user.role;
        } catch (e) {
          console.error('EventService - Error parsing user data:', e);
        }
      }

      // Utiliser axios directement au lieu de l'instance
      const response = await axios.get(`${API_URL}/events/${eventId}/`, { headers });
      console.log(`EventService - Response status for event ${eventId}:`, response.status);
      console.log(`EventService - Response data for event ${eventId}:`, response.data);

      const event = response.data;

      // Les administrateurs peuvent voir tous les événements, mais ne peuvent gérer que ceux qu'ils ont créés
      if (userRole === 'admin' && userId) {
        const isCreator = String(event.creator_id) === String(userId);
        console.log(`EventService - Admin user ${userId} is ${isCreator ? '' : 'not '}the creator of event ${eventId}`);
        // Ne pas bloquer l'accès, les admins peuvent voir tous les événements
      }

      // Si l'événement n'a pas de couleur, essayer de récupérer depuis la variable globale
      if (!event.color || event.color === '') {
        const savedColors = window.eventColors || {};
        if (savedColors[eventId]) {
          console.log(`EventService - Ajout de la couleur sauvegardée pour l'événement ${eventId}:`, savedColors[eventId]);
          event.color = savedColors[eventId];
        } else {
          // Utiliser une des couleurs de la palette
          const defaultColors = [
            '#CDB4DB', // Violet pastel
            '#FFC8DD', // Rose clair pastel
            '#FFAFCC', // Rose vif pastel
            '#BDE0FE', // Bleu clair pastel
            '#A2D2FF'  // Bleu pastel
          ];
          // Utiliser l'ID de l'événement pour choisir une couleur de manière déterministe
          const colorIndex = eventId ? Math.abs(eventId.toString().split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)) % defaultColors.length : 0;
          event.color = defaultColors[colorIndex];
        }
      }

      return event;
    } catch (error) {
      console.error(`EventService - Error fetching event ${eventId}:`, error);

      // Afficher plus de détails sur l'erreur
      if (error.response) {
        console.error(`EventService - Response status for event ${eventId}:`, error.response.status);
        console.error(`EventService - Response data for event ${eventId}:`, error.response.data);
      } else if (error.request) {
        console.error(`EventService - No response received for event ${eventId}`);
      } else {
        console.error(`EventService - Error message for event ${eventId}:`, error.message);
      }

      throw error.response?.data || { message: `Erreur lors de la récupération de l'événement ${eventId}` };
    }
  },

  /**
   * Crée un nouvel événement
   * @param {Object} eventData - Données de l'événement
   * @returns {Promise<Object>} - Événement créé
   */
  async createEvent(eventData) {
    try {
      console.log('EventService - createEvent called with data:', eventData);

      // Vérifier si l'utilisateur est autorisé
      if (!isAuthorized()) {
        console.error('EventService - Access denied: Only admins and employees can create events');
        throw {
          status: 403,
          error: "Vous n'avez pas les droits pour créer des événements"
        };
      }

      // Récupérer le token manuellement pour s'assurer qu'il est à jour
      const token = localStorage.getItem('authToken');
      console.log('EventService - Auth token for request:', token ? 'Token exists' : 'No token');

      if (!token) {
        console.error('EventService - No auth token found for request');
        throw {
          status: 401,
          error: "Vous devez être connecté pour créer des événements"
        };
      }

      // Créer des en-têtes personnalisés pour cette requête
      const headers = {
        'Authorization': `Bearer ${token}`,
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      };

      // Ajout de logs détaillés pour le débogage
      const fullUrl = `${API_URL}/events/`;
      console.log('EventService - Sending POST request to:', fullUrl);
      console.log('EventService - Request headers:', headers);
      console.log('EventService - Request data:', JSON.stringify(eventData));

      // Utiliser axios directement au lieu de l'instance
      const response = await axios.post(fullUrl, eventData, { headers });

      console.log('EventService - Response status:', response.status);
      console.log('EventService - Response data:', response.data);

      // Vérifier si la réponse contient un événement ou est directement l'événement
      const eventResult = response.data.event || response.data;
      console.log('Événement extrait:', eventResult);

      // Sauvegarder la couleur dans la variable globale si elle est présente dans les données envoyées
      if (eventResult.id && eventData.color) {
        try {
          // Utiliser une variable globale sur window pour stocker les couleurs
          if (!window.eventColors) {
            window.eventColors = {};
          }
          window.eventColors[eventResult.id] = eventData.color;
          console.log(`EventService - Couleur sauvegardée pour le nouvel événement ${eventResult.id}:`, eventData.color);

          // S'assurer que l'événement retourné a la couleur
          if (!eventResult.color) {
            eventResult.color = eventData.color;
          }
        } catch (e) {
          console.error('EventService - Erreur lors de la sauvegarde de la couleur:', e);
        }
      }

      return eventResult;
    } catch (error) {
      console.error('Erreur détaillée lors de la création de l\'événement:', error);
      console.error('Configuration de la requête:', error.config);
      console.error('Statut de l\'erreur:', error.response?.status);
      console.error('Données d\'erreur:', error.response?.data);

      // Vérifier si l'erreur est due à un titre dupliqué
      if (error.response?.data?.message && error.response.data.message.includes('title')) {
        throw { message: 'Un événement avec ce titre existe déjà' };
      }
      throw error.response?.data || { message: 'Erreur lors de la création de l\'événement' };
    }
  },

  /**
   * Met à jour un événement existant
   * @param {string} eventId - ID de l'événement
   * @param {Object} eventData - Nouvelles données de l'événement
   * @returns {Promise<Object>} - Événement mis à jour
   */
  async updateEvent(eventId, eventData) {
    try {
      console.log(`EventService - Mise à jour de l'événement ${eventId} avec les données:`, eventData);

      // Vérifier que les données sont complètes et valides
      if (!eventData.start_date || !eventData.end_date || !eventData.start_time || !eventData.end_time) {
        console.error('EventService - Données de date/heure incomplètes:', eventData);
        throw { message: 'Les informations de date et heure sont incomplètes' };
      }

      // Vérifier que les dates sont au bon format
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
      const timeRegex = /^\d{2}:\d{2}$/;

      if (!dateRegex.test(eventData.start_date) || !dateRegex.test(eventData.end_date)) {
        console.error('EventService - Format de date invalide:', eventData);
        throw { message: 'Format de date invalide. Utilisez YYYY-MM-DD' };
      }

      if (!timeRegex.test(eventData.start_time) || !timeRegex.test(eventData.end_time)) {
        console.error('EventService - Format d\'heure invalide:', eventData);
        throw { message: 'Format d\'heure invalide. Utilisez HH:MM' };
      }

      // Forcer la conversion de la couleur en chaîne de caractères hexadécimale valide
      let colorValue = eventData.color;
      if (colorValue === undefined || colorValue === null || typeof colorValue !== 'string' || !colorValue.startsWith('#')) {
        // Si la couleur n'est pas valide, utiliser une des couleurs de la palette
        const defaultColors = [
          '#CDB4DB', // Violet pastel
          '#FFC8DD', // Rose clair pastel
          '#FFAFCC', // Rose vif pastel
          '#BDE0FE', // Bleu clair pastel
          '#A2D2FF'  // Bleu pastel
        ];
        // Utiliser l'ID de l'événement pour choisir une couleur de manière déterministe
        const colorIndex = eventId ? Math.abs(eventId.toString().split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)) % defaultColors.length : 0;
        colorValue = defaultColors[colorIndex];
        console.log('EventService - Utilisation d\'une couleur de la palette:', colorValue);
      }

      // Sauvegarder la couleur dans une variable globale pour la récupérer plus tard
      try {
        // Utiliser une variable globale sur window pour stocker les couleurs
        if (typeof window.eventColors === 'undefined') {
          window.eventColors = {};
          console.log('EventService - Variable globale eventColors initialisée');
        }

        // Vérifier si la couleur est valide
        if (colorValue && typeof colorValue === 'string' && colorValue.startsWith('#')) {
          window.eventColors[eventId] = colorValue;
          console.log(`EventService - Couleur sauvegardée pour l'événement ${eventId}:`, colorValue);

          // Forcer la mise à jour de la couleur dans les données de l'événement
          eventData.color = colorValue;
        } else {
          console.warn(`EventService - Couleur invalide pour l'événement ${eventId}:`, colorValue);
          // Utiliser une des couleurs de la palette
          const defaultColors = [
            '#CDB4DB', // Violet pastel
            '#FFC8DD', // Rose clair pastel
            '#FFAFCC', // Rose vif pastel
            '#BDE0FE', // Bleu clair pastel
            '#A2D2FF'  // Bleu pastel
          ];
          // Utiliser l'ID de l'événement pour choisir une couleur de manière déterministe
          const colorIndex = eventId ? Math.abs(eventId.toString().split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)) % defaultColors.length : 0;
          window.eventColors[eventId] = defaultColors[colorIndex];
          eventData.color = defaultColors[colorIndex];
          console.log(`EventService - Couleur de la palette utilisée pour l'événement ${eventId}:`, window.eventColors[eventId]);
        }

        // Forcer un rafraîchissement des couleurs dans l'interface
        try {
          if (window.dispatchEvent) {
            console.log(`EventService - Émission de l'événement eventColorUpdated pour l'événement ${eventId} avec la couleur ${window.eventColors[eventId]}`);
            window.dispatchEvent(new CustomEvent('eventColorUpdated', {
              detail: {
                eventId,
                color: window.eventColors[eventId]
              }
            }));
          }
        } catch (dispatchError) {
          console.error('EventService - Erreur lors de l\'émission de l\'événement eventColorUpdated:', dispatchError);
        }
      } catch (e) {
        console.error('EventService - Erreur lors de la sauvegarde de la couleur:', e);
      }

      // S'assurer que toutes les propriétés nécessaires sont présentes
      const completeEventData = {
        ...eventData,
        // Ajouter des valeurs par défaut si nécessaire
        title: eventData.title || 'Événement sans titre',
        description: eventData.description || '',
        note: eventData.note || '',
        // Forcer la couleur à être une valeur explicite
        color: colorValue,
        // S'assurer que member_id est explicitement inclus, même si c'est une chaîne vide
        member_id: eventData.member_id !== undefined ? eventData.member_id : ''
      };

      // Log spécifique pour les propriétés problématiques
      console.log('EventService - Couleur envoyée (après validation):', completeEventData.color);
      console.log('EventService - Member ID envoyé:', completeEventData.member_id);

      console.log(`EventService - Données complètes pour la mise à jour:`, completeEventData);

      // Récupérer le token manuellement pour s'assurer qu'il est à jour
      const token = localStorage.getItem('authToken');
      console.log('EventService - Auth token for request:', token ? 'Token exists' : 'No token');

      if (!token) {
        console.error('EventService - No auth token found for request');
        throw {
          status: 401,
          error: "Vous devez être connecté pour mettre à jour des événements"
        };
      }

      // Créer des en-têtes personnalisés pour cette requête
      const headers = {
        'Authorization': `Bearer ${token}`,
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      };

      const fullUrl = `${API_URL}/events/${eventId}/`;
      console.log('EventService - Sending PUT request to:', fullUrl);
      console.log('EventService - Request headers:', headers);

      // Utiliser axios directement au lieu de l'instance
      const response = await axios.put(fullUrl, completeEventData, { headers });
      console.log(`EventService - Response status for event ${eventId}:`, response.status);
      console.log(`EventService - Response data for event ${eventId}:`, response.data);

      // Vérifier que la réponse contient bien les données mises à jour
      const updatedEvent = response.data.event || response.data;

      if (!updatedEvent || !updatedEvent.id) {
        console.error('EventService - Réponse invalide de l\'API:', response.data);
        throw { message: 'La réponse du serveur ne contient pas les données de l\'événement mis à jour' };
      }

      // Si la couleur n'est pas retournée par le backend, l'ajouter manuellement
      if (!updatedEvent.color || updatedEvent.color === '') {
        console.log('EventService - Couleur non retournée par le backend, ajout manuel:', colorValue);
        updatedEvent.color = colorValue;
      }

      return updatedEvent;
    } catch (error) {
      console.error(`EventService - Erreur lors de la mise à jour de l'événement ${eventId}:`, error);

      // Vérifier si l'erreur est due à un titre dupliqué
      if (error.response?.data?.message && error.response.data.message.includes('title')) {
        throw { message: 'Un événement avec ce titre existe déjà' };
      }

      // Vérifier si l'erreur est due à un problème de date
      if (error.response?.data?.message && error.response.data.message.includes('date')) {
        throw { message: 'Problème avec les dates de l\'événement. Vérifiez que la date de fin est après la date de début.' };
      }

      throw error.response?.data || error.message ? { message: error.message } : { message: `Erreur lors de la mise à jour de l'événement ${eventId}` };
    }
  },

  /**
   * Met à jour le statut d'un événement
   * @param {string} eventId - ID de l'événement
   * @param {string} status - Nouveau statut ('pending', 'completed', 'archived')
   * @returns {Promise<Object>} - Événement mis à jour
   */
  async updateEventStatus(eventId, status) {
    try {
      console.log(`EventService - updateEventStatus called for event ${eventId} with status ${status}`);

      // Vérifier si l'utilisateur est autorisé
      if (!isAuthorized()) {
        console.error('EventService - Access denied: Only admins and employees can update event status');
        throw {
          status: 403,
          error: "Vous n'avez pas les droits pour mettre à jour le statut des événements"
        };
      }

      // Récupérer le token manuellement pour s'assurer qu'il est à jour
      const token = localStorage.getItem('authToken');
      console.log('EventService - Auth token for request:', token ? 'Token exists' : 'No token');

      if (!token) {
        console.error('EventService - No auth token found for request');
        throw {
          status: 401,
          error: "Vous devez être connecté pour mettre à jour le statut des événements"
        };
      }

      // Créer des en-têtes personnalisés pour cette requête
      const headers = {
        'Authorization': `Bearer ${token}`,
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      };

      const fullUrl = `${API_URL}/events/${eventId}/status/`;
      console.log('EventService - Sending PUT request to:', fullUrl);
      console.log('EventService - Request headers:', headers);
      console.log('EventService - Request data:', { status });

      // Utiliser axios directement au lieu de l'instance
      const response = await axios.put(fullUrl, { status }, { headers });
      console.log(`EventService - Response status for event ${eventId}:`, response.status);
      console.log(`EventService - Response data for event ${eventId}:`, response.data);

      return response.data.event || response.data;
    } catch (error) {
      console.error(`EventService - Error updating status for event ${eventId}:`, error);

      // Afficher plus de détails sur l'erreur
      if (error.response) {
        console.error(`EventService - Response status for event ${eventId}:`, error.response.status);
        console.error(`EventService - Response data for event ${eventId}:`, error.response.data);
      } else if (error.request) {
        console.error(`EventService - No response received for event ${eventId}`);
      } else {
        console.error(`EventService - Error message for event ${eventId}:`, error.message);
      }

      throw error.response?.data || { message: `Erreur lors de la mise à jour du statut de l'événement ${eventId}` };
    }
  },

  /**
   * Archive un événement
   * @param {string} eventId - ID de l'événement
   * @returns {Promise<Object>} - Événement archivé
   */
  async archiveEvent(eventId) {
    try {
      console.log(`EventService - archiveEvent called for event ${eventId}`);

      // Vérifier si l'utilisateur est autorisé
      if (!isAuthorized()) {
        console.error('EventService - Access denied: Only admins and employees can archive events');
        throw {
          status: 403,
          error: "Vous n'avez pas les droits pour archiver des événements"
        };
      }

      // Récupérer le token manuellement pour s'assurer qu'il est à jour
      const token = localStorage.getItem('authToken');
      console.log('EventService - Auth token for request:', token ? 'Token exists' : 'No token');

      if (!token) {
        console.error('EventService - No auth token found for request');
        throw {
          status: 401,
          error: "Vous devez être connecté pour archiver des événements"
        };
      }

      // Créer des en-têtes personnalisés pour cette requête
      const headers = {
        'Authorization': `Bearer ${token}`,
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      };

      const fullUrl = `${API_URL}/events/${eventId}/archive/`;
      console.log('EventService - Sending PUT request to:', fullUrl);
      console.log('EventService - Request headers:', headers);

      // Utiliser axios directement au lieu de l'instance
      const response = await axios.put(fullUrl, {}, { headers });
      console.log(`EventService - Response status for event ${eventId}:`, response.status);
      console.log(`EventService - Response data for event ${eventId}:`, response.data);

      return response.data.event || response.data;
    } catch (error) {
      console.error(`EventService - Error archiving event ${eventId}:`, error);

      // Afficher plus de détails sur l'erreur
      if (error.response) {
        console.error(`EventService - Response status for event ${eventId}:`, error.response.status);
        console.error(`EventService - Response data for event ${eventId}:`, error.response.data);
      } else if (error.request) {
        console.error(`EventService - No response received for event ${eventId}`);
      } else {
        console.error(`EventService - Error message for event ${eventId}:`, error.message);
      }

      throw error.response?.data || { message: `Erreur lors de l'archivage de l'événement ${eventId}` };
    }
  },

  /**
   * Désarchive un événement
   * @param {string} eventId - ID de l'événement
   * @returns {Promise<Object>} - Événement désarchivé
   */
  async unarchiveEvent(eventId) {
    try {
      console.log(`EventService - unarchiveEvent called for event ${eventId}`);

      // Vérifier si l'utilisateur est autorisé
      if (!isAuthorized()) {
        console.error('EventService - Access denied: Only admins and employees can unarchive events');
        throw {
          status: 403,
          error: "Vous n'avez pas les droits pour désarchiver des événements"
        };
      }

      // Récupérer le token manuellement pour s'assurer qu'il est à jour
      const token = localStorage.getItem('authToken');
      console.log('EventService - Auth token for request:', token ? 'Token exists' : 'No token');

      if (!token) {
        console.error('EventService - No auth token found for request');
        throw {
          status: 401,
          error: "Vous devez être connecté pour désarchiver des événements"
        };
      }

      // Créer des en-têtes personnalisés pour cette requête
      const headers = {
        'Authorization': `Bearer ${token}`,
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      };

      const fullUrl = `${API_URL}/events/${eventId}/unarchive/`;
      console.log('EventService - Sending PUT request to:', fullUrl);
      console.log('EventService - Request headers:', headers);

      // Utiliser axios directement au lieu de l'instance
      const response = await axios.put(fullUrl, {}, { headers });
      console.log(`EventService - Response status for event ${eventId}:`, response.status);
      console.log(`EventService - Response data for event ${eventId}:`, response.data);

      return response.data.event || response.data;
    } catch (error) {
      console.error(`EventService - Error unarchiving event ${eventId}:`, error);

      // Afficher plus de détails sur l'erreur
      if (error.response) {
        console.error(`EventService - Response status for event ${eventId}:`, error.response.status);
        console.error(`EventService - Response data for event ${eventId}:`, error.response.data);
      } else if (error.request) {
        console.error(`EventService - No response received for event ${eventId}`);
      } else {
        console.error(`EventService - Error message for event ${eventId}:`, error.message);
      }

      throw error.response?.data || { message: `Erreur lors du désarchivage de l'événement ${eventId}` };
    }
  },

  /**
   * Supprime un événement
   * @param {string} eventId - ID de l'événement
   * @returns {Promise<Object>} - Réponse de confirmation
   */
  async deleteEvent(eventId) {
    try {
      console.log(`EventService - deleteEvent called for event ${eventId}`);

      // Vérifier si l'utilisateur est autorisé
      if (!isAuthorized()) {
        console.error('EventService - Access denied: Only admins and employees can delete events');
        throw {
          status: 403,
          error: "Vous n'avez pas les droits pour supprimer des événements"
        };
      }

      // Récupérer le token manuellement pour s'assurer qu'il est à jour
      const token = localStorage.getItem('authToken');
      console.log('EventService - Auth token for request:', token ? 'Token exists' : 'No token');

      if (!token) {
        console.error('EventService - No auth token found for request');
        throw {
          status: 401,
          error: "Vous devez être connecté pour supprimer des événements"
        };
      }

      // Créer des en-têtes personnalisés pour cette requête
      const headers = {
        'Authorization': `Bearer ${token}`,
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      };

      const fullUrl = `${API_URL}/events/${eventId}/`;
      console.log('EventService - Sending DELETE request to:', fullUrl);
      console.log('EventService - Request headers:', headers);

      // Utiliser axios directement au lieu de l'instance
      const response = await axios.delete(fullUrl, { headers });
      console.log(`EventService - Response status for event ${eventId}:`, response.status);
      console.log(`EventService - Response data for event ${eventId}:`, response.data);

      return response.data;
    } catch (error) {
      console.error(`EventService - Error deleting event ${eventId}:`, error);

      // Afficher plus de détails sur l'erreur
      if (error.response) {
        console.error(`EventService - Response status for event ${eventId}:`, error.response.status);
        console.error(`EventService - Response data for event ${eventId}:`, error.response.data);
      } else if (error.request) {
        console.error(`EventService - No response received for event ${eventId}`);
      } else {
        console.error(`EventService - Error message for event ${eventId}:`, error.message);
      }

      throw error.response?.data || { message: `Erreur lors de la suppression de l'événement ${eventId}` };
    }
  },

  /**
   * Vérifie les permissions d'un utilisateur pour un événement
   * @param {Object} user - Utilisateur actuel
   * @param {Object} event - Événement à vérifier
   * @returns {Object} - Objet contenant les permissions
   */
  checkEventPermissions(user, event) {
    console.log('EventService - Checking permissions for user:', user?.role, 'event:', event?.id);

    // Utiliser le service de permissions pour vérifier les permissions
    return permissionService.checkEventPermissions(user, event);
  }
};

export default eventService;