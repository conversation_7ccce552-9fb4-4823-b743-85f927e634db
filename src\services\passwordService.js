import axios from 'axios';
import { emailService } from './emailService';

import { API_URL } from '@/config/constants';

export const passwordService = {
    generateTempPassword: (length = 10) => {
        const uppercaseChars = 'ABCDEFGHJKLMNPQRSTUVWXYZ';
        const lowercaseChars = 'abcdefghijkmnpqrstuvwxyz';
        const numberChars = '23456789';
        const specialChars = '@#$%&*';

        const allChars = uppercaseChars + lowercaseChars + numberChars + specialChars;

        let password = [
            uppercaseChars.charAt(Math.floor(Math.random() * uppercaseChars.length)),
            lowercaseChars.charAt(Math.floor(Math.random() * lowercaseChars.length)),
            numberChars.charAt(Math.floor(Math.random() * numberChars.length)),
            specialChars.charAt(Math.floor(Math.random() * specialChars.length))
        ];

        for (let i = 4; i < length; i++) {
            password.push(allChars.charAt(Math.floor(Math.random() * allChars.length)));
        }

        password = password.sort(() => 0.5 - Math.random());

        return password.join('');
    },

    createUserWithTempPassword: async (userData) => {
        try {
            if (!userData.password) {
                const tempPassword = passwordService.generateTempPassword();
                userData.password = tempPassword;
                userData.temp_password_required = true;
            }

            return userData;
        } catch (error) {
            console.error('Erreur lors de la création de l\'utilisateur avec mot de passe temporaire:', error);
            throw error;
        }
    },

    requestPasswordReset: async (email) => {
        try {
            console.log(`Demande de réinitialisation de mot de passe pour: ${email}`);

            // Utiliser le service d'email pour envoyer la demande
            const response = await emailService.sendPasswordResetEmail(email);

            console.log('Réponse de la demande de réinitialisation:', response);

            // Vérifier et traduire le message de réponse
            const originalMessage = response?.message || 'Les instructions de réinitialisation ont été envoyées à votre adresse email';

            // Détecter si c'est le message informatif en anglais
            const isEnglishInformativeMessage = originalMessage.toLowerCase().includes('if this email is associated') ||
                originalMessage.toLowerCase().includes('reset link will be sent');

            const finalMessage = isEnglishInformativeMessage
                ? 'Si cette adresse email est associée à un compte, un lien de réinitialisation sera envoyé'
                : originalMessage;

            // Traitement uniforme des réponses
            return {
                success: true,
                status: 200,
                data: response,
                message: finalMessage,
                type: 'success'
            };
        } catch (error) {
            console.error('Erreur lors de la demande de réinitialisation:', error);

            if (error.response) {
                const status = error.response.status;
                const responseData = error.response.data;

                // Vérifier si c'est un message informatif plutôt qu'une vraie erreur
                const originalMessage = responseData?.message || 'Une erreur est survenue';
                const isInformativeMessage = originalMessage.toLowerCase().includes('reset link will be sent') ||
                    originalMessage.toLowerCase().includes('if this email is associated') ||
                    originalMessage.toLowerCase().includes('lien de réinitialisation sera envoyé');

                if (isInformativeMessage) {
                    // Traduire le message en français
                    const frenchMessage = 'Si cette adresse email est associée à un compte, un lien de réinitialisation sera envoyé';

                    // Traiter comme un succès informatif
                    return {
                        success: true,
                        status: 200,
                        data: responseData,
                        message: frenchMessage,
                        type: 'info'
                    };
                }

                // Messages d'erreur spécifiques
                const errorMessage = status === 404
                    ? 'Aucun compte trouvé avec cette adresse email'
                    : message;

                return {
                    success: false,
                    status: status,
                    data: responseData,
                    message: errorMessage,
                    type: 'error'
                };
            }

            // Erreur réseau ou autre
            return {
                success: false,
                status: 500,
                message: error.message || 'Erreur de connexion au serveur',
                type: 'error'
            };
        }
    },

    resetPassword: async (token, passwords) => {
        try {
            console.log(`Sending password reset request to: ${API_URL}/password/reset/${token}/`);
            console.log('Password data:', { new_password: passwords.new_password });

            // Simplifier la requête pour n'envoyer que le nouveau mot de passe
            const response = await axios.post(`${API_URL}/password/reset/${token}/`, {
                new_password: passwords.new_password
            });

            console.log('Password reset response:', response.data);

            // Traitement uniforme des réponses
            return {
                success: response.status >= 200 && response.status < 300,
                status: response.status,
                data: response.data,
                message: response.data?.message || 'Mot de passe réinitialisé avec succès',
                type: response.status >= 200 && response.status < 300 ? 'success' : 'error'
            };
        } catch (error) {
            console.error('Password reset error:', error);

            if (error.response) {
                console.error('Server response error:', error.response.data);
                // Réponse du serveur avec erreur
                return {
                    success: false,
                    status: error.response.status,
                    data: error.response.data,
                    message: error.response.data?.message || 'Une erreur est survenue lors de la réinitialisation du mot de passe',
                    type: 'error'
                };
            }
            // Erreur réseau ou autre
            return {
                success: false,
                status: 500,
                message: 'Erreur de connexion au serveur',
                type: 'error'
            };
        }
    }
};