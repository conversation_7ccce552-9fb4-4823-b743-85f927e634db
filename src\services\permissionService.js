import { authService } from './authService';

export const permissionService = {
    // Vérifie si l'utilisateur a une permission globale spécifique
    hasPermission(user, permission) {
        if (!user) return false;

        // Vérification basée sur le rôle et la permission
        if (user.role === 'super_admin') {
            // Les super_admin ont des permissions spécifiques
            if (permission === 'manage_users' || permission === 'activate_user_permissions') {
                return true;
            }
            // Les super_admin n'ont pas accès aux équipes
            if (permission === 'manage_teams' ||
                permission === 'manage_team_tasks' ||
                permission === 'manage_team_calendars' ||
                permission === 'view_team_dashboards') {
                return false;
            }
        }

        if (user.role === 'admin') {
            // Les admin ont des permissions spécifiques pour les équipes
            if (permission === 'manage_teams' ||
                permission === 'manage_team_tasks' ||
                permission === 'manage_team_calendars' ||
                permission === 'view_team_dashboards') {
                return true;
            }
        }

        if (user.role === 'employee') {
            // Les employés ont des permissions spécifiques
            if (permission === 'view_team_dashboards' ||
                permission === 'view_assigned_events' ||
                permission === 'update_event_status') {
                return true;
            }

            // Les employés peuvent gérer leurs événements personnels
            if (permission === 'manage_personal_events') {
                return true;
            }
        }

        // Pour les autres rôles, on pourrait implémenter une logique plus complexe
        // basée sur un tableau de permissions stocké dans l'utilisateur
        return false;
    },

    // Vérifie si l'utilisateur peut gérer une équipe spécifique
    canManageTeam(user, team) {
        if (!user || !team) return false;

        // Les super_admin n'ont pas accès aux équipes
        if (user.role === 'super_admin') return false;

        // Un administrateur qui est responsable de l'équipe peut la gérer
        const isAdmin = user.role === 'admin';
        const isResponsable = String(user.id) === String(team.responsable);

        return isAdmin && isResponsable;
    },

    // Vérifie si l'utilisateur peut voir une équipe spécifique
    canViewTeam(user, team) {
        if (!user || !team) return false;

        // Les super_admin n'ont pas accès aux équipes
        if (user.role === 'super_admin') return false;

        // Les administrateurs peuvent voir toutes les équipes
        if (user.role === 'admin') return true;

        // Les employés peuvent voir les équipes dont ils sont membres
        if (user.role === 'employee') {
            // Vérifier si l'utilisateur est membre de l'équipe via la propriété members
            let isMemberViaTeam = false;
            if (team.members) {
                if (Array.isArray(team.members)) {
                    isMemberViaTeam = team.members.some(member =>
                        String(member.id || member.user_id) === String(user.id)
                    );
                } else if (typeof team.members === 'object') {
                    isMemberViaTeam = Object.keys(team.members).includes(String(user.id));
                }
            }

            // Vérifier si l'utilisateur a cette équipe dans sa liste d'équipes
            const isMemberViaUser = user.teams && Array.isArray(user.teams) &&
                user.teams.includes(team.id);

            console.log('PermissionService - canViewTeam:', {
                userId: user.id,
                teamId: team.id,
                isMemberViaTeam,
                isMemberViaUser,
                userTeams: user.teams
            });

            return isMemberViaTeam || isMemberViaUser;
        }

        return false;
    },

    // Retourne un objet complet avec toutes les permissions pour une équipe
    checkTeamPermissions(user, team) {
        if (!user || !team) {
            return {
                canView: false,
                canManage: false,
                canAddMembers: false,
                canRemoveMembers: false,
                canCreateTeam: false,
                isResponsable: false,
                isMember: false
            };
        }

        // Les super_admin n'ont pas accès aux équipes
        if (user.role === 'super_admin') {
            return {
                canView: false,
                canManage: false,
                canAddMembers: false,
                canRemoveMembers: false,
                canCreateTeam: false,
                isResponsable: false,
                isMember: false
            };
        }

        const isAdmin = user.role === 'admin';
        const isResponsable = String(user.id) === String(team.responsable);

        // Vérifier si l'utilisateur est membre de l'équipe
        let isMember = false;
        if (team.members) {
            if (Array.isArray(team.members)) {
                isMember = team.members.some(member =>
                    String(member.id || member.user_id) === String(user.id)
                );
            } else if (typeof team.members === 'object') {
                isMember = Object.keys(team.members).includes(String(user.id));
            }
        }

        // Vérifier également si l'utilisateur a cette équipe dans sa liste d'équipes
        if (!isMember && user.role === 'employee' && user.teams && Array.isArray(user.teams)) {
            isMember = user.teams.includes(team.id);
            console.log('PermissionService - checkTeamPermissions - isMember via user.teams:', isMember);
        }

        // Vérifier si l'utilisateur a la permission de gérer les équipes
        const hasTeamManagementPermission = this.hasPermission(user, 'manage_teams');

        return {
            canView: isAdmin || isMember,
            canManage: isAdmin && isResponsable,
            canAddMembers: isAdmin && isResponsable && hasTeamManagementPermission,
            canRemoveMembers: isAdmin && isResponsable && hasTeamManagementPermission,
            canCreateTeam: isAdmin && hasTeamManagementPermission,
            isResponsable,
            isMember
        };
    },

    // Vérifie si l'utilisateur peut voir un événement spécifique
    canViewEvent(user, event, team = null) {
        if (!user || !event) return false;

        // Les super_admin n'ont pas accès aux événements
        if (user.role === 'super_admin') return false;

        // Vérifier si l'événement est personnel (pas d'équipe associée)
        const isPersonal = !event.team_id;

        // Vérifier si l'utilisateur est le créateur de l'événement
        const isCreator = String(user.id) === String(event.creator_id);

        // Vérifier si l'événement est assigné à l'utilisateur
        const isAssigned = String(user.id) === String(event.member_id);

        // Les administrateurs peuvent voir tous les événements d'équipe
        if (user.role === 'admin') {
            return isPersonal ? isCreator : true;
        }

        // Les employés peuvent voir les événements qui leur sont assignés
        // ou les événements des équipes dont ils sont membres
        if (user.role === 'employee') {
            if (isPersonal) {
                return isCreator;
            }

            if (isAssigned) {
                return true;
            }

            // Vérifier si l'utilisateur est membre de l'équipe associée à l'événement
            if (event.team_id) {
                // Si l'équipe est fournie, utiliser cette information
                if (team) {
                    return this.canViewTeam(user, team);
                }

                // Si l'équipe n'est pas fournie, vérifier si l'utilisateur a des équipes
                if (user.teams && Array.isArray(user.teams)) {
                    return user.teams.includes(event.team_id);
                }
            }
        }

        return false;
    },

    // Vérifie si l'utilisateur peut mettre à jour le statut d'un événement
    canUpdateEventStatus(user, event, team = null) {
        if (!user || !event) return false;

        // Les super_admin ne peuvent pas mettre à jour le statut
        if (user.role === 'super_admin') {
            return false;
        }

        // Les admin ne peuvent pas mettre à jour le statut des événements dans les agendas
        if (user.role === 'admin') {
            // Pour les événements personnels, seul le créateur peut mettre à jour le statut
            if (!event.team_id) {
                return String(user.id) === String(event.creator_id);
            }
            return false; // Les admin ne peuvent pas mettre à jour le statut des événements d'équipe
        }

        // Les employés peuvent mettre à jour le statut des événements qui leur sont assignés
        if (user.role === 'employee') {
            // Si l'événement est assigné à l'utilisateur
            if (String(user.id) === String(event.member_id)) {
                return true;
            }

            // Si l'événement appartient à une équipe dont l'utilisateur est membre
            if (event.team_id) {
                // Vérifier si l'utilisateur est membre de l'équipe via team
                if (team) {
                    if (team.members) {
                        if (Array.isArray(team.members)) {
                            const isMember = team.members.some(member =>
                                String(member.id || member.user_id) === String(user.id)
                            );
                            if (isMember) return true;
                        } else if (typeof team.members === 'object') {
                            if (Object.keys(team.members).includes(String(user.id))) {
                                return true;
                            }
                        }
                    }
                }

                // Vérifier si l'utilisateur a cette équipe dans sa liste d'équipes
                if (user.teams && Array.isArray(user.teams) && user.teams.includes(event.team_id)) {
                    return true;
                }
            }
        }

        return false;
    },

    // Vérifie si l'utilisateur peut gérer un événement personnel
    canManagePersonalEvent(user, event) {
        if (!user || !event) return false;

        // Vérifier si l'événement est personnel (pas d'équipe associée)
        const isPersonal = !event.team_id;

        // Vérifier si l'utilisateur est le créateur de l'événement
        const isCreator = String(user.id) === String(event.creator_id);

        // Seul le créateur peut gérer ses événements personnels
        return isPersonal && isCreator;
    },

    // Retourne un objet complet avec toutes les permissions pour un événement
    checkEventPermissions(user, event, team = null) {
        if (!user || !event) {
            return {
                canView: false,
                canEdit: false,
                canDelete: false,
                canUpdateStatus: false,
                canArchive: false,
                isCreator: false,
                isAssigned: false
            };
        }

        // Vérifier si l'événement est personnel (pas d'équipe associée)
        const isPersonal = !event.team_id;

        // Vérifier si l'utilisateur est le créateur de l'événement
        const isCreator = String(user.id) === String(event.creator_id);

        // Vérifier si l'événement est assigné à l'utilisateur
        const isAssigned = String(user.id) === String(event.member_id);

        // Les super_admin n'ont pas accès aux événements
        if (user.role === 'super_admin') {
            return {
                canView: false,
                canEdit: false,
                canDelete: false,
                canUpdateStatus: false,
                canArchive: false,
                isCreator,
                isAssigned
            };
        }

        // Vérifier si l'utilisateur est responsable de l'équipe associée à l'événement
        let isResponsable = false;
        let isMember = false;

        if (!isPersonal && team) {
            isResponsable = String(user.id) === String(team.responsable);

            // Vérifier si l'utilisateur est membre de l'équipe
            if (team.members) {
                if (Array.isArray(team.members)) {
                    isMember = team.members.some(member =>
                        String(member.id || member.user_id) === String(user.id)
                    );
                } else if (typeof team.members === 'object') {
                    isMember = Object.keys(team.members).includes(String(user.id));
                }
            }
        }

        // Les admin peuvent voir tous les événements d'équipe et ont accès à toutes les actions sauf la mise à jour du statut
        if (user.role === 'admin') {
            return {
                canView: isPersonal ? isCreator : true,
                canEdit: isPersonal ? isCreator : true, // Les admin peuvent modifier tous les événements d'équipe
                canDelete: isPersonal ? isCreator : true, // Les admin peuvent supprimer tous les événements d'équipe
                canUpdateStatus: false, // Les admin ne peuvent pas mettre à jour le statut des événements dans les agendas
                canArchive: isPersonal ? isCreator : true, // Les admin peuvent archiver tous les événements d'équipe
                canUnarchive: isPersonal ? isCreator : true, // Les admin peuvent désarchiver tous les événements d'équipe
                canManage: isPersonal ? isCreator : true, // Les admin peuvent gérer tous les événements d'équipe
                isCreator,
                isAssigned
            };
        }

        // Les employés peuvent voir les événements de leurs équipes et ceux qui leur sont assignés
        // Ils peuvent mettre à jour le statut des événements qui leur sont assignés
        // Ils peuvent gérer leurs propres événements personnels
        if (user.role === 'employee') {
            // Vérifier si l'utilisateur est membre de l'équipe associée à l'événement
            // même si l'équipe n'est pas fournie
            let userIsMember = isMember;
            if (!isPersonal && !userIsMember && event.team_id && user.teams && Array.isArray(user.teams)) {
                userIsMember = user.teams.includes(event.team_id);
            }

            // Vérifier si l'utilisateur peut mettre à jour le statut
            const canUpdateStatus = this.canUpdateEventStatus(user, event, team);

            // Les employés peuvent archiver/désarchiver les événements de leur équipe
            const canArchive = isPersonal ? isCreator : userIsMember;
            const canUnarchive = isPersonal ? isCreator : userIsMember;

            return {
                canView: isPersonal ? isCreator : (userIsMember || isAssigned),
                canEdit: isPersonal ? isCreator : false,
                canDelete: isPersonal ? isCreator : false,
                canUpdateStatus,
                canArchive,
                canUnarchive,
                isCreator,
                isAssigned
            };
        }

        // Par défaut, aucune permission
        return {
            canView: false,
            canEdit: false,
            canDelete: false,
            canUpdateStatus: false,
            canArchive: false,
            canUnarchive: false,
            isCreator,
            isAssigned
        };
    }
};