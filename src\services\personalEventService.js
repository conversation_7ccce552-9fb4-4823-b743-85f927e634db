import axios from 'axios';
import { API_URL } from '@/config/constants';

// Palette de couleurs pastel
const DEFAULT_COLORS = [
  '#CDB4DB', // Violet pastel
  '#FFC8DD', // Rose clair pastel
  '#FFAFCC', // Rose vif pastel
  '#BDE0FE', // Bleu clair pastel
  '#A2D2FF'  // Bleu pastel
];

// Créer une instance axios avec la configuration de base
const axiosInstance = axios.create({
  baseURL: API_URL,
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json'
  }
});

// Ajouter un intercepteur pour les requêtes
axiosInstance.interceptors.request.use(
  (config) => {
    console.log('PersonalEventService - Making request:', {
      url: config.url,
      method: config.method,
      data: config.data
    });

    // Récupérer le token depuis le localStorage
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    } else {
      console.error('PersonalEventService - No auth token found');
    }
    return config;
  },
  (error) => {
    console.error('PersonalEventService - Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Ajouter un intercepteur pour les réponses
axiosInstance.interceptors.response.use(
  (response) => {
    console.log('PersonalEventService - Response received:', {
      url: response.config.url,
      status: response.status,
      data: response.data
    });
    return response;
  },
  async (error) => {
    console.error('PersonalEventService - Response error:', {
      url: error.config?.url,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message
    });

    // Si l'erreur est due à un token expiré (401), essayer de rafraîchir le token
    if (error.response?.status === 401) {
      console.error('PersonalEventService - Authentication error');

      // Vérifier si nous sommes sur la page de connexion ou d'inscription
      const currentPath = window.location.pathname;
      if (currentPath === '/login' || currentPath === '/register') {
        console.log('PersonalEventService - Ignoring auth error on login/register page');
        return Promise.reject(error);
      }

      // Vérifier si nous avons déjà essayé de rafraîchir le token pour cette requête
      if (error.config.__isRetryAttempt) {
        console.log('PersonalEventService - Already attempted to refresh token, redirecting to login');
        // Importer le service d'authentification
        const { authService } = await import('@/services/authService');
        authService.logout();
        window.location.href = '/login';
        return Promise.reject(error);
      }

      try {
        // Importer le service d'authentification
        const { authService } = await import('@/services/authService');

        // Tenter de rafraîchir le token
        const refreshed = await authService.refreshToken();

        if (refreshed) {
          console.log('Token rafraîchi, nouvelle tentative de la requête...');
          // Récupérer le nouveau token
          const newToken = authService.getAccessToken();
          // Mettre à jour le token dans la requête originale
          error.config.headers.Authorization = `Bearer ${newToken}`;
          // Marquer cette requête comme une tentative de rafraîchissement
          error.config.__isRetryAttempt = true;
          // Retenter la requête originale avec le nouveau token
          return axios(error.config);
        } else {
          console.log('PersonalEventService - Token refresh failed, redirecting to login');
          window.location.href = '/login';
        }
      } catch (refreshError) {
        console.error('Échec du rafraîchissement du token:', refreshError);
        // Rediriger vers la page de connexion en cas d'échec
        window.location.href = '/login';
      }
    }

    return Promise.reject(error.response?.data || error);
  }
);

// Validation des données d'événement personnel
const validatePersonalEventData = (data) => {
  console.log('PersonalEventService - Validating personal event data:', data);
  const errors = {};

  if (!data) {
    errors.general = "Données d'événement manquantes";
    return errors;
  }

  // Validation du titre
  if (!data.title) {
    errors.title = "Le titre de l'événement est requis";
  } else if (typeof data.title !== 'string') {
    errors.title = "Le titre doit être une chaîne de caractères";
  } else if (data.title.trim().length < 3) {
    errors.title = "Le titre doit contenir au moins 3 caractères";
  } else if (data.title.length > 100) {
    errors.title = "Le titre ne peut pas dépasser 100 caractères";
  }

  // Validation de la description
  if (data.description) {
    if (typeof data.description !== 'string') {
      errors.description = "La description doit être une chaîne de caractères";
    } else if (data.description.length > 500) {
      errors.description = "La description ne peut pas dépasser 500 caractères";
    }
  }

  // Validation des dates
  if (!data.start_date) {
    errors.start_date = "La date de début est requise";
  } else {
    const startDate = new Date(data.start_date);

    // Vérifier la date de fin si elle existe
    if (data.end_date) {
      const endDate = new Date(data.end_date);
      if (endDate < startDate) {
        errors.end_date = "La date de fin doit être après la date de début";
      }
    }
  }

  // Validation du statut
  if (data.status && !['pending', 'completed', 'archived'].includes(data.status)) {
    errors.status = "Le statut doit être 'pending', 'completed' ou 'archived'";
  }

  if (Object.keys(errors).length > 0) {
    console.warn('PersonalEventService - Validation errors:', errors);
  }
  return errors;
};

const personalEventService = {
  /**
   * Récupère tous les événements personnels de l'utilisateur connecté
   * @param {Object} filters - Filtres optionnels (date, statut, etc.)
   * @returns {Promise<Array>} - Liste des événements personnels
   */
  async getPersonalEvents(filters = {}) {
    try {
      const queryParams = new URLSearchParams();

      // Ajouter les filtres à la requête
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          queryParams.append(key, value);
        }
      });

      console.log('🔧 Query params being sent:', queryParams.toString());

      const response = await axiosInstance.get(`/personal-events/?${queryParams.toString()}`);
      console.log('Réponse de getPersonalEvents:', response.data);

      // Analyser les événements reçus
      if (response.data && Array.isArray(response.data)) {
        const archivedEvents = response.data.filter(event => event.status === 'archived');
        console.log(`📊 Événements reçus: ${response.data.length} total, ${archivedEvents.length} archivés`);

        // Afficher les détails des événements archivés
        archivedEvents.forEach(event => {
          console.log(`📁 Événement archivé: ${event.title} (ID: ${event.id}, status: ${event.status})`);
        });

        // Afficher tous les événements avec leur statut
        console.log('📋 Tous les événements avec statut:');
        response.data.forEach(event => {
          console.log(`  - ${event.title}: status="${event.status}", is_archived=${event.is_archived}`);
        });
      }

      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des événements personnels:', error);
      throw error.response?.data || { message: 'Erreur lors de la récupération des événements personnels' };
    }
  },

  /**
   * Récupère un événement personnel spécifique
   * @param {string} eventId - ID de l'événement personnel
   * @returns {Promise<Object>} - Détails de l'événement personnel
   */
  async getPersonalEvent(eventId) {
    try {
      const response = await axiosInstance.get(`/personal-events/${eventId}/`);
      console.log('Réponse de getPersonalEvent:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Erreur lors de la récupération de l'événement personnel ${eventId}:`, error);
      throw error.response?.data || { message: "Erreur lors de la récupération de l'événement personnel" };
    }
  },

  /**
   * Crée un nouvel événement personnel
   * @param {Object} eventData - Données de l'événement personnel
   * @returns {Promise<Object>} - Événement personnel créé
   */
  async createPersonalEvent(eventData) {
    console.log('PersonalEventService - Validating personal event data: ', eventData);
    const errors = validatePersonalEventData(eventData);
    if (Object.keys(errors).length > 0) {
      throw { errors };
    }

    try {
      // Formater les données pour le backend
      const formattedData = {
        title: eventData.title,
        description: eventData.description || ''
      };

      // Formater les dates correctement
      if (eventData.start_date) {
        if (typeof eventData.start_date === 'string') {
          // Si c'est déjà une chaîne, vérifier le format
          if (eventData.start_date.includes('T')) {
            formattedData.start_date = eventData.start_date.split('T')[0];
          } else {
            formattedData.start_date = eventData.start_date;
          }
        } else {
          // Si c'est un objet Date, le convertir sans problème de timezone
          const date = new Date(eventData.start_date);
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const day = String(date.getDate()).padStart(2, '0');
          formattedData.start_date = `${year}-${month}-${day}`;
        }
      }

      if (eventData.end_date) {
        if (typeof eventData.end_date === 'string') {
          // Si c'est déjà une chaîne, vérifier le format
          if (eventData.end_date.includes('T')) {
            formattedData.end_date = eventData.end_date.split('T')[0];
          } else {
            formattedData.end_date = eventData.end_date;
          }
        } else {
          // Si c'est un objet Date, le convertir sans problème de timezone
          const date = new Date(eventData.end_date);
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const day = String(date.getDate()).padStart(2, '0');
          formattedData.end_date = `${year}-${month}-${day}`;
        }
      }

      // Ajouter les heures
      formattedData.start_time = eventData.start_time || "09:00";
      formattedData.end_time = eventData.end_time || "10:30";

      // Ajouter d'autres champs si nécessaire et s'ils sont présents dans les données
      if (eventData.location) formattedData.location = eventData.location;
      if (eventData.status) formattedData.status = eventData.status;

      // Gérer la couleur de l'événement
      if (eventData.color && typeof eventData.color === 'string' && eventData.color.startsWith('#')) {
        formattedData.color = eventData.color;
      } else {
        // Utiliser une couleur aléatoire de la palette
        const randomIndex = Math.floor(Math.random() * DEFAULT_COLORS.length);
        formattedData.color = DEFAULT_COLORS[randomIndex];
      }

      console.log('PersonalEventService - Making request: ', formattedData);

      const response = await axiosInstance.post('/personal-events/', formattedData);
      console.log('Réponse de createPersonalEvent:', response.data);
      return response.data;
    } catch (error) {
      console.error("PersonalEventService - Response error: ", error.response?.data || error);
      throw error.response?.data || { message: "Erreur lors de la création de l'événement personnel" };
    }
  },

  /**
   * Met à jour un événement personnel existant
   * @param {string} eventId - ID de l'événement personnel
   * @param {Object} eventData - Nouvelles données de l'événement personnel
   * @returns {Promise<Object>} - Événement personnel mis à jour
   */
  async updatePersonalEvent(eventId, eventData) {
    const errors = validatePersonalEventData({ ...eventData, id: eventId });
    if (Object.keys(errors).length > 0) {
      throw { errors };
    }

    try {
      // Formater les données pour le backend
      const formattedData = {
        title: eventData.title,
        description: eventData.description || ''
      };

      // Formater les dates correctement
      if (eventData.start_date) {
        if (typeof eventData.start_date === 'string') {
          // Si c'est déjà une chaîne, vérifier le format
          if (eventData.start_date.includes('T')) {
            formattedData.start_date = eventData.start_date.split('T')[0];
          } else {
            formattedData.start_date = eventData.start_date;
          }
        } else {
          // Si c'est un objet Date, le convertir sans problème de timezone
          const date = new Date(eventData.start_date);
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const day = String(date.getDate()).padStart(2, '0');
          formattedData.start_date = `${year}-${month}-${day}`;
        }
      }

      if (eventData.end_date) {
        if (typeof eventData.end_date === 'string') {
          // Si c'est déjà une chaîne, vérifier le format
          if (eventData.end_date.includes('T')) {
            formattedData.end_date = eventData.end_date.split('T')[0];
          } else {
            formattedData.end_date = eventData.end_date;
          }
        } else {
          // Si c'est un objet Date, le convertir sans problème de timezone
          const date = new Date(eventData.end_date);
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const day = String(date.getDate()).padStart(2, '0');
          formattedData.end_date = `${year}-${month}-${day}`;
        }
      }

      // Ajouter les heures
      formattedData.start_time = eventData.start_time || "09:00";
      formattedData.end_time = eventData.end_time || "10:30";

      // Ajouter d'autres champs si nécessaire et s'ils sont présents dans les données
      if (eventData.location) formattedData.location = eventData.location;
      if (eventData.status) formattedData.status = eventData.status;

      // Gérer la couleur de l'événement
      if (eventData.color && typeof eventData.color === 'string' && eventData.color.startsWith('#')) {
        formattedData.color = eventData.color;
      } else {
        // Utiliser une couleur aléatoire de la palette
        const randomIndex = Math.floor(Math.random() * DEFAULT_COLORS.length);
        formattedData.color = DEFAULT_COLORS[randomIndex];
      }

      console.log('Données formatées pour la mise à jour:', formattedData);

      const response = await axiosInstance.put(`/personal-events/${eventId}/`, formattedData);
      console.log('Réponse de updatePersonalEvent:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Erreur lors de la mise à jour de l'événement personnel ${eventId}:`, error);
      throw error.response?.data || { message: "Erreur lors de la mise à jour de l'événement personnel" };
    }
  },

  /**
   * Met à jour partiellement un événement personnel existant
   * @param {string} eventId - ID de l'événement personnel
   * @param {Object} eventData - Données partielles de l'événement personnel
   * @returns {Promise<Object>} - Événement personnel mis à jour
   */
  async patchPersonalEvent(eventId, eventData) {
    try {
      const response = await axiosInstance.patch(`/personal-events/${eventId}/`, eventData);
      console.log('Réponse de patchPersonalEvent:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Erreur lors de la mise à jour partielle de l'événement personnel ${eventId}:`, error);
      throw error.response?.data || { message: "Erreur lors de la mise à jour de l'événement personnel" };
    }
  },

  /**
   * Met à jour le statut d'un événement personnel
   * @param {string} eventId - ID de l'événement personnel
   * @param {string} status - Nouveau statut ('pending', 'completed', 'archived')
   * @returns {Promise<Object>} - Événement personnel mis à jour
   */
  async updatePersonalEventStatus(eventId, status) {
    if (!['pending', 'completed', 'archived'].includes(status)) {
      throw { message: "Statut invalide. Doit être 'pending', 'completed' ou 'archived'" };
    }

    try {
      const response = await axiosInstance.patch(`/personal-events/${eventId}/`, { status });
      console.log('Réponse de updatePersonalEventStatus:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Erreur lors de la mise à jour du statut de l'événement personnel ${eventId}:`, error);
      throw error.response?.data || { message: "Erreur lors de la mise à jour du statut de l'événement personnel" };
    }
  },

  /**
   * Supprime un événement personnel
   * @param {string} eventId - ID de l'événement personnel
   * @returns {Promise<Object>} - Réponse de suppression
   */
  async deletePersonalEvent(eventId) {
    try {
      const response = await axiosInstance.delete(`/personal-events/${eventId}/`);
      console.log('Réponse de deletePersonalEvent:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Erreur lors de la suppression de l'événement personnel ${eventId}:`, error);
      throw error.response?.data || { message: "Erreur lors de la suppression de l'événement personnel" };
    }
  },

  /**
   * Archive un événement personnel
   * @param {string} eventId - ID de l'événement personnel
   * @returns {Promise<Object>} - Événement personnel archivé
   */
  async archivePersonalEvent(eventId) {
    try {
      console.log(`PersonalEventService - archivePersonalEvent called for event ${eventId}`);
      const response = await axiosInstance.put(`/personal-events/${eventId}/archive/`, {});
      console.log('Réponse de archivePersonalEvent:', response.data);

      // Vérifier que la réponse contient bien l'événement archivé
      const archivedEvent = response.data.event || response.data;
      if (archivedEvent && archivedEvent.id) {
        console.log('Événement archivé reçu:', archivedEvent);
        console.log('Status:', archivedEvent.status);
        console.log('is_archived:', archivedEvent.is_archived);

        // S'assurer que l'événement retourné a les bons champs
        if (archivedEvent.status === 'archived') {
          console.log('✅ Archivage confirmé par le backend');
        } else {
          console.warn('⚠️ Le backend n\'a pas confirmé l\'archivage');
        }
      } else {
        console.warn('Réponse d\'archivage inattendue:', response.data);
      }

      return response.data;
    } catch (error) {
      console.error(`Erreur lors de l'archivage de l'événement personnel ${eventId}:`, error);
      throw error.response?.data || { message: "Erreur lors de l'archivage de l'événement personnel" };
    }
  },

  /**
   * Désarchive un événement personnel
   * @param {string} eventId - ID de l'événement personnel
   * @returns {Promise<Object>} - Événement personnel désarchivé
   */
  async unarchivePersonalEvent(eventId) {
    try {
      console.log(`PersonalEventService - unarchivePersonalEvent called for event ${eventId}`);
      const response = await axiosInstance.put(`/personal-events/${eventId}/unarchive/`, {});
      console.log('Réponse de unarchivePersonalEvent:', response.data);

      // Vérifier que la réponse contient bien l'événement désarchivé
      const unarchivedEvent = response.data.event || response.data;
      if (unarchivedEvent && unarchivedEvent.id) {
        console.log('Événement désarchivé reçu:', unarchivedEvent);
        console.log('is_archived status:', unarchivedEvent.is_archived);
      } else {
        console.warn('Réponse de désarchivage inattendue:', response.data);
      }

      return response.data;
    } catch (error) {
      console.error(`Erreur lors du désarchivage de l'événement personnel ${eventId}:`, error);
      throw error.response?.data || { message: "Erreur lors du désarchivage de l'événement personnel" };
    }
  },

  /**
   * Récupère tous les événements personnels archivés
   * @returns {Promise<Array>} - Liste des événements personnels archivés
   */
  async getArchivedPersonalEvents() {
    try {
      console.log('PersonalEventService - getArchivedPersonalEvents called');
      const response = await axiosInstance.get('/personal-events/archived/list/');
      console.log('Réponse de getArchivedPersonalEvents:', response.data);

      // Le backend retourne une structure spécifique
      const data = response.data;
      if (data && data.archived_events && Array.isArray(data.archived_events)) {
        console.log(`✅ ${data.total_archived} événements archivés récupérés`);
        return data.archived_events;
      } else {
        console.warn('Structure de réponse inattendue pour les événements archivés:', data);
        return [];
      }
    } catch (error) {
      console.error('Erreur lors de la récupération des événements personnels archivés:', error);
      throw error.response?.data || { message: "Erreur lors de la récupération des événements personnels archivés" };
    }
  }
};

export default personalEventService;