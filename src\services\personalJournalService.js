import axios from 'axios';
import { API_URL } from '@/config/constants';

// Créer une instance axios avec la configuration de base
const axiosInstance = axios.create({
  baseURL: API_URL,
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json'
  }
});

// Ajouter un intercepteur pour les requêtes
axiosInstance.interceptors.request.use(
  (config) => {
    console.log('PersonalJournalService - Making request:', {
      url: config.url,
      method: config.method,
      data: config.data
    });

    // Vérifier si nous sommes sur la page d'accueil
    const currentPath = window.location.pathname;
    if (currentPath === '/' || currentPath === '') {
      console.log('PersonalJournalService - Annulation de la requête sur la page d\'accueil');
      // Annuler la requête sur la page d'accueil
      const error = new Error('Requête annulée sur la page d\'accueil');
      error.canceled = true;
      return Promise.reject(error);
    }

    // Récupérer le token depuis le localStorage
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    } else {
      console.error('PersonalJournalService - No auth token found');
    }
    return config;
  },
  (error) => {
    console.error('PersonalJournalService - Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Ajouter un intercepteur pour les réponses
axiosInstance.interceptors.response.use(
  (response) => {
    console.log('PersonalJournalService - Response received:', {
      url: response.config.url,
      status: response.status,
      data: response.data
    });
    return response;
  },
  async (error) => {
    // Si l'erreur est due à une annulation volontaire (page d'accueil), ne pas afficher d'erreur
    if (error.canceled) {
      console.log('PersonalJournalService - Requête annulée volontairement');
      return Promise.reject(error);
    }

    console.error('PersonalJournalService - Response error:', {
      url: error.config?.url,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message
    });

    // Vérifier si nous sommes sur la page d'accueil
    const currentPath = window.location.pathname;
    if (currentPath === '/' || currentPath === '') {
      console.log('PersonalJournalService - Ignoring error on home page');
      return Promise.reject(error);
    }

    // Si l'erreur est due à un token expiré (401), essayer de rafraîchir le token
    if (error.response?.status === 401) {
      console.error('PersonalJournalService - Authentication error');

      // Vérifier si nous sommes sur la page de connexion ou d'inscription
      if (currentPath === '/login' || currentPath === '/register') {
        console.log('PersonalJournalService - Ignoring auth error on login/register page');
        return Promise.reject(error);
      }

      // Vérifier si nous avons déjà essayé de rafraîchir le token pour cette requête
      if (error.config.__isRetryAttempt) {
        console.log('PersonalJournalService - Already attempted to refresh token, redirecting to login');
        // Importer le service d'authentification
        const { authService } = await import('@/services/authService');
        authService.logout();
        window.location.href = '/login';
        return Promise.reject(error);
      }

      try {
        // Importer le service d'authentification
        const { authService } = await import('@/services/authService');

        // Tenter de rafraîchir le token
        const refreshed = await authService.refreshToken();

        if (refreshed) {
          console.log('Token rafraîchi, nouvelle tentative de la requête...');
          // Récupérer le nouveau token
          const newToken = authService.getAccessToken();
          // Mettre à jour le token dans la requête originale
          error.config.headers.Authorization = `Bearer ${newToken}`;
          // Marquer cette requête comme une tentative de rafraîchissement
          error.config.__isRetryAttempt = true;
          // Retenter la requête originale avec le nouveau token
          return axios(error.config);
        } else {
          console.log('PersonalJournalService - Token refresh failed, redirecting to login');
          window.location.href = '/login';
        }
      } catch (refreshError) {
        console.error('Échec du rafraîchissement du token:', refreshError);
        // Rediriger vers la page de connexion en cas d'échec
        window.location.href = '/login';
      }
    }

    return Promise.reject(error.response?.data || error);
  }
);

// Validation des données de journal personnel
const validatePersonalJournalData = (data) => {
  console.log('PersonalJournalService - Validating personal journal data:', data);
  const errors = {};

  if (!data) {
    errors.general = "Données de journal manquantes";
    return errors;
  }

  // Validation du titre
  if (!data.title) {
    errors.title = "Le titre du journal est requis";
  } else if (typeof data.title !== 'string') {
    errors.title = "Le titre doit être une chaîne de caractères";
  } else if (data.title.trim().length < 3) {
    errors.title = "Le titre doit contenir au moins 3 caractères";
  } else if (data.title.length > 100) {
    errors.title = "Le titre ne peut pas dépasser 100 caractères";
  }

  // Validation du contenu
  if (!data.content) {
    errors.content = "Le contenu du journal est requis";
  } else if (typeof data.content !== 'string') {
    errors.content = "Le contenu doit être une chaîne de caractères";
  } else if (data.content.length > 20000) {
    errors.content = "Le contenu ne peut pas dépasser 20000 caractères";
  }

  // Validation de la date
  if (!data.date) {
    errors.date = "La date du journal est requise";
  } else {
    // Vérifier que la date est au format YYYY-MM-DD
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(data.date)) {
      errors.date = "La date doit être au format YYYY-MM-DD";
    } else {
      // Vérifier que la date est valide
      const dateObj = new Date(data.date);
      if (isNaN(dateObj.getTime())) {
        errors.date = "La date saisie n'est pas valide";
      }
    }
  }

  if (Object.keys(errors).length > 0) {
    console.warn('PersonalJournalService - Validation errors:', errors);
  }
  return errors;
};

const personalJournalService = {
  /**
   * Récupère tous les journaux personnels de l'utilisateur connecté
   * @param {Object} filters - Filtres optionnels
   * @returns {Promise<Array>} - Liste des journaux personnels
   */
  async getPersonalJournals(filters = {}) {
    try {
      const queryParams = new URLSearchParams();

      // Ajouter les filtres à la requête
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          queryParams.append(key, value);
        }
      });

      const response = await axiosInstance.get(`/personal-journals/?${queryParams.toString()}`);
      console.log('Réponse de getPersonalJournals:', response.data);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des journaux personnels:', error);
      throw error.response?.data || { message: 'Erreur lors de la récupération des journaux personnels' };
    }
  },

  /**
   * Récupère un journal personnel spécifique
   * @param {string} journalId - ID du journal personnel
   * @returns {Promise<Object>} - Détails du journal personnel
   */
  async getPersonalJournal(journalId) {
    try {
      const response = await axiosInstance.get(`/personal-journals/${journalId}/`);
      console.log('Réponse de getPersonalJournal:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Erreur lors de la récupération du journal personnel ${journalId}:`, error);
      throw error.response?.data || { message: "Erreur lors de la récupération du journal personnel" };
    }
  },

  /**
   * Crée un nouveau journal personnel
   * @param {Object} journalData - Données du journal personnel
   * @returns {Promise<Object>} - Journal personnel créé
   */
  async createPersonalJournal(journalData) {
    // Vérifier que les données sont présentes
    if (!journalData) {
      throw { message: "Aucune donnée fournie pour la création du journal" };
    }

    // Nettoyer les données
    const cleanedData = {
      title: journalData.title ? journalData.title.trim() : '',
      content: journalData.content ? journalData.content.trim() : '',
      date: journalData.date ? journalData.date.trim() : ''
    };

    // Ajouter l'humeur si présente
    if (journalData.mood) {
      cleanedData.mood = journalData.mood.trim();
    }

    // Valider les données nettoyées
    const errors = validatePersonalJournalData(cleanedData);
    if (Object.keys(errors).length > 0) {
      throw { errors };
    }

    try {
      console.log('PersonalJournalService - Tentative de création de journal personnel avec données nettoyées:', cleanedData);

      // Vérifier explicitement le format de la date
      if (cleanedData.date) {
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (!dateRegex.test(cleanedData.date)) {
          console.error("Format de date incorrect:", cleanedData.date);
          throw { message: "La date doit être au format YYYY-MM-DD", field: "date" };
        }
      }

      const response = await axiosInstance.post('/personal-journals/', cleanedData);
      console.log('Réponse de createPersonalJournal:', response.data);
      return response.data;
    } catch (error) {
      console.error("Erreur lors de la création du journal personnel:", error);

      // Vérifier si c'est une erreur d'autorisation
      if (error.response?.status === 403) {
        throw { message: "Vous n'avez pas l'autorisation de créer un journal personnel. Veuillez contacter l'administrateur." };
      }

      // Vérifier si c'est une erreur de validation du backend
      if (error.response?.status === 400) {
        // Extraire les erreurs spécifiques du backend
        const backendErrors = error.response.data;

        if (backendErrors.date) {
          throw { message: `Erreur de date: ${backendErrors.date}`, field: "date" };
        } else if (backendErrors.title) {
          throw { message: `Erreur de titre: ${backendErrors.title}`, field: "title" };
        } else if (backendErrors.content) {
          throw { message: `Erreur de contenu: ${backendErrors.content}`, field: "content" };
        } else if (typeof backendErrors === 'object') {
          const errorMessages = Object.entries(backendErrors)
            .map(([field, message]) => `${field}: ${message}`)
            .join(', ');
          throw { message: `Données invalides: ${errorMessages}` };
        } else {
          throw { message: "Données invalides. Veuillez vérifier les informations saisies." };
        }
      }

      // Si l'erreur a déjà été formatée
      if (error.message) {
        throw error;
      }

      // Autres erreurs
      throw error.response?.data || { message: "Erreur lors de la création du journal personnel. Veuillez réessayer plus tard." };
    }
  },

  /**
   * Met à jour un journal personnel existant
   * @param {string} journalId - ID du journal personnel
   * @param {Object} journalData - Nouvelles données du journal personnel
   * @returns {Promise<Object>} - Journal personnel mis à jour
   */
  async updatePersonalJournal(journalId, journalData) {
    const errors = validatePersonalJournalData({ ...journalData, id: journalId });
    if (Object.keys(errors).length > 0) {
      throw { errors };
    }

    try {
      const response = await axiosInstance.put(`/personal-journals/${journalId}/`, journalData);
      console.log('Réponse de updatePersonalJournal:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Erreur lors de la mise à jour du journal personnel ${journalId}:`, error);
      throw error.response?.data || { message: "Erreur lors de la mise à jour du journal personnel" };
    }
  },

  /**
   * Archive un journal personnel
   * @param {string} journalId - ID du journal personnel
   * @returns {Promise<Object>} - Journal personnel archivé
   */
  async archivePersonalJournal(journalId) {
    try {
      const response = await axiosInstance.put(`/personal-journals/${journalId}/archive/`);
      console.log('Réponse de archivePersonalJournal:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Erreur lors de l'archivage du journal personnel ${journalId}:`, error);
      throw error.response?.data || { message: "Erreur lors de l'archivage du journal personnel" };
    }
  },

  /**
   * Désarchive un journal personnel
   * @param {string} journalId - ID du journal personnel
   * @returns {Promise<Object>} - Journal personnel désarchivé
   */
  async unarchivePersonalJournal(journalId) {
    try {
      const response = await axiosInstance.put(`/personal-journals/${journalId}/unarchive/`);
      console.log('Réponse de unarchivePersonalJournal:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Erreur lors du désarchivage du journal personnel ${journalId}:`, error);
      throw error.response?.data || { message: "Erreur lors du désarchivage du journal personnel" };
    }
  },

  /**
   * Supprime un journal personnel
   * @param {string} journalId - ID du journal personnel
   * @returns {Promise<Object>} - Réponse de suppression
   */
  async deletePersonalJournal(journalId) {
    try {
      const response = await axiosInstance.delete(`/personal-journals/${journalId}/`);
      console.log('Réponse de deletePersonalJournal:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Erreur lors de la suppression du journal personnel ${journalId}:`, error);
      throw error.response?.data || { message: "Erreur lors de la suppression du journal personnel" };
    }
  }
};

export default personalJournalService;
