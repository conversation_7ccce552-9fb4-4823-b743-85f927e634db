import axios from 'axios';
import { API_URL } from '@/config/constants';

// Créer une instance axios avec la configuration de base
const axiosInstance = axios.create({
  baseURL: API_URL,
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json'
  }
});

// Ajouter un intercepteur pour les requêtes
axiosInstance.interceptors.request.use(
  (config) => {
    console.log('PersonalNoteService - Making request:', {
      url: config.url,
      method: config.method,
      data: config.data
    });

    // Vérifier si nous sommes sur la page d'accueil
    const currentPath = window.location.pathname;
    if (currentPath === '/' || currentPath === '') {
      console.log('PersonalNoteService - Annulation de la requête sur la page d\'accueil');
      // Annuler la requête sur la page d'accueil
      const error = new Error('Requête annulée sur la page d\'accueil');
      error.canceled = true;
      return Promise.reject(error);
    }

    // Récupérer le token depuis le localStorage
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    } else {
      console.error('PersonalNoteService - No auth token found');
    }
    return config;
  },
  (error) => {
    console.error('PersonalNoteService - Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Ajouter un intercepteur pour les réponses
axiosInstance.interceptors.response.use(
  (response) => {
    console.log('PersonalNoteService - Response received:', {
      url: response.config.url,
      status: response.status,
      data: response.data
    });
    return response;
  },
  async (error) => {
    // Si l'erreur est due à une annulation volontaire (page d'accueil), ne pas afficher d'erreur
    if (error.canceled) {
      console.log('PersonalNoteService - Requête annulée volontairement');
      return Promise.reject(error);
    }

    console.error('PersonalNoteService - Response error:', {
      url: error.config?.url,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message
    });

    // Vérifier si nous sommes sur la page d'accueil
    const currentPath = window.location.pathname;
    if (currentPath === '/' || currentPath === '') {
      console.log('PersonalNoteService - Ignoring error on home page');
      return Promise.reject(error);
    }

    // Si l'erreur est due à un token expiré (401), essayer de rafraîchir le token
    if (error.response?.status === 401) {
      console.error('PersonalNoteService - Authentication error');

      // Vérifier si nous sommes sur la page de connexion ou d'inscription
      if (currentPath === '/login' || currentPath === '/register') {
        console.log('PersonalNoteService - Ignoring auth error on login/register page');
        return Promise.reject(error);
      }

      // Vérifier si nous avons déjà essayé de rafraîchir le token pour cette requête
      if (error.config.__isRetryAttempt) {
        console.log('PersonalNoteService - Already attempted to refresh token, redirecting to login');
        // Importer le service d'authentification
        const { authService } = await import('@/services/authService');
        authService.logout();
        window.location.href = '/login';
        return Promise.reject(error);
      }

      try {
        // Importer le service d'authentification
        const { authService } = await import('@/services/authService');

        // Tenter de rafraîchir le token
        const refreshed = await authService.refreshToken();

        if (refreshed) {
          console.log('Token rafraîchi, nouvelle tentative de la requête...');
          // Récupérer le nouveau token
          const newToken = authService.getAccessToken();
          // Mettre à jour le token dans la requête originale
          error.config.headers.Authorization = `Bearer ${newToken}`;
          // Marquer cette requête comme une tentative de rafraîchissement
          error.config.__isRetryAttempt = true;
          // Retenter la requête originale avec le nouveau token
          return axios(error.config);
        } else {
          console.log('PersonalNoteService - Token refresh failed, redirecting to login');
          window.location.href = '/login';
        }
      } catch (refreshError) {
        console.error('Échec du rafraîchissement du token:', refreshError);
        // Rediriger vers la page de connexion en cas d'échec
        window.location.href = '/login';
      }
    }

    return Promise.reject(error.response?.data || error);
  }
);

// Validation des données de note personnelle
const validatePersonalNoteData = (data) => {
  console.log('PersonalNoteService - Validating personal note data:', data);
  const errors = {};

  if (!data) {
    errors.general = "Données de note manquantes";
    return errors;
  }

  // Validation du titre
  if (!data.title) {
    errors.title = "Le titre de la note est requis";
  } else if (typeof data.title !== 'string') {
    errors.title = "Le titre doit être une chaîne de caractères";
  } else if (data.title.trim().length < 3) {
    errors.title = "Le titre doit contenir au moins 3 caractères";
  } else if (data.title.length > 100) {
    errors.title = "Le titre ne peut pas dépasser 100 caractères";
  }

  // Validation du contenu
  if (!data.content) {
    errors.content = "Le contenu de la note est requis";
  } else if (typeof data.content !== 'string') {
    errors.content = "Le contenu doit être une chaîne de caractères";
  } else if (data.content.length > 10000) {
    errors.content = "Le contenu ne peut pas dépasser 10000 caractères";
  }

  if (Object.keys(errors).length > 0) {
    console.warn('PersonalNoteService - Validation errors:', errors);
  }
  return errors;
};

const personalNoteService = {
  /**
   * Récupère toutes les notes personnelles de l'utilisateur connecté
   * @param {Object} filters - Filtres optionnels
   * @returns {Promise<Array>} - Liste des notes personnelles
   */
  async getPersonalNotes(filters = {}) {
    try {
      const queryParams = new URLSearchParams();

      // Ajouter les filtres à la requête
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          queryParams.append(key, value);
        }
      });

      const response = await axiosInstance.get(`/personal-notes/?${queryParams.toString()}`);
      console.log('Réponse de getPersonalNotes:', response.data);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des notes personnelles:', error);
      throw error.response?.data || { message: 'Erreur lors de la récupération des notes personnelles' };
    }
  },

  /**
   * Récupère une note personnelle spécifique
   * @param {string} noteId - ID de la note personnelle
   * @returns {Promise<Object>} - Détails de la note personnelle
   */
  async getPersonalNote(noteId) {
    try {
      const response = await axiosInstance.get(`/personal-notes/${noteId}/`);
      console.log('Réponse de getPersonalNote:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Erreur lors de la récupération de la note personnelle ${noteId}:`, error);
      throw error.response?.data || { message: "Erreur lors de la récupération de la note personnelle" };
    }
  },

  /**
   * Crée une nouvelle note personnelle
   * @param {Object} noteData - Données de la note personnelle
   * @returns {Promise<Object>} - Note personnelle créée
   */
  async createPersonalNote(noteData) {
    const errors = validatePersonalNoteData(noteData);
    if (Object.keys(errors).length > 0) {
      throw { errors };
    }

    try {
      const response = await axiosInstance.post('/personal-notes/', noteData);
      console.log('Réponse de createPersonalNote:', response.data);
      return response.data;
    } catch (error) {
      console.error("Erreur lors de la création de la note personnelle:", error);
      throw error.response?.data || { message: "Erreur lors de la création de la note personnelle" };
    }
  },

  /**
   * Met à jour une note personnelle existante
   * @param {string} noteId - ID de la note personnelle
   * @param {Object} noteData - Nouvelles données de la note personnelle
   * @returns {Promise<Object>} - Note personnelle mise à jour
   */
  async updatePersonalNote(noteId, noteData) {
    const errors = validatePersonalNoteData({ ...noteData, id: noteId });
    if (Object.keys(errors).length > 0) {
      throw { errors };
    }

    try {
      const response = await axiosInstance.put(`/personal-notes/${noteId}/`, noteData);
      console.log('Réponse de updatePersonalNote:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Erreur lors de la mise à jour de la note personnelle ${noteId}:`, error);
      throw error.response?.data || { message: "Erreur lors de la mise à jour de la note personnelle" };
    }
  },

  /**
   * Archive une note personnelle
   * @param {string} noteId - ID de la note personnelle
   * @returns {Promise<Object>} - Note personnelle archivée
   */
  async archivePersonalNote(noteId) {
    try {
      const response = await axiosInstance.put(`/personal-notes/${noteId}/archive/`);
      console.log('Réponse de archivePersonalNote:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Erreur lors de l'archivage de la note personnelle ${noteId}:`, error);
      throw error.response?.data || { message: "Erreur lors de l'archivage de la note personnelle" };
    }
  },

  /**
   * Désarchive une note personnelle
   * @param {string} noteId - ID de la note personnelle
   * @returns {Promise<Object>} - Note personnelle désarchivée
   */
  async unarchivePersonalNote(noteId) {
    try {
      const response = await axiosInstance.put(`/personal-notes/${noteId}/unarchive/`);
      console.log('Réponse de unarchivePersonalNote:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Erreur lors du désarchivage de la note personnelle ${noteId}:`, error);
      throw error.response?.data || { message: "Erreur lors du désarchivage de la note personnelle" };
    }
  },

  /**
   * Supprime une note personnelle
   * @param {string} noteId - ID de la note personnelle
   * @returns {Promise<Object>} - Réponse de suppression
   */
  async deletePersonalNote(noteId) {
    try {
      const response = await axiosInstance.delete(`/personal-notes/${noteId}/`);
      console.log('Réponse de deletePersonalNote:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Erreur lors de la suppression de la note personnelle ${noteId}:`, error);
      throw error.response?.data || { message: "Erreur lors de la suppression de la note personnelle" };
    }
  }
};

export default personalNoteService;
