import axios from 'axios';
import { API_URL } from '@/config/constants';

// Créer une instance axios avec la configuration de base
const axiosInstance = axios.create({
  baseURL: API_URL,
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json'
  }
});

// Ajouter un intercepteur pour les requêtes
axiosInstance.interceptors.request.use(
  (config) => {
    console.log('PersonalTaskService - Making request:', {
      url: config.url,
      method: config.method,
      data: config.data
    });

    // Récupérer le token depuis le localStorage
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    } else {
      console.error('PersonalTaskService - No auth token found');
    }
    return config;
  },
  (error) => {
    console.error('PersonalTaskService - Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Ajouter un intercepteur pour les réponses
axiosInstance.interceptors.response.use(
  (response) => {
    console.log('PersonalTaskService - Response received:', {
      url: response.config.url,
      status: response.status,
      data: response.data
    });
    return response;
  },
  async (error) => {
    console.error('PersonalTaskService - Response error:', {
      url: error.config?.url,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message
    });

    // Si l'erreur est due à un token expiré (401), essayer de rafraîchir le token
    if (error.response?.status === 401) {
      console.error('PersonalTaskService - Authentication error');

      // Vérifier si nous sommes sur la page de connexion ou d'inscription
      const currentPath = window.location.pathname;
      if (currentPath === '/login' || currentPath === '/register') {
        console.log('PersonalTaskService - Ignoring auth error on login/register page');
        return Promise.reject(error);
      }

      // Vérifier si nous avons déjà essayé de rafraîchir le token pour cette requête
      if (error.config.__isRetryAttempt) {
        console.log('PersonalTaskService - Already attempted to refresh token, redirecting to login');
        // Importer le service d'authentification
        const { authService } = await import('@/services/authService');
        authService.logout();
        window.location.href = '/login';
        return Promise.reject(error);
      }

      try {
        // Importer le service d'authentification
        const { authService } = await import('@/services/authService');

        // Tenter de rafraîchir le token
        const refreshed = await authService.refreshToken();

        if (refreshed) {
          console.log('Token rafraîchi, nouvelle tentative de la requête...');
          // Récupérer le nouveau token
          const newToken = authService.getAccessToken();
          // Mettre à jour le token dans la requête originale
          error.config.headers.Authorization = `Bearer ${newToken}`;
          // Marquer cette requête comme une tentative de rafraîchissement
          error.config.__isRetryAttempt = true;
          // Retenter la requête originale avec le nouveau token
          return axios(error.config);
        } else {
          console.log('PersonalTaskService - Token refresh failed, redirecting to login');
          window.location.href = '/login';
        }
      } catch (refreshError) {
        console.error('Échec du rafraîchissement du token:', refreshError);
        // Rediriger vers la page de connexion en cas d'échec
        window.location.href = '/login';
      }
    }

    return Promise.reject(error.response?.data || error);
  }
);

// Validation des données de tâche personnelle
const validatePersonalTaskData = (data) => {
  console.log('PersonalTaskService - Validating personal task data:', data);
  const errors = {};

  if (!data) {
    errors.general = "Données de tâche manquantes";
    return errors;
  }

  // Validation du titre
  if (!data.title) {
    errors.title = "Le titre de la tâche est requis";
  } else if (typeof data.title !== 'string') {
    errors.title = "Le titre doit être une chaîne de caractères";
  } else if (data.title.trim().length < 3) {
    errors.title = "Le titre doit contenir au moins 3 caractères";
  } else if (data.title.length > 100) {
    errors.title = "Le titre ne peut pas dépasser 100 caractères";
  }

  // Validation de la description
  if (data.description) {
    if (typeof data.description !== 'string') {
      errors.description = "La description doit être une chaîne de caractères";
    } else if (data.description.length > 500) {
      errors.description = "La description ne peut pas dépasser 500 caractères";
    }
  }

  // Validation des dates
  if (!data.start_date) {
    errors.start_date = "La date de début est requise";
  } else {
    try {
      const startDate = new Date(data.start_date);

      // Vérifier que la date est valide
      if (isNaN(startDate.getTime())) {
        errors.start_date = "La date de début n'est pas valide";
      } else {
        // Vérifier la date de fin si elle existe
        if (data.end_date) {
          const endDate = new Date(data.end_date);
          if (isNaN(endDate.getTime())) {
            errors.end_date = "La date de fin n'est pas valide";
          } else if (endDate < startDate) {
            errors.end_date = "La date de fin doit être après la date de début";
          }
        }
      }
    } catch (error) {
      errors.start_date = "Format de date invalide";
    }
  }

  // Validation du statut (suppression de 'en_revision')
  if (data.status && !['a_faire', 'en_cours', 'achevee', 'archived'].includes(data.status)) {
    errors.status = "Le statut doit être 'a_faire', 'en_cours', 'achevee' ou 'archived'";
  }

  // Validation de la priorité
  if (data.priority && !['faible', 'moyenne', 'haute'].includes(data.priority)) {
    errors.priority = "La priorité doit être 'faible', 'moyenne' ou 'haute'";
  }

  if (Object.keys(errors).length > 0) {
    console.warn('PersonalTaskService - Validation errors:', errors);
  }
  return errors;
};

const personalTaskService = {
  /**
   * Récupère toutes les tâches personnelles de l'utilisateur connecté
   * @param {Object} filters - Filtres optionnels (statut, priorité, etc.)
   * @returns {Promise<Array>} - Liste des tâches personnelles
   */
  async getPersonalTasks(filters = {}) {
    try {
      const queryParams = new URLSearchParams();

      // Ajouter les filtres à la requête
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          queryParams.append(key, value);
        }
      });

      const response = await axiosInstance.get(`/personal-tasks/?${queryParams.toString()}`);
      console.log('Réponse de getPersonalTasks:', response.data);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des tâches personnelles:', error);
      throw error.response?.data || { message: 'Erreur lors de la récupération des tâches personnelles' };
    }
  },

  /**
   * Récupère une tâche personnelle spécifique
   * @param {string} taskId - ID de la tâche personnelle
   * @returns {Promise<Object>} - Détails de la tâche personnelle
   */
  async getPersonalTask(taskId) {
    try {
      const response = await axiosInstance.get(`/personal-tasks/${taskId}/`);
      console.log('Réponse de getPersonalTask:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Erreur lors de la récupération de la tâche personnelle ${taskId}:`, error);
      throw error.response?.data || { message: "Erreur lors de la récupération de la tâche personnelle" };
    }
  },

  /**
   * Crée une nouvelle tâche personnelle
   * @param {Object} taskData - Données de la tâche personnelle
   * @returns {Promise<Object>} - Tâche personnelle créée
   */
  async createPersonalTask(taskData) {
    const errors = validatePersonalTaskData(taskData);
    if (Object.keys(errors).length > 0) {
      throw { errors };
    }

    try {
      const response = await axiosInstance.post('/personal-tasks/', taskData);
      console.log('Réponse de createPersonalTask:', response.data);
      return response.data;
    } catch (error) {
      console.error("Erreur lors de la création de la tâche personnelle:", error);
      throw error.response?.data || { message: "Erreur lors de la création de la tâche personnelle" };
    }
  },

  /**
   * Met à jour une tâche personnelle existante
   * @param {string} taskId - ID de la tâche personnelle
   * @param {Object} taskData - Nouvelles données de la tâche personnelle
   * @returns {Promise<Object>} - Tâche personnelle mise à jour
   */
  async updatePersonalTask(taskId, taskData) {
    const errors = validatePersonalTaskData({ ...taskData, id: taskId });
    if (Object.keys(errors).length > 0) {
      throw { errors };
    }

    try {
      // Mettre à jour la tâche
      const putResponse = await axiosInstance.put(`/personal-tasks/${taskId}/`, taskData);
      console.log('Réponse PUT de updatePersonalTask:', putResponse.data);

      // Récupérer la tâche complète mise à jour
      const getResponse = await axiosInstance.get(`/personal-tasks/${taskId}/`);
      console.log('Réponse GET de updatePersonalTask (tâche complète):', getResponse.data);

      return getResponse.data;
    } catch (error) {
      console.error(`Erreur lors de la mise à jour de la tâche personnelle ${taskId}:`, error);
      throw error.response?.data || { message: "Erreur lors de la mise à jour de la tâche personnelle" };
    }
  },

  /**
   * Met à jour le statut d'une tâche personnelle
   * @param {string} taskId - ID de la tâche personnelle
   * @param {string} status - Nouveau statut ('a_faire', 'en_cours', 'en_revision', 'achevee', 'archived')
   * @returns {Promise<Object>} - Tâche personnelle mise à jour
   */
  async updatePersonalTaskStatus(taskId, status) {
    if (!['a_faire', 'en_cours', 'en_revision', 'achevee', 'archived'].includes(status)) {
      throw { message: "Statut invalide. Doit être 'a_faire', 'en_cours', 'en_revision', 'achevee' ou 'archived'" };
    }

    try {
      // Mettre à jour le statut
      const patchResponse = await axiosInstance.patch(`/personal-tasks/${taskId}/`, { status });
      console.log('Réponse PATCH de updatePersonalTaskStatus:', patchResponse.data);

      // Récupérer la tâche complète mise à jour
      const getResponse = await axiosInstance.get(`/personal-tasks/${taskId}/`);
      console.log('Réponse GET de updatePersonalTaskStatus (tâche complète):', getResponse.data);

      return getResponse.data;
    } catch (error) {
      console.error(`Erreur lors de la mise à jour du statut de la tâche personnelle ${taskId}:`, error);
      throw error.response?.data || { message: "Erreur lors de la mise à jour du statut de la tâche personnelle" };
    }
  },

  /**
   * Archive une tâche personnelle
   * @param {string} taskId - ID de la tâche personnelle
   * @returns {Promise<Object>} - Tâche personnelle archivée
   */
  async archivePersonalTask(taskId) {
    try {
      const response = await axiosInstance.put(`/personal-tasks/${taskId}/archive/`);
      console.log('Réponse de archivePersonalTask:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Erreur lors de l'archivage de la tâche personnelle ${taskId}:`, error);
      throw error.response?.data || { message: "Erreur lors de l'archivage de la tâche personnelle" };
    }
  },

  /**
   * Désarchive une tâche personnelle
   * @param {string} taskId - ID de la tâche personnelle
   * @returns {Promise<Object>} - Tâche personnelle désarchivée
   */
  async unarchivePersonalTask(taskId) {
    try {
      const response = await axiosInstance.put(`/personal-tasks/${taskId}/unarchive/`);
      console.log('Réponse de unarchivePersonalTask:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Erreur lors du désarchivage de la tâche personnelle ${taskId}:`, error);
      throw error.response?.data || { message: "Erreur lors du désarchivage de la tâche personnelle" };
    }
  },

  /**
   * Supprime une tâche personnelle
   * @param {string} taskId - ID de la tâche personnelle
   * @returns {Promise<Object>} - Réponse de suppression
   */
  async deletePersonalTask(taskId) {
    try {
      const response = await axiosInstance.delete(`/personal-tasks/${taskId}/`);
      console.log('Réponse de deletePersonalTask:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Erreur lors de la suppression de la tâche personnelle ${taskId}:`, error);
      throw error.response?.data || { message: "Erreur lors de la suppression de la tâche personnelle" };
    }
  }
};

export default personalTaskService;
