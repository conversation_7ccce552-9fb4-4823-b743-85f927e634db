import axios from 'axios';
import authService from './authService';

import { API_URL } from '@/config/constants';

/**
 * Service pour la gestion des profils utilisateur
 * Compatible avec la nouvelle logique backend first_name/last_name
 */
const profileService = {
    /**
     * Met à jour le profil utilisateur
     * @param {Object} profileData - Données du profil
     * @param {string} profileData.first_name - Prénom
     * @param {string} profileData.last_name - Nom de famille
     * @param {string} profileData.email - Email
     * @param {string} profileData.phone - Téléphone (optionnel)
     * @returns {Promise<Object>} Réponse du serveur
     */
    updateProfile: async (profileData) => {
        try {
            console.log('ProfileService - Mise à jour du profil:', {
                first_name: profileData.first_name,
                last_name: profileData.last_name,
                email: profileData.email,
                phone: profileData.phone || 'non fourni'
            });

            const response = await axios.put(
                `${API_URL}/profile/update/`,
                {
                    first_name: profileData.first_name?.trim(),
                    last_name: profileData.last_name?.trim(),
                    email: profileData.email?.trim(),
                    phone: profileData.phone?.trim() || undefined
                },
                {
                    headers: {
                        ...authService.getAuthHeader(),
                        'Content-Type': 'application/json'
                    }
                }
            );

            console.log('ProfileService - Réponse reçue:', response.data);

            return {
                success: true,
                data: response.data,
                user: response.data.user,
                message: response.data.message || 'Profil mis à jour avec succès'
            };
        } catch (error) {
            console.error('ProfileService - Erreur lors de la mise à jour:', error);

            return {
                success: false,
                error: error.response?.data?.error || 'Erreur lors de la mise à jour du profil',
                details: error.response?.data
            };
        }
    },

    /**
     * Change le mot de passe utilisateur
     * @param {Object} passwordData - Données du mot de passe
     * @param {string} passwordData.current_password - Mot de passe actuel
     * @param {string} passwordData.new_password - Nouveau mot de passe
     * @returns {Promise<Object>} Réponse du serveur
     */
    changePassword: async (passwordData) => {
        try {
            console.log('ProfileService - Changement de mot de passe en cours...');
            console.log('ProfileService - URL:', `${API_URL}/profile/change-password/`);
            console.log('ProfileService - Données envoyées:', {
                current_password: '***',
                new_password: '***'
            });

            // Le backend n'attend que current_password et new_password (pas confirm_password)
            const response = await axios.post(
                `${API_URL}/profile/change-password/`,
                {
                    current_password: passwordData.current_password?.trim(),
                    new_password: passwordData.new_password?.trim()
                },
                {
                    headers: {
                        ...authService.getAuthHeader(),
                        'Content-Type': 'application/json'
                    }
                }
            );

            console.log('ProfileService - Réponse reçue:', response.data);
            console.log('ProfileService - Mot de passe changé avec succès');

            return {
                success: true,
                data: response.data,
                message: response.data.message || 'Mot de passe mis à jour avec succès'
            };
        } catch (error) {
            console.error('ProfileService - Erreur lors du changement de mot de passe:', error);
            console.error('ProfileService - Status:', error.response?.status);
            console.error('ProfileService - Data:', error.response?.data);

            return {
                success: false,
                error: error.response?.data?.error || error.response?.data?.message || 'Erreur lors du changement de mot de passe',
                details: error.response?.data
            };
        }
    },

    /**
     * Récupère le profil utilisateur
     * @returns {Promise<Object>} Données du profil
     */
    getProfile: async () => {
        try {
            const response = await axios.get(`${API_URL}/user/`, {
                headers: authService.getAuthHeader()
            });

            return {
                success: true,
                data: response.data,
                user: response.data
            };
        } catch (error) {
            console.error('ProfileService - Erreur lors de la récupération du profil:', error);

            return {
                success: false,
                error: error.response?.data?.error || 'Erreur lors du chargement du profil',
                details: error.response?.data
            };
        }
    },

    /**
     * Utilitaire pour séparer un nom complet en prénom et nom
     * @param {string} fullName - Nom complet
     * @returns {Object} Objet avec first_name et last_name
     */
    splitFullName: (fullName) => {
        if (!fullName) return { first_name: '', last_name: '' };

        const nameParts = fullName.trim().split(' ');
        const first_name = nameParts[0] || '';
        const last_name = nameParts.slice(1).join(' ') || '';

        return { first_name, last_name };
    },

    /**
     * Utilitaire pour combiner prénom et nom en nom complet
     * @param {string} firstName - Prénom
     * @param {string} lastName - Nom de famille
     * @returns {string} Nom complet
     */
    combineNames: (firstName, lastName) => {
        return `${firstName || ''} ${lastName || ''}`.trim();
    },

    /**
     * Prépare les données de profil pour l'affichage
     * Compatible avec l'ancien format (name) et le nouveau (first_name/last_name)
     * @param {Object} userData - Données utilisateur du backend
     * @returns {Object} Données formatées pour l'affichage
     */
    prepareProfileData: (userData) => {
        if (!userData) return {};

        // Si on a first_name et last_name, les utiliser
        if (userData.first_name || userData.last_name) {
            return {
                first_name: userData.first_name || '',
                last_name: userData.last_name || '',
                name: profileService.combineNames(userData.first_name, userData.last_name),
                email: userData.email || '',
                phone: userData.phone || ''
            };
        }

        // Sinon, séparer le nom complet
        const { first_name, last_name } = profileService.splitFullName(userData.name);
        return {
            first_name,
            last_name,
            name: userData.name || '',
            email: userData.email || '',
            phone: userData.phone || ''
        };
    }
};

export default profileService;
