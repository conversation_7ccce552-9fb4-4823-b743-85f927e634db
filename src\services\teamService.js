import axiosInstance from './axiosConfig';
import { useAuth } from '@/contexts/AuthContext';
import { permissionService } from './permissionService';

// Fonction pour vérifier si l'utilisateur est autorisé à accéder aux équipes (admin ou employee, pas super_admin)
const isAuthorized = () => {
    const userStr = localStorage.getItem('user');
    if (!userStr) return false;
    try {
        const user = JSON.parse(userStr);
        const isAuthorized = user.role === 'admin' || user.role === 'employee';
        console.log('TeamService - User role check:', { role: user.role, isAuthorized });
        return isAuthorized; // Super_admin n'a pas accès aux équipes
    } catch (error) {
        console.error('Error checking user role:', error);
        return false;
    }
};

// Fonction pour vérifier si l'utilisateur est un super admin uniquement
const isSuperAdmin = () => {
    const userStr = localStorage.getItem('user');
    if (!userStr) return false;
    try {
        const user = JSON.parse(userStr);
        return user.role === 'super_admin';
    } catch (error) {
        console.error('Error checking if user is super admin:', error);
        return false;
    }
};

// Les intercepteurs sont maintenant gérés dans axiosConfig.js

// Validation des données d'équipe
const validateTeamData = (data) => {
    console.log('TeamService - Validating team data:', data);
    const errors = {};

    if (!data) {
        errors.general = "Données d'équipe manquantes";
        return errors;
    }

    // Validation du nom
    if (!data.name) {
        errors.name = "Le nom de l'équipe est requis";
    } else if (typeof data.name !== 'string') {
        errors.name = "Le nom doit être une chaîne de caractères";
    } else if (data.name.trim().length < 3) {
        errors.name = "Le nom doit contenir au moins 3 caractères";
    } else if (data.name.length > 50) {
        errors.name = "Le nom ne peut pas dépasser 50 caractères";
    }

    // Validation de la description
    if (data.description) {
        if (typeof data.description !== 'string') {
            errors.description = "La description doit être une chaîne de caractères";
        } else if (data.description.length > 500) {
            errors.description = "La description ne peut pas dépasser 500 caractères";
        }
    }

    if (Object.keys(errors).length > 0) {
        console.warn('TeamService - Validation errors:', errors);
    }
    return errors;
};

// Validation des données de membre
const validateMemberData = (data) => {
    const errors = {};

    if (!data) {
        errors.general = "Données de membre manquantes";
        return errors;
    }

    // Pour un membre existant, on vérifie uniquement l'ID
    if (data.user_id || data.id) {
        // Si user_id ou id existe, c'est un membre existant, pas besoin de validation d'email
        return {}; // Retourner un objet vide = pas d'erreurs
    }

    // Pour un nouveau membre, on valide l'email
    if (!data.email || typeof data.email !== 'string' || !data.email.includes('@')) {
        errors.email = "Email invalide";
    }

    // Validation du rôle pour les nouveaux membres
    if (data.role && !['admin', 'employee'].includes(data.role)) {
        errors.role = "Rôle invalide";
    }

    return errors;
};

// Gestion des erreurs
const handleTeamError = (error) => {
    console.error('TeamService - Handling error:', error);
    let message;

    if (error.response?.data?.error) {
        message = error.response.data.error;
    } else if (error.response?.status) {
        // Traitement spécifique pour les erreurs 403 (Forbidden)
        if (error.response.status === 403) {
            if (error.response.data?.detail) {
                const detail = error.response.data.detail;
                if (detail.includes('not the team manager')) {
                    message = "Vous n'êtes pas le responsable de cette équipe. Seul le responsable peut effectuer cette action.";
                } else if (detail.includes('not an admin')) {
                    message = "Vous n'avez pas les droits d'administration nécessaires pour cette action.";
                } else {
                    message = "Vous n'avez pas les droits nécessaires pour effectuer cette action.";
                }
            } else {
                message = "Accès non autorisé. Vérifiez vos permissions.";
            }
        } else {
            // Pour les autres codes d'erreur
            const errorMessages = {
                404: "Équipe non trouvée",
                400: "Données invalides",
                409: "Nom d'équipe déjà utilisé",
                500: "Erreur de communication avec le serveur"
            };
            message = errorMessages[error.response.status] || "Erreur inattendue";
        }
    } else if (error.request) {
        message = "Impossible de communiquer avec le serveur";
    } else {
        message = error.message || "Une erreur est survenue";
    }

    console.error('TeamService - Error details:', {
        status: error.response?.status,
        data: error.response?.data,
        message: message
    });

    return message;
};

// Fonction utilitaire pour récupérer et valider l'utilisateur courant
const getCurrentUser = () => {
    const userStr = localStorage.getItem('user');
    if (!userStr) {
        throw new Error('Utilisateur non connecté');
    }

    try {
        const user = JSON.parse(userStr);
        if (!user || typeof user !== 'object') {
            throw new Error('Format des données utilisateur invalide');
        }
        return user;
    } catch (e) {
        throw new Error('Données utilisateur invalides');
    }
};

// Fonction utilitaire pour valider l'ID utilisateur
const validateUserId = (user) => {
    if (!user.id) {
        throw new Error('ID utilisateur manquant');
    }

    if (typeof user.id !== 'string' && typeof user.id !== 'number') {
        throw new Error('Type d\'ID utilisateur invalide');
    }

    const userId = parseInt(user.id, 10);
    if (isNaN(userId) || userId <= 0) {
        throw new Error('Valeur d\'ID utilisateur invalide');
    }

    return userId;
};

// Fonction utilitaire pour vérifier les permissions d'équipe
const checkTeamPermissions = (user, team = null) => {
    console.log('TeamService - Checking team permissions for user:', user.id);

    // Vérifier si l'utilisateur est connecté
    if (!user || !user.id) {
        console.error('TeamService - User not authenticated');
        throw new Error('Utilisateur non authentifié');
    }

    // Vérifier si l'utilisateur est un admin (pas super_admin)
    if (user.role === 'super_admin') {
        console.error('TeamService - User is super_admin, they cannot manage teams');
        throw new Error('Les super administrateurs ne peuvent pas gérer les équipes');
    }

    if (user.role !== 'admin') {
        console.error('TeamService - User is not an admin');
        throw new Error('Seuls les administrateurs peuvent effectuer cette action');
    }

    // Si une équipe est fournie, vérifier si l'utilisateur est le responsable
    if (team) {
        const userId = String(user.id);
        // Vérifier si responsable est un objet ou un ID
        const responsableId = team.responsable && typeof team.responsable === 'object'
            ? String(team.responsable.id)
            : String(team.responsable);

        console.log('TeamService - Comparing user ID:', userId, 'with team responsable ID:', responsableId);

        if (userId !== responsableId) {
            console.error('TeamService - User is not the team manager');
            throw new Error('Vous n\'êtes pas le responsable de cette équipe. Seul le responsable peut effectuer cette action.');
        }
    }

    console.log('TeamService - User has permission to manage team');
    return true;
};

// Fonction utilitaire pour vérifier les permissions d'administration
const checkAdminPermissions = (user, teamId = null, team = null) => {
    console.log('TeamService - Checking admin permissions for user:', user.id);

    // Vérifier si l'utilisateur est connecté
    if (!user || !user.id) {
        console.error('TeamService - User not authenticated');
        throw new Error('Utilisateur non authentifié');
    }

    // Vérifier si l'utilisateur est un super_admin (ils ne peuvent pas gérer les équipes)
    if (user.role === 'super_admin') {
        console.error('TeamService - User is super_admin, they cannot manage teams');
        throw new Error('Les super administrateurs ne peuvent pas gérer les équipes');
    }

    // Vérifier si l'utilisateur est un admin
    if (user.role !== 'admin') {
        console.error('TeamService - User is not an admin');
        throw new Error('Seuls les administrateurs peuvent effectuer cette action');
    }

    // Si une équipe est fournie, vérifier si l'utilisateur est le responsable
    if (team) {
        const userId = String(user.id);
        // Vérifier si responsable est un objet ou un ID
        const responsableId = team.responsable && typeof team.responsable === 'object'
            ? String(team.responsable.id)
            : String(team.responsable);

        console.log('TeamService - Comparing user ID:', userId, 'with team responsable ID:', responsableId);

        if (userId !== responsableId) {
            console.error('TeamService - User is not the team manager');
            throw new Error('Vous n\'êtes pas le responsable de cette équipe');
        }
    }

    console.log('TeamService - User has admin permissions');
    return true;
};

// Fonction utilitaire pour vérifier si l'utilisateur peut voir une équipe
const canViewTeam = (user, team) => {
    if (!user || !team) return false;

    // Seuls les administrateurs peuvent voir toutes les équipes (pas super_admin)
    if (user.role === 'admin') return true;

    // Les employés peuvent voir les équipes dont ils sont membres
    if (user.role === 'employee') {
        // Vérifier si l'utilisateur est membre de l'équipe
        if (team.members) {
            if (Array.isArray(team.members)) {
                return team.members.some(member =>
                    String(member.id || member.user_id) === String(user.id)
                );
            } else if (typeof team.members === 'object') {
                return Object.keys(team.members).includes(String(user.id));
            }
        }
    }

    return false;
};

// Fonction utilitaire pour valider la réponse de l'API
const validateApiResponse = (response, requireTeam = true) => {
    if (!response.data) {
        throw new Error('Réponse vide du serveur');
    }

    // Si la réponse contient directement les données de l'équipe
    if (requireTeam && response.data.id) {
        return response.data;
    }

    // Si la réponse contient les données de l'équipe dans un sous-objet team
    if (requireTeam && response.data.team && response.data.team.id) {
        return response.data.team;
    }

    // Si on n'attend pas spécifiquement des données d'équipe
    if (!requireTeam) {
        return response.data;
    }

    throw new Error('Format de réponse invalide du serveur');
};

// Service methods
const teamService = {
    // Récupérer toutes les équipes
    async getTeams() {
        try {
            // Vérifier si l'utilisateur est autorisé (seulement admin, pas super_admin)
            if (!isAuthorized()) {
                console.log('TeamService - Access denied: Only admins can view teams');
                // Retourner un tableau vide au lieu de lancer une erreur
                // Cela évite d'afficher des erreurs dans la console lors de la connexion
                return [];
            }

            const user = getCurrentUser();
            console.log('TeamService - Getting teams for user:', user.id);

            const response = await axiosInstance.get('/teams/');
            console.log('TeamService - Teams response:', response.data);
            return response.data;
        } catch (error) {
            console.error('TeamService - Error getting teams:', error);
            // Si l'erreur est liée aux permissions, retourner un tableau vide
            if (error.status === 403 || (error.response && error.response.status === 403)) {
                console.log('TeamService - Permission error, returning empty array');
                return [];
            }
            throw handleTeamError(error);
        }
    },

    async createTeam(teamData) {
        const errors = validateTeamData(teamData);
        if (Object.keys(errors).length > 0) {
            throw new Error(Object.values(errors)[0]);
        }

        const currentUser = getCurrentUser();
        checkAdminPermissions(currentUser);
        const userId = validateUserId(currentUser);

        const payload = {
            name: teamData.name.trim(),
            description: teamData.description?.trim() || '',
            responsable: userId
        };

        try {
            const response = await axiosInstance.post('/teams/', payload);
            return validateApiResponse(response);
        } catch (error) {
            throw handleTeamError(error);
        }
    },

    async updateTeam(teamId, teamData) {
        if (!teamId) {
            throw new Error('ID d\'équipe manquant');
        }

        const errors = validateTeamData(teamData);
        if (Object.keys(errors).length > 0) {
            throw new Error(Object.values(errors)[0]);
        }

        try {
            const currentUser = getCurrentUser();

            // Récupérer les informations de l'équipe
            const teamResponse = await axiosInstance.get(`/teams/${teamId}/`);
            const team = validateApiResponse(teamResponse);

            // Vérifier si l'utilisateur est connecté
            if (!currentUser || !currentUser.id) {
                console.error('TeamService - User not authenticated');
                throw new Error('Utilisateur non authentifié');
            }

            // Vérifier si l'utilisateur est un admin
            if (currentUser.role !== 'admin') {
                console.error('TeamService - User is not an admin');
                throw new Error('Seuls les administrateurs peuvent effectuer cette action');
            }

            // Vérifier si l'utilisateur est le responsable de l'équipe
            const userId = String(currentUser.id);
            // Vérifier si responsable est un objet ou un ID
            const responsableId = team.responsable && typeof team.responsable === 'object'
                ? String(team.responsable.id)
                : String(team.responsable);

            console.log('TeamService - Comparing user ID:', userId, 'with team responsable ID:', responsableId);

            if (userId !== responsableId) {
                console.error('TeamService - User is not the team manager');
                throw new Error('Vous n\'êtes pas le responsable de cette équipe');
            }

            // Effectuer la mise à jour
            const response = await axiosInstance.put(`/teams/${teamId}/`, {
                name: teamData.name?.trim(),
                description: teamData.description?.trim()
            });

            return validateApiResponse(response);
        } catch (error) {
            throw new Error(handleTeamError(error));
        }
    },

    async deleteTeam(teamId) {
        if (!teamId) {
            throw new Error('ID d\'équipe manquant');
        }

        try {
            const response = await axiosInstance.delete(`/teams/${teamId}/`);
            return response.data;
        } catch (error) {
            if (error.response) {
                switch (error.response.status) {
                    case 403:
                        // Message plus précis pour l'erreur 403
                        if (error.response.data?.detail?.includes('not the team manager')) {
                            throw new Error('Vous n\'êtes pas le responsable de cette équipe. Seul le responsable peut la supprimer.');
                        } else if (error.response.data?.detail?.includes('not an admin')) {
                            throw new Error('Vous n\'avez pas les droits d\'administration nécessaires pour cette action.');
                        } else {
                            throw new Error('Vous n\'avez pas les droits pour supprimer cette équipe');
                        }
                    case 404:
                        throw new Error('Équipe non trouvée');
                    case 500:
                        throw new Error('Erreur serveur lors de la suppression');
                    default:
                        throw new Error('Une erreur est survenue lors de la suppression');
                }
            }
            // Amélioration du message d'erreur pour les problèmes de connexion
            console.error('TeamService - Connection error details:', error);
            throw new Error(error.message || 'Erreur de connexion au serveur');
        }
    },

    async addMember(teamId, memberData) {
        if (!teamId) {
            throw new Error('ID d\'équipe manquant');
        }

        if (!memberData.user_id) {
            throw new Error('L\'ID de l\'utilisateur est requis');
        }

        try {
            const currentUser = getCurrentUser();

            // Récupérer les informations de l'équipe
            const teamResponse = await axiosInstance.get(`/teams/${teamId}/`);
            const team = validateApiResponse(teamResponse);

            // Vérifier si l'utilisateur est connecté
            if (!currentUser || !currentUser.id) {
                console.error('TeamService - User not authenticated');
                throw new Error('Utilisateur non authentifié');
            }

            // Vérifier si l'utilisateur est un admin
            if (currentUser.role !== 'admin') {
                console.error('TeamService - User is not an admin');
                throw new Error('Seuls les administrateurs peuvent effectuer cette action');
            }

            // Vérifier si l'utilisateur est le responsable de l'équipe
            const userId = String(currentUser.id);
            // Vérifier si responsable est un objet ou un ID
            const responsableId = team.responsable && typeof team.responsable === 'object'
                ? String(team.responsable.id)
                : String(team.responsable);

            console.log('TeamService - Comparing user ID:', userId, 'with team responsable ID:', responsableId);

            if (userId !== responsableId) {
                console.error('TeamService - User is not the team manager');
                throw new Error('Vous n\'êtes pas le responsable de cette équipe');
            }

            // Faire l'appel API pour ajouter le membre
            const response = await axiosInstance.post(`/teams/${teamId}/members/`, {
                user_id: memberData.user_id
            });

            return validateApiResponse(response, false);
        } catch (error) {
            throw new Error(handleTeamError(error));
        }
    },

    async createAndAddMember(teamId, memberData) {
        if (!teamId) {
            throw new Error('ID d\'équipe manquant');
        }

        const errors = validateMemberData(memberData);
        if (Object.keys(errors).length > 0) {
            throw new Error(Object.values(errors)[0]);
        }

        try {
            const currentUser = getCurrentUser();

            // Récupérer les informations de l'équipe
            const teamResponse = await axiosInstance.get(`/teams/${teamId}/`);
            const team = validateApiResponse(teamResponse);

            // Vérifier les permissions
            checkTeamPermissions(currentUser, team);

            const response = await axiosInstance.post(`/teams/${teamId}/members/create/`, {
                email: memberData.email,
                role: memberData.role || 'employee'
            });
            return validateApiResponse(response, false);
        } catch (error) {
            throw new Error(handleTeamError(error));
        }
    },

    async removeMember(teamId, memberId) {
        if (!teamId || !memberId) {
            throw new Error('ID d\'équipe ou de membre manquant');
        }

        try {
            const currentUser = getCurrentUser();

            // Récupérer les informations de l'équipe
            const teamResponse = await axiosInstance.get(`/teams/${teamId}/`);
            const team = validateApiResponse(teamResponse);

            // Vérifier les permissions
            checkTeamPermissions(currentUser, team);

            const response = await axiosInstance.delete(`/teams/${teamId}/members/${memberId}/`);
            return validateApiResponse(response, false);
        } catch (error) {
            throw new Error(handleTeamError(error));
        }
    },

    async changeResponsable(teamId, newResponsableId) {
        if (!teamId || !newResponsableId) {
            throw new Error('ID d\'équipe ou du nouveau responsable manquant');
        }

        const currentUser = getCurrentUser();
        const teamResponse = await axiosInstance.get(`/teams/${teamId}/`);
        const team = validateApiResponse(teamResponse);
        checkAdminPermissions(currentUser, teamId, team);

        try {
            const response = await axiosInstance.put(`/teams/${teamId}/responsable/`, {
                new_responsable_id: newResponsableId
            });
            return validateApiResponse(response);
        } catch (error) {
            throw new Error(handleTeamError(error));
        }
    },

    async searchMembers(teamId, query) {
        if (!teamId) {
            throw new Error('ID d\'équipe manquant');
        }

        try {
            const response = await axiosInstance.get(`/teams/${teamId}/members/search/`, {
                params: { query: query || '' }
            });
            return validateApiResponse(response, false);
        } catch (error) {
            throw new Error(handleTeamError(error));
        }
    },

    // Récupérer les employés disponibles pour ajouter à une équipe (non déjà membres)
    // Utilise l'endpoint testé et approuvé : GET /api/teams/{team_id}/members/search/
    async getAvailableEmployeesForTeam(teamId) {
        if (!teamId) {
            throw new Error('ID d\'équipe manquant');
        }

        try {
            const currentUser = getCurrentUser();
            if (!currentUser || currentUser.role !== 'admin') {
                throw new Error('Seuls les administrateurs peuvent voir les employés disponibles');
            }

            console.log('TeamService - Récupération des employés disponibles pour l\'équipe:', teamId);

            // Utiliser l'endpoint testé et approuvé : /teams/{teamId}/members/search/
            // Cet endpoint retourne automatiquement les employés disponibles (non déjà membres)
            const response = await axiosInstance.get(`/teams/${teamId}/members/search/`, {
                params: { query: '' } // Requête vide pour récupérer tous les employés disponibles
            });

            console.log('TeamService - Réponse de l\'endpoint members/search:', response.data);

            // Extraire les employés de la réponse
            let availableEmployees = [];
            if (response.data && Array.isArray(response.data.results)) {
                availableEmployees = response.data.results;
                console.log(`TeamService - ${availableEmployees.length} employés trouvés dans results`);
            } else if (Array.isArray(response.data)) {
                availableEmployees = response.data;
                console.log(`TeamService - ${availableEmployees.length} employés trouvés directement`);
            }

            console.log(`TeamService - ${availableEmployees.length} employés disponibles pour ajout à l'équipe`);
            return availableEmployees;

        } catch (error) {
            console.error('TeamService - Erreur lors de la récupération des employés disponibles:', error);
            throw handleTeamError(error);
        }
    },

    // Rechercher des employés disponibles avec une requête spécifique
    async searchAvailableEmployeesForTeam(teamId, query = '') {
        if (!teamId) {
            throw new Error('ID d\'équipe manquant');
        }

        try {
            const currentUser = getCurrentUser();
            if (!currentUser || currentUser.role !== 'admin') {
                throw new Error('Seuls les administrateurs peuvent rechercher les employés disponibles');
            }

            console.log('TeamService - Recherche d\'employés disponibles pour l\'équipe:', teamId, 'avec requête:', query);

            // Utiliser l'endpoint testé avec la requête de recherche
            const response = await axiosInstance.get(`/teams/${teamId}/members/search/`, {
                params: { query: query || '' }
            });

            console.log('TeamService - Résultats de recherche:', response.data);

            // Extraire les employés de la réponse
            let searchResults = [];
            if (response.data && Array.isArray(response.data.results)) {
                searchResults = response.data.results;
            } else if (Array.isArray(response.data)) {
                searchResults = response.data;
            }

            console.log(`TeamService - ${searchResults.length} employés trouvés pour la recherche "${query}"`);
            return searchResults;

        } catch (error) {
            console.error('TeamService - Erreur lors de la recherche d\'employés:', error);
            throw handleTeamError(error);
        }
    }
};

export default teamService;
export { validateTeamData, validateMemberData, handleTeamError };

// Modifier la fonction updateTeam pour ajouter plus de logs
const updateTeam = async (teamId, teamData) => {
    console.log('TeamService - Updating team with ID:', teamId);
    console.log('TeamService - Update data:', teamData);

    // Récupérer l'utilisateur actuel pour le débogage
    const userStr = localStorage.getItem('user');
    if (userStr) {
        try {
            const user = JSON.parse(userStr);
            console.log('TeamService - User attempting update:', {
                id: user.id,
                role: user.role
            });
        } catch (e) {
            console.error('TeamService - Error parsing user for update:', e);
        }
    }

    try {
        const errors = validateTeamData(teamData);
        if (Object.keys(errors).length > 0) {
            console.error('TeamService - Validation errors:', errors);
            throw new Error(Object.values(errors)[0]);
        }

        const response = await axiosInstance.put(`/teams/${teamId}/`, teamData);
        console.log('TeamService - Update successful:', response.data);
        return response.data;
    } catch (error) {
        console.error('TeamService - Update failed:', error);
        throw new Error(error.response?.data?.message || error.message || 'Erreur lors de la mise à jour de l\'équipe');
    }
};