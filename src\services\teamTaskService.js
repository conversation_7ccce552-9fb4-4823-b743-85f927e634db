import axiosInstance from './axiosConfig';
import { API_URL } from '@/config/constants';
import { permissionService } from './permissionService';

// Fonction utilitaire pour vérifier si l'utilisateur est autorisé (admin ou employee)
const isAuthorized = () => {
  const userStr = localStorage.getItem('user');
  if (!userStr) {
    console.error('TeamTaskService - No user found in localStorage');
    return false;
  }

  try {
    const user = JSON.parse(userStr);
    const isAuth = user.role === 'admin' || user.role === 'employee';
    console.log(`TeamTaskService - User role: ${user.role}, isAuthorized: ${isAuth}`);
    return isAuth;
  } catch (e) {
    console.error('TeamTaskService - Error parsing user data:', e);
    return false;
  }
};

// Fonction utilitaire pour récupérer l'utilisateur courant
const getCurrentUser = () => {
  const userStr = localStorage.getItem('user');
  if (!userStr) {
    console.error('TeamTaskService - No user found in localStorage');
    return null;
  }

  try {
    const user = JSON.parse(userStr);
    console.log(`TeamTaskService - Retrieved user: ID=${user.id}, role=${user.role}`);
    return user;
  } catch (e) {
    console.error('TeamTaskService - Error parsing user data:', e);
    return null;
  }
};

const teamTaskService = {
  /**
   * Récupère toutes les tâches d'équipe selon les filtres et permissions de l'utilisateur
   * @param {Object} filters - Filtres optionnels (équipe, statut, etc.)
   * @returns {Promise<Array>} - Liste des tâches d'équipe
   */
  async getTeamTasks(filters = {}) {
    try {
      console.log('TeamTaskService - getTeamTasks called with filters:', filters);

      // Récupérer l'utilisateur courant pour des vérifications plus précises
      const currentUser = getCurrentUser();
      console.log('TeamTaskService - Current user for task fetch:', currentUser ? { id: currentUser.id, role: currentUser.role } : 'No user');

      // Vérifier si l'utilisateur est autorisé
      if (!currentUser) {
        console.error('TeamTaskService - Access denied: User not authenticated');
        throw {
          status: 401,
          error: "Vous devez être connecté pour accéder à cette ressource"
        };
      }

      // Si l'utilisateur est super_admin, retourner un tableau vide au lieu de lancer une erreur
      if (currentUser.role === 'super_admin') {
        console.log('TeamTaskService - Super admin user detected, returning empty array');
        return [];
      }

      if (currentUser.role !== 'admin' && currentUser.role !== 'employee') {
        console.error(`TeamTaskService - Access denied: User role ${currentUser.role} cannot view team tasks`);
        throw {
          status: 403,
          error: "Vous n'avez pas les droits pour voir les tâches d'équipe"
        };
      }

      // Pour les employés, vérifier qu'ils ont accès aux équipes
      if (currentUser.role === 'employee') {
        // Si un filtre d'équipe est spécifié, vérifier que l'employé est membre de cette équipe
        if (filters.team_id && currentUser.teams && Array.isArray(currentUser.teams)) {
          const isMember = currentUser.teams.some(team => String(team.id) === String(filters.team_id));
          if (!isMember) {
            console.error(`TeamTaskService - Access denied: Employee ${currentUser.id} is not a member of team ${filters.team_id}`);
            throw {
              status: 403,
              error: "Vous n'êtes pas membre de cette équipe"
            };
          }
        }
      }

      // Les administrateurs peuvent voir toutes les tâches, mais ne peuvent gérer que celles qu'ils ont créées
      if (currentUser.role === 'admin') {
        console.log('TeamTaskService - Admin user detected, original filters:', filters);
        // Ne pas filtrer par created_by pour permettre aux admins de voir toutes les tâches
        console.log('TeamTaskService - Admin user can see all tasks, no created_by filter applied');
      } else {
        console.log('TeamTaskService - User role:', currentUser.role, 'User ID:', currentUser.id, 'No created_by filter applied');
      }

      // Construire l'URL avec les filtres
      let url = `/team-tasks/`;
      if (Object.keys(filters).length > 0) {
        const queryParams = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            queryParams.append(key, value);
          }
        });
        url += `?${queryParams.toString()}`;
      }

      console.log('TeamTaskService - Sending GET request to:', url);

      // Utiliser l'instance axios configurée avec le token d'authentification
      const token = localStorage.getItem('authToken');
      console.log('TeamTaskService - Using auth token:', token ? 'Token present' : 'No token');

      const response = await axiosInstance.get(url);

      console.log('TeamTaskService - Response status:', response.status);
      console.log('TeamTaskService - Response data:', response.data);

      // Vérifier si la réponse contient des tâches ou est directement un tableau de tâches
      const tasksData = response.data.tasks || response.data;

      if (!Array.isArray(tasksData)) {
        console.error('TeamTaskService - Invalid response format:', tasksData);
        throw {
          status: 500,
          error: "Format de réponse invalide"
        };
      }

      return tasksData;
    } catch (error) {
      console.error('TeamTaskService - Error fetching team tasks:', error);

      // Améliorer le message d'erreur pour le débogage
      if (error.response) {
        console.error('TeamTaskService - Error response status:', error.response.status);
        console.error('TeamTaskService - Error response data:', error.response.data);
      }

      // Si l'erreur est liée à l'authentification, essayer de rafraîchir le token
      if (error.response && error.response.status === 401) {
        console.log('TeamTaskService - Authentication error, token might be expired');
      }

      throw error.response?.data || { message: 'Erreur lors de la récupération des tâches d\'équipe' };
    }
  },

  /**
   * Récupère une tâche d'équipe par son ID
   * @param {string} taskId - ID de la tâche
   * @returns {Promise<Object>} - Tâche d'équipe
   */
  async getTeamTaskById(taskId) {
    try {
      console.log(`TeamTaskService - getTeamTaskById called for task ${taskId}`);

      // Récupérer l'utilisateur courant pour des vérifications plus précises
      const currentUser = getCurrentUser();
      console.log('TeamTaskService - Current user for task fetch by ID:', currentUser ? { id: currentUser.id, role: currentUser.role } : 'No user');

      // Vérifier si l'utilisateur est autorisé
      if (!currentUser) {
        console.error('TeamTaskService - Access denied: User not authenticated');
        throw {
          status: 401,
          error: "Vous devez être connecté pour accéder à cette ressource"
        };
      }

      // Si l'utilisateur est super_admin, retourner un objet vide au lieu de lancer une erreur
      if (currentUser.role === 'super_admin') {
        console.log('TeamTaskService - Super admin user detected, returning empty object');
        return {};
      }

      if (currentUser.role !== 'admin' && currentUser.role !== 'employee') {
        console.error(`TeamTaskService - Access denied: User role ${currentUser.role} cannot view team tasks`);
        throw {
          status: 403,
          error: "Vous n'avez pas les droits pour voir les tâches d'équipe"
        };
      }

      const url = `/team-tasks/${taskId}/`;
      console.log('TeamTaskService - Sending GET request to:', url);

      // Utiliser l'instance axios configurée
      const token = localStorage.getItem('authToken');
      console.log('TeamTaskService - Using auth token for task fetch by ID:', token ? 'Token present' : 'No token');

      const response = await axiosInstance.get(url);

      console.log(`TeamTaskService - Response status for task ${taskId}:`, response.status);
      console.log(`TeamTaskService - Response data for task ${taskId}:`, response.data);

      const task = response.data.task || response.data;

      // Les administrateurs peuvent voir toutes les tâches, mais ne peuvent gérer que celles qu'ils ont créées
      if (currentUser.role === 'admin') {
        const isCreator = String(currentUser.id) === String(task.created_by);
        console.log(`TeamTaskService - Admin user ${currentUser.id} is ${isCreator ? '' : 'not '}the creator of task ${taskId}`);
        // Ne pas bloquer l'accès, les admins peuvent voir toutes les tâches
      }

      return task;
    } catch (error) {
      console.error(`TeamTaskService - Error fetching team task ${taskId}:`, error);

      // Améliorer le message d'erreur pour le débogage
      if (error.response) {
        console.error('TeamTaskService - Error response status:', error.response.status);
        console.error('TeamTaskService - Error response data:', error.response.data);
      }

      throw error.response?.data || { message: `Erreur lors de la récupération de la tâche d'équipe ${taskId}` };
    }
  },

  /**
   * Crée une nouvelle tâche d'équipe
   * @param {Object} taskData - Données de la tâche
   * @returns {Promise<Object>} - Tâche créée
   */
  async createTeamTask(taskData) {
    try {
      console.log('TeamTaskService - createTeamTask called with data:', taskData);

      // Vérifier si l'utilisateur est autorisé (seulement admin)
      const currentUser = getCurrentUser();
      if (!currentUser || currentUser.role !== 'admin') {
        console.error('TeamTaskService - Access denied: Only admins can create team tasks');
        throw {
          status: 403,
          error: "Vous n'avez pas les droits pour créer des tâches d'équipe"
        };
      }

      // Valider les données requises
      if (!taskData.title) {
        throw {
          status: 400,
          error: "Le titre de la tâche est requis"
        };
      }

      if (!taskData.team_id) {
        throw {
          status: 400,
          error: "L'équipe est requise"
        };
      }

      if (!taskData.start_date || !taskData.end_date) {
        throw {
          status: 400,
          error: "Les dates de début et de fin sont requises"
        };
      }

      // Ajouter l'ID du créateur
      const completeTaskData = {
        ...taskData,
        created_by: currentUser.id,
        status: 'a_faire' // Statut par défaut
      };

      const url = `/team-tasks/`;
      console.log('TeamTaskService - Sending POST request to:', url);
      console.log('TeamTaskService - Request data:', completeTaskData);

      // Utiliser l'instance axios configurée
      const response = await axiosInstance.post(url, completeTaskData);

      console.log('TeamTaskService - Response status:', response.status);
      console.log('TeamTaskService - Response data:', response.data);

      return response.data.task || response.data;
    } catch (error) {
      console.error('TeamTaskService - Error creating team task:', error);
      throw error.response?.data || { message: 'Erreur lors de la création de la tâche d\'équipe' };
    }
  },

  /**
   * Met à jour une tâche d'équipe existante
   * @param {string} taskId - ID de la tâche
   * @param {Object} taskData - Nouvelles données de la tâche
   * @returns {Promise<Object>} - Tâche mise à jour
   */
  async updateTeamTask(taskId, taskData) {
    try {
      console.log(`TeamTaskService - updateTeamTask called for task ${taskId} with data:`, taskData);

      // Vérifier si l'utilisateur est autorisé (seulement admin)
      const currentUser = getCurrentUser();
      if (!currentUser) {
        console.error('TeamTaskService - Access denied: User not authenticated');
        throw {
          status: 401,
          error: "Vous devez être connecté pour accéder à cette ressource"
        };
      }

      if (currentUser.role !== 'admin' && currentUser.role !== 'employee') {
        console.error(`TeamTaskService - Access denied: User role ${currentUser.role} cannot access team tasks`);
        throw {
          status: 403,
          error: "Vous n'avez pas les droits pour accéder aux tâches d'équipe"
        };
      }

      // Récupérer la tâche pour vérifier si l'utilisateur est le créateur
      try {
        const task = await this.getTeamTaskById(taskId);
        const isCreator = String(currentUser.id) === String(task.created_by);

        // Si l'utilisateur est un admin, il ne peut modifier que les tâches qu'il a créées
        if (currentUser.role === 'admin' && !isCreator) {
          console.error(`TeamTaskService - Access denied: Admin ${currentUser.id} is not the creator of task ${taskId}`);
          throw {
            status: 403,
            error: "Vous n'avez pas les droits pour modifier cette tâche d'équipe"
          };
        }

        // Si l'utilisateur est un employé, vérifier s'il est membre de l'équipe
        if (currentUser.role === 'employee') {
          const isMember = currentUser.teams && Array.isArray(currentUser.teams) &&
            currentUser.teams.some(team => String(team.id) === String(task.team_id));

          if (!isMember) {
            console.error(`TeamTaskService - Access denied: Employee ${currentUser.id} is not a member of the team for task ${taskId}`);
            throw {
              status: 403,
              error: "Vous n'avez pas les droits pour modifier cette tâche d'équipe"
            };
          }
        }
      } catch (error) {
        if (error.status === 403) throw error;
        // Si une autre erreur se produit lors de la récupération de la tâche, continuer
        console.warn(`TeamTaskService - Could not verify creator status for task ${taskId}:`, error);
      }

      // Valider les données requises
      if (!taskData.title) {
        throw {
          status: 400,
          error: "Le titre de la tâche est requis"
        };
      }

      if (!taskData.team_id) {
        throw {
          status: 400,
          error: "L'équipe est requise"
        };
      }

      if (!taskData.start_date || !taskData.end_date) {
        throw {
          status: 400,
          error: "Les dates de début et de fin sont requises"
        };
      }

      const url = `/team-tasks/${taskId}/`;
      console.log('TeamTaskService - Sending PUT request to:', url);
      console.log('TeamTaskService - Request data:', taskData);

      // Utiliser l'instance axios configurée
      const response = await axiosInstance.put(url, taskData);

      console.log(`TeamTaskService - Response status for task ${taskId}:`, response.status);
      console.log(`TeamTaskService - Response data for task ${taskId}:`, response.data);

      return response.data.task || response.data;
    } catch (error) {
      console.error(`TeamTaskService - Error updating team task ${taskId}:`, error);
      throw error.response?.data || { message: `Erreur lors de la mise à jour de la tâche d'équipe ${taskId}` };
    }
  },

  /**
   * Met à jour le statut d'une tâche d'équipe
   * @param {string} taskId - ID de la tâche
   * @param {string} status - Nouveau statut ('a_faire', 'en_cours', 'achevee')
   * @returns {Promise<Object>} - Tâche mise à jour
   */
  async updateTeamTaskStatus(taskId, status) {
    try {
      console.log(`TeamTaskService - updateTeamTaskStatus called for task ${taskId} with status ${status}`);

      // Vérifier si l'utilisateur est autorisé
      if (!isAuthorized()) {
        console.error('TeamTaskService - Access denied: Only admins and employees can update team task status');
        throw {
          status: 403,
          error: "Vous n'avez pas les droits pour mettre à jour le statut des tâches d'équipe"
        };
      }

      // Valider le statut
      const validStatuses = ['a_faire', 'en_cours', 'achevee', 'archived'];
      if (!validStatuses.includes(status)) {
        throw {
          status: 400,
          error: "Statut invalide. Les valeurs autorisées sont: a_faire, en_cours, achevee, archived"
        };
      }

      const url = `/team-tasks/${taskId}/`;
      console.log('TeamTaskService - Sending PATCH request to:', url);
      console.log('TeamTaskService - Request data:', { status });

      // Utiliser l'instance axios configurée
      const response = await axiosInstance.patch(url, { status });

      console.log(`TeamTaskService - Response status for task ${taskId}:`, response.status);
      console.log(`TeamTaskService - Response data for task ${taskId}:`, response.data);

      return response.data.task || response.data;
    } catch (error) {
      console.error(`TeamTaskService - Error updating status for team task ${taskId}:`, error);
      throw error.response?.data || { message: `Erreur lors de la mise à jour du statut de la tâche d'équipe ${taskId}` };
    }
  },

  /**
   * Archive une tâche d'équipe
   * @param {string} taskId - ID de la tâche
   * @returns {Promise<Object>} - Tâche archivée
   */
  async archiveTeamTask(taskId) {
    try {
      console.log(`TeamTaskService - archiveTeamTask called for task ${taskId}`);

      // Vérifier si l'utilisateur est autorisé (seulement admin)
      const currentUser = getCurrentUser();
      if (!currentUser) {
        console.error('TeamTaskService - Access denied: User not authenticated');
        throw {
          status: 401,
          error: "Vous devez être connecté pour accéder à cette ressource"
        };
      }

      if (currentUser.role !== 'admin') {
        console.error(`TeamTaskService - Access denied: User role ${currentUser.role} cannot archive team tasks`);
        throw {
          status: 403,
          error: "Seuls les administrateurs peuvent archiver des tâches d'équipe"
        };
      }

      // Récupérer la tâche pour vérifier si l'utilisateur est le créateur
      try {
        const task = await this.getTeamTaskById(taskId);
        const isCreator = String(currentUser.id) === String(task.created_by);

        // Un admin ne peut archiver que les tâches qu'il a créées
        if (!isCreator) {
          console.error(`TeamTaskService - Access denied: Admin ${currentUser.id} is not the creator of task ${taskId}`);
          throw {
            status: 403,
            error: "Vous n'avez pas les droits pour archiver cette tâche d'équipe"
          };
        }
      } catch (error) {
        if (error.status === 403) throw error;
        // Si une autre erreur se produit lors de la récupération de la tâche, continuer
        console.warn(`TeamTaskService - Could not verify creator status for task ${taskId}:`, error);
      }

      const url = `/team-tasks/${taskId}/archive/`;
      console.log('TeamTaskService - Sending PUT request to:', url);

      // Utiliser l'instance axios configurée
      const response = await axiosInstance.put(url, {});

      console.log(`TeamTaskService - Response status for task ${taskId}:`, response.status);
      console.log(`TeamTaskService - Response data for task ${taskId}:`, response.data);

      return response.data.task || response.data;
    } catch (error) {
      console.error(`TeamTaskService - Error archiving team task ${taskId}:`, error);
      throw error.response?.data || { message: `Erreur lors de l'archivage de la tâche d'équipe ${taskId}` };
    }
  },

  /**
   * Désarchive une tâche d'équipe
   * @param {string} taskId - ID de la tâche
   * @returns {Promise<Object>} - Tâche désarchivée
   */
  async unarchiveTeamTask(taskId) {
    try {
      console.log(`TeamTaskService - unarchiveTeamTask called for task ${taskId}`);

      // Vérifier si l'utilisateur est autorisé (seulement admin)
      const currentUser = getCurrentUser();
      if (!currentUser) {
        console.error('TeamTaskService - Access denied: User not authenticated');
        throw {
          status: 401,
          error: "Vous devez être connecté pour accéder à cette ressource"
        };
      }

      if (currentUser.role !== 'admin') {
        console.error(`TeamTaskService - Access denied: User role ${currentUser.role} cannot unarchive team tasks`);
        throw {
          status: 403,
          error: "Seuls les administrateurs peuvent désarchiver des tâches d'équipe"
        };
      }

      // Récupérer la tâche pour vérifier si l'utilisateur est le créateur
      try {
        const task = await this.getTeamTaskById(taskId);
        const isCreator = String(currentUser.id) === String(task.created_by);

        // Un admin ne peut désarchiver que les tâches qu'il a créées
        if (!isCreator) {
          console.error(`TeamTaskService - Access denied: Admin ${currentUser.id} is not the creator of task ${taskId}`);
          throw {
            status: 403,
            error: "Vous n'avez pas les droits pour désarchiver cette tâche d'équipe"
          };
        }
      } catch (error) {
        if (error.status === 403) throw error;
        // Si une autre erreur se produit lors de la récupération de la tâche, continuer
        console.warn(`TeamTaskService - Could not verify creator status for task ${taskId}:`, error);
      }

      const url = `/team-tasks/${taskId}/unarchive/`;
      console.log('TeamTaskService - Sending PUT request to:', url);

      // Utiliser l'instance axios configurée
      const response = await axiosInstance.put(url, {});

      console.log(`TeamTaskService - Response status for task ${taskId}:`, response.status);
      console.log(`TeamTaskService - Response data for task ${taskId}:`, response.data);

      return response.data.task || response.data;
    } catch (error) {
      console.error(`TeamTaskService - Error unarchiving team task ${taskId}:`, error);
      throw error.response?.data || { message: `Erreur lors du désarchivage de la tâche d'équipe ${taskId}` };
    }
  },

  /**
   * Supprime une tâche d'équipe
   * @param {string} taskId - ID de la tâche
   * @returns {Promise<Object>} - Réponse de confirmation
   */
  async deleteTeamTask(taskId) {
    try {
      console.log(`TeamTaskService - deleteTeamTask called for task ${taskId}`);

      // Vérifier si l'utilisateur est autorisé (seulement admin)
      const currentUser = getCurrentUser();
      if (!currentUser) {
        console.error('TeamTaskService - Access denied: User not authenticated');
        throw {
          status: 401,
          error: "Vous devez être connecté pour accéder à cette ressource"
        };
      }

      if (currentUser.role !== 'admin') {
        console.error(`TeamTaskService - Access denied: User role ${currentUser.role} cannot delete team tasks`);
        throw {
          status: 403,
          error: "Seuls les administrateurs peuvent supprimer des tâches d'équipe"
        };
      }

      // Récupérer la tâche pour vérifier si l'utilisateur est le créateur
      try {
        const task = await this.getTeamTaskById(taskId);
        const isCreator = String(currentUser.id) === String(task.created_by);

        // Un admin ne peut supprimer que les tâches qu'il a créées
        if (!isCreator) {
          console.error(`TeamTaskService - Access denied: Admin ${currentUser.id} is not the creator of task ${taskId}`);
          throw {
            status: 403,
            error: "Vous n'avez pas les droits pour supprimer cette tâche d'équipe"
          };
        }
      } catch (error) {
        if (error.status === 403) throw error;
        // Si une autre erreur se produit lors de la récupération de la tâche, continuer
        console.warn(`TeamTaskService - Could not verify creator status for task ${taskId}:`, error);
      }

      const url = `/team-tasks/${taskId}/`;
      console.log('TeamTaskService - Sending DELETE request to:', url);

      // Utiliser l'instance axios configurée
      const response = await axiosInstance.delete(url);

      console.log(`TeamTaskService - Response status for task ${taskId}:`, response.status);
      console.log(`TeamTaskService - Response data for task ${taskId}:`, response.data);

      return response.data;
    } catch (error) {
      console.error(`TeamTaskService - Error deleting team task ${taskId}:`, error);
      throw error.response?.data || { message: `Erreur lors de la suppression de la tâche d'équipe ${taskId}` };
    }
  },

  /**
   * Vérifie les permissions d'un utilisateur pour une tâche d'équipe
   * @param {Object} user - Utilisateur actuel
   * @param {Object} task - Tâche à vérifier
   * @returns {Object} - Objet contenant les permissions
   */
  checkTeamTaskPermissions(user, task) {
    // Réduire les logs pour éviter le spam dans la console
    // console.log('TeamTaskService - Checking permissions for user:', user?.role, 'task:', task?.id);

    if (!user || !task) {
      // console.log('TeamTaskService - No user or task provided for permission check');
      return {
        canView: false,
        canManage: false,
        canUpdateStatus: false,
        canEdit: false,
        canDelete: false,
        canArchive: false,
        canUnarchive: false
      };
    }

    // Les super_admin et les clients n'ont pas accès aux tâches d'équipe
    if (user.role === 'super_admin' || user.role === 'client') {
      // console.log(`TeamTaskService - ${user.role} has no access to team tasks`);
      return {
        canView: false,
        canManage: false,
        canUpdateStatus: false,
        canEdit: false,
        canDelete: false,
        canArchive: false,
        canUnarchive: false
      };
    }

    // Vérifier si l'utilisateur est le responsable de l'équipe
    let isResponsable = false;
    if (user.role === 'admin') {
      // Vérifier dans les équipes de l'utilisateur
      if (user.teams && Array.isArray(user.teams)) {
        isResponsable = user.teams.some(team => {
          const isResp = String(team.id) === String(task.team_id) && team.is_responsable;
          // console.log(`TeamTaskService - Admin ${user.id} is responsable of team ${team.id}: ${isResp}`);
          return isResp;
        });
      }

      // Si l'utilisateur est admin, il devrait avoir accès à ses équipes
      if (!isResponsable) {
        // console.log(`TeamTaskService - Admin ${user.id} is not responsable of team ${task.team_id}`);
        // Pour les admins, on considère qu'ils peuvent voir toutes les tâches des équipes qu'ils gèrent
        // même s'ils ne sont pas explicitement marqués comme responsables
        if (user.teams && Array.isArray(user.teams)) {
          isResponsable = user.teams.some(team => String(team.id) === String(task.team_id));
          // console.log(`TeamTaskService - Admin ${user.id} is member of team ${task.team_id}: ${isResponsable}`);
        }
      }
    }

    // Vérifier si l'utilisateur est membre de l'équipe
    let isMember = false;
    if (task.team_id) {
      // Si l'utilisateur a des équipes dans ses données
      if (user.teams && Array.isArray(user.teams)) {
        isMember = user.teams.some(team => {
          const isMem = String(team.id) === String(task.team_id);
          // console.log(`TeamTaskService - User ${user.id} is member of team ${team.id}: ${isMem}`);
          return isMem;
        });
      }
    }

    // Vérifier si l'utilisateur est assigné à la tâche
    const isAssigned = String(user.id) === String(task.member_id);
    // console.log(`TeamTaskService - User ${user.id} is assigned to task ${task.id}: ${isAssigned}`);

    // Vérifier si l'utilisateur est le créateur de la tâche
    const isCreator = String(user.id) === String(task.created_by);
    // console.log(`TeamTaskService - User ${user.id} is creator of task ${task.id}: ${isCreator}`);

    // Vérifier si la tâche est archivée
    const isArchived = task.status === 'archived';

    // Déterminer les permissions
    const permissions = {
      // Les admins peuvent voir toutes les tâches
      // Les employés peuvent voir les tâches des équipes dont ils sont membres ou auxquelles ils sont assignés
      canView: (user.role === 'admin') || (user.role === 'employee' && (isMember || isAssigned)),

      // Les admins ne peuvent gérer que les tâches qu'ils ont créées
      // Pour les tâches archivées, on limite la gestion
      canManage: (user.role === 'admin' && isCreator),

      // Les employés membres ou assignés peuvent mettre à jour le statut
      // Les tâches archivées ne peuvent pas avoir leur statut mis à jour
      canUpdateStatus: !isArchived && ((user.role === 'employee' && (isAssigned || isMember))),

      // Les admins ne peuvent modifier que les tâches qu'ils ont créées
      // Les tâches archivées ne peuvent pas être modifiées
      canEdit: !isArchived && (user.role === 'admin' && isCreator),

      // Les admins ne peuvent supprimer que les tâches qu'ils ont créées
      // Les tâches archivées peuvent toujours être supprimées
      canDelete: user.role === 'admin' && isCreator,

      // Seuls les admins créateurs de la tâche peuvent archiver les tâches
      // Les employés ne peuvent jamais archiver les tâches
      // Les tâches déjà archivées ne peuvent pas être archivées à nouveau
      canArchive: !isArchived && user.role === 'admin' && isCreator,

      // Seuls les admins créateurs de la tâche peuvent désarchiver les tâches
      // Les employés ne peuvent jamais désarchiver les tâches
      // Seules les tâches archivées peuvent être désarchivées
      canUnarchive: isArchived && user.role === 'admin' && isCreator
    };

    // console.log(`TeamTaskService - Permissions for user ${user.id} on task ${task.id}:`, permissions);
    return permissions;
  }
};

// Utiliser la méthode interne pour la cohérence
teamTaskService.checkTeamTaskPermissions = teamTaskService.checkTeamTaskPermissions;

export default teamTaskService;
