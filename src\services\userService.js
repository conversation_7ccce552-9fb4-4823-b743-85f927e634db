import axiosInstance from './axiosConfig';

import { API_URL } from '@/config/constants';

// Fonction pour vérifier si l'utilisateur est un super admin
const isSuperAdmin = () => {
    const userStr = localStorage.getItem('user');
    if (!userStr) return false;
    try {
        const user = JSON.parse(userStr);
        return user.role === 'super_admin';
    } catch (error) {
        console.error('Error checking user role:', error);
        return false;
    }
};

export const userService = {
    getUsers: async () => {
        // Vérifier si l'utilisateur est un super admin
        if (!isSuperAdmin()) {
            console.error('UserService - Access denied: Only super admins can manage users');
            return {
                success: false,
                status: 403,
                type: 'error',
                message: 'Accès refusé: Seuls les super administrateurs peuvent gérer les utilisateurs'
            };
        }

        try {
            const response = await axiosInstance.get('/users/');
            console.log('UserService - Users fetched successfully:', response.data);

            // Filtrer les super_admin de la liste des utilisateurs
            // Le super_admin ne doit pas voir son propre compte dans la liste
            const filteredUsers = response.data.filter(user => user.role !== 'super_admin');
            console.log('UserService - Filtered users (removed super_admin):', filteredUsers.length);

            return {
                success: true,
                status: response.status,
                type: 'success',
                data: filteredUsers
            };
        } catch (error) {
            console.error('UserService - Error fetching users:', error);
            return {
                success: false,
                status: error.response?.status || 500,
                type: 'error',
                message: error.response?.data?.message || 'Erreur lors du chargement des utilisateurs'
            };
        }
    },

    createUser: async (userData) => {
        // Vérifier si l'utilisateur est un super admin
        if (!isSuperAdmin()) {
            console.error('UserService - Access denied: Only super admins can create users');
            return {
                success: false,
                status: 403,
                type: 'error',
                message: 'Accès refusé: Seuls les super administrateurs peuvent créer des utilisateurs'
            };
        }

        try {
            console.log('UserService - Creating user with data:', userData);

            // S'assurer que les données sont correctement formatées
            const formattedData = {
                name: userData.name?.trim(),
                email: userData.email?.trim(),
                role: userData.role,
                // Autres champs si nécessaire
            };

            console.log('UserService - Formatted user data:', formattedData);
            console.log('UserService - Sending request to:', '/users/create/');

            const response = await axiosInstance.post('/users/create/', formattedData);
            console.log('UserService - Create user response:', response.data);

            return {
                success: true,
                status: response.status,
                type: 'success',
                message: response.data.message || 'Utilisateur créé avec succès',
                data: response.data.user || response.data
            };
        } catch (error) {
            console.error('UserService - Error creating user:', error);

            // Afficher plus de détails sur l'erreur pour le débogage
            if (error.response) {
                console.error('UserService - Error response status:', error.response.status);
                console.error('UserService - Error response data:', error.response.data);
            } else if (error.request) {
                console.error('UserService - Error request:', error.request);
            } else {
                console.error('UserService - Error message:', error.message);
            }

            return {
                success: false,
                status: error.response?.status || 500,
                type: 'error',
                message: error.response?.data?.message || error.response?.data?.error || 'Erreur lors de la création de l\'utilisateur'
            };
        }
    },

    updateUser: async (userId, userData) => {
        // Vérifier si l'utilisateur est un super admin
        if (!isSuperAdmin()) {
            console.error('UserService - Access denied: Only super admins can update users');
            return {
                success: false,
                status: 403,
                type: 'error',
                message: 'Accès refusé: Seuls les super administrateurs peuvent modifier des utilisateurs'
            };
        }

        try {
            const response = await axiosInstance.put(`/users/${userId}/update/`, userData);

            return {
                success: true,
                status: response.status,
                type: 'success',
                message: response.data.message || 'Utilisateur mis à jour avec succès',
                data: response.data.user || response.data
            };
        } catch (error) {
            console.error('UserService - Error updating user:', error);
            return {
                success: false,
                status: error.response?.status || 500,
                type: 'error',
                message: error.response?.data?.message || error.response?.data?.error || 'Erreur lors de la mise à jour de l\'utilisateur'
            };
        }
    },

    deleteUser: async (userId) => {
        // Vérifier si l'utilisateur est un super admin
        if (!isSuperAdmin()) {
            console.error('UserService - Access denied: Only super admins can delete users');
            return {
                success: false,
                status: 403,
                type: 'error',
                message: 'Accès refusé: Seuls les super administrateurs peuvent supprimer des utilisateurs'
            };
        }

        // Vérifier si l'ID utilisateur est valide
        if (!userId) {
            console.error('UserService - Invalid user ID:', userId);
            return {
                success: false,
                status: 400,
                type: 'error',
                message: 'ID utilisateur invalide'
            };
        }

        try {
            // S'assurer que l'ID est une chaîne de caractères et qu'il ne contient pas de caractères spéciaux
            const userIdStr = String(userId).trim();

            // Vérifier que l'ID est valide
            if (!userIdStr.match(/^[a-zA-Z0-9_-]+$/)) {
                console.error('UserService - Invalid user ID format:', userIdStr);
                return {
                    success: false,
                    status: 400,
                    type: 'error',
                    message: 'Format d\'ID utilisateur invalide'
                };
            }

            // Utiliser l'endpoint standard pour la suppression (la logique de cascade est gérée côté backend)
            console.log('UserService - Deleting user with cascade:', userIdStr);

            // Utiliser l'URL correcte pour la suppression d'utilisateur
            const response = await axiosInstance.delete(`/users/${userIdStr}/`);

            console.log('UserService - Delete user response:', response);

            return {
                success: true,
                status: response.status,
                type: 'success',
                message: response.data?.message || 'Utilisateur et toutes ses données associées supprimés avec succès'
            };
        } catch (error) {
            console.error('UserService - Error deleting user:', error);

            // Afficher plus de détails sur l'erreur pour le débogage
            if (error.response) {
                console.error('UserService - Error response status:', error.response.status);
                console.error('UserService - Error response data:', error.response.data);
            } else if (error.request) {
                console.error('UserService - Error request:', error.request);
            } else {
                console.error('UserService - Error message:', error.message);
            }

            // Gestion des différents codes d'erreur selon la spécification
            if (error.response) {
                const status = error.response.status;
                const errorData = error.response.data;

                switch (status) {
                    case 400:
                        return {
                            success: false,
                            status: 400,
                            type: 'error',
                            message: errorData?.message || errorData?.error || 'Vous ne pouvez pas supprimer votre propre compte'
                        };

                    case 403:
                        // Vérifier si l'erreur est liée à la tentative de suppression d'un super_admin
                        if (errorData?.message?.includes('super_admin') ||
                            errorData?.error?.includes('super_admin') ||
                            (typeof errorData === 'string' && errorData.includes('super_admin'))) {
                            return {
                                success: false,
                                status: 403,
                                type: 'error',
                                message: 'Il est interdit de supprimer un super_admin'
                            };
                        }
                        return {
                            success: false,
                            status: 403,
                            type: 'error',
                            message: errorData?.message || errorData?.error || 'Accès refusé: Vous n\'avez pas les droits nécessaires pour cette action'
                        };

                    case 404:
                        return {
                            success: false,
                            status: 404,
                            type: 'error',
                            message: errorData?.message || errorData?.error || 'Utilisateur non trouvé'
                        };

                    case 500:
                        return {
                            success: false,
                            status: 500,
                            type: 'error',
                            message: errorData?.message || errorData?.error || 'Erreur serveur lors de la suppression de l\'utilisateur'
                        };

                    default:
                        return {
                            success: false,
                            status: status || 500,
                            type: 'error',
                            message: errorData?.message || errorData?.error || 'Erreur lors de la suppression de l\'utilisateur'
                        };
                }
            }

            // Erreur générique si pas de réponse du serveur
            return {
                success: false,
                status: 500,
                type: 'error',
                message: error.message || 'Erreur de connexion lors de la suppression de l\'utilisateur'
            };
        }
    },

    // Récupérer un utilisateur par son ID
    getUserById: async (userId) => {
        // Vérifier si l'utilisateur est un super admin
        if (!isSuperAdmin()) {
            console.error('UserService - Access denied: Only super admins can view user details');
            return {
                success: false,
                status: 403,
                type: 'error',
                message: 'Accès refusé: Seuls les super administrateurs peuvent voir les détails des utilisateurs'
            };
        }

        // Vérifier si l'ID utilisateur est valide
        if (!userId) {
            console.error('UserService - Invalid user ID:', userId);
            return {
                success: false,
                status: 400,
                type: 'error',
                message: 'ID utilisateur invalide'
            };
        }

        try {
            const userIdStr = String(userId).trim();
            const response = await axiosInstance.get(`/users/${userIdStr}/`);

            console.log('UserService - User fetched successfully:', response.data);
            return {
                success: true,
                status: response.status,
                type: 'success',
                data: response.data
            };
        } catch (error) {
            console.error('UserService - Error fetching user:', error);
            return {
                success: false,
                status: error.response?.status || 500,
                type: 'error',
                message: error.response?.data?.message || 'Erreur lors du chargement des données de l\'utilisateur'
            };
        }
    },

    // Mettre à jour les permissions d'un utilisateur
    updateUserPermissions: async (userId, permissions) => {
        // Vérifier si l'utilisateur est un super admin
        if (!isSuperAdmin()) {
            console.error('UserService - Access denied: Only super admins can update user permissions');
            return {
                success: false,
                status: 403,
                type: 'error',
                message: 'Accès refusé: Seuls les super administrateurs peuvent modifier les permissions des utilisateurs'
            };
        }

        // Vérifier si l'ID utilisateur est valide
        if (!userId) {
            console.error('UserService - Invalid user ID:', userId);
            return {
                success: false,
                status: 400,
                type: 'error',
                message: 'ID utilisateur invalide'
            };
        }

        try {
            const userIdStr = String(userId).trim();
            const response = await axiosInstance.put(`/users/${userIdStr}/permissions/`, { permissions });

            console.log('UserService - User permissions updated successfully:', response.data);
            return {
                success: true,
                status: response.status,
                type: 'success',
                message: 'Permissions mises à jour avec succès',
                data: response.data
            };
        } catch (error) {
            console.error('UserService - Error updating user permissions:', error);

            // Vérifier si l'erreur indique que les permissions sont statiques
            if (error.response?.status === 400 &&
                (error.response?.data?.message?.includes('statique') ||
                    error.response?.data?.message?.includes('static'))) {
                return {
                    success: false,
                    status: 400,
                    type: 'info',
                    message: 'Les permissions sont statiques et ne peuvent pas être modifiées. Elles sont basées sur le rôle de l\'utilisateur.'
                };
            }

            return {
                success: false,
                status: error.response?.status || 500,
                type: 'error',
                message: error.response?.data?.message || 'Erreur lors de la mise à jour des permissions'
            };
        }
    },

    // Fonction pour définir les permissions par rôle selon les spécifications
    getPermissionsByRole: (role) => {
        const rolePermissions = {
            'super_admin': [], // Super admin n'a pas de liste de permissions dans l'interface
            'admin': [
                'manage_teams',
                'manage_team_tasks',
                'manage_team_calendars',
                'view_team_dashboards'  // Dashboard d'équipes pour les admins
            ],
            'employee': [
                'manage_personal_tasks',
                'manage_personal_calendar',
                'modify_team_event_status',
                'modify_team_task_status',
                'view_personal_dashboard',  // Dashboard personnel pour les employés
                'view_team_dashboards'      // Dashboard des équipes pour les employés
            ],
            'client': [
                'manage_personal_tasks',
                'manage_personal_calendar',  // Gérer calendrier personnel pour les clients
                'manage_journal_notes',
                'activate_focus_mode',
                'view_personal_dashboard'    // Dashboard personnel pour les clients
            ]
        };

        return rolePermissions[role] || [];
    },

    // Nouvelle méthode pour récupérer les permissions d'un utilisateur spécifique
    getUserPermissions: async (userId) => {
        // Vérifier si l'utilisateur est un super admin
        if (!isSuperAdmin()) {
            console.error('UserService - Access denied: Only super admins can view user permissions');
            return {
                success: false,
                status: 403,
                type: 'error',
                message: 'Accès refusé: Seuls les super administrateurs peuvent voir les permissions des utilisateurs'
            };
        }

        // Vérifier si l'ID utilisateur est valide
        if (!userId) {
            console.error('UserService - Invalid user ID:', userId);
            return {
                success: false,
                status: 400,
                type: 'error',
                message: 'ID utilisateur invalide'
            };
        }

        try {
            const userIdStr = String(userId).trim();
            const response = await axiosInstance.get(`/users/${userIdStr}/permissions/`);

            console.log('UserService - User permissions fetched successfully:', response.data);

            // Le backend retourne les permissions sous forme d'objet avec true/false
            // Nous devons convertir cela en tableau pour notre interface
            const permissionsObj = response.data.permissions || {};
            const permissionsArray = Object.keys(permissionsObj).filter(key => permissionsObj[key] === true);

            return {
                success: true,
                status: response.status,
                type: 'success',
                data: {
                    permissions: permissionsArray
                }
            };
        } catch (error) {
            console.error('UserService - Error fetching user permissions:', error);
            return {
                success: false,
                status: error.response?.status || 500,
                type: 'error',
                message: error.response?.data?.message || 'Erreur lors de la récupération des permissions utilisateur'
            };
        }
    },

    // Nouvelle méthode pour récupérer la liste complète des permissions disponibles
    getPermissionsList: async () => {
        try {
            // Retourner la liste des permissions avec descriptions (optionnel pour l'affichage)
            const permissions = {
                'manage_teams': {
                    'name': 'Gérer les équipes',
                    'description': 'Créer, modifier et gérer les équipes',
                    'category': 'Gestion équipes'
                },
                'manage_team_tasks': {
                    'name': 'Gérer tâches d\'équipe',
                    'description': 'Gérer les tâches des équipes',
                    'category': 'Gestion équipes'
                },
                'manage_team_calendars': {
                    'name': 'Gérer calendriers d\'équipe',
                    'description': 'Gérer les calendriers d\'équipe',
                    'category': 'Gestion équipes'
                },
                'modify_team_event_status': {
                    'name': 'Modifier statut événements équipe',
                    'description': 'Modifier le statut des événements d\'équipe assignés',
                    'category': 'Gestion équipes'
                },
                'modify_team_task_status': {
                    'name': 'Modifier statut tâches équipe',
                    'description': 'Modifier le statut des tâches d\'équipe assignées',
                    'category': 'Gestion équipes'
                },
                'manage_personal_tasks': {
                    'name': 'Gérer tâches personnelles',
                    'description': 'Créer, modifier et supprimer ses propres tâches',
                    'category': 'Gestion personnelle'
                },
                'manage_personal_calendar': {
                    'name': 'Gérer calendrier personnel',
                    'description': 'Gérer son calendrier personnel et ses événements',
                    'category': 'Gestion personnelle'
                },
                'manage_journal_notes': {
                    'name': 'Gérer journal & notes personnelles',
                    'description': 'Gérer journal et notes personnelles',
                    'category': 'Gestion personnelle'
                },
                'activate_focus_mode': {
                    'name': 'Activer mode focus',
                    'description': 'Activer le mode focus pour les sessions de travail',
                    'category': 'Gestion personnelle'
                },
                'view_personal_dashboard': {
                    'name': 'Dashboard personnel',
                    'description': 'Consulter son dashboard personnel',
                    'category': 'Tableaux de bord'
                },
                'view_team_dashboards': {
                    'name': 'Dashboards équipes',
                    'description': 'Accès aux dashboards d\'équipe',
                    'category': 'Tableaux de bord'
                },
                'view_main_dashboard': {
                    'name': 'Dashboard principal',
                    'description': 'Accès au dashboard principal',
                    'category': 'Tableaux de bord'
                }
            };

            return {
                success: true,
                data: {
                    permissions: permissions,
                    message: 'Liste des permissions disponibles'
                }
            };
        } catch (error) {
            console.error('Error fetching permissions list:', error);
            return {
                success: false,
                message: error.message || 'Erreur lors de la récupération de la liste des permissions'
            };
        }
    }
};