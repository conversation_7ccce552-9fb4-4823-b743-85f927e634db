/* Modern Calendar Design - Inspired by the provided image */

/* Variables CSS pour la cohérence des couleurs */
:root {
  --calendar-bg: #f8fafc;
  --calendar-white: #ffffff;
  --calendar-border: #e2e8f0;
  --calendar-text-primary: #1a202c;
  --calendar-text-secondary: #4a5568;
  --calendar-text-muted: #718096;
  --calendar-today-bg: #ebf4ff;
  --calendar-hover-bg: #f7fafc;
  --calendar-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  --calendar-shadow-lg: 0 4px 6px rgba(0, 0, 0, 0.07);

  /* Couleurs pastel pour les événements */
  --event-pink: #fce7f3;
  --event-pink-border: #f9a8d4;
  --event-purple: #f3e8ff;
  --event-purple-border: #c084fc;
  --event-blue: #dbeafe;
  --event-blue-border: #60a5fa;
  --event-green: #d1fae5;
  --event-green-border: #6ee7b7;
  --event-yellow: #fef3c7;
  --event-yellow-border: #fbbf24;
  --event-orange: #fed7aa;
  --event-orange-border: #fb923c;
  --event-red: #fee2e2;
  --event-red-border: #f87171;
  --event-gray: #f3f4f6;
  --event-gray-border: #9ca3af;
}

/* Container principal du calendrier */
.modern-calendar-container {
  background: var(--calendar-white);
  border-radius: 16px;
  box-shadow: var(--calendar-shadow-lg);
  overflow: hidden;
  border: 1px solid var(--calendar-border);
}

/* Styles généraux pour tous les calendriers */
.modern-calendar-style {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  background: var(--calendar-white);
  border: none;
  border-radius: 16px;
  overflow: hidden;
}

/* En-têtes des jours de la semaine */
.modern-calendar-style .rbc-header {
  background: var(--calendar-bg);
  border-bottom: 1px solid var(--calendar-border);
  padding: 16px 8px;
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: var(--calendar-text-muted);
  text-align: center;
}

/* Vue mensuelle */
.modern-calendar-style .rbc-month-view {
  border: none;
  background: var(--calendar-white);
}

/* Cellules des jours */
.modern-calendar-style .rbc-day-bg {
  background: var(--calendar-white);
  border-right: 1px solid var(--calendar-border);
  border-bottom: 1px solid var(--calendar-border);
  min-height: 120px;
  transition: background-color 0.2s ease;
}

.modern-calendar-style .rbc-day-bg:hover {
  background: var(--calendar-hover-bg);
}

/* Jours hors du mois actuel */
.modern-calendar-style .rbc-off-range-bg {
  background: var(--calendar-bg);
  color: var(--calendar-text-muted);
}

/* Jour actuel */
.modern-calendar-style .rbc-today {
  background: var(--calendar-today-bg);
  position: relative;
}

/* Numéros des jours */
.modern-calendar-style .rbc-date-cell {
  padding: 8px 12px;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--calendar-text-primary);
  text-align: left;
}

.modern-calendar-style .rbc-date-cell.rbc-now {
  color: #3b82f6;
  font-weight: 700;
}

/* Styles des événements */
.modern-calendar-style .rbc-event {
  border: none;
  border-radius: 8px;
  padding: 4px 8px;
  margin: 2px 4px;
  font-size: 0.75rem;
  font-weight: 500;
  line-height: 1.2;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
}

/* Styles pour les événements personnels avec couleurs personnalisées */
.modern-calendar-style .rbc-event.personal-event {
  border-radius: 6px !important;
  color: #ffffff !important;
  font-weight: 600 !important;
  padding: 2px 6px !important;
  min-height: 20px !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  border-left: none !important;
}

/* Styles pour les événements unifiés (personnel et équipe) */
.modern-calendar-style .rbc-event.unified-event {
  border-radius: 6px !important;
  color: #ffffff !important;
  font-weight: 600 !important;
  padding: 2px 6px !important;
  min-height: 20px !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  border-left: none !important;
  background-image: none !important;
  border-image: none !important;
}

/* Forcer l'affichage des couleurs personnalisées */
.modern-calendar-style .rbc-event.personal-event[style*="background-color"],
.modern-calendar-style .rbc-event.unified-event[style*="background-color"] {
  background: var(--event-bg) !important;
  border: 2px solid var(--event-border) !important;
}

.modern-calendar-style .rbc-event:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Couleurs des événements selon le statut */
.modern-calendar-style .rbc-event.status-pending {
  background: var(--event-blue);
  border-left-color: var(--event-blue-border);
  color: #1e40af;
}

.modern-calendar-style .rbc-event.status-completed {
  background: var(--event-green);
  border-left-color: var(--event-green-border);
  color: #166534;
}

.modern-calendar-style .rbc-event.status-archived {
  background: var(--event-gray);
  border-left-color: var(--event-gray-border);
  color: var(--calendar-text-muted);
  opacity: 0.7;
  text-decoration: line-through;
}

/* Couleurs personnalisées pour les événements */
.modern-calendar-style .rbc-event.color-pink {
  background: var(--event-pink);
  border-left-color: var(--event-pink-border);
  color: #be185d;
}

.modern-calendar-style .rbc-event.color-purple {
  background: var(--event-purple);
  border-left-color: var(--event-purple-border);
  color: #7c3aed;
}

.modern-calendar-style .rbc-event.color-yellow {
  background: var(--event-yellow);
  border-left-color: var(--event-yellow-border);
  color: #d97706;
}

.modern-calendar-style .rbc-event.color-orange {
  background: var(--event-orange);
  border-left-color: var(--event-orange-border);
  color: #ea580c;
}

.modern-calendar-style .rbc-event.color-red {
  background: var(--event-red);
  border-left-color: var(--event-red-border);
  color: #dc2626;
}

/* Barre d'outils */
.modern-calendar-style .rbc-toolbar {
  padding: 20px 24px;
  background: var(--calendar-white);
  border-bottom: 1px solid var(--calendar-border);
  margin-bottom: 0;
}

.modern-calendar-style .rbc-toolbar button {
  background: var(--calendar-white);
  border: 1px solid var(--calendar-border);
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--calendar-text-secondary);
  transition: all 0.2s ease;
  margin: 0 2px;
}

.modern-calendar-style .rbc-toolbar button:hover {
  background: var(--calendar-hover-bg);
  border-color: #cbd5e0;
  color: var(--calendar-text-primary);
}

.modern-calendar-style .rbc-toolbar button.rbc-active {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
  box-shadow: 0 1px 2px rgba(59, 130, 246, 0.3);
}

.modern-calendar-style .rbc-toolbar-label {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--calendar-text-primary);
  margin: 0 16px;
}

/* Masquer la barre d'outils si nécessaire */
.modern-calendar-style.hide-toolbar .rbc-toolbar {
  display: none;
}

/* Vue agenda */
.modern-calendar-style .rbc-agenda-view {
  background: var(--calendar-white);
}

.modern-calendar-style .rbc-agenda-view table.rbc-agenda-table {
  border: 1px solid var(--calendar-border);
  border-radius: 12px;
  overflow: hidden;
  background: var(--calendar-white);
}

.modern-calendar-style .rbc-agenda-view table.rbc-agenda-table thead>tr>th {
  background: var(--calendar-bg);
  padding: 16px;
  font-weight: 600;
  font-size: 0.875rem;
  color: var(--calendar-text-secondary);
  border-bottom: 1px solid var(--calendar-border);
}

.modern-calendar-style .rbc-agenda-view table.rbc-agenda-table tbody>tr>td {
  padding: 16px;
  border-bottom: 1px solid var(--calendar-border);
  color: var(--calendar-text-primary);
}

.modern-calendar-style .rbc-agenda-view table.rbc-agenda-table tbody>tr:last-child>td {
  border-bottom: none;
}

/* Responsive design */
@media (max-width: 768px) {
  .modern-calendar-style .rbc-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .modern-calendar-style .rbc-toolbar-label {
    text-align: center;
    margin: 0;
  }

  .modern-calendar-style .rbc-btn-group {
    display: flex;
    justify-content: center;
    gap: 4px;
  }

  .modern-calendar-style .rbc-day-bg {
    min-height: 80px;
  }

  .modern-calendar-style .rbc-event {
    font-size: 0.7rem;
    padding: 2px 6px;
    margin: 1px 2px;
  }
}

/* Animation pour les événements */
@keyframes eventAppear {
  from {
    opacity: 0;
    transform: scale(0.95);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

.modern-calendar-style .rbc-event {
  animation: eventAppear 0.2s ease-out;
}

/* Styles pour les événements sélectionnés */
.modern-calendar-style .rbc-event.rbc-selected {
  box-shadow: 0 0 0 2px #3b82f6;
  transform: translateY(-1px);
}

/* Amélioration de l'accessibilité */
.modern-calendar-style .rbc-event:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.modern-calendar-style .rbc-toolbar button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}