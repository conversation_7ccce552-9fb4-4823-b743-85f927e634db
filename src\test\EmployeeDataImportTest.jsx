import React, { useEffect, useState } from 'react';
import { useEmployee } from '@/contexts/EmployeeContext';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, XCircle, Loader2, RefreshCw } from 'lucide-react';

/**
 * Composant de test pour vérifier l'importation des données des employés
 * Ce composant teste si les tâches et événements d'équipe sont correctement importés
 */
const EmployeeDataImportTest = () => {
  const { user } = useAuth();
  const { 
    metrics, 
    teams, 
    loading, 
    fetchEmployeeMetrics, 
    teamTasks, 
    teamEvents, 
    personalTasks, 
    personalEvents 
  } = useEmployee();
  
  const [testResults, setTestResults] = useState({});
  const [testing, setTesting] = useState(false);

  // Fonction pour exécuter les tests
  const runTests = async () => {
    setTesting(true);
    const results = {};

    try {
      // Test 1: Vérifier si l'utilisateur est un employé
      results.userRole = {
        name: 'Rôle utilisateur',
        passed: user?.role === 'employee',
        message: user?.role === 'employee' 
          ? 'Utilisateur est bien un employé' 
          : `Utilisateur n'est pas un employé (rôle: ${user?.role})`
      };

      // Test 2: Vérifier si les métriques sont chargées
      results.metricsLoaded = {
        name: 'Métriques chargées',
        passed: metrics !== null,
        message: metrics !== null 
          ? 'Métriques chargées avec succès' 
          : 'Métriques non chargées'
      };

      // Test 3: Vérifier si les équipes sont chargées
      results.teamsLoaded = {
        name: 'Équipes chargées',
        passed: Array.isArray(teams) && teams.length >= 0,
        message: Array.isArray(teams) 
          ? `${teams.length} équipe(s) chargée(s)` 
          : 'Équipes non chargées'
      };

      // Test 4: Vérifier si les tâches d'équipe sont chargées
      results.teamTasksLoaded = {
        name: 'Tâches d\'équipe chargées',
        passed: Array.isArray(teamTasks),
        message: Array.isArray(teamTasks) 
          ? `${teamTasks.length} tâche(s) d'équipe chargée(s)` 
          : 'Tâches d\'équipe non chargées'
      };

      // Test 5: Vérifier si les événements d'équipe sont chargés
      results.teamEventsLoaded = {
        name: 'Événements d\'équipe chargés',
        passed: Array.isArray(teamEvents),
        message: Array.isArray(teamEvents) 
          ? `${teamEvents.length} événement(s) d'équipe chargé(s)` 
          : 'Événements d\'équipe non chargés'
      };

      // Test 6: Vérifier si les tâches personnelles sont chargées
      results.personalTasksLoaded = {
        name: 'Tâches personnelles chargées',
        passed: Array.isArray(personalTasks),
        message: Array.isArray(personalTasks) 
          ? `${personalTasks.length} tâche(s) personnelle(s) chargée(s)` 
          : 'Tâches personnelles non chargées'
      };

      // Test 7: Vérifier si les événements personnels sont chargés
      results.personalEventsLoaded = {
        name: 'Événements personnels chargés',
        passed: Array.isArray(personalEvents),
        message: Array.isArray(personalEvents) 
          ? `${personalEvents.length} événement(s) personnel(s) chargé(s)` 
          : 'Événements personnels non chargés'
      };

      // Test 8: Vérifier la structure des métriques
      if (metrics) {
        results.metricsStructure = {
          name: 'Structure des métriques',
          passed: metrics.personal_metrics && Array.isArray(metrics.team_metrics),
          message: (metrics.personal_metrics && Array.isArray(metrics.team_metrics))
            ? 'Structure des métriques correcte'
            : 'Structure des métriques incorrecte'
        };
      }

      setTestResults(results);
    } catch (error) {
      console.error('Erreur lors des tests:', error);
      results.error = {
        name: 'Erreur générale',
        passed: false,
        message: `Erreur: ${error.message}`
      };
      setTestResults(results);
    } finally {
      setTesting(false);
    }
  };

  // Exécuter les tests au montage du composant
  useEffect(() => {
    if (user?.role === 'employee') {
      runTests();
    }
  }, [user, metrics, teams, teamTasks, teamEvents, personalTasks, personalEvents]);

  // Calculer le nombre de tests réussis
  const passedTests = Object.values(testResults).filter(test => test.passed).length;
  const totalTests = Object.keys(testResults).length;

  if (user?.role !== 'employee') {
    return (
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="text-red-600">Test non applicable</CardTitle>
          <CardDescription>
            Ce test est uniquement pour les employés. Rôle actuel: {user?.role}
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            Test d'importation des données employés
            {testing && <Loader2 className="h-5 w-5 animate-spin" />}
          </CardTitle>
          <CardDescription>
            Vérification de l'importation correcte des tâches et événements d'équipe
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <div className="text-sm text-gray-600">
              Tests réussis: {passedTests}/{totalTests}
            </div>
            <Button 
              onClick={runTests} 
              disabled={testing}
              variant="outline"
              size="sm"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${testing ? 'animate-spin' : ''}`} />
              Relancer les tests
            </Button>
          </div>

          <div className="space-y-3">
            {Object.entries(testResults).map(([key, test]) => (
              <div 
                key={key} 
                className={`flex items-center gap-3 p-3 rounded-lg border ${
                  test.passed 
                    ? 'bg-green-50 border-green-200' 
                    : 'bg-red-50 border-red-200'
                }`}
              >
                {test.passed ? (
                  <CheckCircle className="h-5 w-5 text-green-600" />
                ) : (
                  <XCircle className="h-5 w-5 text-red-600" />
                )}
                <div className="flex-1">
                  <div className="font-medium">{test.name}</div>
                  <div className="text-sm text-gray-600">{test.message}</div>
                </div>
              </div>
            ))}
          </div>

          {totalTests > 0 && (
            <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">Résumé des données:</h4>
              <div className="text-sm text-blue-800 space-y-1">
                <div>• Équipes: {teams?.length || 0}</div>
                <div>• Tâches d'équipe: {teamTasks?.length || 0}</div>
                <div>• Événements d'équipe: {teamEvents?.length || 0}</div>
                <div>• Tâches personnelles: {personalTasks?.length || 0}</div>
                <div>• Événements personnels: {personalEvents?.length || 0}</div>
                <div>• Métriques disponibles: {metrics ? 'Oui' : 'Non'}</div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default EmployeeDataImportTest;
