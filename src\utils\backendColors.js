// Configuration des couleurs backend pour les événements personnels et d'équipe

// Couleurs personnelles disponibles dans le backend
export const PERSONAL_COLORS = [
  { 
    id: 'bleu_personnel', 
    name: 'Bleu Personnel', 
    hex: '#3788d8',
    category: 'personal'
  },
  { 
    id: 'vert_personnel', 
    name: 'Vert Personnel', 
    hex: '#10b981',
    category: 'personal'
  },
  { 
    id: 'rouge_personnel', 
    name: 'Rouge Personnel', 
    hex: '#ef4444',
    category: 'personal'
  },
  { 
    id: 'violet_personnel', 
    name: 'Violet Personnel', 
    hex: '#8b5cf6',
    category: 'personal'
  },
  { 
    id: 'rose_personnel', 
    name: 'Rose <PERSON>', 
    hex: '#ec4899',
    category: 'personal'
  },
  { 
    id: 'orange_personnel', 
    name: 'Orange Personnel', 
    hex: '#f97316',
    category: 'personal'
  }
];

// Couleurs d'équipe disponibles dans le backend
export const TEAM_COLORS = [
  { 
    id: 'bleu_equipe', 
    name: 'Bleu É<PERSON>pe', 
    hex: '#1e40af',
    category: 'team'
  },
  { 
    id: 'vert_equipe', 
    name: '<PERSON><PERSON>', 
    hex: '#059669',
    category: 'team'
  },
  { 
    id: 'rouge_equipe', 
    name: 'Rouge Équipe', 
    hex: '#dc2626',
    category: 'team'
  },
  { 
    id: 'violet_equipe', 
    name: 'Violet Équipe', 
    hex: '#7c3aed',
    category: 'team'
  },
  { 
    id: 'turquoise_equipe', 
    name: 'Turquoise Équipe', 
    hex: '#0891b2',
    category: 'team'
  },
  { 
    id: 'indigo_equipe', 
    name: 'Indigo Équipe', 
    hex: '#4338ca',
    category: 'team'
  }
];

// Toutes les couleurs disponibles
export const ALL_COLORS = [...PERSONAL_COLORS, ...TEAM_COLORS];

// Fonction pour obtenir une couleur par son ID
export const getColorById = (colorId) => {
  return ALL_COLORS.find(color => color.id === colorId);
};

// Fonction pour obtenir la valeur hex d'une couleur
export const getColorHex = (colorData) => {
  // Si c'est déjà une couleur hex
  if (typeof colorData === 'string' && colorData.startsWith('#')) {
    return colorData;
  }
  
  // Si c'est un ID de couleur
  if (typeof colorData === 'string') {
    const color = getColorById(colorData);
    return color ? color.hex : '#3788d8'; // Couleur par défaut
  }
  
  // Si c'est un objet avec des propriétés
  if (typeof colorData === 'object' && colorData !== null) {
    if (colorData.hex) return colorData.hex;
    if (colorData.id) {
      const color = getColorById(colorData.id);
      return color ? color.hex : '#3788d8';
    }
  }
  
  // Couleur par défaut
  return '#3788d8';
};

// Fonction pour obtenir l'ID d'une couleur à partir de sa valeur hex
export const getColorIdByHex = (hex) => {
  const color = ALL_COLORS.find(color => color.hex.toLowerCase() === hex.toLowerCase());
  return color ? color.id : null;
};

// Fonction pour valider une couleur backend
export const validateBackendColor = (colorData) => {
  if (typeof colorData === 'string') {
    // Vérifier si c'est un ID valide
    if (getColorById(colorData)) {
      return { isValid: true, type: 'id', value: colorData };
    }
    // Vérifier si c'est une couleur hex valide
    if (colorData.match(/^#[0-9A-F]{6}$/i)) {
      return { isValid: true, type: 'hex', value: colorData };
    }
  }
  
  if (typeof colorData === 'object' && colorData !== null) {
    if (colorData.id && getColorById(colorData.id)) {
      return { isValid: true, type: 'object', value: colorData };
    }
  }
  
  return { isValid: false, type: 'unknown', value: colorData };
};

export default {
  PERSONAL_COLORS,
  TEAM_COLORS,
  ALL_COLORS,
  getColorById,
  getColorHex,
  getColorIdByHex,
  validateBackendColor
};
