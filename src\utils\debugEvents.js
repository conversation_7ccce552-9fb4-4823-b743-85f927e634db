import axios from 'axios';

import { API_URL } from '@/config/constants';

// Fonction pour tester la récupération des événements
export const testFetchEvents = async () => {
  console.log('Debug - testFetchEvents - Starting test');

  try {
    // Récupérer le token depuis le localStorage
    const token = localStorage.getItem('authToken');
    console.log('Debug - testFetchEvents - Auth token:', token ? 'Token exists' : 'No token');

    if (!token) {
      console.error('Debug - testFetchEvents - No auth token found');
      return { success: false, error: 'No auth token found' };
    }

    // Récupérer l'utilisateur depuis le localStorage
    const userStr = localStorage.getItem('user');
    console.log('Debug - testFetchEvents - User from localStorage:', userStr);

    if (!userStr) {
      console.error('Debug - testFetchEvents - No user found in localStorage');
      return { success: false, error: 'No user found in localStorage' };
    }

    // Analyser l'utilisateur
    let user;
    try {
      user = JSON.parse(userStr);
      console.log('Debug - testFetchEvents - User role:', user.role, 'User ID:', user.id);
    } catch (error) {
      console.error('Debug - testFetchEvents - Error parsing user:', error);
      return { success: false, error: 'Error parsing user' };
    }

    // Vérifier si l'utilisateur est autorisé
    if (user.role === 'super_admin') {
      console.error('Debug - testFetchEvents - Super admin cannot access events');
      return { success: false, error: 'Super admin cannot access events' };
    }

    // Faire la requête
    console.log('Debug - testFetchEvents - Making request to /events/');
    const response = await axios.get(`${API_URL}/events/`, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('Debug - testFetchEvents - Response status:', response.status);
    console.log('Debug - testFetchEvents - Response data length:', response.data ? (Array.isArray(response.data) ? response.data.length : 'object') : 'no data');

    return {
      success: true,
      data: response.data,
      status: response.status
    };
  } catch (error) {
    console.error('Debug - testFetchEvents - Error:', error);

    // Afficher plus de détails sur l'erreur
    if (error.response) {
      console.error('Debug - testFetchEvents - Response status:', error.response.status);
      console.error('Debug - testFetchEvents - Response data:', error.response.data);
    } else if (error.request) {
      console.error('Debug - testFetchEvents - No response received');
    } else {
      console.error('Debug - testFetchEvents - Error message:', error.message);
    }

    return {
      success: false,
      error: error.response?.data || error.message || 'Unknown error',
      status: error.response?.status
    };
  }
};

// Fonction pour tester la récupération d'un événement spécifique
export const testFetchEvent = async (eventId) => {
  console.log(`Debug - testFetchEvent - Starting test for event ID: ${eventId}`);

  try {
    // Récupérer le token depuis le localStorage
    const token = localStorage.getItem('authToken');
    console.log('Debug - testFetchEvent - Auth token:', token ? 'Token exists' : 'No token');

    if (!token) {
      console.error('Debug - testFetchEvent - No auth token found');
      return { success: false, error: 'No auth token found' };
    }

    // Faire la requête
    console.log(`Debug - testFetchEvent - Making request to /events/${eventId}/`);
    const response = await axios.get(`${API_URL}/events/${eventId}/`, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('Debug - testFetchEvent - Response status:', response.status);
    console.log('Debug - testFetchEvent - Response data:', response.data ? 'Data received' : 'No data');

    return {
      success: true,
      data: response.data,
      status: response.status
    };
  } catch (error) {
    console.error('Debug - testFetchEvent - Error:', error);

    // Afficher plus de détails sur l'erreur
    if (error.response) {
      console.error('Debug - testFetchEvent - Response status:', error.response.status);
      console.error('Debug - testFetchEvent - Response data:', error.response.data);
    } else if (error.request) {
      console.error('Debug - testFetchEvent - No response received');
    } else {
      console.error('Debug - testFetchEvent - Error message:', error.message);
    }

    return {
      success: false,
      error: error.response?.data || error.message || 'Unknown error',
      status: error.response?.status
    };
  }
};
