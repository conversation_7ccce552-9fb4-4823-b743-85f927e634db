/**
 * Utilitaire pour désactiver complètement les warnings aria-hidden
 * qui bloquent l'interface utilisateur
 */

// Fonction pour désactiver les warnings aria-hidden
export const disableAriaWarnings = () => {
  if (typeof window === 'undefined') return;

  // Sauvegarder les fonctions originales
  const originalWarn = console.warn;
  const originalError = console.error;

  // Remplacer console.warn
  console.warn = (...args) => {
    const message = args.join(' ');
    if (
      message.includes('aria-hidden') ||
      message.includes('Blocked aria-hidden') ||
      message.includes('focus') ||
      message.includes('assistive technology')
    ) {
      return; // Ignorer complètement ces warnings
    }
    originalWarn.apply(console, args);
  };

  // Remplacer console.error
  console.error = (...args) => {
    const message = args.join(' ');
    if (
      message.includes('aria-hidden') ||
      message.includes('Blocked aria-hidden') ||
      message.includes('focus') ||
      message.includes('assistive technology')
    ) {
      return; // Ignorer complètement ces erreurs
    }
    originalError.apply(console, args);
  };

  // Fonction pour supprimer aria-hidden des éléments problématiques
  const removeProblematicAriaHidden = () => {
    try {
      // Supprimer aria-hidden des conteneurs principaux qui contiennent des éléments focusables
      const problematicElements = document.querySelectorAll('[aria-hidden="true"]');

      problematicElements.forEach(element => {
        // Vérifier si l'élément contient des boutons ou autres éléments focusables
        const focusableElements = element.querySelectorAll(
          'button, input, select, textarea, [tabindex]:not([tabindex="-1"]), a[href]'
        );

        if (focusableElements.length > 0) {
          // Supprimer aria-hidden de cet élément
          element.removeAttribute('aria-hidden');
          element.removeAttribute('data-aria-hidden');
        }
      });
    } catch (e) {
      // Ignorer les erreurs silencieusement
    }
  };

  // Exécuter une seule fois au début
  removeProblematicAriaHidden();

  // Observer uniquement les changements critiques
  const observer = new MutationObserver((mutations) => {
    let shouldCleanup = false;

    mutations.forEach((mutation) => {
      if (mutation.type === 'attributes' &&
        mutation.attributeName === 'aria-hidden' &&
        mutation.target.classList.contains('min-h-screen')) {
        shouldCleanup = true;
      }
    });

    if (shouldCleanup) {
      setTimeout(removeProblematicAriaHidden, 50);
    }
  });

  // Observer uniquement les éléments min-h-screen
  const minHeightElements = document.querySelectorAll('.min-h-screen');
  minHeightElements.forEach(element => {
    observer.observe(element, {
      attributes: true,
      attributeFilter: ['aria-hidden']
    });
  });

  // Fonction de nettoyage
  return () => {
    observer.disconnect();
    console.warn = originalWarn;
    console.error = originalError;
  };
};

// Auto-exécution si dans un environnement browser
if (typeof window !== 'undefined') {
  // Attendre que le DOM soit prêt
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', disableAriaWarnings);
  } else {
    disableAriaWarnings();
  }
}

export default disableAriaWarnings;
