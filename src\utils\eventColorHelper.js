// Utilitaire unifié pour la gestion des couleurs d'événements dans tous les calendriers

import { getColorHex } from './backendColors';

/**
 * Fonction unifiée pour obtenir le style d'un événement
 * Utilisée par tous les calendriers (personnel et équipe)
 */
export const getUnifiedEventStyle = (event, isArchived = false) => {
  // Récupérer la couleur de l'événement
  const eventColorValue = getColorHex(event.color) || getColorHex(event.resource?.color);

  // Style unifié pour tous les événements
  const style = {
    '--event-bg': eventColorValue,
    '--event-border': eventColorValue,
    backgroundColor: eventColorValue,
    border: `2px solid ${eventColorValue}`,
    borderRadius: '6px',
    color: '#ffffff',
    fontWeight: '600',
    fontSize: '0.75rem',
    cursor: 'pointer',
    padding: '2px 6px',
    textDecoration: isArchived ? 'line-through' : 'none',
    opacity: isArchived ? 0.7 : 1,
    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
    minHeight: '20px',
    // Forcer l'affichage avec des propriétés spécifiques
    backgroundImage: 'none',
    borderImage: 'none'
  };

  return {
    style: style,
    className: `unified-event ${isArchived ? 'event-archived' : ''}`.trim()
  };
};

/**
 * Fonction pour formater les événements pour le calendrier
 * Gère les couleurs de manière unifiée
 */
export const formatEventsForUnifiedCalendar = (events) => {
  return (events || [])
    .filter(event => {
      // Filtrer AVANT le mapping pour éviter les erreurs
      if (!event || !event.id || !event.title || !event.start_date || !event.end_date) {
        console.warn('❌ Event filtered out due to missing required data:', event);
        return false;
      }
      return true;
    })
    .map(event => {
      try {
        // Parser les dates de manière sécurisée
        const parseEventDate = (dateString) => {
          if (!dateString) throw new Error('Date string is empty');

          // Si c'est déjà un objet Date, le retourner
          if (dateString instanceof Date) return dateString;

          // Si c'est une chaîne au format YYYY-MM-DD, créer une date locale
          if (typeof dateString === 'string' && dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
            const [year, month, day] = dateString.split('-').map(Number);
            return new Date(year, month - 1, day);
          }

          // Sinon, utiliser le constructeur Date normal
          const date = new Date(dateString);
          if (isNaN(date.getTime())) {
            throw new Error('Invalid date');
          }
          return date;
        };

        const startDate = parseEventDate(event.start_date);
        const endDate = parseEventDate(event.end_date);

        // Validation des dates
        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
          throw new Error('Invalid date objects');
        }

        // Ajouter un jour à la date de fin pour l'affichage correct dans le calendrier
        const displayEndDate = new Date(endDate);
        displayEndDate.setDate(displayEndDate.getDate() + 1);

        // Utiliser la fonction getColorHex pour gérer les couleurs backend
        const eventColor = getColorHex(event.color);

        return {
          id: event.id,
          title: event.title,
          start: startDate,
          end: displayEndDate,
          description: event.description || '',
          status: event.status || 'pending',
          is_archived: event.is_archived || false,
          color: eventColor,
          allDay: true,
          resource: {
            ...event,
            // S'assurer que le statut et is_archived sont correctement transmis
            status: event.status || 'pending',
            is_archived: event.is_archived || false,
            color: eventColor // Ajouter la couleur extraite à la resource
          }
        };
      } catch (error) {
        console.error('Error formatting event dates for event:', event.id, error);
        return null;
      }
    })
    .filter(Boolean); // Filtrer les événements null après le mapping
};

/**
 * Fonction pour créer un eventStyleGetter unifié
 * Utilisée par tous les calendriers
 */
export const createUnifiedEventStyleGetter = (eventType = 'personal') => {
  return (event) => {
    // Déterminer si l'événement est archivé
    const isArchived = event.resource && (
      event.resource.status === 'archived' ||
      event.resource.is_archived === true
    );

    return getUnifiedEventStyle(event, isArchived);
  };
};

/**
 * Messages français unifiés pour tous les calendriers
 */
export const UNIFIED_FRENCH_MESSAGES = {
  allDay: 'Journée',
  previous: 'Précédent',
  next: 'Suivant',
  today: 'Aujourd\'hui',
  month: 'Mois',
  week: 'Semaine',
  day: 'Jour',
  agenda: 'Agenda',
  date: 'Date',
  time: 'Heure',
  event: 'Événement',
  noEventsInRange: 'Aucun événement dans cette période',
  showMore: total => `+ ${total} événement(s) supplémentaire(s)`
};

/**
 * Formats français unifiés pour tous les calendriers
 */
export const UNIFIED_FRENCH_FORMATS = {
  dateFormat: 'DD',
  dayFormat: (date, culture, localizer) => localizer.format(date, 'dddd', culture),
  dayRangeHeaderFormat: ({ start, end }, culture, localizer) => {
    return `${localizer.format(start, 'MMMM YYYY', culture)}`;
  },
  monthHeaderFormat: (date, culture, localizer) => localizer.format(date, 'MMMM YYYY', culture),
  dayHeaderFormat: (date, culture, localizer) => localizer.format(date, 'dddd DD MMMM', culture),
  agendaDateFormat: (date, culture, localizer) => localizer.format(date, 'dddd DD MMMM', culture),
  agendaTimeFormat: (date, culture, localizer) => localizer.format(date, 'HH:mm', culture),
  agendaTimeRangeFormat: ({ start, end }, culture, localizer) => {
    return `${localizer.format(start, 'HH:mm', culture)} - ${localizer.format(end, 'HH:mm', culture)}`;
  }
};

/**
 * Configuration unifiée pour tous les calendriers
 */
export const UNIFIED_CALENDAR_CONFIG = {
  culture: 'fr',
  toolbar: false,
  popup: true,
  popupOffset: { x: 30, y: 20 },
  showMultiDayTimes: true,
  step: 30,
  timeslots: 2,
  className: 'modern-calendar-style hide-toolbar'
};

export default {
  getUnifiedEventStyle,
  formatEventsForUnifiedCalendar,
  createUnifiedEventStyleGetter,
  UNIFIED_FRENCH_MESSAGES,
  UNIFIED_FRENCH_FORMATS,
  UNIFIED_CALENDAR_CONFIG
};
