/**
 * Utilitaire pour gérer le style des événements dans le calendrier
 */

/**
 * Convertit une couleur hexadécimale en RGBA
 * @param {string} hex - Couleur hexadécimale
 * @param {number} alpha - Valeur d'opacité (0-1)
 * @returns {string} - Couleur au format RGBA
 */
export const hexToRgba = (hex, alpha = 0.7) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ?
        `rgba(${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(result[3], 16)}, ${alpha})` :
        hex;
};

/**
 * Convertit une couleur hexadécimale en objet RGB
 * @param {string} hex - Couleur hexadécimale
 * @returns {object|null} - Objet avec les propriétés r, g, b ou null
 */
export const hexToRgb = (hex) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16)
    } : null;
};

/**
 * Génère le style pour un événement dans le calendrier
 * @param {object} event - L'événement à styliser
 * @returns {object} - Objet contenant className, style et attributes
 */
export const getEventStyle = (event) => {
    // Déterminer si l'événement est archivé
    const isArchived = event.status === 'archived' || (event.resource && event.resource.status === 'archived');

    // Récupérer l'ID de l'événement
    const eventId = event.id || (event.resource && event.resource.id);

    // Préparer la classe CSS
    let className = isArchived ? 'event-archived' : '';
    if (eventId) {
        className += ` event-id-${eventId}`;
    }

    // Style de base pour tous les événements
    let style = {
        borderRadius: '8px',
        opacity: isArchived ? 0.7 : 0.85,
        color: isArchived ? '#6B7280' : '#333',
        border: '1px solid rgba(0,0,0,0.1)',
        display: 'block',
        overflow: 'hidden',
        padding: '3px 8px',
        boxShadow: '0 1px 2px rgba(0,0,0,0.05)',
        fontWeight: '500',
        textDecoration: isArchived ? 'line-through' : 'none',
        fontStyle: isArchived ? 'italic' : 'normal'
    };

    // Attributs data pour cibler l'événement via DOM
    const attrs = eventId ? {
        'data-event-id': eventId,
        'data-event-title': event.title || (event.resource && event.resource.title) || 'Événement'
    } : {};

    // SOLUTION RADICALE: Forcer la récupération de la couleur depuis la variable globale
    let eventColor = null;

    // Si l'événement a un ID valide
    if (eventId) {
        // Priorité 1: Propriété color de l'événement (source de vérité)
        if (event.color && typeof event.color === 'string' && event.color.startsWith('#')) {
            eventColor = event.color;
            console.log(`getEventStyle - Couleur récupérée depuis l'événement pour ${eventId}: ${eventColor}`);

            // Mettre à jour la variable globale
            if (typeof window.eventColors === 'undefined') {
                window.eventColors = {};
            }
            window.eventColors[eventId] = eventColor;
        }

        // Priorité 2: Propriété color de event.resource
        else if (event.resource && event.resource.color &&
            typeof event.resource.color === 'string' && event.resource.color.startsWith('#')) {
            eventColor = event.resource.color;
            console.log(`getEventStyle - Couleur récupérée depuis event.resource pour ${eventId}: ${eventColor}`);

            // Mettre à jour la variable globale
            if (typeof window.eventColors === 'undefined') {
                window.eventColors = {};
            }
            window.eventColors[eventId] = eventColor;
        }

        // Priorité 3: Vérifier dans la variable globale
        else if (typeof window.eventColors !== 'undefined' && window.eventColors[eventId]) {
            eventColor = window.eventColors[eventId];
            console.log(`getEventStyle - Couleur récupérée depuis la variable globale pour l'événement ${eventId}: ${eventColor}`);
        }
    }

    // SOLUTION ULTRA-RADICALE: Forcer l'application de la couleur exacte
    if (eventColor) {
        // Appliquer la couleur exacte sans aucune modification
        style.backgroundColor = eventColor;
        style.opacity = isArchived ? 0.7 : 1.0; // Seule modification: opacité pour les événements archivés

        // Ajouter une bordure plus visible
        const rgb = hexToRgb(eventColor);
        if (rgb) {
            // Bordure plus foncée et plus visible
            style.borderLeft = `4px solid rgba(${Math.max(0, rgb.r - 60)}, ${Math.max(0, rgb.g - 60)}, ${Math.max(0, rgb.b - 60)}, 1.0)`;
        } else {
            // Bordure par défaut si la conversion échoue
            style.borderLeft = `4px solid rgba(0, 0, 0, 0.3)`;
        }

        // SOLUTION ULTRA-RADICALE: Ajouter un attribut de style inline avec !important pour forcer la couleur exacte
        // Utiliser une opacité de 1.0 pour s'assurer que la couleur est exactement celle sélectionnée
        attrs['style'] = `
            background-color: ${eventColor} !important;
            opacity: ${isArchived ? 0.7 : 1.0} !important;
            border-left: ${rgb ?
                `4px solid rgba(${Math.max(0, rgb.r - 60)}, ${Math.max(0, rgb.g - 60)}, ${Math.max(0, rgb.b - 60)}, 1.0)` :
                '4px solid rgba(0, 0, 0, 0.3)'
            } !important;
            color: #000000 !important;
            font-weight: bold !important;
        `;
    } else {
        // Couleurs par défaut selon le statut
        let defaultColor;
        if (event.status === 'completed') {
            className += ' event-completed';
            defaultColor = '#BDEBC8';
            style.backgroundColor = defaultColor;
            style.borderLeft = '3px solid #10B981';
        } else if (event.status === 'pending') {
            className += ' event-pending';

            // Palette de couleurs pastel
            const defaultColors = [
                '#CDB4DB', // Violet pastel
                '#FFC8DD', // Rose clair pastel
                '#FFAFCC', // Rose vif pastel
                '#BDE0FE', // Bleu clair pastel
                '#A2D2FF'  // Bleu pastel
            ];

            // Utiliser l'ID de l'événement pour choisir une couleur de manière déterministe
            if (eventId) {
                const colorIndex = Math.abs(eventId.toString().split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)) % defaultColors.length;
                defaultColor = defaultColors[colorIndex];
            } else {
                defaultColor = '#BDE0FE';
            }

            style.backgroundColor = defaultColor;
            style.borderLeft = '3px solid #3B82F6';
        } else if (isArchived) {
            className += ' event-archived';
            defaultColor = '#F3F4F6';
            style.backgroundColor = defaultColor;
            style.borderLeft = '3px solid #9CA3AF';
        } else {
            defaultColor = '#E2D4F5';
            style.backgroundColor = defaultColor;
        }

        // SOLUTION RADICALE: Mettre à jour la variable globale avec la couleur par défaut
        if (eventId) {
            if (typeof window.eventColors === 'undefined') {
                window.eventColors = {};
            }
            window.eventColors[eventId] = defaultColor;

            // SOLUTION RADICALE: Ajouter un attribut de style inline pour forcer la couleur
            attrs['style'] = `background-color: ${defaultColor} !important;`;
        }
    }

    return {
        className: className,
        style: style,
        attributes: attrs
    };
};
