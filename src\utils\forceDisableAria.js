/**
 * Solution ultra-agressive pour désactiver complètement aria-hidden
 * et débloquer l'interface utilisateur
 */

// Fonction ultra-agressive pour désactiver aria-hidden
export const forceDisableAria = () => {
  if (typeof window === 'undefined') return;

  // Désactiver complètement tous les warnings et erreurs
  const originalWarn = console.warn;
  const originalError = console.error;
  
  console.warn = () => {}; // Ignorer TOUS les warnings
  console.error = () => {}; // Ignorer TOUTES les erreurs

  // Fonction ultra-agressive pour supprimer aria-hidden
  const forceRemoveAriaHidden = () => {
    try {
      // Supprimer aria-hidden de TOUS les éléments
      document.querySelectorAll('[aria-hidden="true"]').forEach(el => {
        el.removeAttribute('aria-hidden');
        el.removeAttribute('data-aria-hidden');
      });
      
      // Supprimer spécifiquement des conteneurs problématiques
      document.querySelectorAll('.min-h-screen, div, main, section, article').forEach(el => {
        if (el.hasAttribute('aria-hidden')) {
          el.removeAttribute('aria-hidden');
          el.removeAttribute('data-aria-hidden');
        }
      });
      
      // Forcer tous les éléments à être visibles et cliquables
      document.querySelectorAll('button, a, input, select, textarea').forEach(el => {
        el.style.pointerEvents = 'auto';
        el.style.visibility = 'visible';
        el.style.opacity = '1';
        if (el.hasAttribute('aria-hidden')) {
          el.removeAttribute('aria-hidden');
        }
      });
    } catch (e) {
      // Ignorer toutes les erreurs
    }
  };

  // Exécuter immédiatement et de manière répétée
  forceRemoveAriaHidden();
  
  // Exécuter toutes les 10ms pour être ultra-agressif
  const aggressiveInterval = setInterval(forceRemoveAriaHidden, 10);
  
  // Observer TOUS les changements
  const observer = new MutationObserver(() => {
    forceRemoveAriaHidden();
  });
  
  observer.observe(document.body, {
    attributes: true,
    childList: true,
    subtree: true,
    attributeFilter: ['aria-hidden', 'data-aria-hidden', 'style', 'class']
  });

  // Intercepter et bloquer les tentatives de définir aria-hidden
  const originalSetAttribute = Element.prototype.setAttribute;
  Element.prototype.setAttribute = function(name, value) {
    if (name === 'aria-hidden' || name === 'data-aria-hidden') {
      return; // Bloquer complètement
    }
    return originalSetAttribute.call(this, name, value);
  };

  // Fonction de nettoyage
  return () => {
    clearInterval(aggressiveInterval);
    observer.disconnect();
    Element.prototype.setAttribute = originalSetAttribute;
    console.warn = originalWarn;
    console.error = originalError;
  };
};

// Auto-exécution immédiate
if (typeof window !== 'undefined') {
  // Exécuter dès que possible
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', forceDisableAria);
  } else {
    forceDisableAria();
  }
  
  // Exécuter aussi au chargement de la fenêtre
  window.addEventListener('load', forceDisableAria);
}

export default forceDisableAria;
