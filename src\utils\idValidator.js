/**
 * Utilitaire pour valider et formater les IDs
 * Cet utilitaire permet de s'assurer que les IDs sont correctement formatés
 * avant d'être envoyés au backend.
 */

/**
 * Valide et formate un ID utilisateur
 * @param {any} userId - ID utilisateur à valider
 * @returns {Object} - Résultat de la validation avec l'ID formaté si valide
 */
export const validateUserId = (userId) => {
  // Si l'ID est null, undefined ou vide
  if (userId === null || userId === undefined || userId === '') {
    return {
      isValid: false,
      error: 'ID utilisateur manquant',
      formattedId: null
    };
  }

  // Convertir en chaîne de caractères
  const idStr = String(userId).trim();

  // Vérifier que l'ID n'est pas vide après le trim
  if (idStr === '') {
    return {
      isValid: false,
      error: 'ID utilisateur vide',
      formattedId: null
    };
  }

  // Vérifier que l'ID ne contient que des caractères alphanumériques, tirets et underscores
  if (!idStr.match(/^[a-zA-Z0-9_-]+$/)) {
    return {
      isValid: false,
      error: 'Format d\'ID utilisateur invalide',
      formattedId: null
    };
  }

  // ID valide
  return {
    isValid: true,
    error: null,
    formattedId: idStr
  };
};

/**
 * Valide et formate un ID d'équipe
 * @param {any} teamId - ID d'équipe à valider
 * @returns {Object} - Résultat de la validation avec l'ID formaté si valide
 */
export const validateTeamId = (teamId) => {
  // Utiliser la même logique que pour les IDs utilisateurs
  return validateUserId(teamId);
};

/**
 * Valide et formate un ID d'événement
 * @param {any} eventId - ID d'événement à valider
 * @returns {Object} - Résultat de la validation avec l'ID formaté si valide
 */
export const validateEventId = (eventId) => {
  // Utiliser la même logique que pour les IDs utilisateurs
  return validateUserId(eventId);
};

/**
 * Valide et formate un ID de tâche
 * @param {any} taskId - ID de tâche à valider
 * @returns {Object} - Résultat de la validation avec l'ID formaté si valide
 */
export const validateTaskId = (taskId) => {
  // Utiliser la même logique que pour les IDs utilisateurs
  return validateUserId(taskId);
};
