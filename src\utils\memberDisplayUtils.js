/**
 * Utilitaires pour l'affichage des assignations de membres
 * Selon le guide de test - Affichage des Noms de Membres
 */

/**
 * Formate l'affichage de l'assignation d'un membre
 * @param {string|null} memberId - ID du membre assigné
 * @param {string|null} memberName - Nom du membre assigné
 * @param {Array} teamMembers - Liste des membres de l'équipe (optionnel pour fallback)
 * @returns {string} - Nom du membre ou "Toute l'équipe"
 */
export const formatMemberAssignment = (memberId, memberName, teamMembers = []) => {
  // Si pas de member_id, c'est assigné à toute l'équipe
  if (!memberId || memberId.trim() === '') {
    return "Toute l'équipe";
  }

  // Si on a le nom du membre, l'utiliser
  if (memberName && memberName.trim() !== '') {
    return memberName;
  }

  // Fallback : chercher le nom dans la liste des membres de l'équipe
  if (teamMembers && Array.isArray(teamMembers)) {
    const member = teamMembers.find(m => String(m.id) === String(memberId));
    if (member && member.name) {
      return member.name;
    }
  }

  // Dernier fallback : afficher l'ID si aucun nom n'est trouvé
  return `Membre (${memberId})`;
};

/**
 * Détermine si une tâche/événement est assigné à toute l'équipe
 * @param {string|null} memberId - ID du membre assigné
 * @returns {boolean} - true si assigné à toute l'équipe
 */
export const isAssignedToWholeTeam = (memberId) => {
  return !memberId || memberId.trim() === '';
};

/**
 * Détermine si une tâche/événement est assigné à un membre spécifique
 * @param {string|null} memberId - ID du membre assigné
 * @returns {boolean} - true si assigné à un membre spécifique
 */
export const isAssignedToSpecificMember = (memberId) => {
  return memberId && memberId.trim() !== '';
};

/**
 * Formate l'affichage pour les formulaires de sélection
 * @param {string|null} memberId - ID du membre sélectionné
 * @returns {string} - Texte à afficher dans le formulaire
 */
export const formatMemberSelectionDisplay = (memberId) => {
  if (!memberId || memberId.trim() === '') {
    return "Toute l'équipe";
  }
  return "Membre spécifique";
};

/**
 * Valide qu'un objet tâche/événement a les bonnes propriétés d'assignation
 * @param {Object} item - Tâche ou événement
 * @returns {Object} - Objet avec les propriétés validées
 */
export const validateMemberAssignment = (item) => {
  if (!item) return null;

  return {
    ...item,
    member_id: item.member_id || '',
    member_name: item.member_name || '',
    displayName: formatMemberAssignment(item.member_id, item.member_name)
  };
};

/**
 * Crée un texte descriptif pour l'assignation (pour les tooltips, logs, etc.)
 * @param {string|null} memberId - ID du membre assigné
 * @param {string|null} memberName - Nom du membre assigné
 * @returns {string} - Description de l'assignation
 */
export const getAssignmentDescription = (memberId, memberName) => {
  if (isAssignedToWholeTeam(memberId)) {
    return "Cette tâche/événement est assigné(e) à tous les membres de l'équipe";
  }
  
  const displayName = memberName || `Membre ${memberId}`;
  return `Cette tâche/événement est assigné(e) spécifiquement à ${displayName}`;
};

/**
 * Formate l'affichage pour les listes avec icônes
 * @param {string|null} memberId - ID du membre assigné
 * @param {string|null} memberName - Nom du membre assigné
 * @param {Array} teamMembers - Liste des membres de l'équipe
 * @returns {Object} - Objet avec le texte et le type d'assignation
 */
export const formatMemberAssignmentWithType = (memberId, memberName, teamMembers = []) => {
  const isWholeTeam = isAssignedToWholeTeam(memberId);
  
  return {
    displayText: formatMemberAssignment(memberId, memberName, teamMembers),
    isWholeTeam,
    isSpecificMember: !isWholeTeam,
    type: isWholeTeam ? 'team' : 'individual'
  };
};
