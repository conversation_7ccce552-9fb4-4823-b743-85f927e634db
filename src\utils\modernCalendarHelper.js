// Utilitaires pour le design moderne des calendriers

// Palette de couleurs modernes pour les événements
export const MODERN_EVENT_COLORS = {
  pink: {
    background: '#fce7f3',
    border: '#f9a8d4',
    text: '#be185d',
    name: '<PERSON>'
  },
  purple: {
    background: '#f3e8ff',
    border: '#c084fc',
    text: '#7c3aed',
    name: 'Violet'
  },
  blue: {
    background: '#dbeafe',
    border: '#60a5fa',
    text: '#1e40af',
    name: 'Bleu'
  },
  green: {
    background: '#d1fae5',
    border: '#6ee7b7',
    text: '#166534',
    name: 'Vert'
  },
  yellow: {
    background: '#fef3c7',
    border: '#fbbf24',
    text: '#d97706',
    name: '<PERSON><PERSON><PERSON>'
  },
  orange: {
    background: '#fed7aa',
    border: '#fb923c',
    text: '#ea580c',
    name: 'Orange'
  },
  red: {
    background: '#fee2e2',
    border: '#f87171',
    text: '#dc2626',
    name: 'Rouge'
  },
  gray: {
    background: '#f3f4f6',
    border: '#9ca3af',
    text: '#4b5563',
    name: 'Gris'
  }
};

// Couleurs par statut
export const STATUS_COLORS = {
  pending: MODERN_EVENT_COLORS.blue,
  completed: MODERN_EVENT_COLORS.green,
  archived: MODERN_EVENT_COLORS.gray,
  cancelled: MODERN_EVENT_COLORS.red
};

// Fonction pour obtenir la couleur d'un événement
export const getEventColor = (event) => {
  // Priorité 1: Couleur personnalisée de l'événement
  if (event.color && event.color.startsWith('#')) {
    return {
      background: event.color + '20', // Ajouter de la transparence
      border: event.color,
      text: event.color
    };
  }

  // Priorité 2: Couleur basée sur le statut
  if (event.status && STATUS_COLORS[event.status]) {
    return STATUS_COLORS[event.status];
  }

  // Priorité 3: Couleur par défaut basée sur l'ID
  const colorKeys = Object.keys(MODERN_EVENT_COLORS);
  const colorIndex = event.id ? Math.abs(event.id.toString().split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)) % colorKeys.length : 0;
  const colorKey = colorKeys[colorIndex];
  
  return MODERN_EVENT_COLORS[colorKey];
};

// Fonction pour styliser un événement
export const getModernEventStyle = (event) => {
  const colors = getEventColor(event);
  const isArchived = event.status === 'archived';
  
  return {
    style: {
      backgroundColor: colors.background,
      borderLeftColor: colors.border,
      color: colors.text,
      opacity: isArchived ? 0.7 : 1,
      textDecoration: isArchived ? 'line-through' : 'none',
      fontStyle: isArchived ? 'italic' : 'normal'
    },
    className: `modern-event status-${event.status || 'pending'} ${isArchived ? 'archived' : ''}`
  };
};

// Messages en français pour le calendrier
export const FRENCH_CALENDAR_MESSAGES = {
  allDay: 'Journée entière',
  previous: 'Précédent',
  next: 'Suivant',
  today: 'Aujourd\'hui',
  month: 'Mois',
  week: 'Semaine',
  day: 'Jour',
  agenda: 'Agenda',
  date: 'Date',
  time: 'Heure',
  event: 'Événement',
  noEventsInRange: 'Aucun événement dans cette période',
  showMore: total => `+ ${total} événement(s) supplémentaire(s)`
};

// Formats personnalisés pour les heures
export const FRENCH_CALENDAR_FORMATS = {
  timeGutterFormat: 'HH:mm',
  eventTimeRangeFormat: ({ start, end }, culture, localizer) => {
    return `${localizer.format(start, 'HH:mm', culture)} - ${localizer.format(end, 'HH:mm', culture)}`;
  },
  agendaTimeFormat: 'HH:mm',
  agendaTimeRangeFormat: ({ start, end }, culture, localizer) => {
    return `${localizer.format(start, 'HH:mm', culture)} - ${localizer.format(end, 'HH:mm', culture)}`;
  },
  dayFormat: 'dddd DD/MM',
  dayHeaderFormat: 'dddd DD MMMM',
  dayRangeHeaderFormat: ({ start, end }, culture, localizer) => {
    return `${localizer.format(start, 'DD MMMM', culture)} - ${localizer.format(end, 'DD MMMM YYYY', culture)}`;
  },
  monthHeaderFormat: 'MMMM YYYY',
  weekdayFormat: 'dddd'
};

// Fonction pour convertir les événements au format calendrier
export const formatEventsForCalendar = (events) => {
  return events.map(event => {
    // Vérifier si les données de date et heure sont présentes
    if (!event.start_date || !event.start_time || !event.end_date || !event.end_time) {
      console.warn('Event missing date/time data:', event);
      return {
        id: event.id,
        title: event.title || 'Événement sans titre',
        start: new Date(),
        end: new Date(new Date().getTime() + 60 * 60 * 1000), // +1 heure
        description: event.description || '',
        status: event.status || 'pending',
        allDay: false,
        resource: event
      };
    }

    try {
      // Utiliser moment pour créer les dates
      const moment = require('moment');
      const startDateTime = moment(`${event.start_date} ${event.start_time}`, 'YYYY-MM-DD HH:mm').toDate();
      const endDateTime = moment(`${event.end_date} ${event.end_time}`, 'YYYY-MM-DD HH:mm').toDate();

      return {
        id: event.id,
        title: event.title,
        start: startDateTime,
        end: endDateTime,
        description: event.description,
        status: event.status,
        color: event.color,
        allDay: false,
        resource: event
      };
    } catch (error) {
      console.error('Error formatting event date/time:', error, event);
      return {
        id: event.id,
        title: event.title || 'Événement sans titre',
        start: new Date(),
        end: new Date(new Date().getTime() + 60 * 60 * 1000),
        description: event.description || '',
        status: event.status || 'pending',
        allDay: false,
        resource: event
      };
    }
  });
};

// Configuration par défaut pour les calendriers modernes
export const MODERN_CALENDAR_CONFIG = {
  className: 'modern-calendar-style',
  messages: FRENCH_CALENDAR_MESSAGES,
  formats: FRENCH_CALENDAR_FORMATS,
  culture: 'fr',
  eventPropGetter: getModernEventStyle,
  style: { height: 650 },
  views: ['month', 'week', 'day', 'agenda'],
  defaultView: 'month',
  popup: true,
  popupOffset: { x: 30, y: 20 },
  showMultiDayTimes: true,
  step: 30,
  timeslots: 2
};

// Fonction pour appliquer le thème moderne à un calendrier existant
export const applyModernTheme = (calendarElement) => {
  if (!calendarElement) return;
  
  // Ajouter les classes CSS modernes
  calendarElement.classList.add('modern-calendar-style');
  
  // Ajouter le conteneur moderne si nécessaire
  const container = calendarElement.closest('.calendar-container');
  if (container) {
    container.classList.add('modern-calendar-container');
  }
};

// Fonction pour obtenir les couleurs disponibles pour la sélection
export const getAvailableColors = () => {
  return Object.entries(MODERN_EVENT_COLORS).map(([key, color]) => ({
    key,
    name: color.name,
    background: color.background,
    border: color.border,
    text: color.text
  }));
};

// Fonction pour valider et normaliser une couleur
export const normalizeEventColor = (color) => {
  if (!color) return null;
  
  // Si c'est une couleur hexadécimale valide
  if (typeof color === 'string' && color.match(/^#[0-9A-F]{6}$/i)) {
    return color;
  }
  
  // Si c'est une clé de couleur prédéfinie
  if (typeof color === 'string' && MODERN_EVENT_COLORS[color]) {
    return MODERN_EVENT_COLORS[color].border;
  }
  
  return null;
};

export default {
  MODERN_EVENT_COLORS,
  STATUS_COLORS,
  getEventColor,
  getModernEventStyle,
  FRENCH_CALENDAR_MESSAGES,
  FRENCH_CALENDAR_FORMATS,
  formatEventsForCalendar,
  MODERN_CALENDAR_CONFIG,
  applyModernTheme,
  getAvailableColors,
  normalizeEventColor
};
