// src/utils/toastUtils.js

import { toast } from 'react-toastify';

// Stockage des derniers messages pour éviter les doublons
let lastMessages = new Map();
const DUPLICATE_TIMEOUT = 2000; // 2 secondes

/**
 * Affiche un toast de succès en évitant les doublons
 * @param {string} message - Le message à afficher
 * @param {object} options - Options pour le toast
 */
export const showSuccessToast = (message, options = {}) => {
    const now = Date.now();
    const lastTime = lastMessages.get(message);

    // Si le même message a été affiché récemment, l'ignorer
    if (lastTime && (now - lastTime) < DUPLICATE_TIMEOUT) {
        console.log('Toast dupliqué ignoré:', message);
        return;
    }

    // Enregistrer le message et l'heure
    lastMessages.set(message, now);

    // Nettoyer les anciens messages
    cleanupOldMessages();

    return toast.success(message, {
        toastId: message, // Utiliser le message comme ID unique
        ...options
    });
};

/**
 * Affiche un toast d'erreur en évitant les doublons
 * @param {string} message - Le message à afficher
 * @param {object} options - Options pour le toast
 */
export const showErrorToast = (message, options = {}) => {
    const now = Date.now();
    const lastTime = lastMessages.get(message);

    // Si le même message a été affiché récemment, l'ignorer
    if (lastTime && (now - lastTime) < DUPLICATE_TIMEOUT) {
        console.log('Toast dupliqué ignoré:', message);
        return;
    }

    // Enregistrer le message et l'heure
    lastMessages.set(message, now);

    // Nettoyer les anciens messages
    cleanupOldMessages();

    return toast.error(message, {
        toastId: message, // Utiliser le message comme ID unique
        ...options
    });
};

/**
 * Affiche un toast d'information en évitant les doublons
 * @param {string} message - Le message à afficher
 * @param {object} options - Options pour le toast
 */
export const showInfoToast = (message, options = {}) => {
    const now = Date.now();
    const lastTime = lastMessages.get(message);

    // Si le même message a été affiché récemment, l'ignorer
    if (lastTime && (now - lastTime) < DUPLICATE_TIMEOUT) {
        console.log('Toast dupliqué ignoré:', message);
        return;
    }

    // Enregistrer le message et l'heure
    lastMessages.set(message, now);

    // Nettoyer les anciens messages
    cleanupOldMessages();

    return toast.info(message, {
        toastId: message, // Utiliser le message comme ID unique
        ...options
    });
};

/**
 * Affiche un toast d'avertissement en évitant les doublons
 * @param {string} message - Le message à afficher
 * @param {object} options - Options pour le toast
 */
export const showWarningToast = (message, options = {}) => {
    const now = Date.now();
    const lastTime = lastMessages.get(message);

    // Si le même message a été affiché récemment, l'ignorer
    if (lastTime && (now - lastTime) < DUPLICATE_TIMEOUT) {
        console.log('Toast dupliqué ignoré:', message);
        return;
    }

    // Enregistrer le message et l'heure
    lastMessages.set(message, now);

    // Nettoyer les anciens messages
    cleanupOldMessages();

    return toast.warn(message, {
        toastId: message, // Utiliser le message comme ID unique
        ...options
    });
};

/**
 * Nettoie les anciens messages du cache
 */
const cleanupOldMessages = () => {
    const now = Date.now();
    const cutoff = now - DUPLICATE_TIMEOUT * 2; // Garder pendant 4 secondes

    for (const [message, time] of lastMessages.entries()) {
        if (time < cutoff) {
            lastMessages.delete(message);
        }
    }
};

/**
 * Efface le cache des messages (utile pour les tests)
 */
export const clearToastCache = () => {
    lastMessages.clear();
};

// Exporter les fonctions toast originales pour les cas spéciaux
export { toast };
